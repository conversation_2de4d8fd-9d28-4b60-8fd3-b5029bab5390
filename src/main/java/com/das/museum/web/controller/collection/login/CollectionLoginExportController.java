package com.das.museum.web.controller.collection.login;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.service.biz.collection.CollectionRegisterService;
import com.das.museum.service.biz.collection.action.RegisterQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionRegisterPageDTO;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.collection.login.request.BaseIdsExcelReq;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 藏品登录导出
 *
 * <AUTHOR>
 * @date 2023/01/09
 */
@RestController
@RequestMapping("/collection/login/export")
@Slf4j
public class CollectionLoginExportController {

    @Resource
    private CollectionRegisterService collectionRegisterService;

    /**
     * 导出藏品登录数据(Excel)
     *
     * @param registerNo
     * @param classifyDesc
     * @param name
     * @param sortBy
     * @param pageNum
     * @param pageSize
     * @param response
     */
    @GetMapping("/baseInfoExcel")
    public void baseInfoExcel(@RequestParam(value = "registerNo", required = false) String registerNo,
                              @RequestParam(value = "classifyDesc", required = false) String classifyDesc,
                              @RequestParam(value = "name", required = false) String name,
                              @RequestParam(value = "sortBy", defaultValue = "gmt_create desc") String sortBy,
                              @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                              @RequestParam(value = "pageSize", defaultValue = "100000") Integer pageSize,
                              HttpServletResponse response) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        RegisterQueryPageByConditionAction action = new RegisterQueryPageByConditionAction();
        action.setSortBy(sortBy);
        action.setCompanyId(userBO.getCompanyId());
        action.setRegisterNo(registerNo);
        action.setClassifyDesc(classifyDesc);
        action.setName(name);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<CollectionRegisterPageDTO> pageInfo = this.collectionRegisterService.queryPageByCondition(action);
        this.writerExcelFormTemplate(response, "collection_template.xlsx", pageInfo.getList());
    }

    /**
     * 导出选中藏品登录数据(Excel)
     *
     * @param req
     * @param response
     */
    @PostMapping("/baseSelectedExcel")
    public void baseSelectedExcel(@RequestBody BaseIdsExcelReq req, HttpServletResponse response) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        RegisterQueryPageByConditionAction action = new RegisterQueryPageByConditionAction();
        action.setSortBy("gmt_create desc");
        action.setCompanyId(userBO.getCompanyId());
        action.setCollectionIds(req.getCollectionIds());
        action.setPageNum(1);
        action.setPageSize(10000);
        SimplePageInfo<CollectionRegisterPageDTO> pageInfo = this.collectionRegisterService.queryPageByCondition(action);
        this.writerExcelFormTemplate(response, "collection_template.xlsx", pageInfo.getList());
    }

    private void writerExcelFormTemplate(HttpServletResponse response, String templateName, List<CollectionRegisterPageDTO> dataList) {
        OutputStream out;
        BufferedOutputStream bos;
        try {
            // String templateFileName = FileUtils.getPath() + "templates" + File.separator + templateName;
            org.springframework.core.io.Resource resource = new ClassPathResource("templates" + File.separator + templateName);
            InputStream inputStream = resource.getInputStream();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("藏品信息.xlsx", "utf-8");
            response.setHeader("Content-disposition", "attachment; filename=" +
                    new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            out = response.getOutputStream();
            bos = new BufferedOutputStream(out);
            ExcelWriter excelWriter = EasyExcel.write(bos).withTemplate(inputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(dataList, writeSheet);
            excelWriter.finish();
            bos.flush();
        } catch (Exception e) {
            log.error("CollectionLoginExportController writerExcelFormTemplate Exception:", e);
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "系统错误, 导出失败!");
        }
    }
}
