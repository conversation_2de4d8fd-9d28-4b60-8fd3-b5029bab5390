package com.das.museum.web.controller.collection.registeridentify;

import com.das.museum.common.bo.PlainResult;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.service.biz.collection.CollectionRegisterIdentifyService;
import com.das.museum.service.biz.collection.action.AddCollectionRegisterIdentifyAction;
import com.das.museum.service.biz.collection.action.EditCollectionRegisterIdentifyAction;
import com.das.museum.service.biz.collection.action.RegisterIdentifyQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionRegisterIdentifyDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionRegisterIdentifyPageDTO;
import com.das.museum.web.aop.annoation.PlainResultWrapper;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.collection.registeridentify.request.AddRegisterIdentifyReq;
import com.das.museum.web.controller.collection.registeridentify.request.EditRegisterIdentifyReq;
import com.das.museum.web.controller.collection.registeridentify.response.CollectionRegisterIdentifyDetailVO;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/collection/register/identify")
public class CollectionRegisterIdentifyController {

    @Resource
    private CollectionRegisterIdentifyService collectionRegisterIdentifyService;

    @PostMapping("/add")
    public PlainResult<Long> addRegisterIdentify(@RequestBody AddRegisterIdentifyReq addRegisterIdentifyReq) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        AddCollectionRegisterIdentifyAction action = this.convertAddCollectionRegisterIdentifyAction(addRegisterIdentifyReq, userBO);
        Long registerIdentifyId = this.collectionRegisterIdentifyService.addCollectionRegisterIdentify(action);
        return PlainResult.success(registerIdentifyId, "新增成功");
    }

    @PostMapping("/edit")
    public PlainResult<Boolean> editRegisterIdentify(@RequestBody EditRegisterIdentifyReq editRegisterIdentifyReq) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        EditCollectionRegisterIdentifyAction action = this.convertEditCollectionRegisterIdentifyAction(editRegisterIdentifyReq, userBO);
        this.collectionRegisterIdentifyService.editCollectionRegisterIdentify(action);
        return PlainResult.success(true, "编辑成功");
    }

    @PostMapping("/del")
    public PlainResult<Boolean> delByRegisterIdentifyId(@RequestParam(value = "registerIdentifyId") Long registerIdentifyId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        this.collectionRegisterIdentifyService.delByRegisterIdentifyId(userBO.getCompanyId(), registerIdentifyId);
        return PlainResult.success(true, "删除成功");
    }

    @GetMapping("/pageList")
    public PlainResult<SimplePageInfo<CollectionRegisterIdentifyPageDTO>> getPageByConditionQuery(@RequestParam(value = "collectionId") Long collectionId,
                                                                                                  @RequestParam(value = "sortBy", defaultValue = "gmt_create desc") String sortBy,
                                                                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                                  @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        RegisterIdentifyQueryPageByConditionAction action = new RegisterIdentifyQueryPageByConditionAction();
        action.setCollectionId(collectionId);
        action.setSortBy(sortBy);
        action.setCompanyId(userBO.getCompanyId());
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<CollectionRegisterIdentifyPageDTO> pageInfo = this.collectionRegisterIdentifyService.queryPageByCondition(action);
        return PlainResult.success(pageInfo, "获取成功");
    }

    @GetMapping("/info")
    public PlainResult<CollectionRegisterIdentifyDetailVO> getDetailByRegisterIdentifyId(@RequestParam(value = "registerIdentifyId") Long registerIdentifyId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        CollectionRegisterIdentifyDetailDTO collectionRegisterIdentifyDetailDTO = this.collectionRegisterIdentifyService.queryByRegisterIdentifyId(userBO.getCompanyId(), registerIdentifyId);
        if (Objects.isNull(collectionRegisterIdentifyDetailDTO)) {
            return PlainResult.success(null, "获取成功");
        }
        CollectionRegisterIdentifyDetailVO collectionRegisterIdentifyDetailVO = BeanCopyUtils.copyByJSON(collectionRegisterIdentifyDetailDTO, CollectionRegisterIdentifyDetailVO.class);
        return PlainResult.success(collectionRegisterIdentifyDetailVO, "获取成功");
    }

    private AddCollectionRegisterIdentifyAction convertAddCollectionRegisterIdentifyAction(AddRegisterIdentifyReq req, LoginUserInfoBO userBO) {
        AddCollectionRegisterIdentifyAction action = new AddCollectionRegisterIdentifyAction();
        action.setCollectionId(req.getCollectionId());
        action.setCompanyId(userBO.getCompanyId());
        action.setIdentifyType(req.getIdentifyType());
        action.setAppraiserId(req.getAppraiserId());
        action.setAppraiserName(req.getAppraiserName());
        action.setOrgAgency(req.getOrgAgency());
        action.setIdentifyAgency(req.getIdentifyAgency());
        action.setApproveAgency(req.getApproveAgency());
        action.setIdentifyOpinion(req.getIdentifyOpinion());
        action.setIdentifyRemark(req.getIdentifyRemark());
        action.setIdentifyDate(req.getIdentifyDate());
        action.setDocumentId(req.getDocumentId());
        action.setCreator(userBO.getUserId());
        return action;
    }

    private EditCollectionRegisterIdentifyAction convertEditCollectionRegisterIdentifyAction(EditRegisterIdentifyReq req, LoginUserInfoBO userBO) {
        EditCollectionRegisterIdentifyAction action = new EditCollectionRegisterIdentifyAction();
        action.setRegisterIdentifyId(req.getRegisterIdentifyId());
        action.setCompanyId(userBO.getCompanyId());
        action.setIdentifyType(req.getIdentifyType());
        action.setAppraiserId(req.getAppraiserId());
        action.setAppraiserName(req.getAppraiserName());
        action.setOrgAgency(req.getOrgAgency());
        action.setIdentifyAgency(req.getIdentifyAgency());
        action.setApproveAgency(req.getApproveAgency());
        action.setIdentifyOpinion(req.getIdentifyOpinion());
        action.setIdentifyRemark(req.getIdentifyRemark());
        action.setIdentifyDate(req.getIdentifyDate());
        action.setDocumentId(req.getDocumentId());
        action.setModifier(userBO.getUserId());
        return action;
    }
}
