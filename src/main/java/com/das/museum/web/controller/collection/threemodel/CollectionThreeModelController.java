package com.das.museum.web.controller.collection.threemodel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.das.museum.common.bo.PlainResult;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.infr.mapper.ScsCollectionThreeModelMapper;
import com.das.museum.service.biz.collection.CollectionThreeModelService;
import com.das.museum.service.biz.collection.UploadCollectionThreeModelTask;
import com.das.museum.service.biz.collection.action.EditThreeModelInfoAction;
import com.das.museum.service.biz.collection.action.QueryThreeModelPageAction;
import com.das.museum.service.biz.collection.dto.QueryThreeModelInfoDTO;
import com.das.museum.service.biz.collection.dto.QueryThreeModelPageListDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.education.UploadProcessFileTask;
import com.das.museum.service.config.GlobalExecutor;
import com.das.museum.service.task.ZipToGet3dTask;
import com.das.museum.web.aop.annoation.PlainResultWrapper;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.collection.threemodel.request.EditThreeModelInfoReq;
import com.das.museum.web.controller.education.response.GetUploadStatusVO;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 藏品三维模型
 */

@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/collection/threeModel")
public class CollectionThreeModelController {

    @Value("${minio.bucketName}")
    private String bucketName;

    @Value("${ftp.uploadConf.ftpPath}")
    private String ftpPath;

    @Resource
    private ZipToGet3dTask zipToGet3dTask;

    @Resource
    private CollectionThreeModelService collectionThreeModelService;

    @Resource
    private DocumentService documentService;

    @Resource
    private ScsCollectionThreeModelMapper scsCollectionThreeModelMapper;

    @Resource
    private RedisTemplate<String, JSONObject> redisTemplate;

    private static final String THREE_MODEL = "threeModel";


    /**
     * 分页查询
     * @param registerInfoId
     * @param keyword
     * @param createStartTime
     * @param createEndTime
     * @param updateStartTime
     * @param updateEndTime
     * @param sortBy
     * @param pageNum
     * @param pageSize
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/pageList")
    public PlainResult<SimplePageInfo<QueryThreeModelPageListDTO>> queryThreeModelPageList(@RequestParam(value = "registerInfoId") Long registerInfoId,
                                                                                           @RequestParam(value = "keyword", required = false) String keyword,
                                                                                           @RequestParam(value = "createStartTime", required = false) String createStartTime,
                                                                                           @RequestParam(value = "createEndTime", required = false) String createEndTime,
                                                                                           @RequestParam(value = "updateStartTime", required = false) String updateStartTime,
                                                                                           @RequestParam(value = "updateEndTime", required = false) String updateEndTime,
                                                                                           @RequestParam(value = "sortBy", defaultValue = "gmt_create desc") String sortBy,
                                                                                           @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                           @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                                           HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        QueryThreeModelPageAction action = new QueryThreeModelPageAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setRegisterInfoId(registerInfoId);
        action.setKeyword(keyword);
        action.setCreateStartTime(createStartTime);
        action.setCreateEndTime(createEndTime);
        action.setUpdateStartTime(updateStartTime);
        action.setUpdateEndTime(updateEndTime);
        //action.setOrderBy(sortBy);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<QueryThreeModelPageListDTO> pageInfo = this.collectionThreeModelService.queryThreeModelPageList(action);
        return PlainResult.success(pageInfo, "获取成功");
    }


    /**
     * 查询三维模型详细信息
     * @param threeModelId
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/info")
    public PlainResult<QueryThreeModelInfoDTO> queryThreeModelInfo(@RequestParam(value = "threeModelId") Long threeModelId,
                                                                   HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        QueryThreeModelInfoDTO info = this.collectionThreeModelService.queryThreeModelInfo(userBO.getCompanyId(),threeModelId);
        return PlainResult.success(info, "获取成功");
    }


    /**
     * 编辑
     * @return
     */
    @PostMapping("/edit")
    public PlainResult<Long> editTwoImageInfo(@RequestBody EditThreeModelInfoReq req,
                                              HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        EditThreeModelInfoAction action = new EditThreeModelInfoAction();
        BeanUtils.copyProperties(req,action);
        action.setCompanyId(userBO.getCompanyId());
        action.setModifier(userBO.getUserId());
        this.collectionThreeModelService.editThreeModelInfo(action);
        return PlainResult.success(req.getThreeModelId(), "编辑成功");
    }

    /**
     * 删除
     * @return
     */
    @PostMapping("/del")
    public PlainResult<Long> delThreeModelInfo(@RequestParam(value = "threeModelId") Long threeModelId,
                                             HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        this.collectionThreeModelService.delThreeModelInfo(userBO.getCompanyId(),threeModelId);
        return PlainResult.success(threeModelId, "删除成功");
    }

    /**
     * 过程文件批量上传
     * @param files                文件集合
     * @param registerInfoId   藏品登录id
     * @param request
     * @param session
     * @return
     */
    @PostMapping(value = "/upload/file")
    public PlainResult<Boolean> upload(@RequestParam(value = "files") MultipartFile[] files,
                                       @RequestParam(value = "registerInfoId") Long registerInfoId,
                                       HttpServletRequest request, HttpSession session) {
        if(files == null || files.length == 0){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "文件不能为空！");
        }
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        redisTemplate.opsForValue().set(THREE_MODEL +"_"+ userBO.getCompanyId()+"_"+registerInfoId+"_"+userBO.getUserId(), JSON.parseObject(""), 7, TimeUnit.DAYS);
        try {
            GlobalExecutor.submitUploadRecord(new UploadCollectionThreeModelTask(files, documentService, redisTemplate, registerInfoId, userBO.getCompanyId(),
                    userBO.getUserId(),bucketName,ftpPath,scsCollectionThreeModelMapper,zipToGet3dTask));
        }catch (Exception e){
            return PlainResult.success(false, "文件已提交");
        }
        return PlainResult.success(true, "文件已提交");
    }


    /**
     * 获取文件上传状态
     * @param registerInfoId  藏品登录id
     * @return
     */
    @GetMapping("/getUploadStatus")
    public PlainResult<GetUploadStatusVO> getUploadStatus(@RequestParam(value = "registerInfoId") Long registerInfoId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        String key = THREE_MODEL +"_"+ userBO.getCompanyId()+"_"+registerInfoId+"_"+userBO.getUserId();

        GetUploadStatusVO vo = new GetUploadStatusVO();
        vo.setUploadStatus(0);
        if(redisTemplate.hasKey(key)){
            vo.setUploadStatus(1);
            JSONObject jsonObject = redisTemplate.opsForValue().get(key);
            String jsonStr = JSON.toJSONString(jsonObject);
            if(StringUtils.isNotBlank(jsonStr)) {
                UploadProcessFileTask.UploadResult uploadResult = JSON.toJavaObject(jsonObject, UploadProcessFileTask.UploadResult.class);
                if(Objects.nonNull(uploadResult)) {
                    vo.setUploadStatus(2);
                    vo.setSuccessNum(uploadResult.getSuccessNum());
                    vo.setFailedNum(uploadResult.getFailedNum());
                    if (StringUtils.isNotBlank(uploadResult.getSuccessAll())) {
                        vo.setSuccessList(Arrays.asList(uploadResult.getSuccessAll().split(",")));
                    }
                    if (StringUtils.isNotBlank(uploadResult.getFailedAll())) {
                        vo.setFailedList(Arrays.asList(uploadResult.getFailedAll().split(",")));
                    }
                }
            }
        }
        return PlainResult.success(vo, "获取成功");
    }

    /**
     * 删除文件上传结果状态
     * @param registerInfoId
     * @return
     */
    @PostMapping("/delUploadStatus")
    public PlainResult<Long> delFile(@RequestParam(value = "registerInfoId") Long registerInfoId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        redisTemplate.delete(THREE_MODEL +"_"+ userBO.getCompanyId()+"_"+registerInfoId+"_"+userBO.getUserId());
        return PlainResult.success(registerInfoId, "删除成功");
    }

}
