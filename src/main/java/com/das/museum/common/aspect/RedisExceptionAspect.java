package com.das.museum.common.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.RedisSystemException;
import org.springframework.stereotype.Component;

/**
 * Redis异常处理切面
 * 用于统一处理Redis连接异常，避免影响主业务流程
 */
@Aspect
@Component
@Slf4j
public class RedisExceptionAspect {

    /**
     * 拦截所有使用RedissonClient的方法
     */
    @Around("execution(* com.das.museum.service.province.schedule.*.*(..))")
    public Object handleRedisException(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            return joinPoint.proceed();
        } catch (RedisConnectionFailureException e) {
            log.error("Redis连接失败，方法: {}.{}, 错误: {}", 
                    joinPoint.getTarget().getClass().getSimpleName(),
                    joinPoint.getSignature().getName(),
                    e.getMessage());
            // 可以选择返回默认值或者重新抛出异常
            return handleRedisConnectionFailure(joinPoint, e);
        } catch (RedisSystemException e) {
            log.error("Redis系统异常，方法: {}.{}, 错误: {}", 
                    joinPoint.getTarget().getClass().getSimpleName(),
                    joinPoint.getSignature().getName(),
                    e.getMessage());
            return handleRedisSystemException(joinPoint, e);
        } catch (Exception e) {
            // 检查是否是Redis相关异常
            if (isRedisException(e)) {
                log.error("Redis操作异常，方法: {}.{}, 错误: {}", 
                        joinPoint.getTarget().getClass().getSimpleName(),
                        joinPoint.getSignature().getName(),
                        e.getMessage());
                return handleGeneralRedisException(joinPoint, e);
            } else {
                // 非Redis异常，继续抛出
                throw e;
            }
        }
    }

    /**
     * 处理Redis连接失败
     */
    private Object handleRedisConnectionFailure(ProceedingJoinPoint joinPoint, Exception e) {
        log.warn("Redis连接失败，跳过定时任务执行: {}.{}", 
                joinPoint.getTarget().getClass().getSimpleName(),
                joinPoint.getSignature().getName());
        // 对于定时任务，连接失败时直接返回null，不影响应用运行
        return null;
    }

    /**
     * 处理Redis系统异常
     */
    private Object handleRedisSystemException(ProceedingJoinPoint joinPoint, Exception e) {
        log.warn("Redis系统异常，跳过定时任务执行: {}.{}", 
                joinPoint.getTarget().getClass().getSimpleName(),
                joinPoint.getSignature().getName());
        return null;
    }

    /**
     * 处理一般Redis异常
     */
    private Object handleGeneralRedisException(ProceedingJoinPoint joinPoint, Exception e) {
        log.warn("Redis操作异常，跳过定时任务执行: {}.{}", 
                joinPoint.getTarget().getClass().getSimpleName(),
                joinPoint.getSignature().getName());
        return null;
    }

    /**
     * 判断是否是Redis相关异常
     */
    private boolean isRedisException(Exception e) {
        String message = e.getMessage();
        String className = e.getClass().getName();
        
        return className.contains("redis") || 
               className.contains("Redis") || 
               className.contains("redisson") ||
               className.contains("Redisson") ||
               (message != null && (
                   message.contains("redis") || 
                   message.contains("Redis") ||
                   message.contains("connection") ||
                   message.contains("timeout")
               ));
    }
}
