package com.das.museum.dependency.ticket.channel;

import com.alibaba.fastjson.JSON;
import com.das.museum.common.builder.HttpClientCustomizeBuilder;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.http.HttpConfig;
import com.das.museum.common.http.HttpHeader;
import com.das.museum.common.http.HttpMethods;
import com.das.museum.common.http.HttpResult;
import com.das.museum.common.utils.HttpClientUtil;
import com.das.museum.dependency.bo.HttpResultBO;
import com.das.museum.dependency.ticket.channel.bo.*;
import com.das.museum.dependency.ticket.constant.TicketHttpConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class TicketChannelFacade {

    @Resource(name = "ticketHttpClientCustomizeBuilder")
    private HttpClientCustomizeBuilder httpClientCustomizeBuilder;

    @Value("${ticket.http.protocol}")
    private String protocol;

    @Value("${ticket.http.ipAddress}")
    private String ipAddress;

    @Value("${ticket.http.timeout}")
    private Integer timeout;

    public void batchPushPushTicketChannel(BatchPushTicketChannelBO batchPushTicketChannelBO, String uniqueCode) {
        String urlStringBuilder = protocol + "://" + ipAddress + TicketHttpConstants.SYNC_TICKET_CHANNEL_BATCH_PUSH_URL;
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(TicketHttpConstants.X_UNIQUE_CODE_HEADER, uniqueCode)
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(batchPushTicketChannelBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, httpResultBO.getErrorMessage());
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "批量同步票务渠道信息失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }

    public void editTicketChannel(EditTicketChannelBO editTicketChannelBO, String uniqueCode) {
        String urlStringBuilder = protocol + "://" + ipAddress + TicketHttpConstants.SYNC_TICKET_CHANNEL_EDIT_URL;
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(TicketHttpConstants.X_UNIQUE_CODE_HEADER, uniqueCode)
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(editTicketChannelBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, httpResultBO.getErrorMessage());
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "同步修改票务渠道信息失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }

    public void updatePutCycle(UpdatePutCycleBO updatePutCycleBO, String uniqueCode) {
        String urlStringBuilder = protocol + "://" + ipAddress + TicketHttpConstants.SYNC_TICKET_CHANNEL_UPDATE_PUT_CYCLE_URL;
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(TicketHttpConstants.X_UNIQUE_CODE_HEADER, uniqueCode)
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(updatePutCycleBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, httpResultBO.getErrorMessage());
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "同步修改票务渠道投放周期信息失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }

    public void delTicketChannel(DelTicketChannelBO delTicketChannelBO, String uniqueCode) {
        String urlStringBuilder = protocol + "://" + ipAddress + TicketHttpConstants.SYNC_TICKET_CHANNEL_DEL_URL;
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(TicketHttpConstants.X_UNIQUE_CODE_HEADER, uniqueCode)
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(delTicketChannelBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, httpResultBO.getErrorMessage());
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "同步删除票务渠道投放周期信息失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }

    public void batchDelTicketChannel(BatchDelTicketChannelBO batchDelTicketChannelBO, String uniqueCode) {
        String urlStringBuilder = protocol + "://" + ipAddress + TicketHttpConstants.SYNC_TICKET_CHANNEL_BATCH_DEL_URL;
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(TicketHttpConstants.X_UNIQUE_CODE_HEADER, uniqueCode)
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(batchDelTicketChannelBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, httpResultBO.getErrorMessage());
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "同步批量删除票务渠道投放周期信息失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }
}
