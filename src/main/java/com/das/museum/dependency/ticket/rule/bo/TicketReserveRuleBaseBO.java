package com.das.museum.dependency.ticket.rule.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TicketReserveRuleBaseBO implements Serializable {

    private static final long serialVersionUID = 6153299879580913066L;

    private Byte isToday;

    private Date todayStopTime;

    private Long advanceFutureDay;

    private Byte singleLimit;

    private Byte certificateLimit;

    private Byte idCard;

    private Byte passport;

    private Byte hkMacPermit;

    private Byte timeLimitType;

    private Byte isTeamReserve;

    private String reserveRuleConfig;

    private String remark;

    private List<TimePeriodBO> timePeriodList;
}