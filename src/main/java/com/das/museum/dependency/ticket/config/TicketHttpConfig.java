package com.das.museum.dependency.ticket.config;

import com.das.museum.common.builder.HttpClientCustomizeBuilder;
import com.das.museum.common.http.SslConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class TicketHttpConfig {

    @Bean(name = "ticketHttpClientCustomizeBuilder")
    public HttpClientCustomizeBuilder creatHttpClientCustomizeBuilder() {
        return HttpClientCustomizeBuilder.custom()
                .pool(100, 5)
                .sslpv(SslConfig.SSLProtocolVersion.SSLv3)
                .ssl()
                .retry(3);
    }
}
