package com.das.museum.dependency.ticket.venue.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SaveVenueBO implements Serializable {

    private static final long serialVersionUID = 3312707380617173495L;

    private Byte channelType;

    private String venueName;

    private String venuePhone;

    private String closeDay;

    private Byte isHolidaysPostpone;

    private Byte isTemporaryClose;

    private String temporaryCloseStartDate;

    private String temporaryCloseEndDate;

    private String temporaryCloseNotice;

    private Date openTime;

    private Date closeTime;

    private Date stopTime;

    private String adCode;

    private String cityName;

    private String adCodePath;

    private String cityNamePath;

    private String address;

    private String notice;

    private String remark;

    private String documentBase64;

    /**
     * 封面图片路径
     */
    private String documentUrl;

    /**
     * 经度
     */
    private String venueLng;

    /**
     * 纬度
     */
    private String venueLat;

    /**
     * 全息地址
     */
    private String hologramUrl;

    /**
     * 全景地址
     */
    private String panoramaUrl;
}
