package com.das.museum.dependency.ticket.rule.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TicketWriteOffRuleBO implements Serializable {

    private static final long serialVersionUID = 7705951476980480350L;

    private Byte isTodayCross;

    private Byte noReserve;

    private Date todayStopTime;

    private Long writeOffLimit;

    private String remark;
}