package com.das.museum.dependency.ticket.rule.bo;

import lombok.Data;

import java.io.Serializable;

@Data
public class AddRuleConfigBO implements Serializable {

    private static final long serialVersionUID = -6886139610411874876L;

    private Integer channelType;

    private TicketReserveRuleBaseBO ticketReserveRuleBase;

    private TicketReserveRuleTeamBO ticketReserveRuleTeam;

    private TicketWriteOffRuleBO ticketWriteOffRule;
}
