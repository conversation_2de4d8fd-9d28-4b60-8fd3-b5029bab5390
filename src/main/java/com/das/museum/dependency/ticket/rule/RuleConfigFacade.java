package com.das.museum.dependency.ticket.rule;

import com.alibaba.fastjson.JSON;
import com.das.museum.common.builder.HttpClientCustomizeBuilder;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.http.HttpConfig;
import com.das.museum.common.http.HttpHeader;
import com.das.museum.common.http.HttpMethods;
import com.das.museum.common.http.HttpResult;
import com.das.museum.common.utils.HttpClientUtil;
import com.das.museum.dependency.bo.HttpResultBO;
import com.das.museum.dependency.ticket.constant.TicketHttpConstants;
import com.das.museum.dependency.ticket.rule.bo.AddRuleConfigBO;
import com.das.museum.dependency.ticket.rule.bo.EditRuleConfigBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class RuleConfigFacade {

    @Resource(name = "ticketHttpClientCustomizeBuilder")
    private HttpClientCustomizeBuilder httpClientCustomizeBuilder;

    @Value("${ticket.http.protocol}")
    private String protocol;

    @Value("${ticket.http.ipAddress}")
    private String ipAddress;

    @Value("${ticket.http.timeout}")
    private Integer timeout;

    public void addRuleConfig(AddRuleConfigBO addRuleConfigBO, String uniqueCode) {
        String urlStringBuilder = protocol + "://" + ipAddress + TicketHttpConstants.SYNC_TICKET_RULE_ADD_URL;
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(TicketHttpConstants.X_UNIQUE_CODE_HEADER, uniqueCode)
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(addRuleConfigBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, httpResultBO.getErrorMessage());
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "同步新增票务渠道规则信息失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }

    public void editRuleConfig(EditRuleConfigBO editRuleConfigBO, String uniqueCode) {
        String urlStringBuilder = protocol + "://" + ipAddress + TicketHttpConstants.SYNC_TICKET_RULE_EDIT_URL;
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(TicketHttpConstants.X_UNIQUE_CODE_HEADER, uniqueCode)
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(editRuleConfigBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, httpResultBO.getErrorMessage());
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "同步修改票务渠道规则信息失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }
}
