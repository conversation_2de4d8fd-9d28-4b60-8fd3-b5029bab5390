package com.das.museum.dependency.ticket.channel.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EditTicketChannelBO implements Serializable {

    private static final long serialVersionUID = 7414156664828904627L;

    private String ticketName;

    private String ticketCode;

    private Integer ticketType;

    private Long ticketPrice;

    private Integer ticketStatus;

    private String reserveNotice;

    private String ticketRemark;

    private Integer channelType;

    private Integer putValidType;

    private Date putStartDate;

    private Date putEndDate;

    private String putRuleConfig;

    private String putRemark;
}
