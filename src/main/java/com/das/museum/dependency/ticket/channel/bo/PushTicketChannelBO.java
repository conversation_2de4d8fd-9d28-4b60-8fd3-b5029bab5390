package com.das.museum.dependency.ticket.channel.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PushTicketChannelBO implements Serializable {

    private static final long serialVersionUID = -2207843790880243440L;

    private String ticketName;

    private String ticketCode;

    private Byte ticketType;

    private Long ticketPrice;

    private Byte ticketStatus;

    private String reserveNotice;

    private String ticketRemark;

    private Byte putStatus;

    private Byte channelType;

    private Byte putValidType;

    private Date putStartDate;

    private Date putEndDate;

    private String putRuleConfig;

    private String putRemark;
}
