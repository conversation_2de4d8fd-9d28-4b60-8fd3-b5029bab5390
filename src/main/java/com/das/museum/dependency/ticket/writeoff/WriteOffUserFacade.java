package com.das.museum.dependency.ticket.writeoff;

import com.alibaba.fastjson.JSON;
import com.das.museum.common.builder.HttpClientCustomizeBuilder;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.http.HttpConfig;
import com.das.museum.common.http.HttpHeader;
import com.das.museum.common.http.HttpMethods;
import com.das.museum.common.http.HttpResult;
import com.das.museum.common.utils.HttpClientUtil;
import com.das.museum.dependency.bo.HttpResultBO;
import com.das.museum.dependency.ticket.constant.TicketHttpConstants;
import com.das.museum.dependency.ticket.writeoff.bo.AddWriteOffUserBO;
import com.das.museum.dependency.ticket.writeoff.bo.DelWriteOffUserBO;
import com.das.museum.dependency.ticket.writeoff.bo.DisableWriteOffUserBO;
import com.das.museum.dependency.ticket.writeoff.bo.EnableWriteOffUserBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class WriteOffUserFacade {

    @Resource(name = "ticketHttpClientCustomizeBuilder")
    private HttpClientCustomizeBuilder httpClientCustomizeBuilder;

    @Value("${ticket.http.protocol}")
    private String protocol;

    @Value("${ticket.http.ipAddress}")
    private String ipAddress;

    @Value("${ticket.http.timeout}")
    private Integer timeout;

    public void addWriteOffUser(AddWriteOffUserBO addWriteOffUserBO, String uniqueCode) {
        String urlStringBuilder = protocol + "://" + ipAddress + TicketHttpConstants.SYNC_TICKET_WRITE_OFF_USER_ADD_URL;
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(TicketHttpConstants.X_UNIQUE_CODE_HEADER, uniqueCode)
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(addWriteOffUserBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, httpResultBO.getErrorMessage());
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "同步新增核销人员信息失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }

    public void enableWriteOffUser(EnableWriteOffUserBO enableWriteOffUser, String uniqueCode) {
        String urlStringBuilder = protocol + "://" + ipAddress + TicketHttpConstants.SYNC_TICKET_WRITE_OFF_USER_ENABLE_URL;
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(TicketHttpConstants.X_UNIQUE_CODE_HEADER, uniqueCode)
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(enableWriteOffUser))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, httpResultBO.getErrorMessage());
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "同步启用核销人员信息失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }

    public void disableWriteOffUser(DisableWriteOffUserBO disableWriteOffUserBO, String uniqueCode) {
        String urlStringBuilder = protocol + "://" + ipAddress + TicketHttpConstants.SYNC_TICKET_WRITE_OFF_USER_DISABLE_URL;
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(TicketHttpConstants.X_UNIQUE_CODE_HEADER, uniqueCode)
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(disableWriteOffUserBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, httpResultBO.getErrorMessage());
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "同步禁用核销人员信息失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }

    public void delWriteOffUser(DelWriteOffUserBO delWriteOffUserBO, String uniqueCode) {
        String urlStringBuilder = protocol + "://" + ipAddress + TicketHttpConstants.SYNC_TICKET_WRITE_OFF_USER_DEL_URL;
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(TicketHttpConstants.X_UNIQUE_CODE_HEADER, uniqueCode)
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(delWriteOffUserBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, httpResultBO.getErrorMessage());
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "同步删除核销人员信息失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }
}
