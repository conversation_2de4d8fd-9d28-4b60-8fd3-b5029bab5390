package com.das.museum.dependency.ticket.rule.bo;

import lombok.Data;

import java.io.Serializable;

@Data
public class EditRuleConfigBO implements Serializable {

    private static final long serialVersionUID = -1595748060955511551L;

    private Integer channelType;

    private TicketReserveRuleBaseBO ticketReserveRuleBase;

    private TicketReserveRuleTeamBO ticketReserveRuleTeam;

    private TicketWriteOffRuleBO ticketWriteOffRule;
}
