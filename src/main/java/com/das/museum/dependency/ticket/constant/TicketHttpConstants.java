package com.das.museum.dependency.ticket.constant;

public class TicketHttpConstants {

    public static final String X_UNIQUE_CODE_HEADER = "x-unique-code";

    public static final String SYNC_TICKET_CHANNEL_PUSH_URL = "/sync/ticket/channel/push";

    public static final String SYNC_TICKET_CHANNEL_BATCH_PUSH_URL = "/sync/ticket/channel/batchPush";

    public static final String SYNC_TICKET_CHANNEL_EDIT_URL = "/sync/ticket/channel/edit";

    public static final String SYNC_TICKET_CHANNEL_UPDATE_PUT_CYCLE_URL = "/sync/ticket/channel/updatePutCycle";

    public static final String SYNC_TICKET_CHANNEL_DEL_URL = "/sync/ticket/channel/del";
    public static final String SYNC_TICKET_CHANNEL_BATCH_DEL_URL = "/sync/ticket/channel/batchDel";

    public static final String SYNC_TICKET_RULE_ADD_URL = "/sync/ticket/rule/add";

    public static final String SYNC_TICKET_RULE_EDIT_URL = "/sync/ticket/rule/edit";

    public static final String SYNC_TICKET_WRITE_OFF_USER_ADD_URL = "/sync/ticket/writeOff/user/addWriteOffUser";

    public static final String SYNC_TICKET_WRITE_OFF_USER_ENABLE_URL = "/sync/ticket/writeOff/user/enableWriteOffUser";

    public static final String SYNC_TICKET_WRITE_OFF_USER_DISABLE_URL = "/sync/ticket/writeOff/user/disableWriteOffUser";

    public static final String SYNC_TICKET_WRITE_OFF_USER_DEL_URL = "/sync/ticket/writeOff/user/delWriteOffUser";

    public static final String SYNC_TICKET_VENUE_ADD_URL = "/sync/ticket/venue/add";

    public static final String SYNC_TICKET_VENUE_EDIT_URL = "/sync/ticket/venue/edit";

}
