package com.das.museum.dependency.province.auth;

import com.alibaba.fastjson.JSON;
import com.das.museum.common.builder.HttpClientCustomizeBuilder;
import com.das.museum.common.http.HttpConfig;
import com.das.museum.common.http.HttpHeader;
import com.das.museum.common.http.HttpMethods;
import com.das.museum.common.http.HttpResult;
import com.das.museum.common.utils.HttpClientUtil;
import com.das.museum.common.utils.Md5DigesterUtil;
import com.das.museum.common.utils.RasSecureUtil;
import com.das.museum.dependency.bo.HttpResultBO;
import com.das.museum.dependency.province.auth.bo.GetSessionKeyBO;
import com.das.museum.dependency.province.constant.ProvinceHttpConstants;
import com.das.museum.dependency.province.constant.SendKeyBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/17
 */
@Slf4j
@Service
public class ProvinceAuthFacade {

    @Resource(name = "provinceHttpClientCustomizeBuilder")
    private HttpClientCustomizeBuilder httpClientCustomizeBuilder;

    @Value("${ticket.http.timeout}")
    private Integer timeout;

    @Value("${md5.digester.salt}")
    private String salt;

    public String getSessionKey(GetSessionKeyBO getSessionKeyBO, String uniqueCode, String url) {
        String urlStringBuilder = url + ProvinceHttpConstants.GET_SESSION_KEY_URL;
        try {
            String jsonBody = RasSecureUtil.publicEncrypt(JSON.toJSONString(getSessionKeyBO));
            HttpConfig config = HttpConfig.custom()
                    .timeout(30000)
                    .headers(HttpHeader.custom()
                            .other(ProvinceHttpConstants.X_UNIQUE_CODE_HEADER, Md5DigesterUtil.digestHex(jsonBody+salt, salt))
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(new SendKeyBO(jsonBody)))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            log.info("ProvinceAuthFacade getSessionKey responseBody: [{}]", JSON.toJSON(result));
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    log.error("ProvinceAuthFacade getSessionKey code: [{}] errorMessage: [{}]", httpResultBO.getCode(), httpResultBO.getErrorMessage());
                    return null;
                }
                String sourceContent;
                try {
                    sourceContent = RasSecureUtil.publicDecrypt(httpResultBO.getData());
                } catch (Exception e) {
                    log.error("ProvinceAuthFacade getSessionKey publicDecrypt Exception: ", e);
                    return null;
                }
                return sourceContent;
            } else {
                log.error("ProvinceAuthFacade getSessionKey StatusCode: [{}]", result.getStatusCode());
            }
        } catch (Exception e) {
            log.error("ProvinceAuthFacade getSessionKey error: ", e);
        }
        return null;
    }
}
