package com.das.museum.dependency.province.safe.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 温湿度上报记录
 */
@Data
public class SyncSafeTemperatureHumidityRecordBO implements Serializable {

    private static final long serialVersionUID = -9116465337200540387L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 公司id
     */
    private Long museumBaseId;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 温度
     */
    private String currentTemperature;

    /**
     * 湿度
     */
    private String currentHumidity;

    /**
     * 上报时间
     */
    private Date reportTime;
}