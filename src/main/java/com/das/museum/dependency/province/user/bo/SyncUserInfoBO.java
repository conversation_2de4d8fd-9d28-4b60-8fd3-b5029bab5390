package com.das.museum.dependency.province.user.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 同步用户信息表
 */
@Data
public class SyncUserInfoBO implements Serializable {

    private static final long serialVersionUID = 2867030168983432577L;

    /**
     * 公司id
     */
    private Long museumBaseId;

    /**
     * 岗位编码
     */
    private String postCode;

    /**
     * 数量
     */
    private Long count;

    /**
     * 统计时间
     */
    private Date statisticsDate;
}