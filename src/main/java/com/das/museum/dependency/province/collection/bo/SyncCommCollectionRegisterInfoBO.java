package com.das.museum.dependency.province.collection.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 同步藏品登录基础信息表
 */
@Data
public class SyncCommCollectionRegisterInfoBO implements Serializable {

    private static final long serialVersionUID = 5846246909913752525L;

    /**
     * 公司id
     */
    private Long museumBaseId;

    /**
     * 藏品id
     */
    private Long collectionId;

    /**
     * 总登记号
     */
    private String registerNo;

    /**
     * rfid编号
     */
    private String rfid;

    /**
     * 藏品名称
     */
    private String name;

    /**
     * 原号
     */
    private String originalNo;

    /**
     * 原名
     */
    private String originalName;

    /**
     * 分类号名称
     */
    private String classifyName;

    /**
     * 分类号描述
     */
    private String classifyDesc;

    /**
     * 藏品级别名称
     */
    private String identifyLevelName;

    /**
     * 文物类别名称
     */
    private String categoryName;

    /**
     * 入藏年代(选)名称
     */
    private String inTibetanAgeRangeName;

    /**
     * 入藏时间
     */
    private Date inTibetanDate;

    /**
     * 所在地址
     */
    private String address;

    /**
     * 具体信息
     */
    private String specificInfo;

    /**
     * 年代(填)
     */
    private String era;

    /**
     * 年代(选)名称
     */
    private String age;

    /**
     * 用途
     */
    private String use;

    /**
     * 色泽
     */
    private String color;

    /**
     * 质地名称
     */
    private String textureName;

    /**
     * 完残程度名称
     */
    private String completeDegreeName;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 具体尺寸
     */
    private String size;

    /**
     * 藏品图片url
     */
    private String coverUrl;

    /**
     * 二维影像url,多个用逗号分割
     */
    private String twoModelUrl;

    /**
     * 三维模型url,多个用逗号分割
     */
    private String threeModelUrl;

    /**
     * 藏品状态: ARTS:已登录, ALGF:已注销
     */
    private String registerStatus;

    /**
     * 文物展示状态: 0:未展示, 1:展示中
     */
    private Byte culturalStatus;

    /**
     * 登录时间
     */
    private Date loginDate;
}