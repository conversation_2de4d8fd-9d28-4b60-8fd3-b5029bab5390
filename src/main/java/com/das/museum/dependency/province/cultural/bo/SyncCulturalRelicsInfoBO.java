package com.das.museum.dependency.province.cultural.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 三维全息信息表
 */
@Data
public class SyncCulturalRelicsInfoBO implements Serializable {

    private static final long serialVersionUID = -4804010452676954207L;

    /**
     * 公司id
     */
    private Long museumBaseId;

    /**
     * 原始数据id
     */
    private Long sourceCulturalId;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 文物编号
     */
    private String number;

    /**
     * 文物名称
     */
    private String name;

    /**
     * 文物级别id
     */
    private Long levelId;

    /**
     * 文物级别名称
     */
    private String levelName;

    /**
     * 完成程度id
     */
    private Long completeDegreeId;

    /**
     * 完成程度名称
     */
    private String completeDegreeName;

    /**
     * 朝代id
     */
    private Long dynastyId;

    /**
     * 朝代名称
     */
    private String dynastyName;

    /**
     * 公元纪年id
     */
    private Long adYearId;

    /**
     * 公元纪年名称
     */
    private String adYearName;

    /**
     * 具体年代
     */
    private String specificAge;

    /**
     * 质地id
     */
    private Long textureId;

    /**
     * 质地名称
     */
    private String textureName;

    /**
     * 来源id
     */
    private Long sourceId;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 尺寸
     */
    private String size;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 封面
     */
    private String coverId;

    /**
     * 封面
     */
    private String coverUrl;

    /**
     * 简介
     */
    private String remark;

    /**
     * 3D模型url
     */
    private String threeModelUrl;

    /**
     * 3D模型状态
     */
    private Byte threeModelStatus;

    /**
     * 二维文件id
     */
    private String twoModelId;

    /**
     * 对比文物id
     */
    private String comparedCulturalRelicsId;

    /**
     * 相似文物id
     */
    private String similarCulturalRelicsId;

    /**
     * 音频id
     */
    private String audioId;

    /**
     * 视频id
     */
    private String videoId;

    /**
     * 学术文献id
     */
    private String academicLiteratureId;

    /**
     * 文物状态: 0:未展示, 1:展示中
     */
    private Byte status;

    /**
     * 数据状态: 0:无, 1:二维和三维, 2:二维, 3:三维
     */
    private Byte dataStatus;

    /**
     * 平台域名
     */
    private String domainName;

    /**
     * 全景url
     */
    private String panoramicUrl;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}