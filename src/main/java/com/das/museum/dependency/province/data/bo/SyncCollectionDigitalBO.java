package com.das.museum.dependency.province.data.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 同步博物馆藏品数字化数据
 */
@Data
public class SyncCollectionDigitalBO implements Serializable {

    private static final long serialVersionUID = -1890337926169234407L;

    /**
     * 公司id
     */
    private Long museumBaseId;

    /**
     * 藏品二维数字化数量
     */
    private Integer twoModelNum;

    /**
     * 藏品三维数字化数量
     */
    private Integer threeModelNum;
}