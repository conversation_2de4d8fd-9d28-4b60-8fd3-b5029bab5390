package com.das.museum.dependency.province.constant;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Data
public class SendDataBO implements Serializable {

    private static final long serialVersionUID = -5593815366920636940L;

    private Object data;

    private String dataType;

    public SendDataBO(String dataType, Object data) {
        this.dataType = dataType;
        this.data = data;
    }
}
