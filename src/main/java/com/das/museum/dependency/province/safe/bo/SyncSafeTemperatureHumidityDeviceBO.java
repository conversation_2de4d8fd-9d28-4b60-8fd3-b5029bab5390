package com.das.museum.dependency.province.safe.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 温湿度设备信息
 */
@Data
public class SyncSafeTemperatureHumidityDeviceBO implements Serializable {

    private static final long serialVersionUID = -7585413667096663933L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 公司id
     */
    private Long museumBaseId;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 所在区域(1展厅,2库房,3其他)
     */
    private Byte regionName;

    /**
     * 设备位置
     */
    private String deviceAddress;

    /**
     * 最低温度阈值
     */
    private String minTemperature;

    /**
     * 最高温度阈值
     */
    private String maxTemperature;

    /**
     * 最低湿度阈值
     */
    private String minHumidity;

    /**
     * 最高湿度阈值
     */
    private String maxHumidity;

    /**
     * 最新上报温度
     */
    private String currentTemperature;

    /**
     * 最新上报湿度
     */
    private String currentHumidity;

    /**
     * 最新上报时间
     */
    private Date reportTime;
}