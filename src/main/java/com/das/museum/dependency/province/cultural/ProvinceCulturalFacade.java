package com.das.museum.dependency.province.cultural;

import com.alibaba.fastjson.JSON;
import com.das.museum.common.builder.HttpClientCustomizeBuilder;
import com.das.museum.common.http.HttpConfig;
import com.das.museum.common.http.HttpHeader;
import com.das.museum.common.http.HttpMethods;
import com.das.museum.common.http.HttpResult;
import com.das.museum.common.utils.AesSecureUtil;
import com.das.museum.common.utils.HttpClientUtil;
import com.das.museum.common.utils.Md5DigesterUtil;
import com.das.museum.dependency.bo.HttpResultBO;
import com.das.museum.dependency.province.constant.ProvinceHttpConstants;
import com.das.museum.dependency.province.constant.SendDataBO;
import com.das.museum.dependency.province.constant.SendKeyBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
public class ProvinceCulturalFacade {

    @Resource(name = "provinceHttpClientCustomizeBuilder")
    private HttpClientCustomizeBuilder httpClientCustomizeBuilder;

    @Value("${ticket.http.timeout}")
    private Integer timeout;

    @Value("${md5.digester.salt}")
    private String salt;

    public void syncCulturalInfo(SendDataBO sendDataBO, String key, String url, Long museumBaseId) {
        String urlStringBuilder = url + ProvinceHttpConstants.DATA_RECEIVE_URL;
        try {
            String jsonBody = AesSecureUtil.encrypt(key, JSON.toJSONString(sendDataBO));
            HttpConfig config = HttpConfig.custom()
                    .timeout(timeout)
                    .headers(HttpHeader.custom()
                            .other(ProvinceHttpConstants.X_UNIQUE_CODE_HEADER, Md5DigesterUtil.digestHex(jsonBody+salt, salt))
                            .other(ProvinceHttpConstants.X_UNIQUE_MUSEUM_HEADER, String.valueOf(museumBaseId))
                            .build())
                    .url(urlStringBuilder)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(new SendKeyBO(jsonBody)))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            log.info("ProvinceCulturalFacade syncCulturalInfo responseBody: [{}]", JSON.toJSON(result));
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (HttpStatus.SC_OK != httpResultBO.getCode()) {
                    log.error("ProvinceCulturalFacade syncCulturalInfo code: [{}] errorMessage: [{}]", httpResultBO.getCode(), httpResultBO.getErrorMessage());
                }
            } else {
                log.error("ProvinceCulturalFacade syncCulturalInfo StatusCode: [{}]", result.getStatusCode());
            }
        } catch (Exception e) {
            log.error("ProvinceCulturalFacade syncCulturalInfo error: ", e);
        }
    }
}
