package com.das.museum.dependency.province.cultural.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 同步三维全息信息表
 */
@Data
public class SyncCulturalRelicsBO implements Serializable {

    private static final long serialVersionUID = 4611301386248163756L;

    /**
     * 公司id
     */
    private Long museumBaseId;

    /**
     * 文物编号
     */
    private String number;

    /**
     * 文物名称
     */
    private String name;

    /**
     * 文物级别名称
     */
    private String levelName;

    /**
     * 完成程度名称
     */
    private String completeDegreeName;

    /**
     * 朝代名称
     */
    private String dynastyName;

    /**
     * 公元纪年名称
     */
    private String adYearName;

    /**
     * 具体年代
     */
    private String specificAge;

    /**
     * 质地名称
     */
    private String textureName;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 尺寸
     */
    private String size;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 封面
     */
    private String coverId;

    /**
     * 简介
     */
    private String remark;

    /**
     * 文物状态: 0:未展示, 1:展示中
     */
    private Byte status;

    /**
     * 数据状态: 0:无, 1:二维和三维, 2:二维, 3:三维
     */
    private Byte dataStatus;

    /**
     * 全息访问数
     */
    private Long holographicAccessCount;

    /**
     * 全景访问数
     */
    private Long overallAccessCount;

    /**
     * 统计时间
     */
    private Date statisticsDate;
}