package com.das.museum;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

@SpringBootApplication(scanBasePackages = {"com.das.museum"})
@EnableScheduling
@EnableAsync
@EnableRedisHttpSession
@EnableWebMvc
public class DigitalMuseumApplication {

	public static void main(String[] args) {
		SpringApplication.run(DigitalMuseumApplication.class, args);
		System.out.println("==========> digital museum start successful <==========");
	}

}
