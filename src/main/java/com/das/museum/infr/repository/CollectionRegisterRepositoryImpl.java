package com.das.museum.infr.repository;

import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.domain.collection.entity.CollectionRegisterEntity;
import com.das.museum.domain.collection.repository.CollectionRegisterRepository;
import com.das.museum.domain.collection.valueobject.CollectionRegisterAttrExVO;
import com.das.museum.domain.collection.valueobject.CollectionRegisterAttrInfoVO;
import com.das.museum.domain.collection.valueobject.CollectionRegisterBaseInfoVO;
import com.das.museum.domain.collection.valueobject.CollectionRegisterManageInfoVO;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.mapper.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

@Component
public class CollectionRegisterRepositoryImpl implements CollectionRegisterRepository {

    @Resource
    private ScsCollectionRegisterInfoMapper scsCollectionRegisterInfoMapper;

    @Resource
    private ScsCollectionRegisterInfoSnapshotMapper scsCollectionRegisterInfoSnapshotMapper;

    @Resource
    private ScsCollectionRegisterManageInfoMapper scsCollectionRegisterManageInfoMapper;

    @Resource
    private ScsCollectionRegisterManageInfoSnapshotMapper scsCollectionRegisterManageInfoSnapshotMapper;

    @Resource
    private ScsCollectionRegisterAttrInfoMapper scsCollectionRegisterAttrInfoMapper;

    @Resource
    private ScsCollectionRegisterAttrInfoSnapshotMapper scsCollectionRegisterAttrInfoSnapshotMapper;

    @Resource
    private ScsCollectionRegisterAttrExMapper scsCollectionRegisterAttrExMapper;

    @Resource
    private ScsCollectionRegisterAttrExSnapshotMapper scsCollectionRegisterAttrExSnapshotMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCollectionRegister(CollectionRegisterEntity entity) {
        CollectionRegisterBaseInfoVO registerBaseInfo = entity.getCollectionRegisterBaseInfo();
        if (Objects.isNull(registerBaseInfo)) {
            return;
        }
        String registerNo = entity.getCollectionRegisterBaseInfo().getRegisterNo();
        ScsCollectionRegisterInfoDO registerInfoDO = BeanCopyUtils.copyByJSON(registerBaseInfo, ScsCollectionRegisterInfoDO.class);
        registerInfoDO.setRegisterNo(registerNo);
        registerInfoDO.setAutoAudit(entity.getAutoAudit());
        this.scsCollectionRegisterInfoMapper.insertSelective(registerInfoDO);
        Long registerBaseId = registerInfoDO.getId();
        entity.setCollectionRegisterId(registerBaseId);
        CollectionRegisterManageInfoVO registerManageInfo = entity.getCollectionRegisterManageInfo();
        if (Objects.nonNull(registerManageInfo)) {
            ScsCollectionRegisterManageInfoDO registerManageInfoDO = BeanCopyUtils.copyByJSON(registerManageInfo, ScsCollectionRegisterManageInfoDO.class);
            registerManageInfoDO.setRegisterInfoId(registerBaseId);
            registerManageInfoDO.setRegisterNo(registerNo);
            this.scsCollectionRegisterManageInfoMapper.insertSelective(registerManageInfoDO);
        }
        CollectionRegisterAttrInfoVO registerAttrInfo = entity.getCollectionRegisterAttrInfo();
        if (Objects.nonNull(registerAttrInfo)) {
            ScsCollectionRegisterAttrInfoDO registerAttrInfoDO = BeanCopyUtils.copyByJSON(registerAttrInfo, ScsCollectionRegisterAttrInfoDO.class);
            registerAttrInfoDO.setRegisterInfoId(registerBaseId);
            registerAttrInfoDO.setRegisterNo(registerNo);
            this.scsCollectionRegisterAttrInfoMapper.insertSelective(registerAttrInfoDO);
        }
        CollectionRegisterAttrExVO registerAttrExVO = entity.getCollectionRegisterAttrExVO();
        if (Objects.nonNull(registerAttrExVO)) {
            ScsCollectionRegisterAttrExDO registerAttrExDO = BeanCopyUtils.copyByJSON(registerAttrExVO, ScsCollectionRegisterAttrExDO.class);
            registerAttrExDO.setRegisterInfoId(registerBaseId);
            registerAttrExDO.setRegisterNo(registerNo);
            this.scsCollectionRegisterAttrExMapper.insertSelective(registerAttrExDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionRegister(CollectionRegisterEntity entity) {
        CollectionRegisterBaseInfoVO registerBaseInfo = entity.getCollectionRegisterBaseInfo();
        if (Objects.isNull(registerBaseInfo)) {
            return;
        }
        Long registerBaseId = registerBaseInfo.getRegisterBaseId();
        ScsCollectionRegisterInfoDO registerInfoDO = BeanCopyUtils.copyByJSON(registerBaseInfo, ScsCollectionRegisterInfoDO.class);
        registerInfoDO.setId(registerBaseId);
        registerInfoDO.setAutoAudit(entity.getAutoAudit());
        this.scsCollectionRegisterInfoMapper.updateByPrimaryKeySelective(registerInfoDO);

        CollectionRegisterManageInfoVO registerManageInfo = entity.getCollectionRegisterManageInfo();
        ScsCollectionRegisterManageInfoDO registerManageInfoDO = BeanCopyUtils.copyByJSON(registerManageInfo, ScsCollectionRegisterManageInfoDO.class);
        registerManageInfoDO.setRegisterInfoId(registerBaseId);
        if (Objects.nonNull(registerManageInfo)) {
            this.scsCollectionRegisterManageInfoMapper.updateByRegisterInfoIdAll(registerManageInfoDO);
        } else {
            this.scsCollectionRegisterManageInfoMapper.insertSelective(registerManageInfoDO);
        }

        CollectionRegisterAttrInfoVO registerAttrInfo = entity.getCollectionRegisterAttrInfo();
        ScsCollectionRegisterAttrInfoDO registerAttrInfoDO = BeanCopyUtils.copyByJSON(registerAttrInfo, ScsCollectionRegisterAttrInfoDO.class);
        registerAttrInfoDO.setRegisterInfoId(registerBaseId);
        if (Objects.nonNull(registerAttrInfo)) {
            this.scsCollectionRegisterAttrInfoMapper.updateByRegisterInfoId(registerAttrInfoDO);
        } else {
            this.scsCollectionRegisterAttrInfoMapper.insertSelective(registerAttrInfoDO);
        }

        CollectionRegisterAttrExVO registerAttrExVO = entity.getCollectionRegisterAttrExVO();
        ScsCollectionRegisterAttrExDO registerAttrExDO = BeanCopyUtils.copyByJSON(registerAttrExVO, ScsCollectionRegisterAttrExDO.class);
        registerAttrExDO.setRegisterInfoId(registerBaseId);
        if (Objects.nonNull(registerAttrExVO)) {
            registerAttrExDO.setCollectDate(registerAttrExVO.getCollectsDate());
            this.scsCollectionRegisterAttrExMapper.updateByRegisterInfoId(registerAttrExDO);
        } else {
            this.scsCollectionRegisterAttrExMapper.insertSelective(registerAttrExDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByRegisterNo(Long companyId, String registerNo) {
        this.scsCollectionRegisterInfoMapper.deleteByRegisterNo(companyId, registerNo);
        this.scsCollectionRegisterManageInfoMapper.deleteByRegisterNo(companyId, registerNo);
        this.scsCollectionRegisterAttrInfoMapper.deleteByRegisterNo(companyId, registerNo);
        this.scsCollectionRegisterAttrExMapper.deleteByRegisterNo(companyId, registerNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByRegisterInfoSnapshotId(Long companyId, Long registerInfoSnapshotId) {
        this.scsCollectionRegisterInfoSnapshotMapper.deleteByRegisterInfoId(companyId, registerInfoSnapshotId);
        this.scsCollectionRegisterManageInfoSnapshotMapper.deleteByRegisterInfoId(companyId, registerInfoSnapshotId);
        this.scsCollectionRegisterAttrInfoSnapshotMapper.deleteByRegisterInfoId(companyId, registerInfoSnapshotId);
        this.scsCollectionRegisterAttrExSnapshotMapper.deleteByRegisterInfoId(companyId, registerInfoSnapshotId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateSnapshotByRegisterId(Long companyId, Long userId, Long registerInfoId) {
        ScsCollectionRegisterInfoDO registerInfoDO = this.scsCollectionRegisterInfoMapper.selectById(companyId, registerInfoId);
        if (Objects.isNull(registerInfoDO)) {
            return;
        }
        String registerNo = registerInfoDO.getRegisterNo();
        ScsCollectionRegisterInfoSnapshotDO registerInfoSnapshotDO = BeanCopyUtils.copyByJSON(registerInfoDO, ScsCollectionRegisterInfoSnapshotDO.class);
        registerInfoSnapshotDO.setId(null);
        registerInfoSnapshotDO.setRegisterInfoId(registerInfoId);
        registerInfoSnapshotDO.setCreator(userId);
        registerInfoSnapshotDO.setModifier(userId);
        this.scsCollectionRegisterInfoSnapshotMapper.insertSelective(registerInfoSnapshotDO);
        Long registerInfoSnapshotId = registerInfoSnapshotDO.getId();

        ScsCollectionRegisterManageInfoDO registerManageInfoDO = this.scsCollectionRegisterManageInfoMapper.selectByRegisterNo(companyId, registerNo);
        if (Objects.nonNull(registerManageInfoDO)) {
            ScsCollectionRegisterManageInfoSnapshotDO registerManageInfoSnapshotDO = BeanCopyUtils.copyByJSON(registerManageInfoDO, ScsCollectionRegisterManageInfoSnapshotDO.class);
            registerManageInfoSnapshotDO.setRegisterInfoSnapshotId(registerInfoSnapshotId);
            registerManageInfoSnapshotDO.setId(null);
            registerManageInfoSnapshotDO.setCreator(userId);
            registerManageInfoSnapshotDO.setModifier(userId);
            this.scsCollectionRegisterManageInfoSnapshotMapper.insertSelective(registerManageInfoSnapshotDO);
        }

        ScsCollectionRegisterAttrInfoDO registerAttrInfoDO = this.scsCollectionRegisterAttrInfoMapper.selectByRegisterNo(companyId, registerNo);
        if (Objects.nonNull(registerAttrInfoDO)) {
            ScsCollectionRegisterAttrInfoSnapshotDO registerAttrInfoSnapshotDO = BeanCopyUtils.copyByJSON(registerAttrInfoDO, ScsCollectionRegisterAttrInfoSnapshotDO.class);
            registerAttrInfoSnapshotDO.setRegisterInfoSnapshotId(registerInfoSnapshotId);
            registerAttrInfoSnapshotDO.setId(null);
            registerAttrInfoSnapshotDO.setCreator(userId);
            registerAttrInfoSnapshotDO.setModifier(userId);
            this.scsCollectionRegisterAttrInfoSnapshotMapper.insertSelective(registerAttrInfoSnapshotDO);
        }

        ScsCollectionRegisterAttrExDO registerAttrExDO = this.scsCollectionRegisterAttrExMapper.selectByRegisterNo(companyId, registerNo);
        if (Objects.nonNull(registerAttrExDO)) {
            ScsCollectionRegisterAttrExSnapshotDO registerAttrExSnapshotDO = BeanCopyUtils.copyByJSON(registerAttrExDO, ScsCollectionRegisterAttrExSnapshotDO.class);
            registerAttrExSnapshotDO.setRegisterInfoSnapshotId(registerInfoSnapshotId);
            registerAttrExSnapshotDO.setId(null);
            registerAttrExSnapshotDO.setCreator(userId);
            registerAttrExSnapshotDO.setModifier(userId);
            this.scsCollectionRegisterAttrExSnapshotMapper.insertSelective(registerAttrExSnapshotDO);
        }
    }
}
