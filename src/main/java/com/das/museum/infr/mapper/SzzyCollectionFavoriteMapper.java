package com.das.museum.infr.mapper;

import com.das.museum.infr.dataobject.SzzyCollectionFavoriteDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * SzzyCollectionFavoriteMapper继承基类
 */
@Repository
public interface SzzyCollectionFavoriteMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SzzyCollectionFavoriteDO record);

    int insertSelective(SzzyCollectionFavoriteDO record);

    SzzyCollectionFavoriteDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SzzyCollectionFavoriteDO record);

    int updateByPrimaryKey(SzzyCollectionFavoriteDO record);

    /*
     *===========================手动方法分割线=================================
     */

    /**
     * 根据藏品Id查找
     * @param collectionId
     * @return
     */
    SzzyCollectionFavoriteDO selectByCollectionId(@Param("collectionId")Long collectionId);

    /**
     * 根据藏品Id新增
     */
    int addFavoriteCountByCollectionId(@Param("collectionId")Long collectionId,
                                       @Param("userId")Long userId,
                                       @Param("companyId")Long companyId);
}