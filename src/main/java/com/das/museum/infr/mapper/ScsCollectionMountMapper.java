package com.das.museum.infr.mapper;

import com.das.museum.infr.dataobject.ScsCollectionMountDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ScsCollectionMountMapper {
    int deleteByPrimaryKey(Long id);
    int insert(ScsCollectionMountDO record);
    int insertSelective(ScsCollectionMountDO record);
    ScsCollectionMountDO selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(ScsCollectionMountDO record);
    int updateByPrimaryKey(ScsCollectionMountDO record);
    int deleteById(@Param("companyId") Long companyId, @Param("id") Long id);
    ScsCollectionMountDO selectById(@Param("companyId") Long companyId, @Param("id") Long id);
    List<ScsCollectionMountDO> listPageByCondition(@Param("companyId") Long companyId,
                                                   @Param("collectionId") Long collectionId,
                                                   @Param("sortBy") String sortBy);
}