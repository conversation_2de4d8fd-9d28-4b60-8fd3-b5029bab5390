package com.das.museum.infr.mapper;

import com.das.museum.infr.dataobject.ShjyActivityManageDO;
import com.das.museum.infr.dataobject.ShjyActivityManageDOWithBLOBs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface ShjyActivityManageMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ShjyActivityManageDOWithBLOBs record);

    int insertSelective(ShjyActivityManageDOWithBLOBs record);

    ShjyActivityManageDOWithBLOBs selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ShjyActivityManageDOWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(ShjyActivityManageDOWithBLOBs record);

    int updateByPrimaryKey(ShjyActivityManageDO record);

    ShjyActivityManageDOWithBLOBs selectByActivityUniqueCode(@Param("companyUniqueCode") String companyUniqueCode,
                                                             @Param("activityUniqueCode") String activityUniqueCode);

    List<ShjyActivityManageDOWithBLOBs> selectListByParams(@Param("companyUniqueCode") String companyUniqueCode,
                                                           @Param("activityName") String activityName,
                                                           @Param("activityStatus") Byte activityStatus,
                                                           @Param("currentTime") Date currentTime,
                                                           @Param("publishStatus") Byte publishStatus,
                                                           @Param("applyType") Long applyType,
                                                           @Param("startTime") Date startTime,
                                                           @Param("endTime") Date endTime);

    void updatePublishStatus(@Param("companyUniqueCode") String companyUniqueCode,
                            @Param("activityUniqueCodes") List<String> activityUniqueCodes,
                            @Param("publishStatus") Byte publishStatus,
                            @Param("modifier") Long modifier);

    void deleteByActivityUniqueCodes(@Param("companyUniqueCode") String companyUniqueCode,
                                    @Param("activityUniqueCodes") List<String> activityUniqueCodes);



    List<ShjyActivityManageDOWithBLOBs> listByIds(@Param("ids") List<Long> ids);

    ShjyActivityManageDOWithBLOBs selectAppletByActivityUniqueCode(@Param("activityUniqueCode") String activityUniqueCode, @Param("publishStatus") Byte publishStatus);

    List<ShjyActivityManageDOWithBLOBs> listByCondition(@Param("activityName") String activityName, @Param("publishStatus") Byte publishStatus);

    List<ShjyActivityManageDOWithBLOBs> listByPublishStatus(@Param("companyUniqueCode") String companyUniqueCode, @Param("publishStatus") Byte publishStatus);
}