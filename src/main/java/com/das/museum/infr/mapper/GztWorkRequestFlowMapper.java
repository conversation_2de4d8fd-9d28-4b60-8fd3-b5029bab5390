package com.das.museum.infr.mapper;

import com.das.museum.infr.dataobject.GztWorkRequestFlowDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GztWorkRequestFlowMapper {
    int deleteByPrimaryKey(Long id);

    int insert(GztWorkRequestFlowDO record);

    int insertSelective(GztWorkRequestFlowDO record);

    GztWorkRequestFlowDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GztWorkRequestFlowDO record);

    int updateByPrimaryKey(GztWorkRequestFlowDO record);

    GztWorkRequestFlowDO selectByCompanyIdAndId(@Param("companyId") Long companyId,
                                                @Param("id") Long id);

    List<GztWorkRequestFlowDO> selectListByCompanyId(@Param("companyId") Long companyId);

    GztWorkRequestFlowDO selectByCompanyIdAndApplyType(@Param("companyId") Long companyId,
                                                       @Param("applyType") Byte applyType);

    int updateByCompanyIdAndId(GztWorkRequestFlowDO record);
}