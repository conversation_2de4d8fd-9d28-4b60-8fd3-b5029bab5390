package com.das.museum.domain.collection.extension.ext;

import com.das.museum.common.extension.Extension;
import com.das.museum.domain.collection.entity.CollectCertificateEntity;
import com.das.museum.domain.collection.repository.CollectCertificateRepository;
import com.das.museum.domain.docment.extension.DocumentQueryExtPt;
import com.das.museum.domain.docment.extension.DocumentScenario;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@Extension(bizId = DocumentScenario.BIZ_ID, useCase = DocumentScenario.USE_CASE_QUERY,
        scenario = DocumentScenario.COLLECT_QUERY_SCENARIO)
public class CollectDocumentQueryExt implements DocumentQueryExtPt {
    @Resource
    private CollectCertificateRepository collectCertificateRepository;
    @Override
    public String getByDocumentByBizId(Long companyId, Long bizId) {
        CollectCertificateEntity entity = this.collectCertificateRepository.getByCollectId(companyId, bizId);
        if (Objects.isNull(entity)) {
            return null;
        }
        return entity.getDocumentId();
    }
}
