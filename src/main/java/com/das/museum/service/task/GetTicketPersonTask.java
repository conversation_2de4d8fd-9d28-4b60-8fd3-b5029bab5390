package com.das.museum.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.das.museum.common.bo.TicketPersonalOrderBO;
import com.das.museum.common.builder.HttpClientCustomizeBuilder;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.http.HttpConfig;
import com.das.museum.common.http.HttpHeader;
import com.das.museum.common.http.HttpMethods;
import com.das.museum.common.http.HttpResult;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.HttpClientUtil;
import com.das.museum.dependency.bo.HttpResultBO;
import com.das.museum.dependency.ticket.constant.TicketHttpConstants;
import com.das.museum.service.biz.ticket.OrderManageService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 定时获取闸机观众
 */

@Slf4j
@Service
@Async
public class GetTicketPersonTask {

    @Resource(name = "ticketHttpClientCustomizeBuilder")
    private HttpClientCustomizeBuilder httpClientCustomizeBuilder;

    @Value("${weiman.ticket.url}")
    private String weiManTicketUrl;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 每10分钟执行一次，导入10分钟内观众数据
     */
    //@Scheduled(cron = "0 */10 * * * ?")
    public void getTicketPerson() {
        log.info("GetTicketPersonTask getTicketPerson start.......");
        RLock rLock = redissonClient.getLock("getTicketPerson");
        rLock.lock();
        try {
            Date currentDate = new Date();
            Long endTime = currentDate.getTime()/1000;
            // 10分钟之前的时间点
            Long startTime = DateUtils.getSomeMinutesBefore(currentDate,10L).getTime()/1000;
            setWeiManTicketData(startTime,endTime);
        } catch (Exception e) {
            log.error("GetTicketPersonTask getTicketPerson Exception:", e);
        } finally {
            rLock.unlock();
        }
        log.info("GetTicketPersonTask getTicketPerson end.......");
    }

    /**
     * 伪满皇宫观众数据获取入库
     */
    public void setWeiManTicketData(Long startTime, Long endTime){
        GetTicketPersonBO getTicketPersonBO = new GetTicketPersonBO();
        getTicketPersonBO.setStart_time(startTime);
        getTicketPersonBO.setEnd_time(endTime);
        try {
            HttpConfig config = HttpConfig.custom()
                    .headers(HttpHeader.custom().build())
                    .url(weiManTicketUrl)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(getTicketPersonBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (StringUtils.isNotBlank(httpResultBO.getData())){
                    JSONObject jsonObject = JSONObject.parseObject(httpResultBO.getData());
                    //伪满皇宫
                    JSONArray wmDataArray = JSONArray.parseArray(jsonObject.getString("all"));
                    List<TicketPersonalOrderBO> cachedDataList = new ArrayList<>();
                    if(wmDataArray.size() > 0){
                        for(int i = 0 ;i < wmDataArray.size(); i++) {
                            TicketPersonalOrderBO orderBO = new TicketPersonalOrderBO();
                            JSONObject weiManJson = wmDataArray.getJSONObject(i);
                            String cardName = weiManJson.getString("card_name");
                            String cardNumber = weiManJson.getString("card_number");
                            String courtyardDate = weiManJson.getString("courtyard_date");
                            String cardType = weiManJson.getString("card_type");
                            orderBO.setCompanyId(53L);
                            orderBO.setDepartmentId(22611L);
                            if (StringUtils.isBlank(cardName)) {
                                cardName = "无";
                            }
                            orderBO.setTouristName(cardName);
                            orderBO.setTouristCertificateType(Byte.parseByte(cardType));
                            orderBO.setTouristCertificateNo(cardNumber);
                            orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                            orderBO.setCreator(-1L);

                            if (Byte.parseByte(cardType) == 1 && cardNumber.length() != 18) {
                                continue;
                            }
                            if (StringUtils.isNotBlank(cardNumber)) {
                                //将10分钟内的观众数据缓存起来做重复判断，防止定时任务多个节点重复插入
                                if(!redisTemplate.hasKey("wm-"+cardNumber)){
                                    redisTemplate.opsForValue().set("wm-"+cardNumber,cardNumber,10L, TimeUnit.MINUTES);
                                    cachedDataList.add(orderBO);
                                }
                            }
                        }
                    }


                    // 东北沦陷史陈列馆
                    JSONArray lxDataArray = JSONArray.parseArray(jsonObject.getString("918"));
                    if(lxDataArray.size() > 0){
                        for(int i = 0 ;i < lxDataArray.size(); i++){
                            TicketPersonalOrderBO orderBO = new TicketPersonalOrderBO();
                            JSONObject weiManJson = lxDataArray.getJSONObject(i);
                            String cardName = weiManJson.getString("card_name");
                            String cardNumber = weiManJson.getString("card_number");
                            String courtyardDate = weiManJson.getString("courtyard_date");
                            String cardType = weiManJson.getString("card_type");
                            orderBO.setCompanyId(54L);
                            orderBO.setDepartmentId(23146L);
                            if(StringUtils.isBlank(cardName)){
                                cardName = "无";
                            }
                            orderBO.setTouristName(cardName);
                            orderBO.setTouristCertificateType(Byte.parseByte(cardType));
                            orderBO.setTouristCertificateNo(cardNumber);
                            orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                            orderBO.setCreator(-1L);
                            if(Byte.parseByte(cardType) == 1 && cardNumber.length() != 18){
                                continue;
                            }

                            if(StringUtils.isNotBlank(cardNumber)){
                                //将10分钟内的观众数据缓存起来做重复判断，防止定时任务多个节点重复插入
                                if(!redisTemplate.hasKey("lx-"+cardNumber)){
                                    redisTemplate.opsForValue().set("lx-"+cardNumber,cardNumber,10L, TimeUnit.MINUTES);
                                    cachedDataList.add(orderBO);
                                }
                            }
                        }
                    }
                    if(!CollectionUtils.isEmpty(cachedDataList)){
                        orderManageService.savePersonalData(cachedDataList);
                    }
                }
            } else {
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "获取伪满皇宫闸机数据失败");
            }
        } catch (CommonException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }


    /**
     * 伪满皇宫导入历史数据
     */
    public String setWeiManHistoryTicketData(Long startTime, Long endTime){
        GetTicketPersonBO getTicketPersonBO = new GetTicketPersonBO();
        getTicketPersonBO.setStart_time(startTime);
        getTicketPersonBO.setEnd_time(endTime);
        List<TicketPersonalOrderBO> cachedDataList = new ArrayList<>();
        int num = 0;
        try {
            HttpConfig config = HttpConfig.custom()
                    .headers(HttpHeader.custom().build())
                    .url(weiManTicketUrl)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(getTicketPersonBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (StringUtils.isNotBlank(httpResultBO.getData())){
                    JSONObject jsonObject = JSONObject.parseObject(httpResultBO.getData());
                    //伪满皇宫
                    JSONArray wmDataArray = JSONArray.parseArray(jsonObject.getString("all"));
                    num += wmDataArray.size();
                    if(wmDataArray.size() > 0) {
                        for (int i = 0; i < wmDataArray.size(); i++) {
                            TicketPersonalOrderBO orderBO = new TicketPersonalOrderBO();
                            JSONObject weiManJson = wmDataArray.getJSONObject(i);
                            String cardName = weiManJson.getString("card_name");
                            String cardNumber = weiManJson.getString("card_number");
                            String courtyardDate = weiManJson.getString("courtyard_date");
                            String cardType = weiManJson.getString("card_type");
                            orderBO.setCompanyId(53L);
                            orderBO.setDepartmentId(22611L);
                            if (StringUtils.isBlank(cardName)) {
                                cardName = "无";
                            }
                            if(StringUtils.isBlank(cardNumber)){
                                cardNumber = "******************";
                            }
                            orderBO.setTouristName(cardName);
                            orderBO.setTouristCertificateType(Byte.parseByte(cardType));
                            orderBO.setTouristCertificateNo(cardNumber);
                            orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                            orderBO.setCreator(-1L);

//                            if (Byte.parseByte(cardType) == 1 && cardNumber.length() != 18) {
//                                continue;
//                            }
                            if (StringUtils.isNotBlank(cardNumber)) {
                                cachedDataList.add(orderBO);
                            }
                        }
                    }

                    // 东北沦陷史陈列馆
                    JSONArray lxDataArray = JSONArray.parseArray(jsonObject.getString("918"));
                    num += lxDataArray.size();
                    if(lxDataArray.size() > 0) {
                        for (int i = 0; i < lxDataArray.size(); i++) {
                            TicketPersonalOrderBO orderBO = new TicketPersonalOrderBO();
                            JSONObject weiManJson = lxDataArray.getJSONObject(i);
                            String cardName = weiManJson.getString("card_name");
                            String cardNumber = weiManJson.getString("card_number");
                            String courtyardDate = weiManJson.getString("courtyard_date");
                            String cardType = weiManJson.getString("card_type");
                            orderBO.setCompanyId(54L);
                            orderBO.setDepartmentId(23146L);
                            if (StringUtils.isBlank(cardName)) {
                                cardName = "无";
                            }
                            if(StringUtils.isBlank(cardNumber)){
                                cardNumber = "******************";
                            }
                            orderBO.setTouristName(cardName);
                            orderBO.setTouristCertificateType(Byte.parseByte(cardType));
                            orderBO.setTouristCertificateNo(cardNumber);
                            orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                            orderBO.setCreator(-1L);
//                            if (Byte.parseByte(cardType) == 1 && cardNumber.length() != 18) {
//                                continue;
//                            }

                            if (StringUtils.isNotBlank(cardNumber)) {
                                cachedDataList.add(orderBO);
                            }
                        }
                    }

                    //长影旧址
                    JSONArray cyDataArray = JSONArray.parseArray(jsonObject.getString("union"));
                    num += cyDataArray.size();
                    if(cyDataArray.size() > 0) {
                        for (int i = 0; i < cyDataArray.size(); i++) {
                            TicketPersonalOrderBO orderBO = new TicketPersonalOrderBO();
                            JSONObject weiManJson = cyDataArray.getJSONObject(i);
                            String cardName = weiManJson.getString("card_name");
                            String cardNumber = weiManJson.getString("card_number");
                            String courtyardDate = weiManJson.getString("courtyard_date");
                            String cardType = weiManJson.getString("card_type");
                            orderBO.setCompanyId(15L);
                            orderBO.setDepartmentId(4438L);
                            if (StringUtils.isBlank(cardName)) {
                                cardName = "无";
                            }

                            if(StringUtils.isBlank(cardNumber)){
                                cardNumber = "******************";
                            }

                            orderBO.setTouristName(cardName);
                            orderBO.setTouristCertificateType(Byte.parseByte(cardType));
                            orderBO.setTouristCertificateNo(cardNumber);
                            orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                            orderBO.setCreator(-1L);

//                            if (Byte.parseByte(cardType) == 1 && cardNumber.length() != 18) {
//                                continue;
//                            }
                            if (StringUtils.isNotBlank(cardNumber)) {
                                cachedDataList.add(orderBO);
                            }
                        }
                    }

                    // 删除当前时间段的历史数据

                    if(!CollectionUtils.isEmpty(cachedDataList)){
                        //orderManageService.savePersonalData(cachedDataList);
                    }
                }
            } else {
                log.error("获取伪满皇宫闸机数据接口返回失败：--"+startTime+"-"+endTime);
            }
        } catch (CommonException e) {
            log.error("数据插入失败！",e.getMessage());
        }
        return startTime+"-"+endTime+":"+num;
    }

    @Data
    class GetTicketPersonBO{

        private Long start_time;

        private Long end_time;


    }
}
