package com.das.museum.service.task;

import com.das.museum.dependency.ticket.channel.TicketChannelFacade;
import com.das.museum.infr.mapper.TktTicketChannelPoolMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: shaochengwei
 * @Date: 2022-10-27
 */
@Component
@Slf4j
@EnableScheduling
public class TicketPutStatusTask {

    @Resource
    private TktTicketChannelPoolMapper tktTicketChannelPoolMapper;

    @Resource
    private TicketChannelFacade ticketChannelFacade;

//    /**
//     * 每天17点检查微信票种投放时间段、更新投放状态
//     */
//    @Scheduled(cron = "0 0 17 * * ?")
//    public void updatePutStatus(){
//        List<TktTicketChannelPoolDO> tktTicketChannelPoolDO = tktTicketChannelPoolMapper.selectChannelListByChannelType(ChannelTypeEnum.微信小程序.getCode().byteValue());
//        if(Objects.nonNull(tktTicketChannelPoolDO)){
//            //判断是否有已到截止投放日期票种，投放状态改为已停止
//
//        }
//    }

}
