package com.das.museum.service.task;

import com.das.museum.common.utils.CommandUtil;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.MinIoUtils;
import com.das.museum.common.utils.UUIDUtils;
import com.das.museum.infr.dataobject.SwzsCulturalRelicsDO;
import com.das.museum.infr.mapper.SwzsCulturalRelicsMapper;
import com.das.museum.service.enums.AutoDisplayStatusEnum;
import com.das.museum.service.enums.DisplayStatusEnum;
import com.das.museum.service.enums.ThreeModelStatusEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 模型转换定时任务
 *
 * @Author: shaochengwei
 * @Date: 2022-11-15
 */
@Component
@Slf4j
@EnableScheduling
public class ZipToGet3dTask {

    private LinkedList<File> filesPath;

    @Value("${ftp.uploadConf.ftpUrl}")
    private String ftpUrl;

    @Value("${ftp.uploadConf.ftpPath}")
    private String ftpPath;

    @Value("${minio.bucketName}")
    private String bucketName;

    @Value("${minio.visitUrl}")
    private String visitUrl;


    @Resource
    private SwzsCulturalRelicsMapper swzsCulturalRelicsMapper;

    @Resource
    private RedissonClient redissonClient;

    //@Scheduled(cron = "0 0/2 * * * ?")
    public void unzipModel() {
        log.info("unzipModel start.......");
        RLock rLock = redissonClient.getLock("unzipModel");
        try {
            rLock.lock();
            unzipModelService();
        } catch (Exception e) {
            log.info("ZipToGet3dTask unzipModel Exception:", e);
        } finally {
            rLock.unlock();
        }
        log.info("unzipModel end.......");
    }


    @Transactional
    public void unzipModelService() {
        List<Byte> threeModelStatusList = Lists.newArrayList();
        threeModelStatusList.add(ThreeModelStatusEnum.未转换.getCode().byteValue());
        threeModelStatusList.add(ThreeModelStatusEnum.转换失败.getCode().byteValue());
        List<SwzsCulturalRelicsDO> swzsCulturalRelicsDOList = this.swzsCulturalRelicsMapper.getCollectionListByThreeModelStatus(threeModelStatusList);
        List<Long> ids = swzsCulturalRelicsDOList.stream().map(SwzsCulturalRelicsDO::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ids)){
            return;
        }
        swzsCulturalRelicsMapper.updateThreeModelStatusByIds(ThreeModelStatusEnum.转换中.getCode().byteValue(),ids);
        for(SwzsCulturalRelicsDO swzsCulturalRelicsDO : swzsCulturalRelicsDOList){
            String loadPath = swzsCulturalRelicsDO.getThreeModelUrl();
            Long companyId = swzsCulturalRelicsDO.getCompanyId();
            if(StringUtils.isNotBlank(loadPath)){
                loadPath = loadPath.replace(ftpUrl,ftpPath);
                String path = zipToModel(loadPath,companyId);
                byte threeModelStatus;
                if(StringUtils.isNotBlank(path)){
                    threeModelStatus = ThreeModelStatusEnum.转换成功.getCode().byteValue();
                    // 自动展示的三维模型在转换完毕后自动进行展示
                    if(AutoDisplayStatusEnum.自动展示.getCode().byteValue() == swzsCulturalRelicsDO.getAutoDisplay()){
                        swzsCulturalRelicsDO.setStatus(DisplayStatusEnum.展示中.getCode().byteValue());
                    }
                    path = path.replace(ftpPath,ftpUrl);
                }else{
                    threeModelStatus = ThreeModelStatusEnum.转换失败.getCode().byteValue();
                }
                this.swzsCulturalRelicsMapper.updateThreeModelStatusById(threeModelStatus,path,swzsCulturalRelicsDO.getId(), swzsCulturalRelicsDO.getStatus());
            }
        }
    }

    /**
     * 文物全息模型转换服务
     */
    public void culturalRelicsModeService(SwzsCulturalRelicsDO swzsCulturalRelicsDO){
        String loadPath = swzsCulturalRelicsDO.getThreeModelUrl();
        Long companyId = swzsCulturalRelicsDO.getCompanyId();
        if(StringUtils.isNotBlank(loadPath)){
            loadPath = loadPath.replace(ftpUrl,ftpPath);
            String path = zipToModel(loadPath,companyId);
            byte threeModelStatus;
            if(StringUtils.isNotBlank(path)){
                threeModelStatus = ThreeModelStatusEnum.转换成功.getCode().byteValue();
                // 自动展示的三维模型在转换完毕后自动进行展示
                if(AutoDisplayStatusEnum.自动展示.getCode().byteValue() == swzsCulturalRelicsDO.getAutoDisplay()){
                    swzsCulturalRelicsDO.setStatus(DisplayStatusEnum.展示中.getCode().byteValue());
                }
            }else{
                threeModelStatus = ThreeModelStatusEnum.转换失败.getCode().byteValue();
            }
            this.swzsCulturalRelicsMapper.updateThreeModelStatusById(threeModelStatus,path,swzsCulturalRelicsDO.getId(), swzsCulturalRelicsDO.getStatus());
        }
    }

    public String zipToModel(String zipPath,Long companyId){
        String oldPath = zipPath;
        String returnStr = "";
        if(StringUtils.isBlank(zipPath)){
            return returnStr;
        }
        String resourcePath = zipPath.substring(0,zipPath.lastIndexOf(".")).replace(ftpUrl,ftpPath);
        zipPath = zipPath.replace(ftpUrl,ftpPath);
        unzip(zipPath,resourcePath);
        filesPath = new LinkedList<>();
        getFilesPath(resourcePath);
        String objPath = "";
        for(int i = 0 ;i < filesPath.size();i++){
            if(filesPath.get(i).getPath().endsWith(".obj") || filesPath.get(i).getPath().endsWith(".OBJ")){
                objPath = filesPath.get(i).getPath();
            };
        }
        List<String> params = new ArrayList<>();
        if (System.getProperty("os.name").toLowerCase().startsWith("win")) {
            params.add("cmd");
            params.add("/c");
        }
        params.add("obj2gltf");
        params.add("-i");
        params.add(objPath.replace("/", File.separator));
        params.add("-o");
        String outPath = resourcePath+"/gltf";
        params.add((outPath+"/model.gltf").replace("/",File.separator));
        params.add("-s");
        log.info("-------params:"+String.join(" ",params));
        CommandUtil.command(params);
        if(!new File(outPath+"/model.gltf").exists()){
            log.error("转码失败，未输出GLTF文件！");
            return returnStr;
        }
        List<String> params2 = new ArrayList<>();
        if (System.getProperty("os.name").toLowerCase().startsWith("win")) {
            params2.add("cmd");
            params2.add("/c");
            params2.add("copy");
        }else if (System.getProperty("os.name").toLowerCase().contains("linux")) {
            params2.add("cp");
        }
        params2.add((ftpPath + "model.get3d").replace("/",File.separator));
        params2.add(outPath.replace("/",File.separator));
        CommandUtil.command(params2);
        if(!new File(outPath+"/model.get3d").exists()){
            log.error("复制文件失败！");
            return returnStr;
        }

        //outPath文件下所有文件上传到minio
        String minioPath = uploadModelToMinio(outPath,companyId);

        //删除模型压缩包
        File zipFile = new File(oldPath);
        if(zipFile.exists()){
            zipFile.delete();
        }

        // 删除解压后的模型包
        File modelFile = new File(resourcePath);
        if(modelFile.exists()){
            modelFile.delete();
        }

        returnStr = minioPath+"/model.get3d";
        return returnStr;
    }

    /**
     * 将三维模型文件上传到minio
     * @param outPath     三维模型地址
     * @param companyId   公司id
     */
    private String uploadModelToMinio(String outPath,Long companyId) {
        String currentDate = DateUtils.formatDateYYYMMDD();
        String currentPath = UUIDUtils.generateUUID();
        try {
            File file = new File(outPath);
            File[] files = file.listFiles();
            // 逐个文件上传
            for (File file2 : files) {
                if (file2.isFile()) {
                    StringBuilder baseUrl = new StringBuilder();
                    baseUrl.append(currentDate)
                            .append(MinIoUtils.SEPARATOR)
                            .append(companyId)
                            .append(MinIoUtils.SEPARATOR)
                            .append(currentPath)
                            .append(MinIoUtils.SEPARATOR)
                            .append("gltf")
                            .append(MinIoUtils.SEPARATOR)
                            .append(file2.getName());
                    String baseUrlStr = baseUrl.toString();
                    InputStream inputStream = new FileInputStream(file2);
                    MinIoUtils.putObject(bucketName, baseUrlStr, inputStream);
                }
            }
        }catch (Exception e){
            log.error("将模型文件上传到minio失败！",e);
        }
        return  visitUrl + MinIoUtils.SEPARATOR + bucketName + MinIoUtils.SEPARATOR + currentDate + MinIoUtils.SEPARATOR +
                companyId + MinIoUtils.SEPARATOR + currentPath + MinIoUtils.SEPARATOR + "gltf";
    }


    private void getFilesPath(String dir) {
        File file = new File(dir);
        if(file.exists()){
            if(file.isDirectory()){
                File[] listFiles = file.listFiles();
                for(int i = 0 ; i < listFiles.length ; i++ ){
                    getFilesPath(listFiles[i].getAbsolutePath());
                }
            }else{
                filesPath.add(file);
            }
        }
    }
    public void unzip(String zipPath,String resourcePath){
        File pathFile = new File(resourcePath);
        if(!pathFile.exists()){
            pathFile.mkdirs();
        }
        ZipFile zp;
        try{
            zp=new ZipFile(zipPath, Charset.forName("gbk"));
            Enumeration entries=zp.entries();
            while(entries.hasMoreElements()){
                ZipEntry entry= (ZipEntry) entries.nextElement();
                String zipEntryName=entry.getName();
                InputStream in=zp.getInputStream(entry);
                String outPath = (resourcePath+"/"+zipEntryName).replace("/",File.separator);
                File file = new  File(outPath.substring(0,outPath.lastIndexOf(File.separator)));
                if(!file.exists()){
                    file.mkdirs();
                }
                if(new File(outPath).isDirectory())
                    continue;
                OutputStream out=new FileOutputStream(outPath);
                byte[] bf=new byte[2048];
                int len;
                while ((len=in.read(bf))>0){
                    out.write(bf,0,len);
                }
                in.close();
                out.close();
            }
            zp.close();
        }catch ( Exception e){
            e.printStackTrace();
        }
    }

}
