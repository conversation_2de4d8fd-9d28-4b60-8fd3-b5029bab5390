package com.das.museum.service.task;

import com.das.museum.infr.dataobject.SwzsCulturalAutoBuildDO;
import com.das.museum.infr.mapper.SwzsCulturalAutoBuildMapper;
import com.das.museum.service.enums.ModelBuildStatusEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@EnableScheduling
public class DownloadZipTask {

    @Value("${autobuild.appid}")
    private String appId;
    @Value("${ftp.uploadConf.ftpPath}")
    private String downloadPath;
    @Value("${ftp.uploadConf.ftpUrl}")
    private String ftpUrl;

    @Resource
    private SwzsCulturalAutoBuildMapper swzsCulturalAutoBuildMapper;
    @Resource
    private ZipToGet3dTask zipToGet3dTask;

    //@Scheduled(cron = "0 0/2 * * * ?")
    private void autoDownloadModel(){
        log.info("Scheduled task start for download model");
        List<Byte> modelSwitchStatusList = Lists.newArrayList();
        modelSwitchStatusList.add(ModelBuildStatusEnum.PENDING.getCode().byteValue());
        modelSwitchStatusList.add(ModelBuildStatusEnum.FAILED.getCode().byteValue());
        List<SwzsCulturalAutoBuildDO> swzsCulturalAutoBuildDOList = this.swzsCulturalAutoBuildMapper.selectByModelStatus(
                appId, ModelBuildStatusEnum.COMPLETED.getCode().byteValue(), modelSwitchStatusList);

        List<Long> bizIdList = swzsCulturalAutoBuildDOList.stream().map(SwzsCulturalAutoBuildDO::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(bizIdList)){
            return;
        }
        this.swzsCulturalAutoBuildMapper.updateBybizIds(appId, ModelBuildStatusEnum.RUNNING.getCode().byteValue(), bizIdList);
        for(SwzsCulturalAutoBuildDO swzsCulturalAutoBuildDO : swzsCulturalAutoBuildDOList){
            downLoadModelZip(swzsCulturalAutoBuildDO.getDownloadUrl(), swzsCulturalAutoBuildDO.getModelName(), swzsCulturalAutoBuildDO.getId(),swzsCulturalAutoBuildDO.getCompanyId());
        }
    }
    public void downLoadModelZip(String downloadUrl, String fileName, Long bizId, Long companyId){
        log.info("Start for download model");
        Long startTime = System.currentTimeMillis();
        File savePath = new File(downloadPath);
        if(!savePath.exists()){
            savePath.mkdir();
        }

        SwzsCulturalAutoBuildDO swzsCulturalAutoBuildDO = new SwzsCulturalAutoBuildDO();
        swzsCulturalAutoBuildDO.setId(bizId);
        swzsCulturalAutoBuildDO.setAppId(appId);

        try {
            File file = new File(savePath + "//"+ fileName + ".zip");
            if(file != null && !file.exists()){
                file.createNewFile();
            }
            BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(file));

            URL url = new URL(downloadUrl);
            HttpURLConnection uc = (HttpURLConnection) url.openConnection();
            uc.setDoInput(true);
            uc.connect();
            log.info("use httpurlconnection download: " + downloadUrl);

            InputStream inputStream = uc.getInputStream();
            log.info("fileLength is" + uc.getContentLength());

            byte[] b = new byte[1024 * 4];
            int byteRead = -1;
            while ((byteRead = inputStream.read(b)) != -1){
                bufferedOutputStream.write(b, 0, byteRead);
            }
            inputStream.close();
            bufferedOutputStream.close();
            Long endTime = System.currentTimeMillis();
            log.info("file download success, during time" + (endTime - startTime) / 1000 * 1.0 + "s");

            StringBuffer strb = new StringBuffer();
            BufferedInputStream fs = new BufferedInputStream(new FileInputStream(savePath + "//" + fileName + ".zip"));
            BufferedReader br = new BufferedReader(new InputStreamReader(fs, "utf-8"));
            String lineStr = "";
            while ((lineStr = br.readLine()) != null){
                strb.append(lineStr + "");
            }
            br.close();
            fs.close();

            String get3dPath = zipToGet3dTask.zipToModel(downloadPath + fileName + ".zip",companyId);

            swzsCulturalAutoBuildDO.setBuildStatus(ModelBuildStatusEnum.COMPLETED.getCode().byteValue());
            swzsCulturalAutoBuildDO.setBuildProcess(100L);
            swzsCulturalAutoBuildDO.setDownloadUrl(downloadUrl);

            if(StringUtils.isNotBlank(get3dPath)){
                swzsCulturalAutoBuildDO.setBuildStatus(ModelBuildStatusEnum.COMPLETED.getCode().byteValue());
                swzsCulturalAutoBuildDO.setBuildProcess(100L);
                swzsCulturalAutoBuildDO.setDownloadUrl(downloadUrl);
                swzsCulturalAutoBuildDO.setSwitchStatus(ModelBuildStatusEnum.COMPLETED.getCode().byteValue());
                swzsCulturalAutoBuildDO.setModelUrl(get3dPath.replace(downloadPath, ftpUrl));
                this.swzsCulturalAutoBuildMapper.updateByBizIdAndAppId(swzsCulturalAutoBuildDO);
                log.info("file decompress success");
            }
        }catch (Exception e){
            swzsCulturalAutoBuildDO.setSwitchStatus(ModelBuildStatusEnum.FAILED.getCode().byteValue());
            this.swzsCulturalAutoBuildMapper.updateByBizIdAndAppId(swzsCulturalAutoBuildDO);
            log.error("download error: " + e);
        }
    }
}
