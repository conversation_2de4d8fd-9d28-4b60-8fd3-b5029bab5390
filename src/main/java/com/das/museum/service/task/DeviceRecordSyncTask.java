package com.das.museum.service.task;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.infr.dataobject.SafeTemperatureHumidityDeviceDO;
import com.das.museum.infr.dataobject.SafeTemperatureHumidityRecordDO;
import com.das.museum.infr.mapper.SafeTemperatureHumidityDeviceMapper;
import com.das.museum.infr.mapper.SafeTemperatureHumidityRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;



/**
 * 温湿度设备数据同步任务
 */

@Slf4j
@Service
public class DeviceRecordSyncTask {

    @Value("${jc.url}")
    private String httpUrl;

    @Value("${jc.keyId}")
    private String keyId;

    @Value("${jc.keySecret}")
    private String keySecret;

    @Value("${jc.userName}")
    private String userName;

    @Value("${jc.password}")
    private String password;

    @Resource
    private SafeTemperatureHumidityDeviceMapper safeTemperatureHumidityDeviceMapper;

    @Resource
    private SafeTemperatureHumidityRecordMapper safeTemperatureHumidityRecordMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisTemplate redisTemplate;

    private final static  String TOKEN_KEY = "jc_device_token";

    /**
     * 定时获取精创温湿度设备实时数据
     */
    //@Scheduled(cron = "0 0/5 * * * ?")
    @Transactional
    public void updateDeviceRealData(){
        RLock rLock = redissonClient.getLock("updateDeviceRealData");
        try {
            boolean getLock = rLock.tryLock(2, 10, TimeUnit.SECONDS);
            if(getLock) {
                // 获取精创温湿度设备列表
                List<SafeTemperatureHumidityDeviceDO> deviceDOList = safeTemperatureHumidityDeviceMapper.selectJcList();
                List<String> deviceIds = deviceDOList.stream().map(SafeTemperatureHumidityDeviceDO::getDeviceCode).collect(Collectors.toList());
                JSONArray jsonArray = getRealTimeData(deviceIds);
                if(CollectionUtils.isEmpty(jsonArray)){
                    return;
                }
                Map<String,SafeTemperatureHumidityDeviceDO> map = new HashMap<>();
                deviceDOList.forEach(deviceDO -> map.put(deviceDO.getDeviceCode(),deviceDO));

                for(int i=0;i<jsonArray.size();i++){
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    String deviceCode = jsonObject.getString("deviceGuid");
                    Long lastDataTime = jsonObject.getLong("lastDataTime");
                    String currentTemperature = jsonObject.getString("tmp1");
                    String currentHumidity = jsonObject.getString("hum1");

                    SafeTemperatureHumidityDeviceDO deviceDO = map.get(deviceCode);
                    if(Objects.nonNull(deviceDO)
                            && Objects.nonNull(lastDataTime)){

                        if(StringUtils.isNotBlank(currentTemperature)){
                            currentTemperature = currentTemperature.replace("℃", "");
                        }

                        if(StringUtils.isNotBlank(currentHumidity)){
                            currentHumidity = currentHumidity.replace("%RH", "");
                        }
                        if(deviceDO.getReportTime() == null){
                            // 首次上报
                            deviceDO.setCurrentTemperature(currentTemperature);
                            deviceDO.setCurrentHumidity(currentHumidity);
                            deviceDO.setReportTime(new Date(lastDataTime * 1000));
                            safeTemperatureHumidityDeviceMapper.updateByPrimaryKeySelective(deviceDO);

                            SafeTemperatureHumidityRecordDO recordDO = new SafeTemperatureHumidityRecordDO();
                            recordDO.setCompanyId(deviceDO.getCompanyId());
                            recordDO.setDeviceCode(deviceCode);
                            recordDO.setCurrentTemperature(currentTemperature);
                            recordDO.setCurrentHumidity(currentHumidity);
                            recordDO.setReportTime(new Date(lastDataTime * 1000));
                            recordDO.setIsSync((byte)0);
                            safeTemperatureHumidityRecordMapper.insertSelective(recordDO);
                        }else{
                            // 距离上次更新时间超过2小时，更新实时表数据，并将数据存入历史记录表
                            if(!new Date(lastDataTime * 1000).before(DateUtils.addHours(deviceDO.getReportTime(),2))){
                                // 更新实时表数据
                                deviceDO.setCurrentTemperature(currentTemperature);
                                deviceDO.setCurrentHumidity(currentHumidity);
                                deviceDO.setReportTime(new Date(lastDataTime * 1000));
                                safeTemperatureHumidityDeviceMapper.updateByPrimaryKeySelective(deviceDO);

                                SafeTemperatureHumidityRecordDO recordDO = new SafeTemperatureHumidityRecordDO();
                                recordDO.setCompanyId(deviceDO.getCompanyId());
                                recordDO.setDeviceCode(deviceCode);
                                recordDO.setCurrentTemperature(currentTemperature);
                                recordDO.setCurrentHumidity(currentHumidity);
                                recordDO.setReportTime(new Date(lastDataTime * 1000));
                                recordDO.setIsSync((byte)0);
                                safeTemperatureHumidityRecordMapper.insertSelective(recordDO);
                            }
                        }
                    }
                }
            }
            Thread.sleep(3000);
        } catch (Exception e) {
            log.error("updateDeviceRealData Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
    }


    public String getToken() {
        log.error("调用一次获取token接口！");
        String token = "";
        try {
            String url = httpUrl + "/api/data-api/elitechAccess/getToken";
            JSONObject body = new JSONObject();
            body.put("keyId", keyId);
            body.put("keySecret", keySecret);
            body.put("userName", userName);
            body.put("password", password);
            String resultJson = HttpRequest
                    .post(url)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .body(body.toJSONString())
                    .execute()
                    .body();
            if(!StringUtils.isBlank(resultJson)){
                JSONObject resultJsonObject = JSONObject.parseObject(resultJson);
                Integer code = resultJsonObject.getInteger("code");
                if(code != 0){
                    throw new Exception("获取token接口返回失败，失败信息："+resultJsonObject.getString("message"));
                }
                token = resultJsonObject.getString("data");
                redisTemplate.opsForValue().set(TOKEN_KEY, token, 30, TimeUnit.MINUTES);
            }
        }catch (Exception e){
            log.error("getToken error : ",e);
        }
        return token;
    }

    public JSONArray getRealTimeData(List<String> deviceIds) {
        log.error("调用一次获取温湿度接口：getRealTimeData");
        JSONArray array = new JSONArray();
        Object token = redisTemplate.opsForValue().get(TOKEN_KEY);
        if(Objects.isNull(token)){
            token = getToken();
        }

        try {
            String url = httpUrl + "/api/data-api/elitechAccess/getRealTimeData";
            JSONObject body = new JSONObject();
            body.put("keyId", keyId);
            body.put("keySecret", keySecret);
            body.put("deviceGuids", deviceIds.toArray());
            String resultJson = HttpRequest
                    .post(url)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .header("Authorization", token.toString())
                    .body(body.toJSONString())
                    .execute()
                    .body();
            if(!StringUtils.isBlank(resultJson)){
                JSONObject resultJsonObject = JSONObject.parseObject(resultJson);
                Integer code = resultJsonObject.getInteger("code");
                if(code != 0){
                    throw new Exception("获取实时数据接口返回失败，失败信息："+resultJsonObject.getString("message"));
                }
                array = resultJsonObject.getJSONArray("data");
            }
        }catch (Exception e){
            log.error("getRealTimeData error : ",e);
        }
        return array;
    }

}
