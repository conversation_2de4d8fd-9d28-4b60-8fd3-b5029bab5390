package com.das.museum.service.config;

import com.das.museum.DigitalMuseumApplication;

import java.util.concurrent.ExecutorService;

@SuppressWarnings({"checkstyle:indentation", "PMD.ThreadPoolCreationRule"})
public class GlobalExecutor {

    private static final ExecutorService UPLOAD_RECORD_EXECUTOR = ExecutorFactory.Managed
            .newFixedExecutorService(ClassUtils.getCanonicalName(DigitalMuseumApplication.class), 2,
                    new NameThreadFactory("com.das.museum.service.biz.explain.ExplainRecordServiceImpl.UploadRecordNotifier"));

    public static void submitUploadRecord(Runnable runnable) {
        UPLOAD_RECORD_EXECUTOR.submit(runnable);
    }
}
