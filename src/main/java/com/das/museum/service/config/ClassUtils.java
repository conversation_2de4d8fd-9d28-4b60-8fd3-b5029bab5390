package com.das.museum.service.config;

import cn.hutool.core.img.Img;
import cn.hutool.core.io.FileUtil;

import java.util.Objects;

public final class ClassUtils {
    public static String getName(Object obj) {
        Objects.requireNonNull(obj, "obj");
        return obj.getClass().getName();
    }

    public static String getName(Class cls) {
        Objects.requireNonNull(cls, "cls");
        return cls.getName();
    }

    public static String getCanonicalName(Class cls) {
        Objects.requireNonNull(cls, "cls");
        return cls.getCanonicalName();
    }

    public static void main(String[] args) {
        Img.from(FileUtil.file("/Users/<USER>/Desktop/3.png"))
                .setQuality(0.5)//压缩比率
                .write(FileUtil.file("/Users/<USER>/Desktop/33.jpg"));
    }
}