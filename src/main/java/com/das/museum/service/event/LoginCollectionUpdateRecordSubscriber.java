package com.das.museum.service.event;

import com.das.museum.common.domain.DomainSubscriber;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.DomainEventHolder;
import com.das.museum.domain.aop.CompareField;
import com.das.museum.domain.collection.valueobject.*;
import com.das.museum.domain.event.LoginCollectionUpdateRecordEvent;
import com.das.museum.infr.dataobject.CommClassificationDO;
import com.das.museum.infr.dataobject.ScsCollectionUpdateLogDO;
import com.das.museum.infr.dataobject.ScsCollectionUpdateLogDetailDO;
import com.das.museum.infr.mapper.CommClassificationMapper;
import com.das.museum.infr.mapper.ScsCollectionUpdateLogDetailMapper;
import com.das.museum.infr.mapper.ScsCollectionUpdateLogMapper;
import com.google.common.collect.Lists;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class LoginCollectionUpdateRecordSubscriber implements DomainSubscriber<LoginCollectionUpdateRecordEvent> {

    @Resource
    private ScsCollectionUpdateLogMapper scsCollectionUpdateLogMapper;

    @Resource
    private ScsCollectionUpdateLogDetailMapper scsCollectionUpdateLogDetailMapper;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @PostConstruct
    public  void init(){
        DomainEventHolder.syncRegister(this);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Subscribe
    public void doSubscribe(LoginCollectionUpdateRecordEvent loginCollectionUpdateRecordEvent) {

        List<ScsCollectionUpdateLogDetailDO> updateList = new ArrayList<>();
        Map<String,Object> oldValueMap = new HashMap<>();
        CollectionRegisterDetailVO collectionRegisterDetailVO = loginCollectionUpdateRecordEvent.getCollectionRegisterDetailVO();
        Field[] fields = collectionRegisterDetailVO.getClass().getDeclaredFields();
        for(int i=0;i<fields.length;i++){
            Object value = getFieldValueByName(fields[i].getName(),collectionRegisterDetailVO);
            if(Objects.isNull(value)){
                oldValueMap.put(fields[i].getName(),"");
            }else{
                oldValueMap.put(fields[i].getName(),value);
            }
        }
        CollectionRegisterBaseInfoVO collectionRegisterBaseInfoVO = loginCollectionUpdateRecordEvent.getRegisterEntity().getCollectionRegisterBaseInfo();
        Field[] collectionRegisterBaseInfoFields = collectionRegisterBaseInfoVO.getClass().getDeclaredFields();
        for(int i=1;i<collectionRegisterBaseInfoFields.length;i++){
            Object newValue = getFieldValueByName(collectionRegisterBaseInfoFields[i].getName(),collectionRegisterBaseInfoVO);
            Object oldValue = oldValueMap.get(collectionRegisterBaseInfoFields[i].getName());
            CompareField compareField = collectionRegisterBaseInfoFields[i].getAnnotation(CompareField.class);
            if (compareField == null) {
                continue;
            }
            ScsCollectionUpdateLogDetailDO scsCollectionUpdateLogDetailDO = new ScsCollectionUpdateLogDetailDO();
            scsCollectionUpdateLogDetailDO.setCompanyId(loginCollectionUpdateRecordEvent.getCompanyId());
            //scsCollectionUpdateLogDetailDO.setUpdateLogId(logId);
            scsCollectionUpdateLogDetailDO.setField(compareField.value());
            scsCollectionUpdateLogDetailDO.setFieldEn(collectionRegisterBaseInfoFields[i].getName());
            if(!Objects.isNull(oldValue)  && StringUtils.isNotBlank(oldValue.toString())){
                if("inTibetanDate".equals(scsCollectionUpdateLogDetailDO.getFieldEn())){
                    scsCollectionUpdateLogDetailDO.setOldValue(DateUtils.formatDateYYYMMDD((Date)oldValue));
                }else {
                    scsCollectionUpdateLogDetailDO.setOldValue(oldValue.toString());
                }
            }else{
                scsCollectionUpdateLogDetailDO.setOldValue("");
            }
            if(!Objects.isNull(newValue)  && StringUtils.isNotBlank(newValue.toString())){
                if("inTibetanDate".equals(scsCollectionUpdateLogDetailDO.getFieldEn())){
                    scsCollectionUpdateLogDetailDO.setNewValue(DateUtils.formatDateYYYMMDD((Date)newValue));
                }else {
                    scsCollectionUpdateLogDetailDO.setNewValue(newValue.toString());
                }
            }else{
                scsCollectionUpdateLogDetailDO.setNewValue("");
            }
            if(!scsCollectionUpdateLogDetailDO.getNewValue().equals(scsCollectionUpdateLogDetailDO.getOldValue())){
                updateList.add(scsCollectionUpdateLogDetailDO);
            }
        }
        CollectionRegisterManageInfoVO collectionRegisterManageInfoVO = loginCollectionUpdateRecordEvent.getRegisterEntity().getCollectionRegisterManageInfo();
        Field[] collectionRegisterManageInfoFields = collectionRegisterManageInfoVO.getClass().getDeclaredFields();
        for(int i=1;i<collectionRegisterManageInfoFields.length;i++){
            Object newValue = getFieldValueByName(collectionRegisterManageInfoFields[i].getName(),collectionRegisterManageInfoVO);
            Object oldValue = oldValueMap.get(collectionRegisterManageInfoFields[i].getName());
            CompareField compareField = collectionRegisterManageInfoFields[i].getAnnotation(CompareField.class);
            if (compareField == null) {
                continue;
            }
            //藏品管理信息中没有入藏时间字段
            if("inTibetanDate".equals(collectionRegisterManageInfoFields[i].getName())){
                continue;
            }
            ScsCollectionUpdateLogDetailDO scsCollectionUpdateLogDetailDO = new ScsCollectionUpdateLogDetailDO();
            scsCollectionUpdateLogDetailDO.setCompanyId(loginCollectionUpdateRecordEvent.getCompanyId());
            //scsCollectionUpdateLogDetailDO.setUpdateLogId(logId);
            scsCollectionUpdateLogDetailDO.setField(compareField.value());
            scsCollectionUpdateLogDetailDO.setFieldEn(collectionRegisterManageInfoFields[i].getName());

            if(Objects.nonNull(oldValue) && StringUtils.isNotBlank(oldValue.toString())){
                if("collectDate".equals(scsCollectionUpdateLogDetailDO.getFieldEn()) || "inMuseumDate".equals(scsCollectionUpdateLogDetailDO.getFieldEn())){
                    scsCollectionUpdateLogDetailDO.setOldValue(DateUtils.formatDateYYYMMDD((Date)oldValue));
                }else {
                    scsCollectionUpdateLogDetailDO.setOldValue(oldValue.toString());
                }
            }else{
                scsCollectionUpdateLogDetailDO.setOldValue("");
            }
            if(Objects.nonNull(newValue) && StringUtils.isNotBlank(newValue.toString())){
                if("collectDate".equals(scsCollectionUpdateLogDetailDO.getFieldEn()) || "inMuseumDate".equals(scsCollectionUpdateLogDetailDO.getFieldEn())){
                    scsCollectionUpdateLogDetailDO.setNewValue(DateUtils.formatDateYYYMMDD((Date)newValue));
                }else {
                    scsCollectionUpdateLogDetailDO.setNewValue(newValue.toString());
                }
            }else{
                scsCollectionUpdateLogDetailDO.setNewValue("");
            }
            if(!scsCollectionUpdateLogDetailDO.getNewValue().equals(scsCollectionUpdateLogDetailDO.getOldValue())){
                updateList.add(scsCollectionUpdateLogDetailDO);
            }
        }
        CollectionRegisterAttrInfoVO collectionRegisterAttrInfoVO = loginCollectionUpdateRecordEvent.getRegisterEntity().getCollectionRegisterAttrInfo();
        Field[] collectionRegisterAttrInfoFields = collectionRegisterAttrInfoVO.getClass().getDeclaredFields();
        for(int i=1;i<collectionRegisterAttrInfoFields.length;i++){
            Object newValue = getFieldValueByName(collectionRegisterAttrInfoFields[i].getName(),collectionRegisterAttrInfoVO);
            Object oldValue = oldValueMap.get(collectionRegisterAttrInfoFields[i].getName());
            CompareField compareField = collectionRegisterAttrInfoFields[i].getAnnotation(CompareField.class);
            if (compareField == null) {
                continue;
            }
            ScsCollectionUpdateLogDetailDO scsCollectionUpdateLogDetailDO = new ScsCollectionUpdateLogDetailDO();
            scsCollectionUpdateLogDetailDO.setCompanyId(loginCollectionUpdateRecordEvent.getCompanyId());
            //scsCollectionUpdateLogDetailDO.setUpdateLogId(logId);
            scsCollectionUpdateLogDetailDO.setField(compareField.value());
            scsCollectionUpdateLogDetailDO.setFieldEn(collectionRegisterAttrInfoFields[i].getName());
            if(!Objects.isNull(oldValue)  && StringUtils.isNotBlank(oldValue.toString())){
                if("excavatedDate".equals(scsCollectionUpdateLogDetailDO.getFieldEn())){
                    scsCollectionUpdateLogDetailDO.setOldValue(DateUtils.formatDateYYYMMDD((Date)oldValue));
                }else if("specificQuality".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "length".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "width".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "height".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "presence".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "caliber".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "bottomDiameter".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "amount".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                ){
                    String thisOld = String.valueOf(new BigDecimal(oldValue.toString()).divide(new BigDecimal(100)));
                    scsCollectionUpdateLogDetailDO.setOldValue(thisOld);
                }else {
                    scsCollectionUpdateLogDetailDO.setOldValue(oldValue.toString());
                }
            }else{
                scsCollectionUpdateLogDetailDO.setOldValue("");
            }

            if(!Objects.isNull(newValue)  && StringUtils.isNotBlank(newValue.toString())){
                if("excavatedDate".equals(scsCollectionUpdateLogDetailDO.getFieldEn())){
                    scsCollectionUpdateLogDetailDO.setNewValue(DateUtils.formatDateYYYMMDD((Date)newValue));
                }else if("specificQuality".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                        || "length".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                        || "width".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                        || "height".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                        || "presence".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                        || "caliber".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                        || "bottomDiameter".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                ){
                    String thisNew = String.valueOf(new BigDecimal(newValue.toString()).divide(new BigDecimal(100)));
                    scsCollectionUpdateLogDetailDO.setNewValue(thisNew);
                }else {
                    scsCollectionUpdateLogDetailDO.setNewValue(newValue.toString());
                }
            }else{
                scsCollectionUpdateLogDetailDO.setNewValue("");
            }

            if(!scsCollectionUpdateLogDetailDO.getNewValue().equals(scsCollectionUpdateLogDetailDO.getOldValue())){
                updateList.add(scsCollectionUpdateLogDetailDO);
            }

        }
        CollectionRegisterAttrExVO collectionRegisterAttrExVO = loginCollectionUpdateRecordEvent.getRegisterEntity().getCollectionRegisterAttrExVO();
        Field[] collectionRegisterAttrExFields = collectionRegisterAttrExVO.getClass().getDeclaredFields();
        for(int i=1;i<collectionRegisterAttrExFields.length;i++){
            Object newValue = getFieldValueByName(collectionRegisterAttrExFields[i].getName(),collectionRegisterAttrExVO);
            Object oldValue = oldValueMap.get(collectionRegisterAttrExFields[i].getName());
            CompareField compareField = collectionRegisterAttrExFields[i].getAnnotation(CompareField.class);
            if (compareField == null) {
                continue;
            }
            ScsCollectionUpdateLogDetailDO scsCollectionUpdateLogDetailDO = new ScsCollectionUpdateLogDetailDO();
            scsCollectionUpdateLogDetailDO.setCompanyId(loginCollectionUpdateRecordEvent.getCompanyId());
            //scsCollectionUpdateLogDetailDO.setUpdateLogId(logId);
            scsCollectionUpdateLogDetailDO.setField(compareField.value());
            scsCollectionUpdateLogDetailDO.setFieldEn(collectionRegisterAttrExFields[i].getName());

            if(!Objects.isNull(oldValue)  && StringUtils.isNotBlank(oldValue.toString())){
                if("collectsDate".equals(scsCollectionUpdateLogDetailDO.getFieldEn())){
                    scsCollectionUpdateLogDetailDO.setOldValue(DateUtils.formatDateYYYMMDD((Date)oldValue));
                }else {
                    scsCollectionUpdateLogDetailDO.setOldValue(oldValue.toString());
                }
            }else{
                scsCollectionUpdateLogDetailDO.setOldValue("");
            }

            if(!Objects.isNull(newValue)  && StringUtils.isNotBlank(newValue.toString())){
                if("collectsDate".equals(scsCollectionUpdateLogDetailDO.getFieldEn())){
                    scsCollectionUpdateLogDetailDO.setNewValue(DateUtils.formatDateYYYMMDD((Date)newValue));
                }else {
                    scsCollectionUpdateLogDetailDO.setNewValue(newValue.toString());
                }
            }else{
                scsCollectionUpdateLogDetailDO.setNewValue("");
            }

            if(!scsCollectionUpdateLogDetailDO.getNewValue().equals(scsCollectionUpdateLogDetailDO.getOldValue())){
                updateList.add(scsCollectionUpdateLogDetailDO);
            }
        }
        List<Long> classificationIds = this.getClassificationIds(updateList);
        Map<Long, String> classificationMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(classificationIds)) {
            List<CommClassificationDO> classificationDOList = this.commClassificationMapper.listByIds(loginCollectionUpdateRecordEvent.getCompanyId(), classificationIds);
            if (CollectionUtils.isNotEmpty(classificationDOList)) {
                classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                        CommClassificationDO::getName, (key1, key2) -> key2));
            }
        }
        Map<Long, String> finalClassificationMap = classificationMap;
        List<ScsCollectionUpdateLogDetailDO> resultList = updateList.stream().map(scsCollectionUpdateLogDetailDO -> {
            if("classify".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "identifyLevel".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "category".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "inTibetanAgeRange".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "age".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "warehouseId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "cupboardId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "floorId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "drawerId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "columnId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "numberId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "packageId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "qualityUnit".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "qualityRange".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "countUnit".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "saveStatus".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "completeDegree".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                    || "source".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
            ){
                if(StringUtils.isNotBlank(scsCollectionUpdateLogDetailDO.getNewValue())){
                    scsCollectionUpdateLogDetailDO.setNewValue(finalClassificationMap.get(Long.parseLong(scsCollectionUpdateLogDetailDO.getNewValue())));
                }
                if(StringUtils.isNotBlank(scsCollectionUpdateLogDetailDO.getOldValue())){
                    scsCollectionUpdateLogDetailDO.setOldValue(finalClassificationMap.get(Long.parseLong(scsCollectionUpdateLogDetailDO.getOldValue())));
                }
            }
            if ("texture".equals(scsCollectionUpdateLogDetailDO.getFieldEn())) {
                if(Objects.nonNull(scsCollectionUpdateLogDetailDO.getNewValue())){
                    List<Long> textureIds = Stream.of(scsCollectionUpdateLogDetailDO.getNewValue().split(",")).map(id -> {
                        if (StringUtils.isBlank(id)) {
                            return null;
                        }
                        return Long.parseLong(id);
                    }).distinct().collect(Collectors.toList());
                    StringBuilder textureBuilder = new StringBuilder();
                    textureIds.forEach(textureId -> {
                        textureBuilder.append(finalClassificationMap.get(textureId));
                        textureBuilder.append(",");
                    });
                    textureBuilder.deleteCharAt(textureBuilder.toString().length() - 1);
                    scsCollectionUpdateLogDetailDO.setNewValue(textureBuilder.toString());
                }
                if(Objects.nonNull(scsCollectionUpdateLogDetailDO.getOldValue())){
                    List<Long> textureIds = Stream.of(scsCollectionUpdateLogDetailDO.getOldValue().split(",")).map(id -> {
                        if (StringUtils.isBlank(id)) {
                            return null;
                        }
                        return Long.parseLong(id);
                    }).distinct().collect(Collectors.toList());
                    StringBuilder textureBuilder = new StringBuilder();
                    textureIds.forEach(textureId -> {
                        textureBuilder.append(finalClassificationMap.get(textureId));
                        textureBuilder.append(",");
                    });
                    textureBuilder.deleteCharAt(textureBuilder.toString().length() - 1);
                    scsCollectionUpdateLogDetailDO.setOldValue(textureBuilder.toString());
                }
            }
            scsCollectionUpdateLogDetailDO.setCreator(loginCollectionUpdateRecordEvent.getModifier());
            scsCollectionUpdateLogDetailDO.setModifier(loginCollectionUpdateRecordEvent.getModifier());
            return scsCollectionUpdateLogDetailDO;
        }).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(resultList)){
            ScsCollectionUpdateLogDO scsCollectionUpdateLogDO = new ScsCollectionUpdateLogDO();
            scsCollectionUpdateLogDO.setCompanyId(loginCollectionUpdateRecordEvent.getCompanyId());
            scsCollectionUpdateLogDO.setCollectionId(loginCollectionUpdateRecordEvent.getCollectionId());
            scsCollectionUpdateLogDO.setRegisterInfoId(loginCollectionUpdateRecordEvent.getRegisterInfoId());
            scsCollectionUpdateLogDO.setRegisterNo(loginCollectionUpdateRecordEvent.getRegisterNo());
            scsCollectionUpdateLogDO.setCreator(loginCollectionUpdateRecordEvent.getModifier());
            scsCollectionUpdateLogDO.setModifier(loginCollectionUpdateRecordEvent.getModifier());
            scsCollectionUpdateLogDO.setCreatorName(loginCollectionUpdateRecordEvent.getModifierName());
            this.scsCollectionUpdateLogMapper.insertSelective(scsCollectionUpdateLogDO);
            Long logId = scsCollectionUpdateLogDO.getId();
            resultList.forEach(s-> s.setUpdateLogId(logId));
            scsCollectionUpdateLogDetailMapper.batchInsert(resultList);
        }
    }

    private List<Long> getClassificationIds(List<ScsCollectionUpdateLogDetailDO> scsCollectionUpdateLogDetailDOList) {
        List<Long> classificationIds = Lists.newArrayList();
        scsCollectionUpdateLogDetailDOList.forEach(scsCollectionUpdateLogDetailDO -> {
            if("classify".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "identifyLevel".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "category".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "inTibetanAgeRange".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "age".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "warehouseId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "cupboardId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "floorId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "drawerId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "columnId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "numberId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "packageId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "qualityUnit".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "qualityRange".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "countUnit".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "saveStatus".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "completeDegree".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                || "source".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
            ){
                if(StringUtils.isNotBlank(scsCollectionUpdateLogDetailDO.getNewValue())){
                    classificationIds.add(Long.parseLong(scsCollectionUpdateLogDetailDO.getNewValue()));
                }
                if(StringUtils.isNotBlank(scsCollectionUpdateLogDetailDO.getOldValue())){
                    classificationIds.add(Long.parseLong(scsCollectionUpdateLogDetailDO.getOldValue()));
                }

            }
            if ("texture".equals(scsCollectionUpdateLogDetailDO.getFieldEn()) && StringUtils.isNotBlank(scsCollectionUpdateLogDetailDO.getNewValue())) {
                List<Long> textureIds = Stream.of(scsCollectionUpdateLogDetailDO.getNewValue().split(","))
                        .map(Long::parseLong).distinct().collect(Collectors.toList());
                classificationIds.addAll(textureIds);
            }
            if ("texture".equals(scsCollectionUpdateLogDetailDO.getFieldEn()) && StringUtils.isNotBlank(scsCollectionUpdateLogDetailDO.getOldValue())) {
                List<Long> textureIds = Stream.of(scsCollectionUpdateLogDetailDO.getOldValue().split(","))
                        .map(Long::parseLong).distinct().collect(Collectors.toList());
                classificationIds.addAll(textureIds);
            }

        });
        return classificationIds.stream().distinct().collect(Collectors.toList());
    }

    private static Object getFieldValueByName(String fieldName, Object o) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter, new Class[] {});
            Object value = method.invoke(o, new Object[] {});
            return value;
        } catch (Exception e) {
            return null;
        }
    }
}
