package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-10-24
 */
public enum ReserveDateEnum {

    今日(0, "今日"),
    本周(1, "本周"),
    本月(2, "本月"),
    本年(3, "本年"),
    至今(4, "至今"),
    自定义(5, "自定义"),
    ;

    private Integer code;
    private String desc;

    ReserveDateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ReserveDateEnum fromCode(Integer code) {
        for (ReserveDateEnum iEnum : ReserveDateEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
