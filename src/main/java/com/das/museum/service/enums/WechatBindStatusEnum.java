package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-10-21
 */
public enum WechatBindStatusEnum {
    未绑定(0, "未绑定"),
    已绑定(1, "已绑定"),
    ;

    private Integer code;
    private String desc;

    WechatBindStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WechatBindStatusEnum fromCode(Integer code) {
        for (WechatBindStatusEnum iEnum : WechatBindStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
