package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-12-05
 */
public enum SecurityLevelEnum {
    普通(1, "普通"),
    限制(2, "限制"),
    机密(3, "机密"),
    ;

    private Integer code;
    private String desc;

    SecurityLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SecurityLevelEnum fromCode(Integer code) {
        for (SecurityLevelEnum iEnum : SecurityLevelEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
