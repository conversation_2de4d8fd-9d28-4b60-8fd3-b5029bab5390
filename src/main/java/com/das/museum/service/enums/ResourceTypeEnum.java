package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 *
 */
public enum ResourceTypeEnum {

    资源库(0, "资源库"),
    文件(1, "文件"),
    文件夹(2, "文件夹"),
    资源群(3, "资源群")
    ;

    private Integer code;
    private String desc;

    ResourceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ResourceTypeEnum fromCode(Integer code) {
        for (ResourceTypeEnum iEnum : ResourceTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
