package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-12-05
 */
public enum FunctionPermissionEnum {
    无权限(0, "无权限"),
    仅查看(1, "仅查看"),
    可查看和下载(2, "可查看和下载"),
    ;

    private Integer code;
    private String desc;

    FunctionPermissionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FunctionPermissionEnum fromCode(Integer code) {
        for (FunctionPermissionEnum iEnum : FunctionPermissionEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
