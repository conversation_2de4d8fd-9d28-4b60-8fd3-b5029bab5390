package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * 投放渠道状态
 * @Author: shaochengwei
 * @Date: 2022-10-14
 */
public enum PutStatusEnum {
    未开始(0, "未开始"),
    投放中(1, "投放中"),
    已停止(2, "已停止"),
    ;

    private Integer code;
    private String desc;

    PutStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PutStatusEnum fromCode(Integer code) {
        for (PutStatusEnum iEnum : PutStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
