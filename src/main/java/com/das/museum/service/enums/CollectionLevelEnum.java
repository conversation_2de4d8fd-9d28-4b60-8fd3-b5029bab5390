package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * <AUTHOR>
 * @date 2023/6/5
 */
public enum CollectionLevelEnum {
    一级("一级", "一级"),
    二级("二级", "二级"),
    三级("三级", "三级"),
    未定级("未定级", "未定级"),
    一般("一般", "一般"),
    参考品("参考品", "参考品"),
    资料("资料", "资料"),
    ;

    private String code;
    private String desc;

    CollectionLevelEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static CollectionLevelEnum fromCode(String code){
        for(CollectionLevelEnum iEnum : CollectionLevelEnum.values()){
            if(iEnum.getCode().equals(code)){
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public String getCode(){
        return code;
    }

    public String getDesc(){
        return desc;
    }
}
