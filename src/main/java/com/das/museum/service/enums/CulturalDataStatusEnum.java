package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-11-23
 */
public enum CulturalDataStatusEnum {
    现场窗口(0, "现场窗口"),
    二维和三维(1, "二维和三维"),
    二维(2, "二维"),
    三维(3, "三维"),
    ;

    private Integer code;
    private String desc;

    CulturalDataStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CulturalDataStatusEnum fromCode(Integer code) {
        for (CulturalDataStatusEnum iEnum : CulturalDataStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
