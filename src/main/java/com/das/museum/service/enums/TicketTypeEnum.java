package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * 票种类型
 * @Author: shaochengwei
 * @Date: 2022-10-14
 */
public enum TicketTypeEnum {
    免费票(0, "免费票"),
    收费票(1, "收费票"),
    ;

    private Integer code;
    private String desc;

    TicketTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TicketTypeEnum fromCode(Integer code) {
        for (TicketTypeEnum iEnum : TicketTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
