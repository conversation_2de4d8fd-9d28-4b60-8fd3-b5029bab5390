package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
/**
 * 模型构建状态枚举
 *
 * @Author: Lee
 * @Date: 2022/11/30
 */
public enum ModelBuildStatusEnum {
    PENDING(0, "等待处理"),
    RUNNING(1, "进行中"),
    COMPLETED(2, "成功"),
    FAILED(3, "失败"),
    CANCELLED(4, "被用户取消");


    private Integer code;
    private String desc;

    ModelBuildStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ModelBuildStatusEnum fromCode(Integer code) {
        for (ModelBuildStatusEnum iEnum : ModelBuildStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
