package com.das.museum.service.enums;


import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * <AUTHOR>
 * @date 2022/7/18
 */
public enum InputModeEnum {
    CTE("CTE", "征集录入"),
    ANE("ANE", "鉴定录入"),
    ALY("ALY", "入馆录入"),
    CNE("CNE", "入藏录入"),
    REG("REG", "登录录入"),
    ;

    private String code;
    private String desc;

    InputModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static InputModeEnum fromCode(String code) {
        for (InputModeEnum iEnum : InputModeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
