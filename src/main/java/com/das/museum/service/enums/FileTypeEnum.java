package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

public enum FileTypeEnum {

    ATCHMT("ATCHMT", "附件"),
    SCAS("SCAS", "扫描件"),
    TEXT("TEXT", "文本文件"),
    IMAGE("IMAGE", "图片"),
    AUDIO("AUDIO", "音频"),
    VIDEO("VIDEO", "视频"),
    THREE_MODEL("3D_MODEL", "三维模型")
    ;

    private String code;
    private String desc;

    FileTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FileTypeEnum fromCode(String code) {
        for (FileTypeEnum iEnum : FileTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
