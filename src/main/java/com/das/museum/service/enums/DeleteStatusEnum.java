package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * <AUTHOR>
 * @date 2022/09/25
 */
public enum DeleteStatusEnum {

    否(0, "否"),
    是(1, "是"),
    ;

    private Integer code;
    private String desc;

    DeleteStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DeleteStatusEnum fromCode(Integer code) {
        for (DeleteStatusEnum iEnum : DeleteStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
