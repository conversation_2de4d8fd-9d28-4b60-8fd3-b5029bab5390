package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * 是否 公共枚举
 * @Author: shaochengwei
 * @Date: 2022-10-18
 */
public enum YesOrNoEnum {
    否(0, "否"),
    是(1, "是"),
    ;

    private Integer code;
    private String desc;

    YesOrNoEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static YesOrNoEnum fromCode(Integer code) {
        for (YesOrNoEnum iEnum : YesOrNoEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
