package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * 是否开启投放渠道
 * @Author: shaochengwei
 * @Date: 2022-10-14
 */
public enum StartChannelEnum {
    否(0, "否"),
    是(1, "是"),
    ;

    private Integer code;
    private String desc;

    StartChannelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static StartChannelEnum fromCode(Integer code) {
        for (StartChannelEnum iEnum : StartChannelEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
