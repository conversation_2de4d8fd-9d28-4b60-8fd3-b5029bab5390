package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-11-14
 */
public enum DisplayStatusEnum {

    未展示(0, "未展示"),
    展示中(1, "展示中"),
    ;

    private Integer code;
    private String desc;

    DisplayStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DisplayStatusEnum fromCode(Integer code) {
        for (DisplayStatusEnum iEnum : DisplayStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
