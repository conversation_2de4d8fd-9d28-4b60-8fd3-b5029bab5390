package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * <AUTHOR>
 * @date 2022/7/20
 */
public enum ProcessStatusEnum {

    NTBT("NTBT", "未入藏"),
    CNAD("CNAD", "已分配保管员"),
    CCKD("CCKD", "已查收"),
    RTND("RTND", "已退回"),
    ;

    private String code;
    private String desc;

    ProcessStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProcessStatusEnum fromCode(String code) {
        for (ProcessStatusEnum iEnum : ProcessStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
