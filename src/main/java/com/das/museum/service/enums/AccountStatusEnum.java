package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * <AUTHOR>
 * @date 2022/09/25
 */
public enum AccountStatusEnum {

    禁用(0, "禁用"),
    启用(1, "启用"),
    ;

    private Integer code;
    private String desc;

    AccountStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AccountStatusEnum fromCode(Integer code) {
        for (AccountStatusEnum iEnum : AccountStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
