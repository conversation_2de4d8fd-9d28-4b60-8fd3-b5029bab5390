package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-10-24
 */
public enum OrderTypeEnum {

    个人预约(1, "个人预约"),
    团队预约(2, "团队预约"),
    ;

    private Integer code;
    private String desc;

    OrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OrderTypeEnum fromCode(Integer code) {
        for (OrderTypeEnum iEnum : OrderTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
