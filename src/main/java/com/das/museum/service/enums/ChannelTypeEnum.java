package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * 投放渠道
 * @Author: shaochengwei
 * @Date: 2022-10-14
 */
public enum ChannelTypeEnum {
    现场窗口(1, "现场窗口"),
    微信小程序(2, "微信小程序"),
    ;

    private Integer code;
    private String desc;

    ChannelTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ChannelTypeEnum fromCode(Integer code) {
        for (ChannelTypeEnum iEnum : ChannelTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
