package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-10-24
 */
public enum ReserveTypeEnum {

    提前预约(1, "提前预约"),
    免预约通行(2, "免预约通行"),
    ;

    private Integer code;
    private String desc;

    ReserveTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ReserveTypeEnum fromCode(Integer code) {
        for (ReserveTypeEnum iEnum : ReserveTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
