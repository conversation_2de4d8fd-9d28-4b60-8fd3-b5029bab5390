package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-12-03
 */
public enum ApplyStatusEnum {
    申请中(0, "申请中"),
    已通过(1, "已通过"),
    未通过(2, "未通过"),
    ;

    private Integer code;
    private String desc;

    ApplyStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ApplyStatusEnum fromCode(Integer code) {
        for (ApplyStatusEnum iEnum : ApplyStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
