package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * <AUTHOR>
 * @date 2022/09/25
 */
public enum SpecialProcessEnum {

    压缩(1, "压缩"),
    ;

    private Integer code;
    private String desc;

    SpecialProcessEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SpecialProcessEnum fromCode(Integer code) {
        for (SpecialProcessEnum iEnum : SpecialProcessEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST, "资源处理类型不存在");
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
