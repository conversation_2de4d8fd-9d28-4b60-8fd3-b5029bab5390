package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-12-05
 */
public enum UseTimeLimitEnum {
    七天(0, "七天"),
    永久(1, "永久"),
    ;

    private Integer code;
    private String desc;

    UseTimeLimitEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UseTimeLimitEnum fromCode(Integer code) {
        for (UseTimeLimitEnum iEnum : UseTimeLimitEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
