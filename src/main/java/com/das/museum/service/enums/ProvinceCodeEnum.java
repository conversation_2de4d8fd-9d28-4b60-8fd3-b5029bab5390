package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 *
 * @Author: shaochengwei
 * @Date: 2023-02-21
 */
public enum ProvinceCodeEnum {
    北京市("110", "北京市"),
    天津市("120", "天津市"),
    河北省("130", "河北省"),
    山西省("140", "山西省"),
    内蒙古自治区("150", "内蒙古自治区"),
    辽宁省("210", "辽宁省"),
    吉林省("220", "吉林省"),
    黑龙江省("230", "黑龙江省"),
    上海市("310", "上海市"),
    江苏省("320", "江苏省"),
    浙江省("330", "浙江省"),
    安徽省("340", "安徽省"),
    福建省("350", "福建省"),
    江西省("360", "江西省"),
    山东省("370", "山东省"),
    河南省("410", "河南省"),
    湖北省("420", "湖北省"),
    湖南省("430", "湖南省"),
    广东省("440", "广东省"),
    广西壮族自治区("450", "广西壮族自治区"),
    海南省("460", "海南省"),
    四川省("510", "四川省"),
    贵州省("520", "贵州省"),
    云南省("530", "云南省"),
    西藏自治区("540", "西藏自治区"),
    重庆市("500", "重庆市"),
    陕西省("610", "陕西省"),
    甘肃省("620", "甘肃省"),
    青海省("630", "青海省"),
    宁夏回族自治区("640", "宁夏回族自治区"),
    新疆维吾尔自治区("650", "新疆维吾尔自治区"),
    香港特别行政区("810", "香港特别行政区"),
    澳门特别行政区("820", "澳门特别行政区"),
    台湾省("830", "台湾省"),
    ;

    private String code;
    private String desc;

    ProvinceCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProvinceCodeEnum fromCode(String code) {
        for (ProvinceCodeEnum iEnum : ProvinceCodeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
