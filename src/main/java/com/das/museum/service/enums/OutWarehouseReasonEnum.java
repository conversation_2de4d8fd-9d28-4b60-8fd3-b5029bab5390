package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * <AUTHOR>
 */

public enum OutWarehouseReasonEnum {

    GNZS("GNZS", "馆内展览"),
    GWZL("G<PERSON><PERSON><PERSON>", "馆外展览（国内）"),
    JWZL("JWZL", "境外展览");

    private String code;
    private String desc;

    OutWarehouseReasonEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static OutWarehouseReasonEnum fromCode(String code){
        for(OutWarehouseReasonEnum iEnum : OutWarehouseReasonEnum.values()){
            if(iEnum.getCode().equals(code)){
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public String getCode(){
        return code;
    }

    public String getDesc(){
        return desc;
    }
}
