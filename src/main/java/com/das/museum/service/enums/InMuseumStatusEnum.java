package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * <AUTHOR>
 * @date 2022/7/20
 */
public enum InMuseumStatusEnum {

    NMSM("NMSM", "未入馆"),
    AMSM("AMSM", "已入馆"),
    ;

    private String code;
    private String desc;

    InMuseumStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static InputTypeEnum fromCode(String code) {
        for (InputTypeEnum iEnum : InputTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
