package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-12-05
 */
public enum DataPermissionEnum {
    不可见(0, "不可见"),
    可见(1, "可见"),
    ;

    private Integer code;
    private String desc;

    DataPermissionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DataPermissionEnum fromCode(Integer code) {
        for (DataPermissionEnum iEnum : DataPermissionEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
