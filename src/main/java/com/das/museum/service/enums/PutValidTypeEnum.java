package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * 渠道有效期类型
 * @Author: shaochengwei
 * @Date: 2022-10-14
 */
public enum PutValidTypeEnum {

    长期有效(0, "长期有效"),
    限时有效(1, "限时有效"),
    ;

    private Integer code;
    private String desc;

    PutValidTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PutValidTypeEnum fromCode(Integer code) {
        for (PutValidTypeEnum iEnum : PutValidTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
