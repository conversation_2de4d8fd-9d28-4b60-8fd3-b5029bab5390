package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-12-06
 */
public enum ApplyUseEnum {
    查看(0, "查看"),
    下载(1, "下载"),
    查看并下载(2, "查看并下载"),
    ;

    private Integer code;
    private String desc;

    ApplyUseEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ApplyUseEnum fromCode(Integer code) {
        for (ApplyUseEnum iEnum : ApplyUseEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
