package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * 三维模型转换状态
 * @Author: shaochengwei
 * @Date: 2022-11-15
 */
public enum ThreeModelStatusEnum {
    未转换(0, "未转换"),
    转换中(1, "转换中"),
    转换成功(2, "转换成功"),
    转换失败(3, "转换失败"),
    ;

    private Integer code;
    private String desc;

    ThreeModelStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ThreeModelStatusEnum fromCode(Integer code) {
        for (ThreeModelStatusEnum iEnum : ThreeModelStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
