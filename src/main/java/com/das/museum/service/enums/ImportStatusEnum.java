package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2023-01-11
 */
public enum ImportStatusEnum {
    导入中(0, "导入中"),
    导入成功(1, "导入成功"),
    导入失败(2, "导入失败")
    ;

    private Integer code;
    private String desc;

    ImportStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ImportStatusEnum fromCode(Integer code) {
        for (ImportStatusEnum iEnum : ImportStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
