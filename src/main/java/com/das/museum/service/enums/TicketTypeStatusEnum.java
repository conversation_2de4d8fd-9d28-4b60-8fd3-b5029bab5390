package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * 票种禁用启用状态
 * @Author: shaochengwei
 * @Date: 2022-10-17
 */
public enum TicketTypeStatusEnum {
    禁用(0, "禁用"),
    启用(1, "启用"),
    ;

    private Integer code;
    private String desc;

    TicketTypeStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TicketTypeStatusEnum fromCode(Integer code) {
        for (TicketTypeStatusEnum iEnum : TicketTypeStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
