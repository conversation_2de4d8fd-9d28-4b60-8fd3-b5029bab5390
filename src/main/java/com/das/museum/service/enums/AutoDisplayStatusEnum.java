package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: Lee
 * @Date: 2022/12/26
 * 是否自动展示的状态枚举
 */
public enum AutoDisplayStatusEnum {

    不自动展示(0, "不自动展示"),
    自动展示(1, "自动展示"),
    ;

    private Integer code;
    private String desc;

    AutoDisplayStatusEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static AutoDisplayStatusEnum fromCode(Integer code){
        for(AutoDisplayStatusEnum iEnum : AutoDisplayStatusEnum.values()){
            if(iEnum.getCode().equals(code)){
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode(){
        return code;
    }

    public String getDesc(){
        return desc;
    }
}
