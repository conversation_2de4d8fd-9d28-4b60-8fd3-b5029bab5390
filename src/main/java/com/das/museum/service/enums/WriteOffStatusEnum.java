package com.das.museum.service.enums;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;

/**
 * @Author: shaochengwei
 * @Date: 2022-10-24
 */
public enum WriteOffStatusEnum {

    未核销(0, "未核销"),
    已核销(1, "已核销"),
    ;

    private Integer code;
    private String desc;

    WriteOffStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WriteOffStatusEnum fromCode(Integer code) {
        for (WriteOffStatusEnum iEnum : WriteOffStatusEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
