package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionFileMediaAction;
import com.das.museum.service.biz.collection.action.EditCollectionFileMediaAction;
import com.das.museum.service.biz.collection.action.MediaQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionFileMediaDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionFileMediaPageDTO;
import com.das.museum.common.bo.SimplePageInfo;

public interface CollectionFileMediaService {
    Long addCollectionFileMedia(AddCollectionFileMediaAction action);
    void editCollectionFileMedia(EditCollectionFileMediaAction action);
    void delByCollectionFileMediaId(Long companyId, Long collectionFileMediaId);
    SimplePageInfo<CollectionFileMediaPageDTO> queryPageByCondition(MediaQueryPageByConditionAction action);
    CollectionFileMediaDetailDTO queryByCollectionFileMediaId(Long companyId, Long collectionFileMediaId);
}
