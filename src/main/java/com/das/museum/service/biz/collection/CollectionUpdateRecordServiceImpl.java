package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.CollectionUpdateRecordAction;
import com.das.museum.service.biz.collection.action.CollectionUpdateRecordDetailAction;
import com.das.museum.service.biz.collection.dto.CollectionUpdateRecordDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionUpdateRecordPageDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.infr.dataobject.ScsCollectionUpdateLogDO;
import com.das.museum.infr.dataobject.ScsCollectionUpdateLogDetailDO;
import com.das.museum.infr.mapper.ScsCollectionUpdateLogDetailMapper;
import com.das.museum.infr.mapper.ScsCollectionUpdateLogMapper;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class CollectionUpdateRecordServiceImpl implements CollectionUpdateRecordService{

    @Resource
    private ScsCollectionUpdateLogMapper scsCollectionUpdateLogMapper;

    @Resource
    private ScsCollectionUpdateLogDetailMapper scsCollectionUpdateLogDetailMapper;

    @Resource
    private DocumentService documentService;

    @Override
    public SimplePageInfo<CollectionUpdateRecordPageDTO> queryPageByCondition(CollectionUpdateRecordAction action){
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionUpdateLogDO> scsCollectionUpdateLogList = this.scsCollectionUpdateLogMapper
                .listPageByCondition(companyId, action.getRegisterInfoId(),action.getSortBy());
        if (CollectionUtils.isEmpty(scsCollectionUpdateLogList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<CollectionUpdateRecordPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(scsCollectionUpdateLogList, CollectionUpdateRecordPageDTO.class);
        List<CollectionUpdateRecordPageDTO> resultList = scsCollectionUpdateLogList.stream().map(
                scsCollectionUpdateLogDO -> {
                    CollectionUpdateRecordPageDTO dto = new CollectionUpdateRecordPageDTO();
                    dto.setId(scsCollectionUpdateLogDO.getId());
                    dto.setCreatorName(scsCollectionUpdateLogDO.getCreatorName());
                    if(Objects.nonNull(scsCollectionUpdateLogDO.getGmtCreate())){
                        dto.setGmtCreate(DateUtils.formatDateYYYMMDDHHmmss(scsCollectionUpdateLogDO.getGmtCreate()));
                    }
                    return dto;
                }
        ).collect(Collectors.toList());
        pageInfo.setList(resultList);
        return pageInfo;
    }

    @Override
    public List<CollectionUpdateRecordDetailDTO> queryUpdateRecordDetail(CollectionUpdateRecordDetailAction action){
        Long companyId = action.getCompanyId();
        Long updateLogId = action.getUpdateLogId();
        List<ScsCollectionUpdateLogDetailDO> scsCollectionUpdateLogDetailList = this.scsCollectionUpdateLogDetailMapper.getUpdateLogDetail(companyId,updateLogId);
        List<CollectionUpdateRecordDetailDTO> resultList = scsCollectionUpdateLogDetailList.stream().map(
                scsCollectionUpdateLogDetailDO -> {
                    CollectionUpdateRecordDetailDTO collectionUpdateRecordDetailDTO = new CollectionUpdateRecordDetailDTO();
                    collectionUpdateRecordDetailDTO.setField(scsCollectionUpdateLogDetailDO.getField());
                    //如果字段是 是否有囊匣、存疑、对外、预订
                    if("isBox".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                        || "isDoubtful".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                        || "isForeign".equals(scsCollectionUpdateLogDetailDO.getFieldEn())
                        || "isBooking".equals(scsCollectionUpdateLogDetailDO.getFieldEn())){
                        if("0".equals(scsCollectionUpdateLogDetailDO.getOldValue())){
                            collectionUpdateRecordDetailDTO.setOldValue("否");
                        }else if("1".equals(scsCollectionUpdateLogDetailDO.getOldValue())){
                            collectionUpdateRecordDetailDTO.setOldValue("是");
                        }else{
                            collectionUpdateRecordDetailDTO.setOldValue("");
                        }

                        if("0".equals(scsCollectionUpdateLogDetailDO.getNewValue())){
                            collectionUpdateRecordDetailDTO.setNewValue("否");
                        }else if("1".equals(scsCollectionUpdateLogDetailDO.getNewValue())){
                            collectionUpdateRecordDetailDTO.setNewValue("是");
                        }else{
                            collectionUpdateRecordDetailDTO.setNewValue("");
                        }
                    }else if("documentId".equals(scsCollectionUpdateLogDetailDO.getFieldEn())){
                        if(StringUtils.isNotBlank(scsCollectionUpdateLogDetailDO.getOldValue())){
                            CommonDocumentDTO commonDocumentDTO =  documentService.queryByDocId(companyId, Long.parseLong(scsCollectionUpdateLogDetailDO.getOldValue()));
                            collectionUpdateRecordDetailDTO.setOldValue(commonDocumentDTO.getUrl());
                        }
                        if(StringUtils.isNotBlank(scsCollectionUpdateLogDetailDO.getNewValue())){
                            CommonDocumentDTO commonDocumentDTO =  documentService.queryByDocId(companyId, Long.parseLong(scsCollectionUpdateLogDetailDO.getNewValue()));
                            collectionUpdateRecordDetailDTO.setNewValue(commonDocumentDTO.getUrl());
                        }
                    }else{
                        collectionUpdateRecordDetailDTO.setOldValue(scsCollectionUpdateLogDetailDO.getOldValue());
                        collectionUpdateRecordDetailDTO.setNewValue(scsCollectionUpdateLogDetailDO.getNewValue());
                    }

                    return collectionUpdateRecordDetailDTO;
                }
        ).collect(Collectors.toList());
        return resultList;
    }
}
