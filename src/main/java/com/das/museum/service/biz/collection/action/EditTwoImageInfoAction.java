package com.das.museum.service.biz.collection.action;

import lombok.Data;

@Data
public class EditTwoImageInfoAction {

    /**
     * 二维影像id
     */
    private Long twoImageId;

    /**
     * 文件名称
     */
    private String documentName;

    /**
     * 文件批次
     */
    private String fileBatch;

    /**
     * 制作单位
     */
    private String makeUnit;

    /**
     * 制作日期
     */
    private String makeTime;

    /**
     * 分辨率
     */
    private String resolutionRatio;

    /**
     * 采集设备
     */
    private String collectDevice;

    /**
     * 说明备注
     */
    private String remark;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 更新人
     */
    private Long modifier;

}
