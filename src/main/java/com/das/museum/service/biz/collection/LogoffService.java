package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddLogoffAction;
import com.das.museum.service.biz.collection.action.EditLogoffAction;
import com.das.museum.service.biz.collection.action.LogoffConditionQueryAction;
import com.das.museum.service.biz.collection.action.PageLogoffCollectionAction;
import com.das.museum.service.biz.collection.dto.LogoffCollectionDTO;
import com.das.museum.service.biz.collection.dto.LogoffInfoDetailDTO;
import com.das.museum.service.biz.collection.dto.LogoffInfoPageDTO;
import com.das.museum.common.bo.SimplePageInfo;

public interface LogoffService {
    Long addLogoffCertificateInfo(AddLogoffAction addLogoffAction);
    void editLogoffCertificateInfo(EditLogoffAction editLogoffAction);
    void delLogoffCertificateById(Long companyId, Long logoffId);
    LogoffInfoDetailDTO queryDetailInfoById(Long companyId, Long logoffId);
    SimplePageInfo<LogoffInfoPageDTO> queryPageByConditionQuery(LogoffConditionQueryAction action);
    SimplePageInfo<LogoffCollectionDTO> queryPageCollectionByLogoffId(PageLogoffCollectionAction action);
}
