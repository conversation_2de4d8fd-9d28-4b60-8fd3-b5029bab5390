package com.das.museum.service.biz.collection;

import com.das.museum.common.utils.PaginationUtils;
import com.das.museum.infr.dataobject.CommIncrSegmentDO;
import com.das.museum.infr.mapper.CommIncrSegmentMapper;
import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.*;
import com.das.museum.common.constants.CollectionConstant;
import com.das.museum.service.biz.user.UserService;
import com.das.museum.service.enums.InputModeEnum;
import com.das.museum.service.enums.InputTypeEnum;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.domain.collection.entity.IdentifyCertificateEntity;
import com.das.museum.domain.collection.entity.IdentifyCollectionEntity;
import com.das.museum.domain.collection.repository.CollectionRepository;
import com.das.museum.domain.collection.repository.IdentifyCertificateRepository;
import com.das.museum.domain.collection.repository.IdentifyCollectionRepository;
import com.das.museum.domain.collection.valueobject.CollectionVO;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.infr.dataobject.ScsIdentifyCertificateInfoDO;
import com.das.museum.infr.dataobject.ScsIdentifyInfoDO;
import com.das.museum.infr.mapper.ScsIdentifyCertificateInfoMapper;
import com.das.museum.infr.mapper.ScsIdentifyInfoMapper;
import com.das.museum.infr.query.IdentifyConditionQuery;
import com.das.museum.service.enums.YesOrNoEnum;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
@Slf4j
@Service
public class IdentifyCertificateServiceImpl implements IdentifyCertificateService{

    @Resource
    private IdentifyCertificateRepository identifyCertificateRepository;

    @Resource
    private CollectionRepository collectionRepository;

    @Resource
    private ScsIdentifyCertificateInfoMapper scsIdentifyCertificateInfoMapper;

    @Resource
    private IdentifyCollectionRepository identifyCollectionRepository;

    @Resource
    private UserService userService;

    @Resource
    private CollectionService collectionService;

    @Resource
    private CheckService checkService;

    @Resource
    private ScsIdentifyInfoMapper scsIdentifyInfoMapper;

    @Resource
    private CommIncrSegmentMapper commIncrSegmentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addIdentifyCertificateInfo(AddIdentifyAction addIdentifyAction) {
        String bizDimension = DateUtils.formatDateYYYMMDD().substring(0, 4);
        CommIncrSegmentDO commIncrSegmentDO = this.commIncrSegmentMapper
                .selectByCompanyIdAndBizCode(addIdentifyAction.getCompanyId(), CollectionBizTypeEnum.IDY.getCode(), bizDimension);
        if (Objects.isNull(commIncrSegmentDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "鉴定凭证自动编号配置不存在");
        }
        Long bizCurrIndex = commIncrSegmentDO.getBizCurrIndex();
        String identifyCertificateNo = addIdentifyAction.getPrefixName()
                + CollectionConstant.IDENTIFY_CERTIFICATE_MIDDLE + (bizCurrIndex + 1)
                + CollectionConstant.CERTIFICATE_TAIL;
        IdentifyCertificateEntity identifyCertificateEntity = this.convertIdentifyCertificateEntity(addIdentifyAction);
        identifyCertificateEntity.setSuffixNumber(bizCurrIndex + 1);
        identifyCertificateEntity.setIdentifyCertificateNo(identifyCertificateNo);
        identifyCertificateEntity.setAutoAudit(addIdentifyAction.getAutoAudit());
        this.identifyCertificateRepository.saveIdentifyCertificate(identifyCertificateEntity);
        Long identifyId = identifyCertificateEntity.getIdentifyId();
        this.collectionRepository.saveByBizIdAndBizType(addIdentifyAction.getCompanyId(), identifyId,
                CollectionBizTypeEnum.IDY.getCode(), identifyCertificateEntity.getIdentifyCollectionList());

        /*IdentifyCertificateEntity updateEntity = new IdentifyCertificateEntity();
        updateEntity.setIdentifyId(identifyId);
        updateEntity.setSuffixNumber(identifyId);
        updateEntity.setIdentifyCertificateNo(addIdentifyAction.getPrefixName()
                + CollectionConstant.IDENTIFY_CERTIFICATE_MIDDLE + identifyId
                + CollectionConstant.CERTIFICATE_TAIL);
        updateEntity.setAutoAudit(addIdentifyAction.getAutoAudit());
        this.identifyCertificateRepository.editIdentifyCertificate(updateEntity);*/
        int row = this.commIncrSegmentMapper.updateByBizCurrIndex(commIncrSegmentDO.getId(), bizCurrIndex);
        if (row <= 0) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "鉴定凭证自动获取编号失败");
        }
        // 自动提审
        if(addIdentifyAction.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(addIdentifyAction.getCreator());
            submitCheckAction.setCompanyId(addIdentifyAction.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.IDY);
            submitCheckAction.setModelId(identifyId);
            submitCheckAction.setCreatorName(addIdentifyAction.getCreatorName());
            this.checkService.submitCheck(submitCheckAction);
        }

        return identifyId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editIdentifyCertificateInfo(EditIdentifyAction editCollectAction){
        Long identifyId = editCollectAction.getIdentifyId();
        Long companyId = editCollectAction.getCompanyId();
        inMuseumCertificateInfoCheck(companyId,identifyId);
        IdentifyCertificateEntity identifyCertificateEntity = this.convertIdentifyCertificateEntity(editCollectAction);
        identifyCertificateEntity.setSuffixNumber(identifyId);
        identifyCertificateEntity.setIdentifyCertificateNo(editCollectAction.getPrefixName()
                + CollectionConstant.IDENTIFY_CERTIFICATE_MIDDLE + identifyId
                + CollectionConstant.CERTIFICATE_TAIL);
        List<Long> excludeCollectionIds = null;
        if (CollectionUtils.isNotEmpty(identifyCertificateEntity.getIdentifyCollectionList())) {
            excludeCollectionIds = identifyCertificateEntity.getIdentifyCollectionList().stream()
                    .map(CollectionVO::getCollectionId).distinct().collect(Collectors.toList());
        }
        this.collectionRepository.delByBizIdAndBizTypeExcludeCLT(editCollectAction.getCompanyId(), identifyId,
                CollectionBizTypeEnum.IDY.getCode(), excludeCollectionIds, InputModeEnum.ANE.getCode(), InputTypeEnum.MLE.getCode());
        this.identifyCertificateRepository.editIdentifyCertificate(identifyCertificateEntity);
        this.collectionRepository.saveByBizIdAndBizType(companyId, identifyId, CollectionBizTypeEnum.IDY.getCode(), identifyCertificateEntity.getIdentifyCollectionList());

        // 自动提审
        if(editCollectAction.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(editCollectAction.getCreator());
            submitCheckAction.setCompanyId(editCollectAction.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.IDY);
            submitCheckAction.setModelId(identifyId);
            submitCheckAction.setCreatorName(editCollectAction.getCreatorName());
            this.checkService.submitCheck(submitCheckAction);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delIdentifyCertificateInfoById(Long companyId, Long identifyId){
        inMuseumCertificateInfoCheck(companyId,identifyId);
        this.identifyCertificateRepository.delIdentifyCertificateById(companyId, identifyId);
        this.collectionRepository.delByBizIdAndBizTypeExcludeCLT(companyId, identifyId, CollectionBizTypeEnum.IDY.getCode(), Lists.newArrayList(),InputModeEnum.ANE.getCode(), InputTypeEnum.MLE.getCode());
    }

    @Override
    public SimplePageInfo<IdentifyInfoPageDTO> queryPageByQuery(IdentifyQueryAction identifyQueryAction){
        Long companyId = identifyQueryAction.getCompanyId();
        // identifyQueryAction.startPage();
        IdentifyConditionQuery query = BeanCopyUtils.copyByJSON(identifyQueryAction, IdentifyConditionQuery.class);
        List<ScsIdentifyCertificateInfoDO> identifyDOList = this.scsIdentifyCertificateInfoMapper
                .listPageByCondition(companyId, identifyQueryAction.getIdentifyCertificateNo(), identifyQueryAction.getSortBy(), query);
        SimplePageInfo<IdentifyInfoPageDTO> pageInfo = new SimplePageInfo<>();
        if (CollectionUtils.isEmpty(identifyDOList)) {
            pageInfo.setList(Lists.newArrayList());
            return pageInfo;
        }
        /*pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(identifyDOList, IdentifyInfoPageDTO.class);*/
        List<IdentifyInfoPageDTO> collectInfoPageDTOList = this.setCollectAttributes(companyId, identifyDOList, identifyQueryAction.getCheckStatus(), identifyQueryAction.getIsUserChecked());
        // pageInfo.setList(collectInfoPageDTOList);
        return PaginationUtils.pagination(collectInfoPageDTOList, identifyQueryAction.getPageNum(), identifyQueryAction.getPageSize());

    }

    @Override
    public IdentifyInfoDetailDTO queryDetailInfoById(Long companyId, Long identifyId){
        ScsIdentifyCertificateInfoDO identifyDO = this.scsIdentifyCertificateInfoMapper.selectByPrimaryKey(identifyId);
        if (Objects.isNull(identifyDO)) {
            return null;
        }
        IdentifyInfoDetailDTO collectInfoDetailDTO = BeanCopyUtils.copyByJSON(identifyDO, IdentifyInfoDetailDTO.class);
        collectInfoDetailDTO.setIdentifyId(identifyDO.getId());
        return collectInfoDetailDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void identifyCollectionById(IdentifyCollectionAction identifyCollectionAction){
        Long companyId = identifyCollectionAction.getCompanyId();
        Long collectionId = identifyCollectionAction.getCollectionId();
        CollectionInfoDetailDTO collectionInfoDetailDTO = this.collectionService.queryCollectionInfoById(companyId,collectionId);
        if (Objects.isNull(collectionInfoDetailDTO)) {
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "藏品信息不存在");
        }
        IdentifyCollectionEntity identifyCollectionEntity = convertIdentifyCollectionEntity(identifyCollectionAction);
        this.identifyCollectionRepository.saveIdentifyCollection(identifyCollectionEntity);
    }

    @Override
    public List<IdentifyCheckedCollectionInfoDTO> queryCheckedCollectionInfoList(Long companyId, Long identifyId){
        Map<Long,Object> collectionMap = new HashMap<>();
        List<IdentifyCollectionInfoPageDTO> collectionList = this.collectionService.queryCollectionInfoByIdentifyIdNoPage(companyId,identifyId);
        List<Long> collectionIds = collectionList.stream().map(IdentifyCollectionInfoPageDTO::getCollectionId).collect(Collectors.toList());
        QueryCheckedCollectionIdAction action  = new QueryCheckedCollectionIdAction();
        action.setCompanyId(companyId);
        action.setBelongModel(CollectionBizTypeEnum.IDY);
        action.setModelId(identifyId);
        action.setCollectionIds(collectionIds);
        List<Long> checkCollectionIdList =  checkService.queryCheckedCollectionId(action);
        for(Long id:checkCollectionIdList){
            collectionMap.put(id,"");
        }

        return collectionList.stream().map(identifyCollectionInfoPageDTO -> {
            IdentifyCheckedCollectionInfoDTO identifyCheckedCollectionInfoDTO =  BeanCopyUtils.copyByJSON(identifyCollectionInfoPageDTO, IdentifyCheckedCollectionInfoDTO.class);
            identifyCheckedCollectionInfoDTO.setChecked(collectionMap.containsKey(identifyCollectionInfoPageDTO.getCollectionId()));
            return identifyCheckedCollectionInfoDTO;
        }).collect(Collectors.toList());
    }

    private ScsIdentifyCertificateInfoDO inMuseumCertificateInfoCheck(Long companyId, Long identifyId) {
        ScsIdentifyCertificateInfoDO scsIdentifyCertificateInfoDO =
                this.scsIdentifyCertificateInfoMapper.selectByPrimaryKey(identifyId);
        if (Objects.isNull(scsIdentifyCertificateInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "鉴定凭证信息不存在");
        }
        CheckStatusByModelIdAction checkStatusByModelIdAction = new CheckStatusByModelIdAction();
        checkStatusByModelIdAction.setBelongModel(CollectionBizTypeEnum.IDY);
        checkStatusByModelIdAction.setCompanyId(companyId);
        checkStatusByModelIdAction.setModelId(identifyId);
        this.checkService.checkStatusByModelId(checkStatusByModelIdAction);
        return scsIdentifyCertificateInfoDO;
    }

    private IdentifyCertificateEntity convertIdentifyCertificateEntity(AddIdentifyAction addIdentifyAction) {
        IdentifyCertificateEntity identifyCertificateEntity = new IdentifyCertificateEntity();
        identifyCertificateEntity.setIdentifyCertificateNo(addIdentifyAction.getPrefixName()
                + CollectionConstant.IDENTIFY_CERTIFICATE_MIDDLE);
        identifyCertificateEntity.setPrefixName(addIdentifyAction.getPrefixName());
        identifyCertificateEntity.setSuffixNumber(addIdentifyAction.getSuffixNumber());
        identifyCertificateEntity.setCompanyId(addIdentifyAction.getCompanyId());
        identifyCertificateEntity.setCreator(addIdentifyAction.getCreator());
        identifyCertificateEntity.setModifier(addIdentifyAction.getCreator());
        identifyCertificateEntity.setAutoAudit(addIdentifyAction.getAutoAudit());

        List<AddIdentifyCollectionAction> identifyCollectionList =
                addIdentifyAction.getIdentifyCollectionList();
        if (CollectionUtils.isEmpty(identifyCollectionList)) {
            return identifyCertificateEntity;
        }
        List<CollectionVO> identifyCollectionVOList =
                identifyCollectionList.stream().map(collection -> {
                    CollectionVO collectionVO = new CollectionVO();
                    // 过滤手动新增藏品时前端默认给的藏品id
                    if(collection.getCollectionId() != 0){
                        collectionVO.setCollectionId(collection.getCollectionId());
                    }
                    collectionVO.setCompanyId(addIdentifyAction.getCompanyId());
                    collectionVO.setName(collection.getName());
                    collectionVO.setActualCount(collection.getActualCount());
                    collectionVO.setCountUnit(collection.getCountUnit());
                    collectionVO.setCollectionCount(collection.getCollectionCount());
                    collectionVO.setEra(collection.getEra());
                    collectionVO.setAge(collection.getAge());
                    collectionVO.setTextureCategory(collection.getTextureCategory());
                    collectionVO.setTexture(collection.getTexture());
                    collectionVO.setSize(collection.getSize());
                    collectionVO.setSizeUnit(collection.getSizeUnit());
                    collectionVO.setCompleteDegree(collection.getCompleteDegree());
                    collectionVO.setCompleteInfo(collection.getCompleteInfo());
                    collectionVO.setInitialLevel(collection.getInitialLevel());
                    collectionVO.setAmount(collection.getAmount());
                    collectionVO.setCollectType(collection.getCollectType());
                    collectionVO.setCollectDate(collection.getCollectDate());
                    collectionVO.setHolder(collection.getHolder());
                    collectionVO.setPayVoucherNo(collection.getPayVoucherNo());
                    collectionVO.setRemark(collection.getRemark());
                    collectionVO.setAppendageDesc(collection.getAppendageDesc());
                    collectionVO.setSourceInfo(collection.getSourceInfo());
                    collectionVO.setIntroduce(collection.getIntroduce());
                    collectionVO.setInputMode(collection.getInputMode().getCode());
                    collectionVO.setInputType(collection.getInputType().getCode());
                    collectionVO.setDocumentId(collection.getDocumentId());
                    collectionVO.setCreator(addIdentifyAction.getCreator());
                    collectionVO.setModifier(addIdentifyAction.getCreator());
                    collectionVO.setAppraiserId(collection.getAppraiserId());
                    collectionVO.setAppraiserName(collection.getAppraiserName());
                    collectionVO.setIdentifyAgency(collection.getIdentifyAgency());
                    collectionVO.setIdentifyOpinion(collection.getIdentifyOpinion());
                    collectionVO.setIdentifyRemark(collection.getIdentifyOpinion());
                    collectionVO.setIdentifyDate(collection.getIdentifyDate());
                    collectionVO.setIdentifyDocumentId(collection.getIdentifyDocumentId());

                    return collectionVO;
                }).collect(Collectors.toList());
        identifyCertificateEntity.setIdentifyCollectionList(identifyCollectionVOList);

        return identifyCertificateEntity;
    }

    private IdentifyCertificateEntity convertIdentifyCertificateEntity(EditIdentifyAction editIdentifyAction) {
        IdentifyCertificateEntity identifyCertificateEntity = new IdentifyCertificateEntity();
        identifyCertificateEntity.setIdentifyId(editIdentifyAction.getIdentifyId());
        identifyCertificateEntity.setIdentifyCertificateNo(editIdentifyAction.getPrefixName()
                + CollectionConstant.IDENTIFY_CERTIFICATE_MIDDLE);
        identifyCertificateEntity.setPrefixName(editIdentifyAction.getPrefixName());
        identifyCertificateEntity.setSuffixNumber(editIdentifyAction.getSuffixNumber());
        identifyCertificateEntity.setCompanyId(editIdentifyAction.getCompanyId());
        identifyCertificateEntity.setCreator(editIdentifyAction.getCreator());
        identifyCertificateEntity.setModifier(editIdentifyAction.getCreator());
        identifyCertificateEntity.setAutoAudit(editIdentifyAction.getAutoAudit());

        List<EditIdentifyCollectionAction> identifyCollectionList =
                editIdentifyAction.getIdentifyCollectionList();
        if (CollectionUtils.isEmpty(identifyCollectionList)) {
            return identifyCertificateEntity;
        }
        List<CollectionVO> identifyCollectionVOList =
                identifyCollectionList.stream().map(collection -> {
                    CollectionVO collectionVO = new CollectionVO();
                    collectionVO.setCollectionId(collection.getCollectionId());
                    collectionVO.setCompanyId(editIdentifyAction.getCompanyId());
                    collectionVO.setName(collection.getName());
                    collectionVO.setActualCount(collection.getActualCount());
                    collectionVO.setCountUnit(collection.getCountUnit());
                    collectionVO.setCollectionCount(collection.getCollectionCount());
                    collectionVO.setEra(collection.getEra());
                    collectionVO.setAge(collection.getAge());
                    collectionVO.setTextureCategory(collection.getTextureCategory());
                    collectionVO.setTexture(collection.getTexture());
                    collectionVO.setSize(collection.getSize());
                    collectionVO.setSizeUnit(collection.getSizeUnit());
                    collectionVO.setCompleteDegree(collection.getCompleteDegree());
                    collectionVO.setCompleteInfo(collection.getCompleteInfo());
                    collectionVO.setInitialLevel(collection.getInitialLevel());
                    collectionVO.setAmount(collection.getAmount());
                    collectionVO.setCollectType(collection.getCollectType());
                    collectionVO.setCollectDate(collection.getCollectDate());
                    collectionVO.setHolder(collection.getHolder());
                    collectionVO.setPayVoucherNo(collection.getPayVoucherNo());
                    collectionVO.setRemark(collection.getRemark());
                    collectionVO.setAppendageDesc(collection.getAppendageDesc());
                    collectionVO.setSourceInfo(collection.getSourceInfo());
                    collectionVO.setIntroduce(collection.getIntroduce());
                    collectionVO.setInputMode(collection.getInputMode().getCode());
                    collectionVO.setInputType(collection.getInputType().getCode());
                    collectionVO.setDocumentId(collection.getDocumentId());
                    collectionVO.setCreator(editIdentifyAction.getCreator());
                    collectionVO.setModifier(editIdentifyAction.getCreator());
                    collectionVO.setAppraiserId(collection.getAppraiserId());
                    collectionVO.setAppraiserName(collection.getAppraiserName());
                    collectionVO.setIdentifyAgency(collection.getIdentifyAgency());
                    collectionVO.setIdentifyOpinion(collection.getIdentifyOpinion());
                    collectionVO.setIdentifyRemark(collection.getIdentifyOpinion());
                    collectionVO.setIdentifyDate(collection.getIdentifyDate());
                    collectionVO.setIdentifyDocumentId(collection.getIdentifyDocumentId());

                    return collectionVO;
                }).collect(Collectors.toList());
        identifyCertificateEntity.setIdentifyCollectionList(identifyCollectionVOList);

        return identifyCertificateEntity;
    }

    private List<IdentifyInfoPageDTO> setCollectAttributes(Long companyId, List<ScsIdentifyCertificateInfoDO> identifyDOList, String checkStatus, Integer isUserChecked) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        if (CollectionUtils.isEmpty(identifyDOList)) {
            return Lists.newArrayList();
        }
        List<Long> modeIds = identifyDOList.stream().map(ScsIdentifyCertificateInfoDO::getId).collect(Collectors.toList());
        QueryBatchCheckStatusAction queryBatchCheckStatusAction = new QueryBatchCheckStatusAction();
        queryBatchCheckStatusAction.setCompanyId(companyId);
        queryBatchCheckStatusAction.setBelongModel(CollectionBizTypeEnum.IDY);
        queryBatchCheckStatusAction.setModelIds(modeIds);
        Map<Long,CheckCurrentInfoDTO> checkStatusMap = this.checkService.queryBatchCheckStatus(queryBatchCheckStatusAction);
        List<Long> userIds = identifyDOList.stream()
                .map(ScsIdentifyCertificateInfoDO::getCreator).distinct().collect(Collectors.toList());
        Map<Long, String> userNameMap = this.userService.queryUserNameMap(companyId, userIds);
        List<Long> collectIds = identifyDOList.stream()
                .map(ScsIdentifyCertificateInfoDO::getId).collect(Collectors.toList());
        Map<Long, List<BizCollectionRelDTO>> bizCollectionRelMap = this.collectionService
                .queryBizCollectionRelMap(companyId, collectIds, CollectionBizTypeEnum.IDY);
        List<ScsIdentifyInfoDO> identifyInfoDOList = this.scsIdentifyInfoMapper.selectIdentifyInfoList(companyId);
        Map<Long,List<ScsIdentifyInfoDO>> identifyInfoMap = identifyInfoDOList.stream().collect(Collectors.groupingBy(ScsIdentifyInfoDO::getIdentifyCertificateId));
        return identifyDOList.stream().map(collectDO -> {
            IdentifyInfoPageDTO identifyInfoPageDTO = BeanCopyUtils.copyByJSON(collectDO, IdentifyInfoPageDTO.class);
            identifyInfoPageDTO.setIdentifyId(collectDO.getId());
            CheckCurrentInfoDTO checkCurrentInfoDTO = checkStatusMap.get(collectDO.getId());
            if(!Objects.isNull(checkCurrentInfoDTO)){
                identifyInfoPageDTO.setCheckStatus(checkCurrentInfoDTO.getCheckStatus().getCode());
                identifyInfoPageDTO.setCheckStatusName(checkCurrentInfoDTO.getCheckStatus().getDesc());
                identifyInfoPageDTO.setNeedManName(checkCurrentInfoDTO.getNeedManName());
                identifyInfoPageDTO.setHasChecked(YesOrNoEnum.否.getCode().byteValue());
                if(Objects.nonNull(checkCurrentInfoDTO.getNeedManId()) && checkCurrentInfoDTO.getNeedManId().contains(userBO.getUserId())){
                    identifyInfoPageDTO.setHasChecked(YesOrNoEnum.是.getCode().byteValue());
                }
            }
            if (userNameMap != null && !userNameMap.isEmpty()) {
                identifyInfoPageDTO.setCreator(collectDO.getCreator());
                identifyInfoPageDTO.setCreatorName(userNameMap.get(collectDO.getCreator()));
            }
            if(!Objects.isNull(collectDO.getGmtCreate())){
                identifyInfoPageDTO.setCreateTime(DateUtils.formatDateYYYMMDDHHmmss(collectDO.getGmtCreate()));
            }
            if (bizCollectionRelMap != null && !bizCollectionRelMap.isEmpty()) {
                List<BizCollectionRelDTO> collectionRelDTOList = bizCollectionRelMap.get(collectDO.getId());
                if (CollectionUtils.isNotEmpty(collectionRelDTOList)) {
                    identifyInfoPageDTO.setCollectionNum(collectionRelDTOList.size());
                } else {
                    identifyInfoPageDTO.setCollectionNum(0);
                }
            }
            if (identifyInfoMap != null && !identifyInfoMap.isEmpty()) {
                List<ScsIdentifyInfoDO> identifyList = identifyInfoMap.get(collectDO.getId());
                if (CollectionUtils.isNotEmpty(identifyList)) {
                    identifyInfoPageDTO.setIdentifyNum(identifyList.size());
                } else {
                    identifyInfoPageDTO.setIdentifyNum(0);
                }
            }
            if (StringUtils.isNotBlank(checkStatus)) {
                if (checkStatus.equals(identifyInfoPageDTO.getCheckStatus())) {
                    return identifyInfoPageDTO;
                } else {
                    return null;
                }
            }
            if (isUserChecked == 1) {
                if (identifyInfoPageDTO.getHasChecked() == YesOrNoEnum.否.getCode().byteValue()) {
                    return null;
                }
            }
            return identifyInfoPageDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private IdentifyCollectionEntity convertIdentifyCollectionEntity(IdentifyCollectionAction identifyCollectionAction){
        IdentifyCollectionEntity identifyCollectionEntity = new IdentifyCollectionEntity();
        identifyCollectionEntity.setCompanyId(identifyCollectionAction.getCompanyId());
        identifyCollectionEntity.setIdentifyCertificateId(identifyCollectionAction.getIdentifyCertificateId());
        identifyCollectionEntity.setCollectionId(identifyCollectionAction.getCollectionId());
        identifyCollectionEntity.setAppraiserId(identifyCollectionAction.getAppraiserId());
        identifyCollectionEntity.setAppraiserName(identifyCollectionAction.getAppraiserName());
        identifyCollectionEntity.setIdentifyOpinion(identifyCollectionAction.getIdentifyOpinion());
        identifyCollectionEntity.setIdentifyAgency(identifyCollectionAction.getIdentifyAgency());
        identifyCollectionEntity.setIdentifyRemark(identifyCollectionAction.getIdentifyRemark());
        identifyCollectionEntity.setIdentifyDate(identifyCollectionAction.getIdentifyDate());
        identifyCollectionEntity.setDocumentId(identifyCollectionAction.getDocumentId());
        identifyCollectionEntity.setCreator(identifyCollectionAction.getCreator());
        identifyCollectionEntity.setModifier(identifyCollectionAction.getModifier());
        return identifyCollectionEntity;
    }
}
