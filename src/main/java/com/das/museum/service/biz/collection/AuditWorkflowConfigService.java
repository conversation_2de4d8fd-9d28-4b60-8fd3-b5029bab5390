package com.das.museum.service.biz.collection;

import com.das.museum.domain.enums.AuditWorkflowBizTypeEnum;
import com.das.museum.service.biz.collection.action.AddAuditWorkflowConfigAction;
import com.das.museum.service.biz.collection.action.EditAuditWorkflowConfigAction;
import com.das.museum.service.biz.collection.dto.AuditWorkflowConfigDTO;
import com.das.museum.web.controller.collection.process.response.GetWorkflowConfigByBizIdVO;

import java.util.List;

public interface AuditWorkflowConfigService {
    Long addAuditWorkflowConfig(AddAuditWorkflowConfigAction action);
    void editAuditWorkflowConfig(EditAuditWorkflowConfigAction action);
    void delAuditWorkflowConfig(Long companyId, Long auditWorkflowConfigId);
    AuditWorkflowConfigDTO queryByAuditWorkflowConfigId(Long companyId, Long auditWorkflowConfigId);
    AuditWorkflowConfigDTO queryByBizIdAndFlowName(Long companyId, Long bizId, String flowName);
    Boolean duplicateFlowNameCheck(Long companyId, Long bizId, String flowName);
    List<AuditWorkflowConfigDTO> queryByBizId(Long companyId, Long bizId);
    List<AuditWorkflowConfigDTO> queryByClassificationBizCode(Long companyId, AuditWorkflowBizTypeEnum auditEnum);

    GetWorkflowConfigByBizIdVO getWorkflowConfigByBizId(Long companyId, Long bizId);

    void editWorkflowConfig(Long companyId, Long userId, Long configId, Byte autoRemove);
}
