package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddInTibetanAction;
import com.das.museum.service.biz.collection.action.AssignWarehouseAction;
import com.das.museum.service.biz.collection.action.EditInTibetanAction;
import com.das.museum.service.biz.collection.action.InTibetanConditionQueryAction;
import com.das.museum.service.biz.collection.dto.CollectCheckedCollectionInfoDTO;
import com.das.museum.service.biz.collection.dto.InTibetanInfoDetailDTO;
import com.das.museum.service.biz.collection.dto.InTibetanInfoPageDTO;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;

import java.util.List;

public interface InTibetanService {
    Long addInTibetanCertificate(AddInTibetanAction action);
    void editInTibetanCertificate(Long companyId, Long inTibetanId, EditInTibetanAction action);
    void delByInTibetanCertificateById(Long companyId, Long inTibetanId);
    InTibetanInfoDetailDTO queryDetailInfoById(Long companyId, Long inTibetanId);
    SimplePageInfo<InTibetanInfoPageDTO> queryPageByConditionQuery(InTibetanConditionQueryAction action);
    List<CommonDocumentDTO> queryAttachmentByInTibetanId(Long companyId, Long inTibetanId);
    void assignWarehouseByCollectionIds(AssignWarehouseAction action);
    List<CollectCheckedCollectionInfoDTO> queryCheckedCollectionInfoList(Long companyId, Long inTibetanId);
}
