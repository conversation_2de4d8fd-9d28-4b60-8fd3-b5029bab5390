package com.das.museum.service.biz.collection;

import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.dataobjectexpand.QueryTwoImagePageDO;
import com.das.museum.infr.mapper.AccUserMapper;
import com.das.museum.infr.mapper.CommDocumentMapper;
import com.das.museum.infr.mapper.ScsCollectionRegisterInfoMapper;
import com.das.museum.infr.mapper.ScsCollectionTwoImageMapper;
import com.das.museum.service.biz.collection.action.EditTwoImageInfoAction;
import com.das.museum.service.biz.collection.action.QueryTwoImagePageAction;
import com.das.museum.service.biz.collection.dto.CollectionUpdateRecordPageDTO;
import com.das.museum.service.biz.collection.dto.QueryTwoImageInfoDTO;
import com.das.museum.service.biz.collection.dto.QueryTwoImagePageListDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class CollectionTwoImageServiceImpl implements CollectionTwoImageService{

    @Resource
    private ScsCollectionTwoImageMapper scsCollectionTwoImageMapper;

    @Resource
    private ScsCollectionRegisterInfoMapper scsCollectionRegisterInfoMapper;

    @Resource
    private AccUserMapper accUserMapper;

    @Resource
    private DocumentService documentService;

    @Resource
    private CommDocumentMapper commDocumentMapper;

    @Override
    public SimplePageInfo<QueryTwoImagePageListDTO> queryTwoImagePageList(QueryTwoImagePageAction action){

        Long companyId = action.getCompanyId();
        action.startPage();
        List<QueryTwoImagePageDO> twoImageList = this.scsCollectionTwoImageMapper
                .listPageByCondition(companyId, action.getRegisterInfoId(),action.getKeyword(),action.getCreateStartTime(),action.getCreateEndTime(),action.getUpdateStartTime(),action.getUpdateEndTime());
        if (CollectionUtils.isEmpty(twoImageList)) {
            return new SimplePageInfo<>();
        }

        List<Long> creatorList = twoImageList.stream().map(QueryTwoImagePageDO::getCreator).collect(Collectors.toList());
        List<Long> modifierList = twoImageList.stream().map(QueryTwoImagePageDO::getModifier).collect(Collectors.toList());
        List<Long> userIds = Lists.newArrayList();
        userIds.addAll(creatorList);
        userIds.addAll(modifierList);
        List<AccUserDO> accUserDOList = accUserMapper.listByIds(companyId,userIds);
        Map<Long,AccUserDO> accUserDOMap = new HashMap<>();
        accUserDOList.forEach(s -> accUserDOMap.put(s.getId(),s));

        SimplePageInfo<QueryTwoImagePageListDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(twoImageList, QueryTwoImagePageListDTO.class);
        List<QueryTwoImagePageListDTO> resultList = twoImageList.stream().map(queryTwoImagePageDO -> {
            QueryTwoImagePageListDTO dto = new QueryTwoImagePageListDTO();
            BeanUtils.copyProperties(queryTwoImagePageDO,dto);
            if(accUserDOMap.containsKey(queryTwoImagePageDO.getCreator())){
                dto.setCreatorName(accUserDOMap.get(queryTwoImagePageDO.getCreator()).getRealName());
            }

            if(accUserDOMap.containsKey(queryTwoImagePageDO.getModifier())){
                dto.setModifierName(accUserDOMap.get(queryTwoImagePageDO.getModifier()).getRealName());
            }

            if(Objects.nonNull(queryTwoImagePageDO.getGmtCreate())){
                dto.setGmtCreate(DateUtils.formatDateYYYMMDDHHmmss(queryTwoImagePageDO.getGmtCreate()));
            }
            if(Objects.nonNull(queryTwoImagePageDO.getGmtModified())){
                dto.setGmtModified(DateUtils.formatDateYYYMMDDHHmmss(queryTwoImagePageDO.getGmtModified()));
            }
            return dto;
            }
        ).collect(Collectors.toList());
        pageInfo.setList(resultList);
        return pageInfo;

    }

    @Override
    public QueryTwoImageInfoDTO queryTwoImagePageList(Long companyId, Long twoImageId){
        QueryTwoImageInfoDTO dto = new QueryTwoImageInfoDTO();
        ScsCollectionTwoImageDO scsCollectionTwoImageDO = scsCollectionTwoImageMapper.selectByCompanyIdAndId(companyId,twoImageId);
        if(Objects.isNull(scsCollectionTwoImageDO)){
            return dto;
        }

        BeanUtils.copyProperties(scsCollectionTwoImageDO,dto);
        dto.setTwoImageId(scsCollectionTwoImageDO.getId());
        if(scsCollectionTwoImageDO.getMakeTime() != null){
            dto.setMakeTime(DateUtils.formatDateYYYMMDD(scsCollectionTwoImageDO.getMakeTime()));
        }

        Long documentId = scsCollectionTwoImageDO.getDocumentId();
        if(documentId != null){
            CommonDocumentDTO commonDocumentDTO = documentService.queryByDocId(companyId,documentId);
            if(Objects.nonNull(commonDocumentDTO)){
                dto.setDocumentName(commonDocumentDTO.getSourceFileName());
                dto.setUrl(commonDocumentDTO.getUrl());
            }
        }
        return dto;
    }


    @Override
    @Transactional
    public void editTwoImageInfo(EditTwoImageInfoAction action){
        ScsCollectionTwoImageDO scsCollectionTwoImageDO = scsCollectionTwoImageMapper.selectByCompanyIdAndId(action.getCompanyId(),action.getTwoImageId());
        if(Objects.isNull(scsCollectionTwoImageDO)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "二维影像信息不存在！");
        }
        BeanUtils.copyProperties(action,scsCollectionTwoImageDO);
        if(StringUtils.isNotBlank(action.getMakeTime())){
            scsCollectionTwoImageDO.setMakeTime(DateUtils.parseDateYYYMMDD(action.getMakeTime()));
        }
        scsCollectionTwoImageMapper.updateByCompanyIdAndId(scsCollectionTwoImageDO);

        CommDocumentDO documentDO = commDocumentMapper.selectById(action.getCompanyId(),scsCollectionTwoImageDO.getDocumentId());
        if(Objects.nonNull(documentDO)){
            documentDO.setSourceFileName(action.getDocumentName());
            commDocumentMapper.updateByCompanyIdAndId(documentDO);
        }
    }

    @Override
    @Transactional
    public void delTwoImageInfo(Long companyId,Long twoImageId){
        ScsCollectionTwoImageDO scsCollectionTwoImageDO = scsCollectionTwoImageMapper.selectByCompanyIdAndId(companyId,twoImageId);
        if(Objects.isNull(scsCollectionTwoImageDO)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "二维影像信息不存在！");
        }

        scsCollectionTwoImageMapper.deleteByCompanyIdAndId(companyId,twoImageId);

        if(scsCollectionTwoImageDO.getDocumentId() != null){
            documentService.delByDocId(companyId,scsCollectionTwoImageDO.getDocumentId());
        }

        ScsCollectionRegisterInfoDO scsCollectionRegisterInfoDO = scsCollectionRegisterInfoMapper.selectById(companyId,scsCollectionTwoImageDO.getRegisterInfoId());
        if(scsCollectionTwoImageDO.getDocumentId().toString().equals(scsCollectionRegisterInfoDO.getDocumentId()) ){
            scsCollectionRegisterInfoDO.setDocumentId(null);
            scsCollectionRegisterInfoMapper.updateByCompanyIdAndId(scsCollectionRegisterInfoDO);
        }


    }
}
