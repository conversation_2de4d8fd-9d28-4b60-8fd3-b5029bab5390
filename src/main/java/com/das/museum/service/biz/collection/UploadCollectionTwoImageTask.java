package com.das.museum.service.biz.collection;

import cn.hutool.core.img.Img;
import com.alibaba.fastjson.JSONObject;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.MinIoUtils;
import com.das.museum.common.utils.UUIDUtils;
import com.das.museum.infr.dataobject.CommDocumentExtendDO;
import com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO;
import com.das.museum.infr.dataobject.ScsCollectionTwoImageDO;
import com.das.museum.infr.dataobject.ShjyActivityManageDOWithBLOBs;
import com.das.museum.infr.mapper.CommDocumentExtendMapper;
import com.das.museum.infr.mapper.ScsCollectionRegisterInfoMapper;
import com.das.museum.infr.mapper.ScsCollectionTwoImageMapper;
import com.das.museum.infr.mapper.ShjyActivityManageMapper;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.action.AddDocumentAction;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.service.biz.education.UploadProcessFileTask;
import com.das.museum.service.enums.FileTypeEnum;
import com.das.museum.service.enums.SpecialProcessEnum;
import com.sun.image.codec.jpeg.JPEGCodec;
import com.sun.image.codec.jpeg.JPEGImageDecoder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.MDC;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class UploadCollectionTwoImageTask implements Runnable{

    private static final String TRACE_ID = "TraceId";

    private static final String TWO_IMAGE = "twoImage";

    private MultipartFile[] files;

    private DocumentService documentService;

    private RedisTemplate<String, JSONObject> redisTemplate;

    private ScsCollectionTwoImageMapper scsCollectionTwoImageMapper;

    private ScsCollectionRegisterInfoMapper scsCollectionRegisterInfoMapper;

    private CommDocumentExtendMapper commDocumentExtendMapper;

    private Long registerInfoId;

    private Long companyId;

    private Long userId;

    private String bucketName;

    UploadCollectionTwoImageTask(){

    }

    public UploadCollectionTwoImageTask(MultipartFile[] files,
                                 DocumentService documentService,
                                 RedisTemplate<String, JSONObject> redisTemplate,
                                 Long registerInfoId,
                                 Long companyId,
                                 Long userId,
                                 String bucketName,
                                 ScsCollectionTwoImageMapper scsCollectionTwoImageMapper,
                                 ScsCollectionRegisterInfoMapper scsCollectionRegisterInfoMapper,
                                 CommDocumentExtendMapper commDocumentExtendMapper){
        this.files = files;
        this.documentService = documentService;
        this.redisTemplate = redisTemplate;
        this.registerInfoId = registerInfoId;
        this.companyId = companyId;
        this.userId = userId;
        this.bucketName = bucketName;
        this.scsCollectionTwoImageMapper = scsCollectionTwoImageMapper;
        this.scsCollectionRegisterInfoMapper = scsCollectionRegisterInfoMapper;
        this.commDocumentExtendMapper = commDocumentExtendMapper;
    }

    @Override
    public void run() {
        log.info("UploadCollectionTwoImageTask task started");
        try {
            String requestId = UUIDUtils.generateUUID(16);
            MDC.put(TRACE_ID, requestId);
            handle();
        } catch (Throwable e) {
            log.error("UploadCollectionTwoImageTask Error task", e);
        } finally {
            MDC.remove(TRACE_ID);
        }
    }

    private void handle() {
        UploadCollectionTwoImageTask.UploadResult uploadResult = new UploadCollectionTwoImageTask.UploadResult();
        List<String> docIds = Lists.newArrayList();
        List<String> successList = Lists.newArrayList();
        List<String> failedList = Lists.newArrayList();
        for(MultipartFile file : files){
            String fileName = file.getOriginalFilename();
            Long documentId = uploadFile(file);
            if(documentId > 0){
                docIds.add(documentId.toString());
                successList.add(fileName);
            }else{
                failedList.add(fileName);
            }
        }
        uploadResult.setSuccessNum(successList.size());
        uploadResult.setFailedNum(failedList.size());
        uploadResult.setSuccessAll(Strings.join(successList, ','));
        uploadResult.setFailedAll(Strings.join(failedList,','));
        redisTemplate.opsForValue().set(TWO_IMAGE +"_"+ companyId+"_"+registerInfoId+"_"+userId, (JSONObject) JSONObject.toJSON(uploadResult), 7, TimeUnit.DAYS);
        docIds.forEach(s -> {
            ScsCollectionTwoImageDO scsCollectionTwoImageDO = new ScsCollectionTwoImageDO();
            scsCollectionTwoImageDO.setCompanyId(companyId);
            scsCollectionTwoImageDO.setDocumentId(Long.parseLong(s));
            scsCollectionTwoImageDO.setRegisterInfoId(registerInfoId);
            scsCollectionTwoImageDO.setCreator(userId);
            scsCollectionTwoImageDO.setModifier(userId);
            scsCollectionTwoImageMapper.insertSelective(scsCollectionTwoImageDO);
        });

        // 如果有上传成功的图片，且藏品不存在封面图片
        if(CollectionUtils.isNotEmpty(docIds)){
            ScsCollectionRegisterInfoDO registerInfoDO = scsCollectionRegisterInfoMapper.selectById(companyId,registerInfoId);
            if(Objects.nonNull(registerInfoDO) && registerInfoDO.getDocumentId() == null){
                registerInfoDO.setDocumentId(docIds.get(0));
                scsCollectionRegisterInfoMapper.updateByCompanyIdAndId(registerInfoDO);
            }
        }
    }

    private Long uploadFile(MultipartFile file){
        CommonDocumentDTO commonDocumentDTO;
        try {
            String fileName = file.getOriginalFilename();
            if (StringUtils.isBlank(fileName)) {
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST);
            }
            String fileFormat = fileName.substring(fileName.lastIndexOf('.') + 1);
            String saveFileName = RandomStringUtils.randomAlphanumeric(32) + "_" + fileName;
            String baseUrlStr = DateUtils.formatDateYYYMMDD() + MinIoUtils.SEPARATOR +
                    companyId + MinIoUtils.SEPARATOR + saveFileName;
            Long fileSize = file.getSize();
            String url = MinIoUtils.getVisitUrl() + baseUrlStr;
            MinIoUtils.putObject(bucketName, file, baseUrlStr, file.getContentType());
            AddDocumentAction action = new AddDocumentAction();
            action.setCompanyId(companyId);
            action.setCreator(userId);
            action.setSourceFileName(fileName);
            action.setSaveFileName(saveFileName);
            action.setFileFormat(fileFormat);
            action.setFileType("");
            action.setFileSize(fileSize);
            action.setBucket(bucketName);
            action.setPath(baseUrlStr);
            action.setUrl(url);
            commonDocumentDTO = this.documentService.addDocument(action);

            // 处理压缩文件
            ByteArrayOutputStream outputStream = null;
            InputStream fileInputStream = null;
            ByteArrayOutputStream outputStreamTarget = null;
            try {
                fileInputStream = file.getInputStream();
                JPEGImageDecoder decoder = JPEGCodec.createJPEGDecoder(fileInputStream);
                BufferedImage image = decoder.decodeAsBufferedImage();
                outputStreamTarget = new ByteArrayOutputStream();
                Img.from(image)
                        .setQuality(0.5)
                        .setTargetImageType("jpg")
                        .setBackgroundColor(Color.white)
                        .write(outputStreamTarget);
                String compressionFileName = 0.5 + "_" + saveFileName.substring(0, saveFileName.lastIndexOf(".") + 1) + "jpg";
                String compressionBaseUrlStr = DateUtils.formatDateYYYMMDD() + MinIoUtils.SEPARATOR +
                        companyId + MinIoUtils.SEPARATOR + compressionFileName;
                MinIoUtils.putObject(bucketName, compressionBaseUrlStr, new ByteArrayInputStream(outputStreamTarget.toByteArray()), file.getContentType());
                CommDocumentExtendDO documentExtendDO = new CommDocumentExtendDO();
                documentExtendDO.setCompanyId(companyId);
                documentExtendDO.setCreator(userId);
                documentExtendDO.setModifier(userId);
                documentExtendDO.setProcessType(SpecialProcessEnum.压缩.getCode().byteValue());
                documentExtendDO.setSourceDocId(commonDocumentDTO.getDocumentId());
                documentExtendDO.setSaveFileName(compressionFileName);
                documentExtendDO.setFileFormat(fileFormat);
                documentExtendDO.setFileSize((long) outputStreamTarget.size());
                documentExtendDO.setBucket(bucketName);
                documentExtendDO.setPath(compressionBaseUrlStr);
                String compressionUrl = MinIoUtils.getVisitUrl() + compressionBaseUrlStr;
                documentExtendDO.setUrl(compressionUrl);
                commDocumentExtendMapper.insertSelective(documentExtendDO);
            } catch (Exception e) {
                log.error("uploadFile compression File Exception:", e);
                throw new CommonException(CommonErrorCodeEnum.UPLOAD_FILE_FAIL, "压缩失败");
            } finally {
                if (outputStreamTarget != null) {
                    try {
                        outputStreamTarget.close();
                    } catch (IOException e) {
                        log.error("uploadFile compression File outputStreamTarget close IOException:", e);
                    }
                }
                if (outputStream != null) {
                    try {
                        outputStream.close();
                    } catch (IOException e) {
                        log.error("uploadFile compression File outputStream close IOException:", e);
                    }
                }
                if (fileInputStream != null) {
                    try {
                        fileInputStream.close();
                    } catch (IOException e) {
                        log.error("uploadFile compression File fileInputStream close IOException:", e);
                    }
                }
            }
        } catch (Throwable e) {
            log.error("UploadRecordTask task", e);
            return -1L;
        }
        return commonDocumentDTO.getDocumentId();
    }


    @Data
    public static class UploadResult{
        /**
         * 上传成功个数
         */
        private int successNum;

        /**
         * 上传成功文件名称，多个用英文逗号分隔
         */
        private String successAll;

        /**
         * 上传失败个数
         */
        private int failedNum;

        /**
         * 上传失败文件名称，多个用英文逗号分隔
         */
        private String failedAll;
    }
}
