package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EditCollectionBookAction implements Serializable {
    private static final long serialVersionUID = -18110279322957637L;
    private Long collectionBookId;
    private Long collectionId;
    private String author;
    private Date recordDate;
    private Long bookLevel;
    private String pressName;
    private String article;
    private String content;
    private String remark;
    private String documentId;
    private Long companyId;
    private Long modifier;
}
