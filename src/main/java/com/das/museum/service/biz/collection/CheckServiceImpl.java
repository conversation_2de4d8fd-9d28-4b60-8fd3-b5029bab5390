package com.das.museum.service.biz.collection;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.domain.collection.repository.CollectionRepository;
import com.das.museum.domain.enums.*;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.mapper.*;
import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.*;
import com.das.museum.web.controller.collection.check.response.BatchToCancelApplyDTO;
import com.das.museum.web.controller.collection.check.response.BatchToSubmitDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class CheckServiceImpl implements CheckService{

    @Resource
    private AuditWorkflowConfigService auditWorkflowConfigService;

    @Resource
    private CollectionRegisterService collectionRegisterService;

    @Resource
    private LogoffCollectionService logoffCollectionService;

    @Resource
    private AccUserMapper accUserMapper;

    @Resource
    private CollectionRepository collectionRepository;

    @Resource
    private CommCheckMainMapper commCheckMainMapper;

    @Resource
    private CommCheckChildMapper commCheckChildMapper;

    @Resource
    private CommCheckLogMapper commCheckLogMapper;

    @Resource
    private ScsIdentifyInfoMapper scsIdentifyInfoMapper;

    @Resource
    private CommWorkflowConfigMapper commWorkflowConfigMapper;

    @Resource
    private ScsBizCollectionRelMapper scsBizCollectionRelMapper;

    private static Map<String,AuditWorkflowBizTypeEnum> EnumMap = new HashMap<>();


    static {
        EnumMap.put(CollectionBizTypeEnum.CLT.getCode(),AuditWorkflowBizTypeEnum.ZJSH);
        EnumMap.put(CollectionBizTypeEnum.IDY.getCode(),AuditWorkflowBizTypeEnum.JDSH);
        EnumMap.put(CollectionBizTypeEnum.MSM.getCode(),AuditWorkflowBizTypeEnum.RGSH);
        EnumMap.put(CollectionBizTypeEnum.TBT.getCode(),AuditWorkflowBizTypeEnum.RCSH);
        EnumMap.put(CollectionBizTypeEnum.OUTIN.getCode(),AuditWorkflowBizTypeEnum.CRKSH);
        EnumMap.put(CollectionBizTypeEnum.LOGIN.getCode(),AuditWorkflowBizTypeEnum.DLSH);
        EnumMap.put(CollectionBizTypeEnum.LOGOUT.getCode(),AuditWorkflowBizTypeEnum.ZXSH);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitCheck(SubmitCheckAction submitCheckAction){
        AuditWorkflowBizTypeEnum auditEnum = EnumMap.get(submitCheckAction.getBelongModel().getCode());
        if(Objects.isNull(auditEnum)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "模板映射不存在！");
        }
        List<AuditWorkflowConfigDTO> auditWorkflowConfigDTOList = auditWorkflowConfigService.queryByClassificationBizCode(submitCheckAction.getCompanyId(),auditEnum);
        if(CollectionUtils.isEmpty(auditWorkflowConfigDTOList)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "请前往流程管理配置审核流程！");
        }

        Date date = new Date();
        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByModelAndId(submitCheckAction.getCompanyId(),submitCheckAction.getBelongModel().getCode(),submitCheckAction.getModelId());
        if(Objects.nonNull(commCheckMainDO)){
            if(!CheckMainStatusEnum.REVE.getCode().equals(commCheckMainDO.getCheckStatus())
                    && !CheckMainStatusEnum.NATP.getCode().equals(commCheckMainDO.getCheckStatus())){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "当前审核状态为"+CheckMainStatusEnum.fromCode(commCheckMainDO.getCheckStatus()).getDesc()+",不能重新提交审核");
            }
            commCheckMainDO.setCheckStatus(CheckMainStatusEnum.ATD.getCode());
            commCheckMainDO.setStartManId(submitCheckAction.getCreator());
            commCheckMainDO.setStartManName(submitCheckAction.getCreatorName());
            commCheckMainDO.setStartTime(date);
            commCheckMainDO.setCreator(submitCheckAction.getCreator());
            this.commCheckMainMapper.updateByPrimaryKeySelective(commCheckMainDO);
        }else{
            commCheckMainDO = new CommCheckMainDO();
            commCheckMainDO.setCompanyId(submitCheckAction.getCompanyId());
            commCheckMainDO.setBelongModel(submitCheckAction.getBelongModel().getCode());
            commCheckMainDO.setModelId(submitCheckAction.getModelId());
            commCheckMainDO.setCheckStatus(CheckMainStatusEnum.ATD.getCode());
            commCheckMainDO.setStartManId(submitCheckAction.getCreator());
            commCheckMainDO.setStartManName(submitCheckAction.getCreatorName());
            commCheckMainDO.setStartTime(date);
            commCheckMainDO.setCreator(submitCheckAction.getCreator());
            commCheckMainDO.setModifier(submitCheckAction.getCreator());
            this.commCheckMainMapper.insertSelective(commCheckMainDO);

            if (CollectionBizTypeEnum.LOGIN.getCode().equals(submitCheckAction.getBelongModel().getCode())) {
                CommCheckLogDO commCheckLogDO = new CommCheckLogDO();
                commCheckLogDO.setBelongModel(submitCheckAction.getBelongModel().getCode());
                commCheckLogDO.setModelId(submitCheckAction.getModelId());
                commCheckLogDO.setMainId(commCheckMainDO.getId());
                this.commCheckLogMapper.updateByModelId(commCheckLogDO);
            }
        }

        Long mainId = commCheckMainDO.getId();
        //删除该流程的子流程
        this.commCheckChildMapper.deleteByMainId(mainId);

        //判断是否需要自动去重
        Long bizId = auditWorkflowConfigDTOList.get(0).getBizId();
        CommWorkflowConfigDO commWorkflowConfigDO = commWorkflowConfigMapper.selectByCompanyIdAndBizId(submitCheckAction.getCompanyId(),bizId);
        byte autoRemove = commWorkflowConfigDO.getAutoRemove();

        //记录跳过审核的节点
        AuditWorkflowConfigDTO autoWorkflowConfigDTO = null;

        // 如果是征集、鉴定、入馆、入藏，记录本次需审核的藏品id
        List<ScsBizCollectionRelDO> collectionList = scsBizCollectionRelMapper.listByBizIdAndBizType(submitCheckAction.getModelId(),submitCheckAction.getBelongModel().getCode());
        List<String> collectionIdList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(collectionList)){
            for(ScsBizCollectionRelDO relDO : collectionList){
                if(relDO.getCollectionId() != null) {
                    collectionIdList.add(relDO.getCollectionId().toString());
                }
            }
        }
        String collectionIds = String.join(",",collectionIdList);

        for(AuditWorkflowConfigDTO auditWorkflowConfigDTO : auditWorkflowConfigDTOList){
            //记录日志
            CommCheckLogDO commCheckLogDO = new CommCheckLogDO();
            commCheckLogDO.setBelongModel(submitCheckAction.getBelongModel().getCode());
            commCheckLogDO.setModelId(submitCheckAction.getModelId());
            commCheckLogDO.setMainId(commCheckMainDO.getId());

            // 待审核人列表
            List<String> reviewerList = Arrays.asList(auditWorkflowConfigDTO.getReviewer().split(","));
            CommCheckChildDO commCheckChildDO = new CommCheckChildDO();

            //判断第一个节点审核人
            commCheckChildDO.setMainId(mainId);
            if(auditWorkflowConfigDTO.getSortIndex()!=null) {
                commCheckChildDO.setNodeIndex(auditWorkflowConfigDTO.getSortIndex().intValue());
            }
            commCheckChildDO.setEndNode(auditWorkflowConfigDTO.getNextNodeId() == null ? 1:0);

            //第一个节点
            if(auditWorkflowConfigDTO.getPrevNodeId() == null) {
                commCheckChildDO.setArriveTime(date);
                commCheckChildDO.setCheckStatus(CheckStatusChildEnum.ATD.getCode());
                if(autoRemove == 1 && reviewerList.contains(submitCheckAction.getCreator().toString())){
                    commCheckChildDO.setCheckStatus(CheckStatusChildEnum.ATP.getCode());
                    commCheckChildDO.setCheckManId(submitCheckAction.getCreator());
                    commCheckChildDO.setCheckManName(submitCheckAction.getCreatorName());
                    commCheckChildDO.setCheckTime(new Date());
                    commCheckChildDO.setCollectionId(collectionIds);
                    //记录此节点
                    autoWorkflowConfigDTO = auditWorkflowConfigDTO;

                    // 如果是最后一个节点,修改主流程状态为已通过
                    if(auditWorkflowConfigDTO.getNextNodeId() == null){
                        commCheckMainDO.setCheckStatus(CheckMainStatusEnum.ATP.getCode());
                        this.commCheckMainMapper.updateByPrimaryKeySelective(commCheckMainDO);
                        updateCollectionRegisterStatus(submitCheckAction.getCompanyId(),submitCheckAction.getBelongModel(),submitCheckAction.getModelId());
                        collectionRepository.updateCollectionStatus(submitCheckAction.getCompanyId(),submitCheckAction.getModelId(),submitCheckAction.getBelongModel().getCode());
                    }

                    commCheckLogDO.setCheckStatus(CheckStatusChildEnum.ATP.getCode());
                    commCheckLogDO.setCheckOpinion("自动通过");
                    commCheckLogDO.setCheckManId(submitCheckAction.getCreator());
                    commCheckLogDO.setCheckManName(submitCheckAction.getCreatorName());
                    commCheckLogDO.setCreator(submitCheckAction.getCreator());
                    commCheckLogDO.setModifier(submitCheckAction.getCreator());
                    commCheckLogDO.setCheckTime(new Date());
                    this.commCheckLogMapper.insertSelective(commCheckLogDO);
                }
            }else{
                if(autoWorkflowConfigDTO == null){
                    commCheckChildDO.setCheckStatus(CheckStatusChildEnum.NAT.getCode());
                }else{
                    if(reviewerList.contains(submitCheckAction.getCreator().toString())){
                        commCheckChildDO.setCheckStatus(CheckStatusChildEnum.ATP.getCode());
                        commCheckChildDO.setCheckManId(submitCheckAction.getCreator());
                        commCheckChildDO.setCheckManName(submitCheckAction.getCreatorName());
                        commCheckChildDO.setCheckTime(new Date());
                        commCheckChildDO.setCollectionId(collectionIds);

                        // 如果是最后一个节点,修改主流程状态为已通过
                        if(auditWorkflowConfigDTO.getNextNodeId() == null){
                            commCheckMainDO.setCheckStatus(CheckMainStatusEnum.ATP.getCode());
                            this.commCheckMainMapper.updateByPrimaryKeySelective(commCheckMainDO);
                            updateCollectionRegisterStatus(submitCheckAction.getCompanyId(),submitCheckAction.getBelongModel(),submitCheckAction.getModelId());
                            collectionRepository.updateCollectionStatus(submitCheckAction.getCompanyId(),submitCheckAction.getModelId(),submitCheckAction.getBelongModel().getCode());
                        }

                        commCheckLogDO.setCheckStatus(CheckStatusChildEnum.ATP.getCode());
                        commCheckLogDO.setCheckOpinion("自动通过");
                        commCheckLogDO.setCheckManId(submitCheckAction.getCreator());
                        commCheckLogDO.setCheckManName(submitCheckAction.getCreatorName());
                        commCheckLogDO.setCreator(submitCheckAction.getCreator());
                        commCheckLogDO.setModifier(submitCheckAction.getCreator());
                        commCheckLogDO.setCheckTime(new Date());
                        this.commCheckLogMapper.insertSelective(commCheckLogDO);
                    }else{
                        commCheckChildDO.setCheckStatus(CheckStatusChildEnum.ATD.getCode());
                        autoWorkflowConfigDTO = null;
                    }
                }
            }
            commCheckChildDO.setFlowName(auditWorkflowConfigDTO.getFlowName());
            commCheckChildDO.setNeedManId(auditWorkflowConfigDTO.getReviewer());
            commCheckChildDO.setNeedManName(auditWorkflowConfigDTO.getReviewerName());
            commCheckChildDO.setCreator(submitCheckAction.getCreator());
            commCheckChildDO.setModifier(submitCheckAction.getCreator());
            this.commCheckChildMapper.insertSelective(commCheckChildDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void check(CheckAction checkAction){
        if(!CheckStatusChildEnum.ATP.getCode().equals(checkAction.getCheckStatus()) && !CheckStatusChildEnum.NATP.getCode().equals(checkAction.getCheckStatus())){
            throw new CommonException(CommonErrorCodeEnum.PARAM_ERROR, "审核状态错误！");
        }
        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByModelAndId(checkAction.getCompanyId(),checkAction.getBelongModel().getCode(),checkAction.getModelId());
        if(Objects.isNull(commCheckMainDO)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "审核流程不存在！");
        }

        if(!CheckMainStatusEnum.ATD.getCode().equals(commCheckMainDO.getCheckStatus())){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "当前状态为"+CheckMainStatusEnum.fromCode(commCheckMainDO.getCheckStatus()).getDesc()+"，不可进行审核！");
        }

        CommCheckChildDO currentNodeDO = this.commCheckChildMapper.selectByMainIdAndStatus(commCheckMainDO.getId(),CheckStatusChildEnum.ATD.getCode());
        if(Objects.isNull(currentNodeDO)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "审核流程不存在！");
        }

        Date date = new Date();
        currentNodeDO.setCheckStatus(checkAction.getCheckStatus());
        currentNodeDO.setCheckManId(checkAction.getCreator());
        currentNodeDO.setCheckManName(checkAction.getCreatorName());
        currentNodeDO.setCheckTime(date);
        currentNodeDO.setCheckOpinion(checkAction.getCheckOpinion());
        currentNodeDO.setCollectionId(checkAction.getCollectionId());
        currentNodeDO.setModifier(checkAction.getCreator());
        this.commCheckChildMapper.updateByPrimaryKeySelective(currentNodeDO);
        if(CheckStatusChildEnum.ATP.getCode().equals(checkAction.getCheckStatus())) {
            if(currentNodeDO.getEndNode() == 0){
                CommCheckChildDO nextNodeDO = this.commCheckChildMapper.selectByMainIdAndNodeIndex(commCheckMainDO.getId(),currentNodeDO.getNodeIndex()+1);
                if(Objects.isNull(nextNodeDO)){
                    //此处防止模板中间节点被删除，导致nodeIndex断层问题
                    for(int i=2;i<1000;i++){
                        nextNodeDO = this.commCheckChildMapper.selectByMainIdAndNodeIndex(commCheckMainDO.getId(),currentNodeDO.getNodeIndex()+i);
                        if(Objects.nonNull(nextNodeDO)){
                            break;
                        }
                    }
                }
                nextNodeDO.setCheckStatus(CheckStatusChildEnum.ATD.getCode());
                nextNodeDO.setArriveTime(date);
                this.commCheckChildMapper.updateByPrimaryKeySelective(nextNodeDO);

                //判断是不是配置了自动去重，是不是满足自动去重条件
                AuditWorkflowBizTypeEnum auditEnum = EnumMap.get(checkAction.getBelongModel().getCode());
                if(Objects.isNull(auditEnum)){
                    throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "模板映射不存在！");
                }
                List<AuditWorkflowConfigDTO> auditWorkflowConfigDTOList = auditWorkflowConfigService.queryByClassificationBizCode(checkAction.getCompanyId(),auditEnum);
                if(CollectionUtils.isEmpty(auditWorkflowConfigDTOList)){
                    throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "审核配置模板不存在！");
                }
                Long bizId = auditWorkflowConfigDTOList.get(0).getBizId();
                CommWorkflowConfigDO commWorkflowConfigDO = commWorkflowConfigMapper.selectByCompanyIdAndBizId(checkAction.getCompanyId(),bizId);
                byte autoRemove = commWorkflowConfigDO.getAutoRemove();
                // 开启自动去重
                if(autoRemove == 1){
                    //当前待审核人
                    List<String> needManIds = Arrays.asList(nextNodeDO.getNeedManId().split(","));
                    // 获取本流程已审核过的节点的
                    List<CommCheckChildDO> commCheckChildDOList = commCheckChildMapper.selectListByMainIdAndStatus(commCheckMainDO.getId(),CheckStatusChildEnum.ATP.getCode());

                    //标记是否自动去重 0否1是
                    byte removeFlag = 0;
                    // 自动审核人id(默认值无实际意义)
                    Long autoUserId = 1L;

                    for(CommCheckChildDO commCheckChildDO : commCheckChildDOList){
                        if(nextNodeDO.getNeedManId().equals(commCheckChildDO.getNeedManId()) || needManIds.contains(commCheckChildDO.getCheckManId().toString())){
                            removeFlag = 1;
                            autoUserId = commCheckChildDO.getCheckManId();
                            break;
                        }
                    }

                    //判断待审核人是不是发起人，待审核人是不是已审核过此流程
                    if(needManIds.contains(commCheckMainDO.getStartManId().toString())){
                        CheckAction action = new CheckAction();
                        action.setModelId(commCheckMainDO.getModelId());
                        action.setBelongModel(CollectionBizTypeEnum.fromCode(commCheckMainDO.getBelongModel()));
                        action.setCompanyId(commCheckMainDO.getCompanyId());
                        action.setCreator(commCheckMainDO.getStartManId());
                        action.setCreatorName(commCheckMainDO.getStartManName());
                        action.setCheckStatus(CheckStatusChildEnum.ATP.getCode());
                        action.setCheckOpinion("");
                        action.setCollectionId(checkAction.getCollectionId());
                        action.setAutoRemove((byte)1);

                        // 回调审核接口
                        this.check(action);
                    }else if(removeFlag == 1){
                        CheckAction action = new CheckAction();
                        action.setModelId(commCheckMainDO.getModelId());
                        action.setBelongModel(CollectionBizTypeEnum.fromCode(commCheckMainDO.getBelongModel()));
                        action.setCompanyId(commCheckMainDO.getCompanyId());
                        action.setCreator(autoUserId);
                        //根据UserId获取用户名称
                        AccUserDO userDO = this.accUserMapper.selectByUserId(checkAction.getCompanyId(), autoUserId);
                        if(Objects.nonNull(userDO)){
                            action.setCreatorName(userDO.getUserName());
                        }
                        action.setCheckStatus(CheckStatusChildEnum.ATP.getCode());
                        action.setCheckOpinion("");
                        action.setCollectionId(checkAction.getCollectionId());
                        action.setAutoRemove((byte)1);

                        // 回调审核接口
                        this.check(action);
                    }
                }
            }else{
                commCheckMainDO.setCheckStatus(CheckMainStatusEnum.ATP.getCode());
                this.commCheckMainMapper.updateByPrimaryKeySelective(commCheckMainDO);
                updateCollectionRegisterStatus(checkAction.getCompanyId(),checkAction.getBelongModel(),checkAction.getModelId());
                collectionRepository.updateCollectionStatus(checkAction.getCompanyId(),checkAction.getModelId(),checkAction.getBelongModel().getCode());
            }
        }else{
            commCheckMainDO.setCheckStatus(CheckMainStatusEnum.NATP.getCode());
            this.commCheckMainMapper.updateByPrimaryKeySelective(commCheckMainDO);
        }

        CommCheckLogDO commCheckLogDO = new CommCheckLogDO();
        commCheckLogDO.setMainId(commCheckMainDO.getId());
        commCheckLogDO.setBelongModel(checkAction.getBelongModel().getCode());
        commCheckLogDO.setModelId(checkAction.getModelId());
        commCheckLogDO.setCheckStatus(checkAction.getCheckStatus());

        if(checkAction.getAutoRemove() == 1){
            commCheckLogDO.setCheckOpinion("自动通过");
        }else{
            commCheckLogDO.setCheckOpinion(checkAction.getCheckOpinion());
        }
        commCheckLogDO.setCheckManId(checkAction.getCreator());
        commCheckLogDO.setCheckManName(checkAction.getCreatorName());
        commCheckLogDO.setCreator(checkAction.getCreator());
        commCheckLogDO.setModifier(checkAction.getCreator());
        commCheckLogDO.setCheckTime(date);
        this.commCheckLogMapper.insertSelective(commCheckLogDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelApply(CancelApplyAction cancelApplyAction){
        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByModelAndId(cancelApplyAction.getCompanyId(),cancelApplyAction.getBelongModel().getCode(),cancelApplyAction.getModelId());
        if(Objects.isNull(commCheckMainDO)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "审核流程不存在！");
        }
        if(CheckMainStatusEnum.ATP.getCode().equals(commCheckMainDO.getCheckStatus())){
            throw new CommonException(CommonErrorCodeEnum.PARAM_ERROR, "审核通过的流程不能撤销！");
        }

        commCheckMainDO.setCheckStatus(CheckMainStatusEnum.REVE.getCode());
        this.commCheckMainMapper.updateByPrimaryKeySelective(commCheckMainDO);

        this.commCheckChildMapper.deleteByMainId(commCheckMainDO.getId());

        CommCheckLogDO commCheckLogDO = new CommCheckLogDO();
        commCheckLogDO.setMainId(commCheckMainDO.getId());
        commCheckLogDO.setBelongModel(cancelApplyAction.getBelongModel().getCode());
        commCheckLogDO.setModelId(cancelApplyAction.getModelId());
        commCheckLogDO.setCheckStatus(CheckMainStatusEnum.REVE.getCode());
        commCheckLogDO.setCheckManId(cancelApplyAction.getCreator());
        commCheckLogDO.setCheckManName(cancelApplyAction.getCreatorName());
        commCheckLogDO.setCheckTime(new Date());
        commCheckLogDO.setCheckOpinion("");
        commCheckLogDO.setCreator(cancelApplyAction.getCreator());
        commCheckLogDO.setModifier(cancelApplyAction.getCreator());
        this.commCheckLogMapper.insertSelective(commCheckLogDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFlow(DeleteFlowAction deleteFlowAction){
        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByModelAndId(deleteFlowAction.getCompanyId(),deleteFlowAction.getBelongModel().getCode(),deleteFlowAction.getModelId());
        if(Objects.isNull(commCheckMainDO)){
            return;
        }
        this.commCheckChildMapper.deleteByMainId(commCheckMainDO.getId());
        this.commCheckMainMapper.deleteByPrimaryKey(commCheckMainDO.getId());
        this.commCheckLogMapper.deleteByMainId(commCheckMainDO.getId());
    }

    @Override
    public List<CheckFlowDTO> queryFlowList(QueryCheckFlowAction queryCheckFlowAction){

        AuditWorkflowBizTypeEnum auditEnum = EnumMap.get(queryCheckFlowAction.getBelongModel().getCode());
        if(Objects.isNull(auditEnum)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "模板映射不存在！");
        }
        List<AuditWorkflowConfigDTO> auditWorkflowConfigDTOList = auditWorkflowConfigService.queryByClassificationBizCode(queryCheckFlowAction.getCompanyId(),auditEnum);
        if(CollectionUtils.isEmpty(auditWorkflowConfigDTOList)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "审核配置模板不存在！");
        }
        List<CheckFlowDTO> flowList = auditWorkflowConfigDTOList.stream().map(auditWorkflowConfigDTO -> {
            CheckFlowDTO checkFlowDTO = new CheckFlowDTO();
            checkFlowDTO.setFlowName(auditWorkflowConfigDTO.getFlowName());
            checkFlowDTO.setCheckStatus(CheckStatusChildEnum.NAT.getCode());
            checkFlowDTO.setCheckStatusName(CheckStatusChildEnum.NAT.getDesc());
            checkFlowDTO.setNodeIndex(auditWorkflowConfigDTO.getSortIndex().intValue());
            checkFlowDTO.setNeedManName(auditWorkflowConfigDTO.getReviewerName());
            return checkFlowDTO;
        }).collect(Collectors.toList());

        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByModelAndId(queryCheckFlowAction.getCompanyId(),queryCheckFlowAction.getBelongModel().getCode(),queryCheckFlowAction.getModelId());
        if(Objects.isNull(commCheckMainDO)){
            CheckFlowDTO dto = new CheckFlowDTO();
            dto.setNodeIndex(0);
            dto.setCheckStatus(CheckStatusChildEnum.NAT.getCode());
            dto.setCheckStatusName("待发起申请");
            flowList.add(dto);
            return flowList.stream().sorted(Comparator.comparing(CheckFlowDTO::getNodeIndex)).collect(Collectors.toList());
        }

        List<CommCheckChildDO> doList = this.commCheckChildMapper.selectCheckListByMainId(commCheckMainDO.getId());
        if(CollectionUtils.isEmpty(doList)){
            return flowList;
        }
        List<CheckFlowDTO> resultList = BeanCopyUtils.copyArrayByJSON(doList, CheckFlowDTO.class);
        resultList = resultList.stream().map(
                checkFlowDTO -> {
                    if(StringUtils.isNotBlank(checkFlowDTO.getCheckStatus())){
                        checkFlowDTO.setCheckStatusName(CheckStatusChildEnum.fromCode(checkFlowDTO.getCheckStatus()).getDesc());
                    }
                    return checkFlowDTO;
                }
        ).collect(Collectors.toList());
        CheckFlowDTO dto = new CheckFlowDTO();
        dto.setNodeIndex(0);
        dto.setCheckStatus(CheckStatusChildEnum.ATP.getCode());
        dto.setCheckStatusName("发起申请");
        dto.setNeedManName(commCheckMainDO.getStartManName());
        resultList.add(dto);
        return resultList.stream().sorted(Comparator.comparing(CheckFlowDTO::getNodeIndex)).collect(Collectors.toList());
    }

    @Override
    public List<CheckRecordDTO> queryRecordList(QueryCheckRecordAction queryCheckRecordAction){
        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByModelAndId(queryCheckRecordAction.getCompanyId(),queryCheckRecordAction.getBelongModel().getCode(),queryCheckRecordAction.getModelId());
        if(Objects.isNull(commCheckMainDO)){
            return Lists.newArrayList();
        }

        List<CommCheckLogDO> doList = this.commCheckLogMapper.selectLogListByMainId(commCheckMainDO.getId());
        if(CollectionUtils.isEmpty(doList)){
            return Lists.newArrayList();
        }

        List<CheckRecordDTO> resultList = doList.stream().map(commCheckLogDO -> {
            CheckRecordDTO checkRecordDTO = new CheckRecordDTO();
            checkRecordDTO.setCheckMan(commCheckLogDO.getCheckManName());
            checkRecordDTO.setCheckStatus(commCheckLogDO.getCheckStatus());
            if(StringUtils.isNotBlank(commCheckLogDO.getCheckStatus())){
                checkRecordDTO.setCheckStatusName(CheckStatusChildEnum.fromCode(commCheckLogDO.getCheckStatus()).getDesc());
            }
            if(!Objects.isNull(commCheckLogDO.getCheckTime())){
                checkRecordDTO.setCheckTime(DateUtils.formatDateYYYMMDDHHmmss(commCheckLogDO.getCheckTime()));
            }
            checkRecordDTO.setCheckOpinion(commCheckLogDO.getCheckOpinion());
            return checkRecordDTO;
        }).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public CheckMainStatusEnum queryCheckStatus(QueryCheckStatusAction queryCheckStatusAction){
        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByModelAndId(queryCheckStatusAction.getCompanyId(),queryCheckStatusAction.getBelongModel().getCode(),queryCheckStatusAction.getModelId());
        if(Objects.isNull(commCheckMainDO)){
            return CheckMainStatusEnum.USB;
        }
        return CheckMainStatusEnum.fromCode(commCheckMainDO.getCheckStatus());
    }

    @Override
    public void checkStatusByModelId(CheckStatusByModelIdAction action) {
        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByModelAndId(action.getCompanyId(),action.getBelongModel().getCode(),action.getModelId());
        if(Objects.isNull(commCheckMainDO)){
            return;
        }
        CheckMainStatusEnum checkMainStatusEnum = CheckMainStatusEnum.fromCode(commCheckMainDO.getCheckStatus());
        if (CheckMainStatusEnum.ATD.equals(checkMainStatusEnum) ||
                CheckMainStatusEnum.ATP.equals(checkMainStatusEnum)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "当前状态为[" + checkMainStatusEnum.getDesc() + "], 不可进行编辑!");
        }
    }

    @Override
    public Map<Long,CheckCurrentInfoDTO> queryBatchCheckStatus(QueryBatchCheckStatusAction queryBatchCheckStatusAction){
        Map<Long, CheckCurrentInfoDTO> checkStatusMap = new HashMap<>();
        if(CollectionUtils.isEmpty(queryBatchCheckStatusAction.getModelIds())){
            return null;
        }

        Map<Long, CommCheckMainDO> scsCheckMainDOMap = new HashMap<>();
        List<CommCheckMainDO> commCheckMainDOList = this.commCheckMainMapper.selectByModelAndIds(queryBatchCheckStatusAction.getCompanyId(),queryBatchCheckStatusAction.getBelongModel().getCode(),queryBatchCheckStatusAction.getModelIds());

        Map<Long, CommCheckChildDO> scsCheckChildDOMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(commCheckMainDOList)) {
            for(CommCheckMainDO commCheckMainDO : commCheckMainDOList){
                scsCheckMainDOMap.put(commCheckMainDO.getModelId(), commCheckMainDO);
            }
            List<Long> mainIds = commCheckMainDOList.stream().map(CommCheckMainDO::getId).collect(Collectors.toList());
            List<CommCheckChildDO> commCheckChildDOList = this.commCheckChildMapper.selectBatchByMainIdAndStatus(mainIds, CheckStatusChildEnum.ATD.getCode());
            for (CommCheckChildDO commCheckChildDO : commCheckChildDOList) {
                scsCheckChildDOMap.put(commCheckChildDO.getMainId(), commCheckChildDO);
            }
        }

        for(Long modelId : queryBatchCheckStatusAction.getModelIds()){
            CheckCurrentInfoDTO checkCurrentInfoDTO = new CheckCurrentInfoDTO();
            if(scsCheckMainDOMap.containsKey(modelId)){
                checkCurrentInfoDTO.setCheckStatus(CheckMainStatusEnum.fromCode(scsCheckMainDOMap.get(modelId).getCheckStatus()));
                if(scsCheckChildDOMap.containsKey(scsCheckMainDOMap.get(modelId).getId())){
                    String needManId = scsCheckChildDOMap.get(scsCheckMainDOMap.get(modelId).getId()).getNeedManId();
                    if(StringUtils.isNotBlank(needManId)){
                        List<Long> needManIds = Arrays.stream(needManId.split(",")).map(Long::parseLong).collect(Collectors.toList());
                        checkCurrentInfoDTO.setNeedManId(needManIds);
                        checkCurrentInfoDTO.setNeedManName(scsCheckChildDOMap.get(scsCheckMainDOMap.get(modelId).getId()).getNeedManName());
                    }
                }
            }else{
                checkCurrentInfoDTO.setCheckStatus(CheckMainStatusEnum.USB);
            }
            checkStatusMap.put(modelId,checkCurrentInfoDTO);
        }

        return checkStatusMap;
    }

    @Override
    public List<Long> queryCheckedCollectionId(QueryCheckedCollectionIdAction queryCheckedCollectionIdAction){
        List<Long> resultList = new ArrayList<>();
        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByModelAndId(queryCheckedCollectionIdAction.getCompanyId(),queryCheckedCollectionIdAction.getBelongModel().getCode(),queryCheckedCollectionIdAction.getModelId());
        if(Objects.isNull(commCheckMainDO)){
            return Lists.newArrayList();
        }

        if(CheckMainStatusEnum.REVE.getCode().equals(commCheckMainDO.getCheckStatus()) || CheckMainStatusEnum.NATP.getCode().equals(commCheckMainDO.getCheckStatus())){
            return Lists.newArrayList();
        }

        if(CheckMainStatusEnum.ATP.getCode().equals(commCheckMainDO.getCheckStatus())){
            CommCheckChildDO endNode = this.commCheckChildMapper.selectEndNodeByMainId(commCheckMainDO.getId());
            if (Objects.isNull(endNode)) {
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "审核流程最终节点不存在！");
            }
            if (StringUtils.isBlank(endNode.getCollectionId())) {
                return Lists.newArrayList();
            }
            return Stream.of(endNode.getCollectionId().split(",")).map(id -> {
                if (StringUtils.isBlank(id)) {
                    return null;
                }
                return Long.parseLong(id);
            }).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        }

        CommCheckChildDO currentNodeDO = this.commCheckChildMapper.selectByMainIdAndStatus(commCheckMainDO.getId(),CheckStatusChildEnum.ATD.getCode());
        if(Objects.isNull(currentNodeDO)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "审核流程不存在！");
        }

        if(currentNodeDO.getNodeIndex() == 1){
            return Lists.newArrayList();
        }else{
            CommCheckChildDO previousNodeDO = this.commCheckChildMapper.selectByMainIdAndNodeIndex(commCheckMainDO.getId(),currentNodeDO.getNodeIndex()-1);
            String collectionIds = previousNodeDO.getCollectionId();
            if(!Objects.isNull(collectionIds)){
                List<String> strList = Arrays.asList(collectionIds.split(","));
                resultList = strList.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            }
            return resultList;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchToSubmitDTO batchToSubmitCheck(BatchToSubmitCheckAction submitCheckAction) {
        AuditWorkflowBizTypeEnum auditEnum = EnumMap.get(submitCheckAction.getBelongModel().getCode());
        if(Objects.isNull(auditEnum)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "模板映射不存在！");
        }
        List<AuditWorkflowConfigDTO> auditWorkflowConfigDTOList = auditWorkflowConfigService.queryByClassificationBizCode(submitCheckAction.getCompanyId(),auditEnum);
        if(CollectionUtils.isEmpty(auditWorkflowConfigDTOList)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "审核配置模板不存在！");
        }

        BatchToSubmitDTO batchToSubmitDTO = new BatchToSubmitDTO();
        AtomicReference<Long> success = new AtomicReference<>(0L);
        AtomicReference<Long> fail = new AtomicReference<>(0L);

        List<Long> modelIds = new ArrayList<>();
        // 藏品鉴定需要特殊处理
        if(submitCheckAction.getBelongModel().equals(CollectionBizTypeEnum.IDY)){
            List<ScsIdentifyInfoDO> identifyInfoDOList = this.scsIdentifyInfoMapper.selectIdentifyInfoList(submitCheckAction.getCompanyId());
            Map<Long,List<ScsIdentifyInfoDO>> identifyInfoMap = identifyInfoDOList.stream().collect(Collectors.groupingBy(ScsIdentifyInfoDO::getIdentifyCertificateId));
            List<Long> finalModelIds = modelIds;
            submitCheckAction.getModelIds().forEach(id ->{
                if(identifyInfoMap.containsKey(id)){
                    finalModelIds.add(id);
                }else {
                    fail.getAndSet(fail.get() + 1L);
                }
            });
        }else {
            modelIds = submitCheckAction.getModelIds();
        }

        Date date = new Date();
        List<CommCheckMainDO> commCheckMainDOList = this.commCheckMainMapper.selectByModelAndIds(submitCheckAction.getCompanyId(), submitCheckAction.getBelongModel().getCode(),
                modelIds);
        if(CollectionUtils.isEmpty(commCheckMainDOList)){ // 如果全部都是未提交审核，则直接插入数据
            submitCheckAction.getModelIds().forEach(id ->{
                CommCheckMainDO commCheckMainDO = new CommCheckMainDO();
                commCheckMainDO.setCompanyId(submitCheckAction.getCompanyId());
                commCheckMainDO.setBelongModel(submitCheckAction.getBelongModel().getCode());
                commCheckMainDO.setModelId(id);
                commCheckMainDO.setCheckStatus(CheckMainStatusEnum.ATD.getCode());
                commCheckMainDO.setStartManId(submitCheckAction.getCreator());
                commCheckMainDO.setStartManName(submitCheckAction.getCreatorName());
                commCheckMainDO.setStartTime(date);
                commCheckMainDO.setCreator(submitCheckAction.getCreator());
                commCheckMainDO.setModifier(submitCheckAction.getCreator());
                this.commCheckMainMapper.insertSelective(commCheckMainDO);

                updateCheckChildMapper(commCheckMainDO.getId(), submitCheckAction.getCreator(), date, auditWorkflowConfigDTOList);
                if (CollectionBizTypeEnum.LOGIN.getCode().equals(submitCheckAction.getBelongModel().getCode())) {
                    CommCheckLogDO commCheckLogDO = new CommCheckLogDO();
                    commCheckLogDO.setBelongModel(submitCheckAction.getBelongModel().getCode());
                    commCheckLogDO.setModelId(id);
                    commCheckLogDO.setMainId(commCheckMainDO.getId());
                    this.commCheckLogMapper.updateByModelId(commCheckLogDO);
                }
                success.getAndSet(success.get() + 1L);
            });
        }else {
            Map<Long, CommCheckMainDO> scsDOMap = new HashMap<>();
            commCheckMainDOList.forEach(scsCheckMainDO -> {
                scsDOMap.put(scsCheckMainDO.getModelId(), scsCheckMainDO);
            });
            modelIds.forEach(id ->{
                CommCheckMainDO commCheckMainDO;
                if(Objects.nonNull(scsDOMap.get(id))){ // 数据库中已有的审核
                    commCheckMainDO = scsDOMap.get(id);
                    if(!CheckMainStatusEnum.ATP.getCode().equals(commCheckMainDO.getCheckStatus())
                            && !CheckMainStatusEnum.ATD.getCode().equals(commCheckMainDO.getCheckStatus())){ // 除“审核通过”和“审核中”的都可以重新提交审核
                        commCheckMainDO.setCheckStatus(CheckMainStatusEnum.ATD.getCode());
                        commCheckMainDO.setStartManId(submitCheckAction.getCreator());
                        commCheckMainDO.setStartManName(submitCheckAction.getCreatorName());
                        commCheckMainDO.setStartTime(date);
                        commCheckMainDO.setCreator(submitCheckAction.getCreator());
                        this.commCheckMainMapper.updateByPrimaryKeySelective(commCheckMainDO);

                        updateCheckChildMapper(commCheckMainDO.getId(), submitCheckAction.getCreator(), date, auditWorkflowConfigDTOList);

                        success.getAndSet(success.get() + 1L);
                    }else {
                        fail.getAndSet(fail.get() + 1L);
                    }
                }else { // 数据库中没有的审核直接提交
                    commCheckMainDO = new CommCheckMainDO();
                    commCheckMainDO.setCompanyId(submitCheckAction.getCompanyId());
                    commCheckMainDO.setBelongModel(submitCheckAction.getBelongModel().getCode());
                    commCheckMainDO.setModelId(id);
                    commCheckMainDO.setCheckStatus(CheckMainStatusEnum.ATD.getCode());
                    commCheckMainDO.setStartManId(submitCheckAction.getCreator());
                    commCheckMainDO.setStartManName(submitCheckAction.getCreatorName());
                    commCheckMainDO.setStartTime(date);
                    commCheckMainDO.setCreator(submitCheckAction.getCreator());
                    commCheckMainDO.setModifier(submitCheckAction.getCreator());
                    this.commCheckMainMapper.insertSelective(commCheckMainDO);

                    updateCheckChildMapper(commCheckMainDO.getId(), submitCheckAction.getCreator(), date, auditWorkflowConfigDTOList);
                    if (CollectionBizTypeEnum.LOGIN.getCode().equals(submitCheckAction.getBelongModel().getCode())) {
                        CommCheckLogDO commCheckLogDO = new CommCheckLogDO();
                        commCheckLogDO.setBelongModel(submitCheckAction.getBelongModel().getCode());
                        commCheckLogDO.setModelId(id);
                        commCheckLogDO.setMainId(commCheckMainDO.getId());
                        this.commCheckLogMapper.updateByModelId(commCheckLogDO);
                    }
                    success.getAndSet(success.get() + 1L);
                }
            });
        }
        return batchToSubmitDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchToCancelApplyDTO batchToCancelApply(BatchToCancelApplyAction cancelApplyAction) {
        List<CommCheckMainDO> commCheckMainDOList = this.commCheckMainMapper.selectByModelAndIds(cancelApplyAction.getCompanyId(),cancelApplyAction.getBelongModel().getCode(),cancelApplyAction.getModelIds());
        if(CollectionUtils.isEmpty(commCheckMainDOList)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "审核流程不存在！");
        }

        BatchToCancelApplyDTO batchToCancelApplyDTO = new BatchToCancelApplyDTO();
        AtomicReference<Long> success = new AtomicReference<>(0L);
        AtomicReference<Long> fail = new AtomicReference<>(0L);

        Date date = new Date();
        commCheckMainDOList.forEach(scsCheckMainDO -> {
            if(CheckMainStatusEnum.ATD.getCode().equals(scsCheckMainDO.getCheckStatus())){ // 只有审核中的进行操作
                scsCheckMainDO.setCheckStatus(CheckMainStatusEnum.REVE.getCode());
                this.commCheckMainMapper.updateByPrimaryKeySelective(scsCheckMainDO);

                this.commCheckChildMapper.deleteByMainId(scsCheckMainDO.getId());

                CommCheckLogDO commCheckLogDO = new CommCheckLogDO();
                commCheckLogDO.setMainId(scsCheckMainDO.getId());
                commCheckLogDO.setBelongModel(cancelApplyAction.getBelongModel().getCode());
                commCheckLogDO.setModelId(scsCheckMainDO.getModelId());
                commCheckLogDO.setCheckStatus(CheckMainStatusEnum.REVE.getCode());
                commCheckLogDO.setCheckManId(cancelApplyAction.getCreator());
                commCheckLogDO.setCheckManName(cancelApplyAction.getCreatorName());
                commCheckLogDO.setCheckTime(date);
                commCheckLogDO.setCheckOpinion("");
                commCheckLogDO.setCreator(cancelApplyAction.getCreator());
                commCheckLogDO.setModifier(cancelApplyAction.getCreator());
                this.commCheckLogMapper.insertSelective(commCheckLogDO);

                success.getAndSet(success.get() + 1L);
            }else {
                fail.getAndSet(fail.get() + 1L);
            }
        });

        // 加上未提审的部分
        fail.getAndSet(fail.get() + ((long)cancelApplyAction.getModelIds().size() - (long) commCheckMainDOList.size()));

        batchToCancelApplyDTO.setSuccessCount(success.get());
        batchToCancelApplyDTO.setFailCount(fail.get());

        return batchToCancelApplyDTO;
    }

    private void updateCollectionRegisterStatus(Long companyId,CollectionBizTypeEnum belongModel,Long modelId){
        List<Long> collectionIds;
        if(CollectionBizTypeEnum.LOGIN.equals(belongModel)){
            List<Long> ids = Lists.newArrayList();
            ids.add(modelId);
            this.collectionRegisterService.updateRegisterStatusById(companyId,ids, RegisterStatusEnum.ARTS);
        }else if(CollectionBizTypeEnum.OUTIN.equals(belongModel)){
            collectionIds = collectionRepository.queryByBizIdAndBizType(modelId,CollectionBizTypeEnum.OUTIN.getCode());
            this.collectionRegisterService.updateRegisterStatusByCollectionId(companyId,collectionIds,RegisterStatusEnum.POWH);
        }else if(CollectionBizTypeEnum.LOGOUT.equals(belongModel)){
            collectionIds = this.logoffCollectionService.queryLogoffCollectionIds(companyId,modelId);
            this.collectionRegisterService.updateRegisterStatusByCollectionId(companyId,collectionIds,RegisterStatusEnum.ALGF);
        }else{

        }
    }

    private void updateCheckChildMapper(Long mainId, Long userId, Date date, List<AuditWorkflowConfigDTO> auditWorkflowConfigDTOList){
        //删除该流程的子流程
        this.commCheckChildMapper.deleteByMainId(mainId);
        //生成新流程
        for(AuditWorkflowConfigDTO auditWorkflowConfigDTO : auditWorkflowConfigDTOList){
            CommCheckChildDO commCheckChildDO = new CommCheckChildDO();
            commCheckChildDO.setMainId(mainId);
            if(auditWorkflowConfigDTO.getSortIndex()!=null) {
                commCheckChildDO.setNodeIndex(auditWorkflowConfigDTO.getSortIndex().intValue());
            }
            commCheckChildDO.setEndNode(auditWorkflowConfigDTO.getNextNodeId() == null ? 1:0);

            if(auditWorkflowConfigDTO.getPrevNodeId() == null) {
                commCheckChildDO.setCheckStatus(CheckStatusChildEnum.ATD.getCode());
                commCheckChildDO.setArriveTime(date);
            }else{
                commCheckChildDO.setCheckStatus(CheckStatusChildEnum.NAT.getCode());
            }
            commCheckChildDO.setFlowName(auditWorkflowConfigDTO.getFlowName());
            commCheckChildDO.setNeedManId(auditWorkflowConfigDTO.getReviewer());
            commCheckChildDO.setNeedManName(auditWorkflowConfigDTO.getReviewerName());
            commCheckChildDO.setCreator(userId);
            commCheckChildDO.setModifier(userId);
            this.commCheckChildMapper.insertSelective(commCheckChildDO);
        }
    }

}
