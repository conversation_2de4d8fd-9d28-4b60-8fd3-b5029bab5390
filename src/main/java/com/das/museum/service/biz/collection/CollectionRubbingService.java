package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionRubbingAction;
import com.das.museum.service.biz.collection.action.EditCollectionRubbingAction;
import com.das.museum.service.biz.collection.action.RubbingQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionRubbingDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionRubbingPageDTO;
import com.das.museum.common.bo.SimplePageInfo;
public interface CollectionRubbingService {
    Long addCollectionRubbing(AddCollectionRubbingAction action);
    void editCollectionRubbing(EditCollectionRubbingAction action);
    void delByCollectionRubbingId(Long companyId, Long collectionRubbingId);
    SimplePageInfo<CollectionRubbingPageDTO> queryPageByCondition(RubbingQueryPageByConditionAction action);
    CollectionRubbingDetailDTO queryByCollectionRubbingId(Long companyId, Long collectionRubbingId);
}
