package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import com.das.museum.domain.enums.CollectionMediaTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class MediaQueryPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = -451736774156090472L;
    private Long companyId;
    private Long collectionId;
    private CollectionMediaTypeEnum mediaType;
    private String sortBy;
}
