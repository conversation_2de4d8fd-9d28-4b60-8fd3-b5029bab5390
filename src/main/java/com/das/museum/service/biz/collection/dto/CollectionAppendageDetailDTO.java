package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionAppendageDetailDTO implements Serializable {
    private static final long serialVersionUID = 6560217061984935802L;
    private Long collectionAppendageId;
    private Long companyId;
    private Long collectionId;
    private String name;
    private Integer actualCount;
    private Long meteringUnit;
    private String meteringUnitName;
    private String shape;
    private Long completeDegree;
    private String size;
    private String texture;
    private String weight;
    private String currentSituation;
    private String remark;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private List<CommonDocumentDTO> commDocumentList;
}
