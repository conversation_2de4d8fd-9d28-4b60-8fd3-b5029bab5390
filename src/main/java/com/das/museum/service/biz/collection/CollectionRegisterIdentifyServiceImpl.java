package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionRegisterIdentifyAction;
import com.das.museum.service.biz.collection.action.EditCollectionRegisterIdentifyAction;
import com.das.museum.service.biz.collection.action.RegisterIdentifyQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionRegisterIdentifyDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionRegisterIdentifyPageDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectionRegisterIdentifyEntity;
import com.das.museum.domain.collection.repository.CollectionRegisterIdentifyRepository;
import com.das.museum.infr.dataobject.CommClassificationDO;
import com.das.museum.infr.dataobject.ScsCollectionIdentifyDO;
import com.das.museum.infr.mapper.CommClassificationMapper;
import com.das.museum.infr.mapper.ScsCollectionIdentifyMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CollectionRegisterIdentifyServiceImpl implements CollectionRegisterIdentifyService {

    @Resource
    private CollectionRegisterIdentifyRepository collectionRegisterIdentifyRepository;

    @Resource
    private ScsCollectionIdentifyMapper scsCollectionIdentifyMapper;

    @Resource
    private DocumentService documentService;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCollectionRegisterIdentify(AddCollectionRegisterIdentifyAction action) {
        CollectionRegisterIdentifyEntity entity = this.convertCollectionRegisterIdentifyEntity(action);
        this.collectionRegisterIdentifyRepository.saveCollectionRegisterIdentify(entity);
        return entity.getRegisterIdentifyId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionRegisterIdentify(EditCollectionRegisterIdentifyAction action) {
        Long companyId = action.getCompanyId();
        Long registerIdentifyId = action.getRegisterIdentifyId();
        CollectionRegisterIdentifyDetailDTO collectionIdentifyDTO = this.queryByRegisterIdentifyId(companyId, registerIdentifyId);
        if (Objects.isNull(collectionIdentifyDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品鉴定信息不存在");
        }
        CollectionRegisterIdentifyEntity entity = this.convertCollectionRegisterIdentifyEntity(action);
        this.collectionRegisterIdentifyRepository.editCollectionRegisterIdentify(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByRegisterIdentifyId(Long companyId, Long registerIdentifyId) {
        CollectionRegisterIdentifyDetailDTO collectionIdentifyDTO = this.queryByRegisterIdentifyId(companyId, registerIdentifyId);
        if (Objects.isNull(collectionIdentifyDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品鉴定信息不存在");
        }
        this.collectionRegisterIdentifyRepository.delByRegisterIdentifyId(companyId, registerIdentifyId);
        if (StringUtils.isBlank(collectionIdentifyDTO.getDocumentId())) {
            return;
        }
        this.documentService.delByDocIds(companyId, collectionIdentifyDTO.getDocumentId());
    }

    @Override
    public SimplePageInfo<CollectionRegisterIdentifyPageDTO> queryPageByCondition(RegisterIdentifyQueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionIdentifyDO> collectionIdentifyDOList = this.scsCollectionIdentifyMapper.listPageByCondition(companyId,
                action.getCollectionId(), action.getSortBy());
        if (CollectionUtils.isEmpty(collectionIdentifyDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<CollectionRegisterIdentifyPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectionIdentifyDOList, CollectionRegisterIdentifyPageDTO.class);
        List<CollectionRegisterIdentifyPageDTO> identifyPageDTOList = collectionIdentifyDOList.stream().map(collectionIdentifyDO -> {
            CollectionRegisterIdentifyPageDTO identifyPageDTO = BeanCopyUtils.copyByJSON(collectionIdentifyDO, CollectionRegisterIdentifyPageDTO.class);
            identifyPageDTO.setRegisterIdentifyId(collectionIdentifyDO.getId());
            return identifyPageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(identifyPageDTOList);
        List<Long> identifyTypeIds = collectionIdentifyDOList.stream().map(ScsCollectionIdentifyDO::getIdentifyType).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(identifyTypeIds)) {
            return pageInfo;
        }
        List<CommClassificationDO> classificationDOList = this.commClassificationMapper.listByIds(companyId, identifyTypeIds);
        if (CollectionUtils.isEmpty(classificationDOList)) {
            return pageInfo;
        }
        Map<Long, String> classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                CommClassificationDO::getName, (key1, key2) -> key2));
        pageInfo.getList().forEach(collectionIdentifyPageDTO -> {
            Long identifyTypeId = collectionIdentifyPageDTO.getIdentifyType();
            if (Objects.nonNull(identifyTypeId) && classificationMap.containsKey(identifyTypeId)) {
                collectionIdentifyPageDTO.setIdentifyTypeName(classificationMap.get(identifyTypeId));
            }
        });
        return pageInfo;
    }

    @Override
    public CollectionRegisterIdentifyDetailDTO queryByRegisterIdentifyId(Long companyId, Long registerIdentifyId) {
        ScsCollectionIdentifyDO collectionIdentifyDO = this.scsCollectionIdentifyMapper.selectById(companyId, registerIdentifyId);
        if (Objects.isNull(collectionIdentifyDO)) {
            return null;
        }
        CollectionRegisterIdentifyDetailDTO collectionIdentifyDTO = BeanCopyUtils.copyByJSON(collectionIdentifyDO, CollectionRegisterIdentifyDetailDTO.class);
        collectionIdentifyDTO.setRegisterIdentifyId(collectionIdentifyDO.getId());
        String documentId = collectionIdentifyDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return collectionIdentifyDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        collectionIdentifyDTO.setCommDocumentList(commDocumentList);
        return collectionIdentifyDTO;
    }
    private CollectionRegisterIdentifyEntity convertCollectionRegisterIdentifyEntity(AddCollectionRegisterIdentifyAction action) {
        CollectionRegisterIdentifyEntity entity = new CollectionRegisterIdentifyEntity();
        entity.setCollectionId(action.getCollectionId());
        entity.setCompanyId(action.getCompanyId());
        entity.setIdentifyType(action.getIdentifyType());
        entity.setAppraiserId(action.getAppraiserId());
        entity.setAppraiserName(action.getAppraiserName());
        entity.setOrgAgency(action.getOrgAgency());
        entity.setIdentifyAgency(action.getIdentifyAgency());
        entity.setApproveAgency(action.getApproveAgency());
        entity.setIdentifyOpinion(action.getIdentifyOpinion());
        entity.setIdentifyRemark(action.getIdentifyRemark());
        entity.setIdentifyDate(action.getIdentifyDate());
        entity.setDocumentId(action.getDocumentId());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        return entity;
    }
    private CollectionRegisterIdentifyEntity convertCollectionRegisterIdentifyEntity(EditCollectionRegisterIdentifyAction action) {
        CollectionRegisterIdentifyEntity entity = new CollectionRegisterIdentifyEntity();
        entity.setRegisterIdentifyId(action.getRegisterIdentifyId());
        entity.setCollectionId(action.getCollectionId());
        entity.setIdentifyType(action.getIdentifyType());
        entity.setAppraiserId(action.getAppraiserId());
        entity.setAppraiserName(action.getAppraiserName());
        entity.setOrgAgency(action.getOrgAgency());
        entity.setIdentifyAgency(action.getIdentifyAgency());
        entity.setApproveAgency(action.getApproveAgency());
        entity.setIdentifyOpinion(action.getIdentifyOpinion());
        entity.setIdentifyRemark(action.getIdentifyRemark());
        entity.setIdentifyDate(action.getIdentifyDate());
        entity.setDocumentId(action.getDocumentId());
        entity.setModifier(action.getModifier());
        return entity;
    }
}
