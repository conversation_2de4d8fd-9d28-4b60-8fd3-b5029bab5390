package com.das.museum.service.biz.collection;

import com.das.museum.common.utils.DateUtils;
import com.das.museum.service.biz.collection.action.AddCollectionMountAction;
import com.das.museum.service.biz.collection.action.EditCollectionMountAction;
import com.das.museum.service.biz.collection.action.MountQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionMountDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionMountPageDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectionMountEntity;
import com.das.museum.domain.collection.repository.CollectionMountRepository;
import com.das.museum.infr.dataobject.ScsCollectionMountDO;
import com.das.museum.infr.mapper.ScsCollectionMountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
@Slf4j
@Service
public class CollectionMountServiceImpl implements CollectionMountService {

    @Resource
    private CollectionMountRepository collectionMountRepository;

    @Resource
    private ScsCollectionMountMapper scsCollectionMountMapper;

    @Resource
    private DocumentService documentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCollectionMount(AddCollectionMountAction action) {
        CollectionMountEntity entity = this.convertCollectionMountEntity(action);
        this.collectionMountRepository.saveCollectionMount(entity);
        return entity.getCollectionMountId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionMount(EditCollectionMountAction action) {
        Long companyId = action.getCompanyId();
        Long collectionMountId = action.getCollectionMountId();
        CollectionMountDetailDTO collectionMountDTO = this.queryByCollectionMountId(companyId, collectionMountId);
        if (Objects.isNull(collectionMountDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "装裱记录不存在");
        }
        CollectionMountEntity entity = this.convertCollectionMountEntity(action);
        this.collectionMountRepository.editCollectionMount(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByCollectionMountId(Long companyId, Long collectionMountId) {
        CollectionMountDetailDTO collectionMountDTO = this.queryByCollectionMountId(companyId, collectionMountId);
        if (Objects.isNull(collectionMountDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "装裱记录不存在");
        }
        this.collectionMountRepository.delByCollectionMountId(companyId, collectionMountId);
        if (StringUtils.isBlank(collectionMountDTO.getDocumentId())) {
            return;
        }
        this.documentService.delByDocIds(companyId, collectionMountDTO.getDocumentId());
    }

    @Override
    public SimplePageInfo<CollectionMountPageDTO> queryPageByCondition(MountQueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionMountDO> collectionMountDOList = this.scsCollectionMountMapper.listPageByCondition(companyId,
                action.getCollectionId(), action.getSortBy());
        if (CollectionUtils.isEmpty(collectionMountDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<CollectionMountPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectionMountDOList, CollectionMountPageDTO.class);
        List<CollectionMountPageDTO> collectionMountPageDTOList = collectionMountDOList.stream().map(collectionMountDO -> {
            CollectionMountPageDTO collectionDamagePageDTO = BeanCopyUtils.copyByJSON(collectionMountDO, CollectionMountPageDTO.class);
            collectionDamagePageDTO.setCollectionMountId(collectionMountDO.getId());
            if(collectionMountDO.getMountDate() != null){
                collectionDamagePageDTO.setMountDate(DateUtils.formatDateYYYMMDDHHmmss(collectionMountDO.getMountDate()));
            }
            if(collectionMountDO.getGmtCreate()!=null){
                collectionDamagePageDTO.setGmtCreate(DateUtils.formatDateYYYMMDDHHmmss(collectionMountDO.getGmtCreate()));
            }
            if(collectionMountDO.getGmtModified()!=null){
                collectionDamagePageDTO.setGmtModified(DateUtils.formatDateYYYMMDDHHmmss(collectionMountDO.getGmtModified()));
            }
            return collectionDamagePageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectionMountPageDTOList);
        return pageInfo;
    }

    @Override
    public CollectionMountDetailDTO queryByCollectionMountId(Long companyId, Long collectionMountId) {
        ScsCollectionMountDO collectionMountDO = this.scsCollectionMountMapper.selectById(companyId, collectionMountId);
        if (Objects.isNull(collectionMountDO)) {
            return null;
        }
        CollectionMountDetailDTO collectionMountDTO = BeanCopyUtils.copyByJSON(collectionMountDO, CollectionMountDetailDTO.class);
        collectionMountDTO.setCollectionMountId(collectionMountDO.getId());
        String documentId = collectionMountDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return collectionMountDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        collectionMountDTO.setCommDocumentList(commDocumentList);
        return collectionMountDTO;
    }
    private CollectionMountEntity convertCollectionMountEntity(AddCollectionMountAction action) {
        CollectionMountEntity entity = new CollectionMountEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setCollectionId(action.getCollectionId());
        entity.setContractUnit(action.getContractUnit());
        entity.setProducerId(action.getProducerId());
        entity.setProducerName(action.getProducerName());
        entity.setRemark(action.getRemark());
        entity.setMountDate(action.getMountDate());
        entity.setDocumentId(action.getDocumentId());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        return entity;
    }
    private CollectionMountEntity convertCollectionMountEntity(EditCollectionMountAction action) {
        CollectionMountEntity entity = new CollectionMountEntity();
        entity.setCollectionMountId(action.getCollectionMountId());
        entity.setCompanyId(action.getCompanyId());
        entity.setContractUnit(action.getContractUnit());
        entity.setProducerId(action.getProducerId());
        entity.setProducerName(action.getProducerName());
        entity.setRemark(action.getRemark());
        entity.setMountDate(action.getMountDate());
        entity.setDocumentId(action.getDocumentId());
        entity.setModifier(action.getModifier());
        return entity;
    }
}
