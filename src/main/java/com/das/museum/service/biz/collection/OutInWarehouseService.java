package com.das.museum.service.biz.collection;

import com.das.museum.domain.enums.RegisterStatusEnum;
import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.OutInWarehouseCollectionDTO;
import com.das.museum.service.biz.collection.dto.OutInWarehouseInfoDetailDTO;
import com.das.museum.service.biz.collection.dto.OutInWarehouseInfoPageDTO;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;

import java.util.List;

public interface OutInWarehouseService {
    Long addOutInWarehouseCertificate(AddOutInWarehouseAction action);
    void editOutInWarehouseCertificate(EditOutInWarehouseAction action);
    void delOutInWarehouseCertificateById(Long companyId, Long outInWarehouseId);
    OutInWarehouseInfoDetailDTO queryById(Long companyId, Long outInWarehouseId);
    SimplePageInfo<OutInWarehouseInfoPageDTO> queryPageByConditionQuery(QueryOutInPageByConditionAction action);
    SimplePageInfo<OutInWarehouseCollectionDTO> queryCollectionPageById(Long companyId, Long outInWarehouseId, String collectionName, String registerNo,
                                                                        List<RegisterStatusEnum> registerStatus, Integer pageNum, Integer pageSize);
    List<CommonDocumentDTO> queryAttachmentByOutInWarehouseId(Long companyId, Long outInWarehouseId);
    void collectionOutWarehouse(CollectionOutWarehouseAction action);
    void collectionInWarehouse(CollectionInWarehouseAction action);
}
