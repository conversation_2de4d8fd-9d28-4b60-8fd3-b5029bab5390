package com.das.museum.service.biz.collection;

import com.das.museum.common.bo.PageInfoBaseReq;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.PaginationUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectCertificateEntity;
import com.das.museum.domain.collection.repository.CollectCertificateRepository;
import com.das.museum.domain.collection.repository.CollectionRepository;
import com.das.museum.domain.collection.valueobject.CollectionVO;
import com.das.museum.domain.enums.CheckMainStatusEnum;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.mapper.*;
import com.das.museum.infr.query.CollectConditionQuery;
import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.*;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.constants.CollectionConstant;
import com.das.museum.service.biz.user.UserService;
import com.das.museum.service.enums.YesOrNoEnum;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class CollectCertificateServiceImpl implements CollectCertificateService {

    @Resource
    private CollectCertificateRepository collectCertificateRepository;

    @Resource
    private ScsCollectCertificateInfoMapper scsCollectCertificateInfoMapper;

    @Resource
    private CollectionRepository collectionRepository;

    @Resource
    private DocumentService documentService;

    @Resource
    private CollectionService collectionService;

    @Resource
    private UserService userService;

    @Resource
    private CheckService checkService;

    @Resource
    private CommCheckMainMapper commCheckMainMapper;

    @Resource
    private CommCheckChildMapper commCheckChildMapper;

    @Resource
    private ScsBizCollectionRelMapper scsBizCollectionRelMapper;

    @Resource
    private CommIncrSegmentMapper commIncrSegmentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCollectCertificateInfo(AddCollectAction addCollectAction) {
        String bizDimension = DateUtils.formatDateYYYMMDD().substring(0, 4);
        CommIncrSegmentDO commIncrSegmentDO = this.commIncrSegmentMapper
                .selectByCompanyIdAndBizCode(addCollectAction.getCompanyId(), CollectionBizTypeEnum.CLT.getCode(), bizDimension);
        if (Objects.isNull(commIncrSegmentDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "征集凭证自动编号配置不存在");
        }
        Long bizCurrIndex = commIncrSegmentDO.getBizCurrIndex();
        String collectCertificateNo = addCollectAction.getPrefixName()
                + CollectionConstant.COLLECT_CERTIFICATE_MIDDLE + (bizCurrIndex + 1)
                + CollectionConstant.CERTIFICATE_TAIL;
        CollectCertificateEntity collectCertificateEntity = this.convertCollectCertificateEntity(addCollectAction);
        collectCertificateEntity.setSuffixNumber(bizCurrIndex + 1);
        collectCertificateEntity.setCollectCertificateNo(collectCertificateNo);
        collectCertificateEntity.setAutoAudit(addCollectAction.getAutoAudit());
        this.collectCertificateRepository.saveCollectCertificate(collectCertificateEntity);
        Long collectId = collectCertificateEntity.getCollectId();
        this.collectionRepository.saveByBizIdAndBizType(addCollectAction.getCompanyId(), collectId,
                CollectionBizTypeEnum.CLT.getCode(), collectCertificateEntity.getCollectCollectionList());
        /*CollectCertificateEntity updateEntity = new CollectCertificateEntity();
        updateEntity.setCollectId(collectId);
        updateEntity.setSuffixNumber(collectId);
        updateEntity.setCollectCertificateNo(addCollectAction.getPrefixName()
                + CollectionConstant.COLLECT_CERTIFICATE_MIDDLE + collectId
                + CollectionConstant.CERTIFICATE_TAIL);
        updateEntity.setAutoAudit(addCollectAction.getAutoAudit());
        this.collectCertificateRepository.editCollectCertificate(updateEntity);*/

        int row = this.commIncrSegmentMapper.updateByBizCurrIndex(commIncrSegmentDO.getId(), bizCurrIndex);
        if (row <= 0) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "征集凭证自动获取编号失败");
        }
        // 自动提审
        if(addCollectAction.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(addCollectAction.getCreator());
            submitCheckAction.setCompanyId(addCollectAction.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.CLT);
            submitCheckAction.setModelId(collectId);
            submitCheckAction.setCreatorName(addCollectAction.getCreatorName());
            this.checkService.submitCheck(submitCheckAction);
        }
        return collectId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectCertificateInfo(Long companyId, Long creator, String creatorName, Long collectId, EditCollectAction editCollectAction) {
        ScsCollectCertificateInfoDO collectCertificateInfoDO = this.collectCertificateInfoCheck(companyId, collectId);
        CollectCertificateEntity entity = this.convertCollectCertificateEntity(editCollectAction);
        entity.setSuffixNumber(collectCertificateInfoDO.getSuffixNumber());
        entity.setCollectCertificateNo(editCollectAction.getPrefixName()
                + CollectionConstant.COLLECT_CERTIFICATE_MIDDLE + collectCertificateInfoDO.getSuffixNumber()
                + CollectionConstant.CERTIFICATE_TAIL);
        List<Long> excludeCollectionIds = null;
        if (CollectionUtils.isNotEmpty(entity.getCollectCollectionList())) {
            excludeCollectionIds = entity.getCollectCollectionList().stream()
                    .map(CollectionVO::getCollectionId).distinct().collect(Collectors.toList());
        }
        this.collectionRepository.delByBizIdAndBizTypeIncludeCLT(companyId, collectId,
                CollectionBizTypeEnum.CLT.getCode(), excludeCollectionIds);
        this.collectCertificateRepository.editCollectCertificate(entity);
        this.collectionRepository.saveByBizIdAndBizType(companyId, collectId, CollectionBizTypeEnum.CLT.getCode(), entity.getCollectCollectionList());

        // 自动提审
        if(editCollectAction.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(creator);
            submitCheckAction.setCompanyId(editCollectAction.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.CLT);
            submitCheckAction.setModelId(collectId);
            submitCheckAction.setCreatorName(creatorName);
            this.checkService.submitCheck(submitCheckAction);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByCollectCertificateInfoById(Long companyId, Long collectId) {
        ScsCollectCertificateInfoDO collectCertificateInfoDO = this.collectCertificateInfoCheck(companyId, collectId);
        this.collectCertificateRepository.delCollectCertificateById(companyId, collectId);
        this.collectionRepository.delByBizIdAndBizType(companyId, collectId, CollectionBizTypeEnum.CLT.getCode());
        this.documentService.delByDocIds(companyId, collectCertificateInfoDO.getDocumentId());
        DeleteFlowAction deleteFlowAction = new DeleteFlowAction();
        deleteFlowAction.setBelongModel(CollectionBizTypeEnum.CLT);
        deleteFlowAction.setCompanyId(companyId);
        deleteFlowAction.setModelId(collectId);
        this.checkService.deleteFlow(deleteFlowAction);
    }

    @Override
    public CollectInfoDetailDTO queryDetailInfoById(Long companyId, Long collectId) {
        ScsCollectCertificateInfoDO collectDO = this.scsCollectCertificateInfoMapper.selectByPrimaryKey(collectId);
        if (Objects.isNull(collectDO)) {
            return null;
        }
        CollectInfoDetailDTO collectInfoDetailDTO = BeanCopyUtils.copyByJSON(collectDO, CollectInfoDetailDTO.class);
        collectInfoDetailDTO.setCollectId(collectDO.getId());
        String documentId = collectDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return collectInfoDetailDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        collectInfoDetailDTO.setCommDocumentList(commDocumentList);
        return collectInfoDetailDTO;
    }

    @Override
    public List<CommonDocumentDTO> queryAttachmentByCollectId(Long companyId, Long collectId) {
        ScsCollectCertificateInfoDO collectCertificateInfoDO = this.scsCollectCertificateInfoMapper.selectByPrimaryKey(collectId);
        if (Objects.isNull(collectCertificateInfoDO)) {
            return Lists.newArrayList();
        }
        String documentId = collectCertificateInfoDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return Lists.newArrayList();
        }
        return this.documentService.listByDocIds(companyId, documentId);
    }

    @Override
    public SimplePageInfo<CollectInfoPageDTO> queryPageByConditionQuery(CollectConditionQueryAction action) {
        Long companyId = action.getCompanyId();
        // action.startPage();
        CollectConditionQuery query = BeanCopyUtils.copyByJSON(action, CollectConditionQuery.class);
        List<ScsCollectCertificateInfoDO> collectDOList = this.scsCollectCertificateInfoMapper
                .listPageByCondition(companyId, action.getCollectCertificateNo(), action.getSortBy(), query, action.getFromCollect());
        if (CollectionUtils.isEmpty(collectDOList)) {
            return new SimplePageInfo<>();
        }
        List<CollectInfoPageDTO> collectInfoPageDTOList = this.setCollectAttributes(companyId, collectDOList, action.getCheckStatus(), action.getIsUserChecked());
        return PaginationUtils.pagination(collectInfoPageDTOList, action.getPageNum(), action.getPageSize());
    }

    @Override
    public SimplePageInfo<CollectInfoPageDTO> queryPageByBizTypeList(Long companyId, String collectCertificateNo,
                                                                     String sortBy, CollectionBizTypeEnum targetBizType, CollectionBizTypeEnum sourceBizType,
                                                                     Integer pageNum, Integer pageSize) {
        List<BizCollectionRelDTO> idyRelDTOList = this.collectionService.listByBizType(companyId, targetBizType);
        List<Long> collectionIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(idyRelDTOList)) {
            collectionIds = idyRelDTOList.stream().map(BizCollectionRelDTO::getCollectionId).distinct().collect(Collectors.toList());
        }
        List<String> atpCollectionList = this.commCheckMainMapper.listByBelongModel(companyId, sourceBizType.getCode());
        if (CollectionUtils.isNotEmpty(atpCollectionList)) {
            List<Long> aptCollectionIds = Lists.newArrayList();
            atpCollectionList.forEach(k -> {
                if (StringUtils.isBlank(k)) {
                    return;
                }
                List<Long> atpCollectionIds = Stream.of(k.split(",")).map(id -> {
                    if (StringUtils.isBlank(id)) {
                        return null;
                    }
                    return Long.parseLong(id);
                }).distinct().filter(Objects::nonNull).collect(Collectors.toList());
                aptCollectionIds.addAll(atpCollectionIds);
            });
            if (CollectionUtils.isNotEmpty(aptCollectionIds)) {
                List<ScsBizCollectionRelDO> nAtpCollectionList = this.scsBizCollectionRelMapper.listByBizTypeExclude(sourceBizType.getCode(), aptCollectionIds);
                if (CollectionUtils.isNotEmpty(nAtpCollectionList)) {
                    List<Long> nAtpCollectionIds = nAtpCollectionList.stream().map(ScsBizCollectionRelDO::getCollectionId).distinct().collect(Collectors.toList());
                    collectionIds.addAll(nAtpCollectionIds);
                }
            }
        }
        if (CollectionUtils.isEmpty(collectionIds)) {
            collectionIds = null;
        }
        PageInfoBaseReq pageReq = new PageInfoBaseReq();
        pageReq.startPage(pageNum, pageSize, null);
        List<ScsCollectCertificateInfoDO> collectDOList = this.scsCollectCertificateInfoMapper.listPageByIdentify(companyId, collectCertificateNo, collectionIds);
        if (CollectionUtils.isEmpty(collectDOList)) {
            return new SimplePageInfo<>();
        }
        List<Long> collectIds = collectDOList.stream()
                .map(ScsCollectCertificateInfoDO::getId).collect(Collectors.toList());
        List<BizCollectionRelDTO> bizCollectionRelDTOList = this.collectionService
                .listByBizIdsAndBizTypeExclude(collectIds, CollectionBizTypeEnum.CLT, collectionIds);
        Map<Long, List<BizCollectionRelDTO>> bizCollectionRelMap = null;
        if (CollectionUtils.isNotEmpty(bizCollectionRelDTOList)) {
            bizCollectionRelMap = bizCollectionRelDTOList.stream().collect(Collectors.groupingBy(BizCollectionRelDTO::getBizId));
        }
        SimplePageInfo<CollectInfoPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectDOList, CollectInfoPageDTO.class);
        Map<Long, List<BizCollectionRelDTO>> finalBizCollectionRelMap = bizCollectionRelMap;
        List<CollectInfoPageDTO> collectInfoPageDTOList = collectDOList.stream().map(collectDO -> {
            CollectInfoPageDTO collectInfoPageDTO = BeanCopyUtils.copyByJSON(collectDO, CollectInfoPageDTO.class);
            collectInfoPageDTO.setCollectId(collectDO.getId());
            if (finalBizCollectionRelMap != null && finalBizCollectionRelMap.get(collectDO.getId()) != null) {
                collectInfoPageDTO.setCollectionCount(finalBizCollectionRelMap.get(collectDO.getId()).size());
            }
            return collectInfoPageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectInfoPageDTOList);
        return pageInfo;
    }

    @Override
    public List<CollectCheckedCollectionInfoDTO> queryCheckedCollectionInfoList(Long companyId, Long collectId){
        Map<Long,Object> collectionMap = new HashMap<>();
        List<CollectionInfoDetailDTO> collectionList = this.collectionService.queryByBizIdAndBizType(companyId,collectId,CollectionBizTypeEnum.CLT);
        List<Long> collectionIds = collectionList.stream().map(CollectionInfoDetailDTO::getCollectionId).collect(Collectors.toList());
        QueryCheckedCollectionIdAction action  = new QueryCheckedCollectionIdAction();
        action.setCompanyId(companyId);
        action.setBelongModel(CollectionBizTypeEnum.CLT);
        action.setModelId(collectId);
        action.setCollectionIds(collectionIds);
        List<Long> checkCollectionIdList =  checkService.queryCheckedCollectionId(action);
        for(Long id:checkCollectionIdList){
            collectionMap.put(id,"");
        }

        return collectionList.stream().map(collectionInfoDetailDTO -> {
            CollectCheckedCollectionInfoDTO collectCheckedCollectionInfoDTO =  BeanCopyUtils.copyByJSON(collectionInfoDetailDTO, CollectCheckedCollectionInfoDTO.class);
            if(collectionMap.containsKey(collectionInfoDetailDTO.getCollectionId())){
                collectCheckedCollectionInfoDTO.setChecked(true);
            }else{
                collectCheckedCollectionInfoDTO.setChecked(false);
            }
            return collectCheckedCollectionInfoDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public SimplePageInfo<CollectCheckedCollectionInfoDTO> queryAtpCollectionInfoList(Long companyId, Long collectId, CollectionBizTypeEnum targetBizType, String collectionName, String era, Integer pageNum, Integer pageSize) {
        CommCheckMainDO checkMainDO = this.commCheckMainMapper.selectByModelAndId(companyId, CollectionBizTypeEnum.CLT.getCode(), collectId);
        if (Objects.isNull(checkMainDO)
                || StringUtils.isBlank(checkMainDO.getCheckStatus())
                || !CheckMainStatusEnum.ATP.getCode().equals(checkMainDO.getCheckStatus())) {
            return new SimplePageInfo<>();
        }
        CommCheckChildDO endCheckChildDO = this.commCheckChildMapper.selectEndNodeByMainId(checkMainDO.getId());
        if (Objects.isNull(endCheckChildDO)) {
            return new SimplePageInfo<>();
        }
        Set<Long> atpCollectionIds = Sets.newHashSet();
        if (StringUtils.isNotBlank(endCheckChildDO.getCollectionId())) {
            Stream.of(endCheckChildDO.getCollectionId().split(",")).forEach(id -> {
                if (StringUtils.isBlank(id)) {
                    return;
                }
                atpCollectionIds.add(Long.parseLong(id));
            });
        }
        if (targetBizType.equals(CollectionBizTypeEnum.MSM)) {
            List<ScsBizCollectionRelDO> msmCollections = this.scsBizCollectionRelMapper.listByBizType(CollectionBizTypeEnum.MSM.getCode());
            if (CollectionUtils.isNotEmpty(msmCollections)) {
                List<Long> tbtCollectionIds = msmCollections.stream().map(ScsBizCollectionRelDO::getCollectionId).collect(Collectors.toList());
                tbtCollectionIds.forEach(atpCollectionIds::remove);
            }
        }
        List<CollectionInfoDetailDTO> collectionList = this.collectionService.queryByBizIdAndBizType(companyId, collectId, CollectionBizTypeEnum.CLT);
        if (CollectionUtils.isEmpty(collectionList)) {
            return new SimplePageInfo<>();
        }
        List<CollectCheckedCollectionInfoDTO> result = collectionList.stream().map(collectionInfoDetailDTO -> {
            CollectCheckedCollectionInfoDTO collectCheckedCollectionInfoDTO = BeanCopyUtils.copyByJSON(collectionInfoDetailDTO, CollectCheckedCollectionInfoDTO.class);
            if(atpCollectionIds.contains(collectionInfoDetailDTO.getCollectionId())){
                return collectCheckedCollectionInfoDTO;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if(StringUtils.isNotBlank(collectionName)){
            result = result.stream().filter(k -> k.getName().contains(collectionName)).collect(Collectors.toList());
        }
        if(StringUtils.isNotBlank(era)){
            result = result.stream().filter(k -> k.getEra().contains(era)).collect(Collectors.toList());
        }
        return PaginationUtils.pagination(result, pageNum, pageSize);
    }

    private ScsCollectCertificateInfoDO collectCertificateInfoCheck(Long companyId, Long collectId) {
        ScsCollectCertificateInfoDO collectCertificateInfoDO = this.scsCollectCertificateInfoMapper.selectByPrimaryKey(collectId);
        if (Objects.isNull(collectCertificateInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "征集凭证不存在");
        }
        CheckStatusByModelIdAction checkStatusByModelIdAction = new CheckStatusByModelIdAction();
        checkStatusByModelIdAction.setBelongModel(CollectionBizTypeEnum.CLT);
        checkStatusByModelIdAction.setCompanyId(companyId);
        checkStatusByModelIdAction.setModelId(collectId);
        this.checkService.checkStatusByModelId(checkStatusByModelIdAction);
        return collectCertificateInfoDO;
    }

    private List<CollectInfoPageDTO> setCollectAttributes(Long companyId, List<ScsCollectCertificateInfoDO> collectDOList, String checkStatus, Integer isUserChecked) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        if (CollectionUtils.isEmpty(collectDOList)) {
            return Lists.newArrayList();
        }
        List<Long> userIds = collectDOList.stream()
                .map(ScsCollectCertificateInfoDO::getCreator).distinct().collect(Collectors.toList());
        Map<Long, String> userNameMap = this.userService.queryUserNameMap(companyId, userIds);

        List<Long> collectIds = collectDOList.stream()
                .map(ScsCollectCertificateInfoDO::getId).collect(Collectors.toList());
        Map<Long, List<BizCollectionRelDTO>> bizCollectionRelMap = this.collectionService
                .queryBizCollectionRelMap(companyId, collectIds, CollectionBizTypeEnum.CLT);

        QueryBatchCheckStatusAction batchCheckStatusAction = new QueryBatchCheckStatusAction();
        batchCheckStatusAction.setBelongModel(CollectionBizTypeEnum.CLT);
        batchCheckStatusAction.setCompanyId(companyId);
        batchCheckStatusAction.setModelIds(collectIds);
        Map<Long,CheckCurrentInfoDTO> checkInfoMap = this.checkService.queryBatchCheckStatus(batchCheckStatusAction);

        return collectDOList.stream().map(collectDO -> {
            CollectInfoPageDTO collectInfoPageDTO = BeanCopyUtils.copyByJSON(collectDO, CollectInfoPageDTO.class);
            collectInfoPageDTO.setCollectId(collectDO.getId());
            collectInfoPageDTO.setGmtCreate(collectDO.getGmtCreate());
            if (checkInfoMap != null && checkInfoMap.containsKey(collectDO.getId())) {
                CheckCurrentInfoDTO checkCurrentInfoDTO = checkInfoMap.get(collectDO.getId());
                collectInfoPageDTO.setCheckStatus(checkCurrentInfoDTO.getCheckStatus().getCode());
                collectInfoPageDTO.setCheckStatusDesc(checkCurrentInfoDTO.getCheckStatus().getDesc());
                collectInfoPageDTO.setNeedManName(checkCurrentInfoDTO.getNeedManName());
                collectInfoPageDTO.setHasChecked(YesOrNoEnum.否.getCode().byteValue());
                if(Objects.nonNull(checkCurrentInfoDTO.getNeedManId()) && checkCurrentInfoDTO.getNeedManId().contains(userBO.getUserId())){
                    collectInfoPageDTO.setHasChecked(YesOrNoEnum.是.getCode().byteValue());
                }
            }
            if (userNameMap != null && !userNameMap.isEmpty()) {
                collectInfoPageDTO.setCreatorName(userNameMap.get(collectDO.getCreator()));
            }
            if (bizCollectionRelMap != null && !bizCollectionRelMap.isEmpty()) {
                List<BizCollectionRelDTO> collectionRelDTOList = bizCollectionRelMap.get(collectDO.getId());
                if (CollectionUtils.isNotEmpty(collectionRelDTOList)) {
                    collectInfoPageDTO.setCollectionCount(collectionRelDTOList.size());
                } else {
                    collectInfoPageDTO.setCollectionCount(0);
                }
            }
            if (StringUtils.isNotBlank(checkStatus)) {
                if (checkStatus.equals(collectInfoPageDTO.getCheckStatus())) {
                    return collectInfoPageDTO;
                } else {
                    return null;
                }
            }
            if (isUserChecked == 1) {
                if (collectInfoPageDTO.getHasChecked() == YesOrNoEnum.否.getCode().byteValue()) {
                    return null;
                }
            }
            return collectInfoPageDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private CollectCertificateEntity convertCollectCertificateEntity(AddCollectAction addCollectAction) {
        CollectCertificateEntity collectCertificateEntity = new CollectCertificateEntity();
        collectCertificateEntity.setCollectCertificateNo(addCollectAction.getPrefixName()
                + CollectionConstant.COLLECT_CERTIFICATE_MIDDLE);
        collectCertificateEntity.setPrefixName(addCollectAction.getPrefixName());
        collectCertificateEntity.setSuffixNumber(addCollectAction.getSuffixNumber());
        collectCertificateEntity.setBatch(addCollectAction.getBatch());
        collectCertificateEntity.setTransferor(addCollectAction.getTransferor());
        collectCertificateEntity.setDepartmentHead(addCollectAction.getDepartmentHead());
        collectCertificateEntity.setDocumentId(addCollectAction.getDocumentId());
        collectCertificateEntity.setCompanyId(addCollectAction.getCompanyId());
        collectCertificateEntity.setCreator(addCollectAction.getCreator());
        collectCertificateEntity.setModifier(addCollectAction.getCreator());
        collectCertificateEntity.setAutoAudit(addCollectAction.getAutoAudit());

        List<AddCollectCollectionAction> collectCollectionList =
                addCollectAction.getCollectCollectionList();
        if (CollectionUtils.isEmpty(collectCollectionList)) {
            return collectCertificateEntity;
        }
        List<CollectionVO> collectCollectionVOList =
                collectCollectionList.stream().map(collection -> {
                    CollectionVO collectCollectionVO = new CollectionVO();
                    collectCollectionVO.setCompanyId(addCollectAction.getCompanyId());
                    collectCollectionVO.setName(collection.getName());
                    collectCollectionVO.setActualCount(collection.getActualCount());
                    collectCollectionVO.setCountUnit(collection.getCountUnit());
                    collectCollectionVO.setCollectionCount(collection.getCollectionCount());
                    collectCollectionVO.setEra(collection.getEra());
                    collectCollectionVO.setAge(collection.getAge());
                    collectCollectionVO.setTextureCategory(collection.getTextureCategory());
                    collectCollectionVO.setTexture(collection.getTexture());
                    collectCollectionVO.setSize(collection.getSize());
                    collectCollectionVO.setSizeUnit(collection.getSizeUnit());
                    collectCollectionVO.setCompleteDegree(collection.getCompleteDegree());
                    collectCollectionVO.setCompleteInfo(collection.getCompleteInfo());
                    collectCollectionVO.setInitialLevel(collection.getInitialLevel());
                    collectCollectionVO.setAmount(collection.getAmount());
                    collectCollectionVO.setCollectType(collection.getCollectType());
                    collectCollectionVO.setCollectDate(collection.getCollectDate());
                    collectCollectionVO.setHolder(collection.getHolder());
                    collectCollectionVO.setPayVoucherNo(collection.getPayVoucherNo());
                    collectCollectionVO.setRemark(collection.getRemark());
                    collectCollectionVO.setAppendageDesc(collection.getAppendageDesc());
                    collectCollectionVO.setSourceInfo(collection.getSourceInfo());
                    collectCollectionVO.setIntroduce(collection.getIntroduce());
                    collectCollectionVO.setInputMode(collection.getInputMode().getCode());
                    collectCollectionVO.setInputType(collection.getInputType().getCode());
                    collectCollectionVO.setDocumentId(collection.getDocumentId());
                    collectCollectionVO.setCreator(addCollectAction.getCreator());
                    collectCollectionVO.setModifier(addCollectAction.getCreator());
                    return collectCollectionVO;
                }).collect(Collectors.toList());
        collectCertificateEntity.setCollectCollectionList(collectCollectionVOList);
        return collectCertificateEntity;
    }

    private CollectCertificateEntity convertCollectCertificateEntity(EditCollectAction editCollectAction) {
        CollectCertificateEntity collectCertificateEntity = new CollectCertificateEntity();
        collectCertificateEntity.setCollectId(editCollectAction.getCollectId());
        collectCertificateEntity.setPrefixName(editCollectAction.getPrefixName());
        collectCertificateEntity.setBatch(editCollectAction.getBatch());
        collectCertificateEntity.setTransferor(editCollectAction.getTransferor());
        collectCertificateEntity.setDepartmentHead(editCollectAction.getDepartmentHead());
        collectCertificateEntity.setDocumentId(editCollectAction.getDocumentId());
        collectCertificateEntity.setModifier(editCollectAction.getModifier());
        collectCertificateEntity.setAutoAudit(editCollectAction.getAutoAudit());

        List<EditCollectCollectionAction> collectCollectionList =
                editCollectAction.getCollectCollectionList();
        if (CollectionUtils.isEmpty(collectCollectionList)) {
            return collectCertificateEntity;
        }
        List<CollectionVO> collectCollectionVOList =
                collectCollectionList.stream().map(collection -> {
                    CollectionVO collectCollectionVO = new CollectionVO();
                    collectCollectionVO.setCollectionId(collection.getCollectionId());
                    collectCollectionVO.setName(collection.getName());
                    collectCollectionVO.setActualCount(collection.getActualCount());
                    collectCollectionVO.setCountUnit(collection.getCountUnit());
                    collectCollectionVO.setCollectionCount(collection.getCollectionCount());
                    collectCollectionVO.setEra(collection.getEra());
                    collectCollectionVO.setAge(collection.getAge());
                    collectCollectionVO.setTextureCategory(collection.getTextureCategory());
                    collectCollectionVO.setTexture(collection.getTexture());
                    collectCollectionVO.setSize(collection.getSize());
                    collectCollectionVO.setSizeUnit(collection.getSizeUnit());
                    collectCollectionVO.setCompleteDegree(collection.getCompleteDegree());
                    collectCollectionVO.setCompleteInfo(collection.getCompleteInfo());
                    collectCollectionVO.setInitialLevel(collection.getInitialLevel());
                    collectCollectionVO.setAmount(collection.getAmount());
                    collectCollectionVO.setCollectType(collection.getCollectType());
                    collectCollectionVO.setCollectDate(collection.getCollectDate());
                    collectCollectionVO.setHolder(collection.getHolder());
                    collectCollectionVO.setPayVoucherNo(collection.getPayVoucherNo());
                    collectCollectionVO.setRemark(collection.getRemark());
                    collectCollectionVO.setAppendageDesc(collection.getAppendageDesc());
                    collectCollectionVO.setSourceInfo(collection.getSourceInfo());
                    collectCollectionVO.setIntroduce(collection.getIntroduce());
                    collectCollectionVO.setInputMode(collection.getInputMode().getCode());
                    collectCollectionVO.setInputType(collection.getInputType().getCode());
                    collectCollectionVO.setDocumentId(collection.getDocumentId());
                    collectCollectionVO.setModifier(editCollectAction.getModifier());
                    if (Objects.isNull(collection.getCollectionId())) {
                        collectCollectionVO.setCreator(editCollectAction.getModifier());
                        collectCollectionVO.setCompanyId(editCollectAction.getCompanyId());
                    }
                    return collectCollectionVO;
                }).collect(Collectors.toList());
        collectCertificateEntity.setCollectCollectionList(collectCollectionVOList);
        return collectCertificateEntity;
    }
}
