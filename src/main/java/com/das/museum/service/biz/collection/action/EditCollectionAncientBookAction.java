package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;

@Data
public class EditCollectionAncientBookAction implements Serializable {
    private static final long serialVersionUID = -2933861857891595037L;
    private Long collectionAncientBookId;
    private Long companyId;
    private String classification;
    private String version;
    private String archive;
    private String frame;
    private String format;
    private String documentId;
    private Long modifier;
}
