package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.IdentifyCheckedCollectionInfoDTO;
import com.das.museum.service.biz.collection.dto.IdentifyInfoDetailDTO;
import com.das.museum.service.biz.collection.dto.IdentifyInfoPageDTO;
import com.das.museum.common.bo.SimplePageInfo;

import java.util.List;

public interface IdentifyCertificateService {
    Long addIdentifyCertificateInfo(AddIdentifyAction addCollectAction);
    void editIdentifyCertificateInfo(EditIdentifyAction editCollectAction);
    void delIdentifyCertificateInfoById(Long companyId, Long identifyId);
    SimplePageInfo<IdentifyInfoPageDTO> queryPageByQuery(IdentifyQueryAction identifyQueryAction);
    IdentifyInfoDetailDTO queryDetailInfoById(Long companyId, Long identifyId);
    void identifyCollectionById(IdentifyCollectionAction identifyCollectionAction);
    List<IdentifyCheckedCollectionInfoDTO> queryCheckedCollectionInfoList(Long companyId, Long identifyId);
}
