package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AddCollectionRegisterIdentifyAction implements Serializable {
    private static final long serialVersionUID = -3866873756031657140L;
    private Long companyId;
    private Long collectionId;
    private Long identifyType;
    private Long identifyTypeName;
    private Long appraiserId;
    private String appraiserName;
    private String orgAgency;
    private String identifyAgency;
    private String approveAgency;
    private String identifyOpinion;
    private String identifyRemark;
    private Date identifyDate;
    private String documentId;
    private Long creator;
}
