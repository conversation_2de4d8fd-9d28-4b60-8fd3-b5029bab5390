package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionDamageAction;
import com.das.museum.service.biz.collection.action.DamageQueryPageByConditionAction;
import com.das.museum.service.biz.collection.action.EditCollectionDamageAction;
import com.das.museum.service.biz.collection.dto.CollectionDamageDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionDamagePageDTO;
import com.das.museum.common.bo.SimplePageInfo;

public interface CollectionDamageService {
    Long addCollectionDamage(AddCollectionDamageAction action);
    void editCollectionDamage(EditCollectionDamageAction action);
    void delByCollectionDamageId(Long companyId, Long collectionDamageId);
    SimplePageInfo<CollectionDamagePageDTO> queryPageByCondition(DamageQueryPageByConditionAction action);
    CollectionDamageDetailDTO queryByCollectionDamageId(Long companyId, Long collectionDamageId);
}
