package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LogoffConditionQueryAction extends PageInfoBaseReq {
    private static final long serialVersionUID = 1348781467587584509L;
    private Long companyId;
    private String logoffCertificateNo;
    private String registerNo;
    private String collectionName;
    private String checkStatus;
    private String sortBy;
    private Integer isUserChecked;
}
