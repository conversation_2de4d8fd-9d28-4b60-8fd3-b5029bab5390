package com.das.museum.service.biz.collection.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/01/11
 */
@Data
public class CollectionStatisticalDTO implements Serializable {

    private static final long serialVersionUID = -672412618787551621L;

    /**
     * 待出库code
     */
    private String powhCode;

    /**
     * 待出库数量
     */
    private Long powhCount;

    /**
     * 已出库code
     */
    private String aowhCode;

    /**
     * 已出库数量
     */
    private Long aowhCount;

    /**
     * 已回库code
     */
    private String aiwhCode;

    /**
     * 已回库数量
     */
    private Long aiwhCount;
}
