package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.CollectionUpdateRecordAction;
import com.das.museum.service.biz.collection.action.CollectionUpdateRecordDetailAction;
import com.das.museum.service.biz.collection.dto.CollectionUpdateRecordDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionUpdateRecordPageDTO;
import com.das.museum.common.bo.SimplePageInfo;

import java.util.List;

public interface CollectionUpdateRecordService {
    SimplePageInfo<CollectionUpdateRecordPageDTO>  queryPageByCondition(CollectionUpdateRecordAction action);
    List<CollectionUpdateRecordDetailDTO> queryUpdateRecordDetail(CollectionUpdateRecordDetailAction action);
}
