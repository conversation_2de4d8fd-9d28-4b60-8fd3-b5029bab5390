package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EditInTibetanAction implements Serializable {
    private static final long serialVersionUID = 7120669638951232602L;
    private Long inTibetanId;
    private String inTibetanCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private String documentId;
    private Long companyId;
    private Long modifier;
    private String modifierName;
    private List<AddInTibetanCollectionAction> inTibetanCollectionList;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}
