package com.das.museum.service.biz.collection.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectionDrawPageDTO implements Serializable {
    private static final long serialVersionUID = 8048504463169106267L;
    private Long collectionDrawId;
    private Long companyId;
    private Long collectionId;
    private String title;
    private String drawNo;
    private Long draughtsmanId;
    private String draughtsmanName;
    private String proportion;
    private Date drawDate;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}