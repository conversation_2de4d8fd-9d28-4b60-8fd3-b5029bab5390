package com.das.museum.service.biz.collection.dto;

import lombok.Data;

@Data
public class QueryTwoImagePageListDTO {

    /**
     * 二维影像id
     */
    private Long twoImageId;

    /**
     * 文件地址
     */
    private String url;

    /**
     * 文件名称
     */
    private String sourceFileName;

    /**
     * 文件批次
     */
    private String fileBatch;

    /**
     * 文件格式
     */
    private String fileFormat;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 上传人
     */
    private String creatorName;

    /**
     * 更新人
     */
    private String modifierName;

    /**
     * 上传时间
     */
    private String gmtCreate;

    /**
     * 更新时间
     */
    private String gmtModified;

}
