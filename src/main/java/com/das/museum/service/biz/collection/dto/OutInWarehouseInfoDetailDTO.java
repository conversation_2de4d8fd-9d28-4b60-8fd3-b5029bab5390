package com.das.museum.service.biz.collection.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OutInWarehouseInfoDetailDTO implements Serializable {
    private static final long serialVersionUID = -8220713676061375343L;
    private Long outInWarehouseId;
    private Long companyId;
    private String outInWarehouseCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private Long reason;
    /**
     * 出库原因
     */
    private String reasonName;
    private String reasonInfo;
    private String useId;
    private String useName;
    private String userDepartmentId;
    private String userDepartmentName;
    private String useAddress;
    private String remark;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date useDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date preReturnDate;
    private Long submitter;
    private String submitterName;
    private String documentId;
    private Long creator;
    private Long modifier;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModified;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}