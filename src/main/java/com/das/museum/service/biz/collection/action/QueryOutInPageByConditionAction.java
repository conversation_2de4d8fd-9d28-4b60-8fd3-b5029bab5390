package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryOutInPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = -231587273949616679L;
    private Long companyId;
    private String outInWarehouseCertificateNo;
    private String registerNo;
    private String useId;
    private String userDepartmentId;
    private String collectionName;
    private Long reason;
    private String checkStatus;
    private String sortBy;
    private Integer isUserChecked;
}
