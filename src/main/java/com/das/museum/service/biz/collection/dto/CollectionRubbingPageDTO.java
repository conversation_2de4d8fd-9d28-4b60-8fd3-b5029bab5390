package com.das.museum.service.biz.collection.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectionRubbingPageDTO implements Serializable {
    private static final long serialVersionUID = 6488701664205478954L;
    private Long collectionRubbingId;
    private Long companyId;
    private Long collectionId;
    private String bookStyle;
    private String mountSituation;
    private String rubbingNo;
    private String recognition;
    private String seal;
    private String postscript;
    private String interpretation;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}