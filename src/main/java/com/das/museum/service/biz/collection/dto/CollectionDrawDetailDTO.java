package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionDrawDetailDTO implements Serializable {
    private static final long serialVersionUID = 4470792105972336691L;
    private Long collectionDrawId;
    private Long companyId;
    private Long collectionId;
    private String title;
    private String drawNo;
    private Long draughtsmanId;
    private String draughtsmanName;
    private String proportion;
    private Date drawDate;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private List<CommonDocumentDTO> commDocumentList;
}