package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AssignWarehouseAction implements Serializable {
    private static final long serialVersionUID = -6574763649664023662L;
    private Long companyId;
    private Long inTibetanId;
    private List<Long> collectionIds;
    private Long warehouseId;
    private Long modifier;
}
