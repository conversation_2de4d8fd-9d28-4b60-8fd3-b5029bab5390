package com.das.museum.service.biz.collection;

import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.service.biz.collection.action.EditThreeModelInfoAction;
import com.das.museum.service.biz.collection.action.QueryThreeModelPageAction;
import com.das.museum.service.biz.collection.dto.QueryThreeModelInfoDTO;
import com.das.museum.service.biz.collection.dto.QueryThreeModelPageListDTO;

public interface CollectionThreeModelService {

    SimplePageInfo<QueryThreeModelPageListDTO>  queryThreeModelPageList(QueryThreeModelPageAction action);

    QueryThreeModelInfoDTO queryThreeModelInfo(Long companyId, Long threeModelId);

    void editThreeModelInfo(EditThreeModelInfoAction action);

    void delThreeModelInfo(Long companyId, Long threeModelId);
}
