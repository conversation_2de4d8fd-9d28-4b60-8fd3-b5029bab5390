package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DrawQueryPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = 4252344182896238061L;
    private Long companyId;
    private Long collectionId;
    private String title;
    private String drawNo;
    private String sortBy;
}
