package com.das.museum.service.biz.collection.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectionRegisterIdentifyPageDTO implements Serializable {
    private static final long serialVersionUID = -1729014059413713720L;
    private Long registerIdentifyId;
    private Long collectionId;
    private Long identifyType;
    private String identifyTypeName;
    private Long appraiserId;
    private String appraiserName;
    private String orgAgency;
    private String identifyAgency;
    private String approveAgency;
    private String identifyOpinion;
    private String identifyRemark;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date identifyDate;
    private String documentId;
}