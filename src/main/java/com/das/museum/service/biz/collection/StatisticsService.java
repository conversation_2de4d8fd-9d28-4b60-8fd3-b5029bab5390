package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.dto.OutWarehouseTrendDTO;
import com.das.museum.service.biz.collection.dto.StatisticDigitalDTO;
import com.das.museum.service.biz.collection.dto.StatisticsAllCollectionDTO;
import com.das.museum.service.biz.collection.dto.StatisticsByTypeDTO;

import java.util.List;

public interface StatisticsService {
    StatisticsAllCollectionDTO statisticsAllCollection();

    OutWarehouseTrendDTO getOutWarehouseTrend();

    List<StatisticsByTypeDTO> getStatisticsByType(Byte type);

    StatisticDigitalDTO getStatisticDigital();
}
