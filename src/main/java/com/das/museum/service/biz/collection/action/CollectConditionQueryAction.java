package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class CollectConditionQueryAction extends PageInfoBaseReq {
    private static final long serialVersionUID = -6218225334454851428L;
    private Long companyId;
    private String collectCertificateNo;
    private Long collectType;
    private String collectionName;
    private Date collectionStartDate;
    private Date collectionEndDate;
    private Long completeDegree;
    private Long initialLevel;
    private String era;
    private String texture;
    private Date collectStartDate;
    private Date collectEndDate;
    private String checkStatus;
    private String sortBy;
    private int fromCollect;
    private Integer isUserChecked;
}
