package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AddInTibetanAction implements Serializable {
    private static final long serialVersionUID = 5582321773283958139L;
    private String inTibetanCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private String documentId;
    private Long companyId;
    private Long creator;
    private String creatorName;
    private List<AddInTibetanCollectionAction> inTibetanCollectionList;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}
