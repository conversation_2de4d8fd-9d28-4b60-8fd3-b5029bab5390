package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionInWarehouseAction implements Serializable {
    private static final long serialVersionUID = 1765803017403951081L;
    private Long companyId;
    private Long outInWarehouseId;
    private List<InWarehouseCollectionAction> inWarehouseCollection;
    private Long transferor;
    private String transferorName;
    private Long receiver;
    private String receiverName;
    private Date returnDate;
    private String returnAddress;
    private String collectionRemark;
    private String returnRemark;
    private Long creator;
}
