package com.das.museum.service.biz.collection;

import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.infr.dataobject.AccUserDO;
import com.das.museum.infr.dataobject.CommDocumentDO;
import com.das.museum.infr.dataobject.ScsCollectionThreeModelDO;
import com.das.museum.infr.dataobject.ScsCollectionTwoImageDO;
import com.das.museum.infr.dataobjectexpand.QueryThreeModelPageDO;
import com.das.museum.infr.mapper.AccUserMapper;
import com.das.museum.infr.mapper.CommDocumentMapper;
import com.das.museum.infr.mapper.ScsCollectionThreeModelMapper;
import com.das.museum.service.biz.collection.action.EditThreeModelInfoAction;
import com.das.museum.service.biz.collection.action.QueryThreeModelPageAction;
import com.das.museum.service.biz.collection.dto.QueryThreeModelInfoDTO;
import com.das.museum.service.biz.collection.dto.QueryThreeModelPageListDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class CollectionThreeModelServiceImpl implements CollectionThreeModelService{

    @Resource
    private DocumentService documentService;

    @Resource
    private ScsCollectionThreeModelMapper scsCollectionThreeModelMapper;

    @Resource
    private AccUserMapper accUserMapper;

    @Resource
    private CommDocumentMapper commDocumentMapper;

   @Override
    public SimplePageInfo<QueryThreeModelPageListDTO> queryThreeModelPageList(QueryThreeModelPageAction action){
        Long companyId = action.getCompanyId();
        action.startPage();
        List<QueryThreeModelPageDO> threeModelList = this.scsCollectionThreeModelMapper
                .listPageByCondition(companyId, action.getRegisterInfoId(),action.getKeyword(),action.getCreateStartTime(),action.getCreateEndTime(),action.getUpdateStartTime(),action.getUpdateEndTime());
        if (CollectionUtils.isEmpty(threeModelList)) {
            return new SimplePageInfo<>();
        }

        List<Long> creatorList = threeModelList.stream().map(QueryThreeModelPageDO::getCreator).collect(Collectors.toList());
        List<Long> modifierList = threeModelList.stream().map(QueryThreeModelPageDO::getModifier).collect(Collectors.toList());
        List<Long> userIds = Lists.newArrayList();
        userIds.addAll(creatorList);
        userIds.addAll(modifierList);
        List<AccUserDO> accUserDOList = accUserMapper.listByIds(companyId,userIds);
        Map<Long,AccUserDO> accUserDOMap = new HashMap<>();
        accUserDOList.forEach(s -> accUserDOMap.put(s.getId(),s));

        SimplePageInfo<QueryThreeModelPageListDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(threeModelList, QueryThreeModelPageListDTO.class);
        List<QueryThreeModelPageListDTO> resultList = threeModelList.stream().map(queryThreeModelPageDO -> {
            QueryThreeModelPageListDTO dto = new QueryThreeModelPageListDTO();
            BeanUtils.copyProperties(queryThreeModelPageDO,dto);
            if(accUserDOMap.containsKey(queryThreeModelPageDO.getCreator())){
                dto.setCreatorName(accUserDOMap.get(queryThreeModelPageDO.getCreator()).getRealName());
            }

            if(accUserDOMap.containsKey(queryThreeModelPageDO.getModifier())){
                dto.setModifierName(accUserDOMap.get(queryThreeModelPageDO.getModifier()).getRealName());
            }

            if(Objects.nonNull(queryThreeModelPageDO.getGmtCreate())){
                dto.setGmtCreate(DateUtils.formatDateYYYMMDDHHmmss(queryThreeModelPageDO.getGmtCreate()));
            }
            if(Objects.nonNull(queryThreeModelPageDO.getGmtModified())){
                dto.setGmtModified(DateUtils.formatDateYYYMMDDHHmmss(queryThreeModelPageDO.getGmtModified()));
            }
            return dto;
        }).collect(Collectors.toList());
        pageInfo.setList(resultList);
        return pageInfo;
    }

    @Override
    public QueryThreeModelInfoDTO queryThreeModelInfo(Long companyId, Long threeModelId){
        QueryThreeModelInfoDTO dto = new QueryThreeModelInfoDTO();
        ScsCollectionThreeModelDO scsCollectionThreeModelDO = scsCollectionThreeModelMapper.selectByCompanyIdAndId(companyId,threeModelId);
        if(Objects.isNull(scsCollectionThreeModelDO)){
            return dto;
        }
        BeanUtils.copyProperties(scsCollectionThreeModelDO,dto);
        dto.setThreeModelId(scsCollectionThreeModelDO.getId());
        if(scsCollectionThreeModelDO.getMakeTime() != null){
            dto.setMakeTime(DateUtils.formatDateYYYMMDD(scsCollectionThreeModelDO.getMakeTime()));
        }

        Long documentId = scsCollectionThreeModelDO.getDocumentId();
        if(documentId != null){
            CommonDocumentDTO commonDocumentDTO = documentService.queryByDocId(companyId,documentId);
            if(Objects.nonNull(commonDocumentDTO)){
                dto.setThreeModelName(commonDocumentDTO.getSourceFileName());
            }
        }
        return dto;
    }

    @Override
    @Transactional
    public void editThreeModelInfo(EditThreeModelInfoAction action){
        ScsCollectionThreeModelDO scsCollectionThreeModelDO = scsCollectionThreeModelMapper.selectByCompanyIdAndId(action.getCompanyId(),action.getThreeModelId());
        if(Objects.isNull(scsCollectionThreeModelDO)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "三维模型信息不存在！");
        }
        BeanUtils.copyProperties(action,scsCollectionThreeModelDO);
        if(StringUtils.isNotBlank(action.getMakeTime())){
            scsCollectionThreeModelDO.setMakeTime(DateUtils.parseDateYYYMMDD(action.getMakeTime()));
        }
        scsCollectionThreeModelMapper.updateByCompanyIdAndId(scsCollectionThreeModelDO);

        CommDocumentDO documentDO = commDocumentMapper.selectById(action.getCompanyId(),scsCollectionThreeModelDO.getDocumentId());
        if(Objects.nonNull(documentDO)){
            documentDO.setSourceFileName(action.getThreeModelName());
            commDocumentMapper.updateByCompanyIdAndId(documentDO);
        }
    }

    @Override
    @Transactional
    public void delThreeModelInfo(Long companyId, Long threeModelId){
        ScsCollectionThreeModelDO scsCollectionThreeModelDO = scsCollectionThreeModelMapper.selectByCompanyIdAndId(companyId,threeModelId);
        if(Objects.isNull(scsCollectionThreeModelDO)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "三维模型信息不存在！");
        }
        scsCollectionThreeModelMapper.deleteByCompanyIdAndId(companyId,threeModelId);

        if(scsCollectionThreeModelDO.getDocumentId() != null){
            documentService.delByDocId(companyId,scsCollectionThreeModelDO.getDocumentId());
        }
    }

}
