package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class NoteQueryPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = 6107388317391167092L;
    private Long companyId;
    private Long collectionId;
    private String sortBy;
}
