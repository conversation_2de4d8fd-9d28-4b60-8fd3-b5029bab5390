package com.das.museum.service.biz.collection.action;

import com.das.museum.service.enums.InputModeEnum;
import com.das.museum.service.enums.InputTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AddCollectCollectionAction implements Serializable {
    private static final long serialVersionUID = 6065135908995826204L;
    private Long collectionId;
    private String name;
    private Integer actualCount;
    private Long countUnit;
    private Integer collectionCount;
    private String era;
    private Long age;
    private Long textureCategory;
    private String texture;
    private String size;
    private Long sizeUnit;
    private Long completeDegree;
    private String completeInfo;
    private Long initialLevel;
    private Long amount;
    private Long collectType;
    private Date collectDate;
    private String holder;
    private String payVoucherNo;
    private String remark;
    private String appendageDesc;
    private String sourceInfo;
    private String introduce;
    private InputModeEnum inputMode;
    private InputTypeEnum inputType;
    private String documentId;
}
