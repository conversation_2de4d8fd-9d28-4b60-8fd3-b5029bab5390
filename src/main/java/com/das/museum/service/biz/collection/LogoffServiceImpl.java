package com.das.museum.service.biz.collection;

import com.das.museum.common.utils.PaginationUtils;
import com.das.museum.infr.dataobject.CommIncrSegmentDO;
import com.das.museum.infr.mapper.CommIncrSegmentMapper;
import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.CheckCurrentInfoDTO;
import com.das.museum.service.biz.collection.dto.LogoffCollectionDTO;
import com.das.museum.service.biz.collection.dto.LogoffInfoDetailDTO;
import com.das.museum.service.biz.collection.dto.LogoffInfoPageDTO;
import com.das.museum.common.constants.CollectionConstant;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.LogoffCertificateEntity;
import com.das.museum.domain.collection.repository.LogoffCertificateRepository;
import com.das.museum.domain.collection.valueobject.LogoffCollectionVO;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.domain.enums.RegisterStatusEnum;
import com.das.museum.infr.dataobject.ScsLogoffCertificateInfoDO;
import com.das.museum.infr.dataobject.ScsLogoffCollectionInfoDO;
import com.das.museum.infr.mapper.ScsLogoffCertificateInfoMapper;
import com.das.museum.infr.mapper.ScsLogoffCollectionInfoMapper;
import com.das.museum.infr.query.LogoffConditionQuery;
import com.das.museum.service.biz.user.UserService;
import com.das.museum.service.enums.YesOrNoEnum;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LogoffServiceImpl implements LogoffService {

    @Resource
    private LogoffCertificateRepository logoffCertificateRepository;

    @Resource
    private ScsLogoffCertificateInfoMapper scsLogoffCertificateInfoMapper;

    @Resource
    private ScsLogoffCollectionInfoMapper scsLogoffCollectionInfoMapper;

    @Resource
    private UserService userService;

    @Resource
    private CheckService checkService;

    @Resource
    private CollectionRegisterService collectionRegisterService;

    @Resource
    private CommIncrSegmentMapper commIncrSegmentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addLogoffCertificateInfo(AddLogoffAction addLogoffAction) {
        String bizDimension = DateUtils.formatDateYYYMMDD().substring(0, 4);
        CommIncrSegmentDO commIncrSegmentDO = this.commIncrSegmentMapper
                .selectByCompanyIdAndBizCode(addLogoffAction.getCompanyId(), CollectionBizTypeEnum.LOGOUT.getCode(), bizDimension);
        if (Objects.isNull(commIncrSegmentDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "注销凭证自动编号配置不存在");
        }
        Long bizCurrIndex = commIncrSegmentDO.getBizCurrIndex();
        LogoffCertificateEntity entity = this.convertLogoffCertificateEntity(addLogoffAction);
        String logoffCertificateNo = entity.getPrefixName()
                + CollectionConstant.IN_LOGOFF_CERTIFICATE_MIDDLE + (bizCurrIndex + 1)
                + CollectionConstant.CERTIFICATE_TAIL;
        entity.setLogoffCertificateNo(logoffCertificateNo);
        entity.setAutoAudit(addLogoffAction.getAutoAudit());
        entity.setSuffixNumber(bizCurrIndex + 1);
        this.logoffCertificateRepository.saveLogoffCertificate(entity);
        Long logoffId = entity.getLogoffId();
        /*ScsLogoffCertificateInfoDO updateDO = new ScsLogoffCertificateInfoDO();
        updateDO.setId(logoffId);
        updateDO.setSuffixNumber(logoffId);
        updateDO.setLogoffCertificateNo(entity.getPrefixName()
                + CollectionConstant.IN_LOGOFF_CERTIFICATE_MIDDLE + updateDO.getSuffixNumber()
                + CollectionConstant.CERTIFICATE_TAIL);
        updateDO.setAutoAudit(addLogoffAction.getAutoAudit());
        this.scsLogoffCertificateInfoMapper.updateByPrimaryKeySelective(updateDO);*/

        ScsLogoffCollectionInfoDO logoffCollectionInfoDO = new ScsLogoffCollectionInfoDO();
        logoffCollectionInfoDO.setLogoffCertificateNo(logoffCertificateNo);
        logoffCollectionInfoDO.setLogoffId(logoffId);
        logoffCollectionInfoDO.setCompanyId(addLogoffAction.getCompanyId());
        this.scsLogoffCollectionInfoMapper.updateByLogoffId(logoffCollectionInfoDO);
        List<AddLogoffCollectionAction> logoffCollectionList = addLogoffAction.getLogoffCollectionList();
        if (CollectionUtils.isEmpty(logoffCollectionList)) {
            return logoffId;
        }
        List<Long> collectionIds = logoffCollectionList.stream().map(AddLogoffCollectionAction::getCollectionId).collect(Collectors.toList());;
        this.collectionRegisterService.updateRegisterStatuesByCollectionIds(addLogoffAction.getCompanyId(), RegisterStatusEnum.WLGF, collectionIds,addLogoffAction.getCreator());
        int row = this.commIncrSegmentMapper.updateByBizCurrIndex(commIncrSegmentDO.getId(), bizCurrIndex);
        if (row <= 0) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "注销库凭证自动获取编号失败");
        }
        // 自动提审
        if(addLogoffAction.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(addLogoffAction.getCreator());
            submitCheckAction.setCompanyId(addLogoffAction.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.LOGOUT);
            submitCheckAction.setModelId(logoffId);
            submitCheckAction.setCreatorName(addLogoffAction.getCreatorName());
            this.checkService.submitCheck(submitCheckAction);
        }

        return logoffId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editLogoffCertificateInfo(EditLogoffAction editLogoffAction) {
        ScsLogoffCertificateInfoDO logoffCertificateDO = this.scsLogoffCertificateInfoMapper.selectById(editLogoffAction.getCompanyId(),
                editLogoffAction.getLogoffId());
        if (Objects.isNull(logoffCertificateDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "注销凭证不存在");
        }
        CheckStatusByModelIdAction checkStatusByModelIdAction = new CheckStatusByModelIdAction();
        checkStatusByModelIdAction.setBelongModel(CollectionBizTypeEnum.LOGOUT);
        checkStatusByModelIdAction.setCompanyId(editLogoffAction.getCompanyId());
        checkStatusByModelIdAction.setModelId(editLogoffAction.getLogoffId());
        this.checkService.checkStatusByModelId(checkStatusByModelIdAction);
        String logoffCertificateNo = editLogoffAction.getPrefixName()
                + CollectionConstant.IN_LOGOFF_CERTIFICATE_MIDDLE + logoffCertificateDO.getSuffixNumber()
                + CollectionConstant.CERTIFICATE_TAIL;
        LogoffCertificateEntity entity = this.convertLogoffCertificateEntity(editLogoffAction, logoffCertificateNo);
        entity.setSuffixNumber(logoffCertificateDO.getSuffixNumber());
        this.logoffCertificateRepository.editLogoffCertificate(entity);
        ScsLogoffCollectionInfoDO logoffCollectionInfoDO = new ScsLogoffCollectionInfoDO();
        logoffCollectionInfoDO.setRegisterNo(entity.getLogoffCertificateNo());
        logoffCollectionInfoDO.setLogoffId(editLogoffAction.getLogoffId());
        logoffCollectionInfoDO.setCompanyId(editLogoffAction.getCompanyId());
        this.scsLogoffCollectionInfoMapper.updateByLogoffId(logoffCollectionInfoDO);

        List<Long> collectionIds = entity.getLogoffCollectionList().stream().map(LogoffCollectionVO::getCollectionId).collect(Collectors.toList());
        this.collectionRegisterService.updateRegisterStatuesByCollectionIds(editLogoffAction.getCompanyId(), RegisterStatusEnum.WLGF, collectionIds,editLogoffAction.getModifier());

        // 自动提审
        if(editLogoffAction.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(editLogoffAction.getModifier());
            submitCheckAction.setCompanyId(editLogoffAction.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.LOGOUT);
            submitCheckAction.setModelId(editLogoffAction.getLogoffId());
            submitCheckAction.setCreatorName(editLogoffAction.getModifierName());
            this.checkService.submitCheck(submitCheckAction);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delLogoffCertificateById(Long companyId, Long logoffId) {
        ScsLogoffCertificateInfoDO logoffCertificateDO = this.scsLogoffCertificateInfoMapper.selectById(companyId, logoffId);
        if (Objects.isNull(logoffCertificateDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "注销凭证不存在");
        }
        CheckStatusByModelIdAction checkStatusByModelIdAction = new CheckStatusByModelIdAction();
        checkStatusByModelIdAction.setBelongModel(CollectionBizTypeEnum.LOGOUT);
        checkStatusByModelIdAction.setCompanyId(companyId);
        checkStatusByModelIdAction.setModelId(logoffId);
        this.checkService.checkStatusByModelId(checkStatusByModelIdAction);
        this.logoffCertificateRepository.delLogoffCertificateById(companyId, logoffId);
    }

    @Override
    public LogoffInfoDetailDTO queryDetailInfoById(Long companyId, Long logoffId) {
        ScsLogoffCertificateInfoDO logoffCertificateDO = this.scsLogoffCertificateInfoMapper.selectById(companyId, logoffId);
        if (Objects.isNull(logoffCertificateDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "注销凭证不存在");
        }
        LogoffInfoDetailDTO logoffInfoDetailDTO = BeanCopyUtils.copyByJSON(logoffCertificateDO, LogoffInfoDetailDTO.class);
        logoffInfoDetailDTO.setLogoffId(logoffCertificateDO.getId());
        return logoffInfoDetailDTO;
    }

    @Override
    public SimplePageInfo<LogoffInfoPageDTO> queryPageByConditionQuery(LogoffConditionQueryAction action) {
        // action.startPage();
        LogoffConditionQuery query = new LogoffConditionQuery();
        query.setCollectionName(action.getCollectionName());
        query.setRegisterNo(action.getRegisterNo());
        List<ScsLogoffCertificateInfoDO> logoffCertificateList = this.scsLogoffCertificateInfoMapper
                .listPageByCondition(action.getCompanyId(), action.getLogoffCertificateNo(), action.getSortBy(), query);
        if (CollectionUtils.isEmpty(logoffCertificateList)) {
            return new SimplePageInfo<>();
        }
        /*SimplePageInfo<LogoffInfoPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(logoffCertificateList, LogoffInfoPageDTO.class);*/
        List<LogoffInfoPageDTO> logoffInfoPageDTOList = this.setLogoffAttributes(action.getCompanyId(), logoffCertificateList, action.getCheckStatus(), action.getIsUserChecked());
        // pageInfo.setList(logoffInfoPageDTOList);
        return PaginationUtils.pagination(logoffInfoPageDTOList, action.getPageNum(), action.getPageSize());
    }

    @Override
    public SimplePageInfo<LogoffCollectionDTO> queryPageCollectionByLogoffId(PageLogoffCollectionAction action) {
        action.startPage();
        List<ScsLogoffCollectionInfoDO> logoffCollectionInfoDOList = this.scsLogoffCollectionInfoMapper
                .listByLogoffId(action.getCompanyId(), action.getLogoffId());
        if (CollectionUtils.isEmpty(logoffCollectionInfoDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<LogoffCollectionDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(logoffCollectionInfoDOList, LogoffCollectionDTO.class);
        List<LogoffCollectionDTO> logoffCollectionDTOList = logoffCollectionInfoDOList.stream().map(collectionDO -> {
            LogoffCollectionDTO logoffCollectionDTO = BeanCopyUtils.copyByJSON(collectionDO, LogoffCollectionDTO.class);
            logoffCollectionDTO.setLogoffCollectionId(collectionDO.getId());
            return logoffCollectionDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(logoffCollectionDTOList);
        return pageInfo;
    }
    private List<LogoffInfoPageDTO> setLogoffAttributes(Long companyId, List<ScsLogoffCertificateInfoDO> logoffList, String checkStatus, Integer isUserChecked) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<Long> userIds = logoffList.stream()
                .map(ScsLogoffCertificateInfoDO::getCreator).distinct().collect(Collectors.toList());
        Map<Long, String> userNameMap = this.userService.queryUserNameMap(companyId, userIds);
        List<Long> logoffIds = logoffList.stream()
                .map(ScsLogoffCertificateInfoDO::getId).distinct().collect(Collectors.toList());
        List<ScsLogoffCollectionInfoDO> logoffCollectionList = this.scsLogoffCollectionInfoMapper.listByLogoffIds(companyId, logoffIds);
        Map<Long, List<ScsLogoffCollectionInfoDO>> logoffCollectionMap = logoffCollectionList.stream()
                .collect(Collectors.groupingBy(ScsLogoffCollectionInfoDO::getLogoffId));
        QueryBatchCheckStatusAction batchCheckStatusAction = new QueryBatchCheckStatusAction();
        batchCheckStatusAction.setBelongModel(CollectionBizTypeEnum.LOGOUT);
        batchCheckStatusAction.setCompanyId(companyId);
        batchCheckStatusAction.setModelIds(logoffIds);
        Map<Long, CheckCurrentInfoDTO> checkInfoMap = this.checkService.queryBatchCheckStatus(batchCheckStatusAction);
        return logoffList.stream().map(logoffCertificateDO -> {
            LogoffInfoPageDTO logoffInfoPageDTO = BeanCopyUtils.copyByJSON(logoffCertificateDO, LogoffInfoPageDTO.class);
            logoffInfoPageDTO.setLogoffId(logoffCertificateDO.getId());
            if (checkInfoMap != null && checkInfoMap.containsKey(logoffCertificateDO.getId())) {
                CheckCurrentInfoDTO checkCurrentInfoDTO = checkInfoMap.get(logoffCertificateDO.getId());
                logoffInfoPageDTO.setCheckStatus(checkCurrentInfoDTO.getCheckStatus().getCode());
                logoffInfoPageDTO.setCheckStatusDesc(checkCurrentInfoDTO.getCheckStatus().getDesc());
                logoffInfoPageDTO.setNeedManName(checkCurrentInfoDTO.getNeedManName());
                logoffInfoPageDTO.setHasChecked(YesOrNoEnum.否.getCode().byteValue());
                if(Objects.nonNull(checkCurrentInfoDTO.getNeedManId()) && checkCurrentInfoDTO.getNeedManId().contains(userBO.getUserId())){
                    logoffInfoPageDTO.setHasChecked(YesOrNoEnum.是.getCode().byteValue());
                }
            }
            logoffInfoPageDTO.setCreatorName(userNameMap.get(logoffCertificateDO.getCreator()));
            if(!Objects.isNull(logoffCertificateDO.getGmtCreate())){
                logoffInfoPageDTO.setGmtCreate(DateUtils.formatDateYYYMMDDHHmmss(logoffCertificateDO.getGmtCreate()));
            }
            if (logoffCollectionMap != null
                    && CollectionUtils.isNotEmpty(logoffCollectionMap.get(logoffCertificateDO.getId()))) {
                logoffInfoPageDTO.setCollectionCount(logoffCollectionMap.get(logoffCertificateDO.getId()).size());
            }
            if (StringUtils.isNotBlank(checkStatus)) {
                if (checkStatus.equals(logoffInfoPageDTO.getCheckStatus())) {
                    return logoffInfoPageDTO;
                } else {
                    return null;
                }
            }
            if (isUserChecked == 1) {
                if (logoffInfoPageDTO.getHasChecked() == YesOrNoEnum.否.getCode().byteValue()) {
                    return null;
                }
            }
            if(userNameMap.containsKey(logoffCertificateDO.getCreator())){
                logoffInfoPageDTO.setCreatorName(userNameMap.get(logoffCertificateDO.getCreator()));
            }
            if(logoffCertificateDO.getLogoffDate() != null){
                logoffInfoPageDTO.setLogoffDate(DateUtils.formatDateYYYMMDD(logoffCertificateDO.getLogoffDate()));
            }
            return logoffInfoPageDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
    private LogoffCertificateEntity convertLogoffCertificateEntity(AddLogoffAction addLogoffAction) {
        LogoffCertificateEntity entity = new LogoffCertificateEntity();
        entity.setCompanyId(addLogoffAction.getCompanyId());
        entity.setLogoffCertificateNo(addLogoffAction.getPrefixName()
                + CollectionConstant.IN_LOGOFF_CERTIFICATE_MIDDLE);
        entity.setPrefixName(addLogoffAction.getPrefixName());
        entity.setSuffixNumber(addLogoffAction.getSuffixNumber());
        entity.setLogoffDate(addLogoffAction.getLogoffDate());
        entity.setApproveNo(addLogoffAction.getApproveNo());
        entity.setDocumentId(addLogoffAction.getDocumentId());
        entity.setCreator(addLogoffAction.getCreator());
        entity.setModifier(addLogoffAction.getCreator());
        List<LogoffCollectionVO> logoffCollectionVOList = this.convertLogoffCollectionVO(addLogoffAction.getLogoffCollectionList(), entity.getLogoffCertificateNo());
        entity.setLogoffCollectionList(logoffCollectionVOList);
        entity.setAutoAudit(addLogoffAction.getAutoAudit());
        return entity;
    }
    private LogoffCertificateEntity convertLogoffCertificateEntity(EditLogoffAction editLogoffAction, String logoffCertificateNo) {
        LogoffCertificateEntity entity = new LogoffCertificateEntity();
        entity.setLogoffId(editLogoffAction.getLogoffId());
        entity.setCompanyId(editLogoffAction.getCompanyId());
        entity.setLogoffCertificateNo(logoffCertificateNo);
        entity.setPrefixName(editLogoffAction.getPrefixName());
        entity.setSuffixNumber(editLogoffAction.getSuffixNumber());
        entity.setLogoffDate(editLogoffAction.getLogoffDate());
        entity.setApproveNo(editLogoffAction.getApproveNo());
        entity.setDocumentId(editLogoffAction.getDocumentId());
        entity.setModifier(editLogoffAction.getModifier());
        List<LogoffCollectionVO> logoffCollectionVOList = this.convertLogoffCollectionVO(editLogoffAction.getLogoffCollectionList(), logoffCertificateNo);
        entity.setLogoffCollectionList(logoffCollectionVOList);
        entity.setAutoAudit(editLogoffAction.getAutoAudit());
        return entity;
    }
    private List<LogoffCollectionVO> convertLogoffCollectionVO(List<AddLogoffCollectionAction> logoffCollectionList, String logoffCertificateNo) {
        if (CollectionUtils.isEmpty(logoffCollectionList)) {
            return Lists.newArrayList();
        }
        return logoffCollectionList.stream().map(logoffCollection -> {
            LogoffCollectionVO logoffCollectionVO = new LogoffCollectionVO();
            logoffCollectionVO.setCompanyId(logoffCollection.getCompanyId());
            logoffCollectionVO.setCollectionId(logoffCollection.getCollectionId());
            logoffCollectionVO.setLogoffCertificateNo(logoffCertificateNo);
            logoffCollectionVO.setRegisterNo(logoffCollection.getRegisterNo());
            logoffCollectionVO.setClassify(logoffCollection.getClassify());
            logoffCollectionVO.setClassifyName(logoffCollection.getClassifyName());
            logoffCollectionVO.setCollectionName(logoffCollection.getCollectionName());
            logoffCollectionVO.setAge(logoffCollection.getAge());
            logoffCollectionVO.setAgeName(logoffCollection.getAgeName());
            logoffCollectionVO.setEra(logoffCollection.getEra());
            logoffCollectionVO.setCollectionCount(logoffCollection.getCollectionCount());
            logoffCollectionVO.setCountUnit(logoffCollection.getCountUnit());
            logoffCollectionVO.setCountUnitName(logoffCollection.getCountUnitName());
            logoffCollectionVO.setIdentifyLevel(logoffCollection.getIdentifyLevel());
            logoffCollectionVO.setIdentifyLevelName(logoffCollection.getIdentifyLevelName());
            logoffCollectionVO.setSituation(logoffCollection.getSituation());
            logoffCollectionVO.setReason(logoffCollection.getReason());
            logoffCollectionVO.setWhereabouts(logoffCollection.getWhereabouts());
            logoffCollectionVO.setRemark(logoffCollection.getRemark());
            logoffCollectionVO.setCreator(logoffCollection.getCreator());
            logoffCollectionVO.setModifier(logoffCollection.getModifier());
            return logoffCollectionVO;
        }).collect(Collectors.toList());
    }
}
