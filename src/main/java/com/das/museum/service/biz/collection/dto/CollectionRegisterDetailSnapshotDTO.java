package com.das.museum.service.biz.collection.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectionRegisterDetailSnapshotDTO implements Serializable {
    private static final long serialVersionUID = 9156120847986509739L;
    private Long registerInfoId;
    private Long registerInfoSnapshotId;
    private String registerNo;
    private String name;
    private Long classify;
    private String classifyDesc;
    private Long identifyLevel;
    private String identifyLevelName;
    private String era;
    private Long age;
    private String ageName;
    private String size;
    private Integer actualCount;
    private Integer collectionCount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
}
