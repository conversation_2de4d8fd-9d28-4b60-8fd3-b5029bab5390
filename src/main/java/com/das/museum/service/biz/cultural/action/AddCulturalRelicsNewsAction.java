package com.das.museum.service.biz.cultural.action;

import lombok.Data;

@Data
public class AddCulturalRelicsNewsAction {

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 资讯标题
     */
    private String newsTitle;

    /**
     * 资讯类型: 1:馆务公开, 2:新闻报道, 3:公告通知
     */
    private Byte newsType;

    /**
     * 正文内容
     */
    private String newsContent;

    /**
     * 是否发布状态: 0:否, 1:是
     */
    private Byte publishStatus;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改人
     */
    private Long modifier;

}
