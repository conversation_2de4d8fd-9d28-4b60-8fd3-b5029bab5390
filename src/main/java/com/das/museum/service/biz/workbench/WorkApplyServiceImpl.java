package com.das.museum.service.biz.workbench;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.domain.enums.CheckMainStatusEnum;
import com.das.museum.domain.enums.CheckStatusChildEnum;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.dataobjectexpand.MyCheckListDO;
import com.das.museum.infr.dataobjectexpand.MyHasCheckedListDO;
import com.das.museum.infr.mapper.*;
import com.das.museum.service.biz.collection.dto.CheckFlowDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.service.biz.workbench.action.CheckWorkApplyAction;
import com.das.museum.service.biz.workbench.action.SubmitWorkApplyAction;
import com.das.museum.service.biz.workbench.dto.FlowSetListDTO;
import com.das.museum.service.biz.workbench.dto.MyApplyFlowListDTO;
import com.das.museum.service.biz.workbench.dto.MyCheckScheduleDTO;
import com.das.museum.service.enums.YesOrNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkApplyServiceImpl implements WorkApplyService{

    @Resource
    private GztWorkRequestFlowMapper gztWorkRequestFlowMapper;

    @Resource
    private GztUserWorkRequestMapper gztUserWorkRequestMapper;

    @Resource
    private CommCheckMainMapper commCheckMainMapper;

    @Resource
    private CommCheckChildMapper commCheckChildMapper;

    @Resource
    private AccUserMapper accUserMapper;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private DocumentService documentService;

    @Override
    public List<FlowSetListDTO> flowSetList(Long companyId){
        List<FlowSetListDTO> dtoList = Lists.newArrayList();
        List<GztWorkRequestFlowDO> gztWorkRequestFlowDOList = gztWorkRequestFlowMapper.selectListByCompanyId(companyId);
        if(CollectionUtils.isEmpty(gztWorkRequestFlowDOList)){
            return dtoList;
        }
        return gztWorkRequestFlowDOList.stream().map(gztWorkRequestFlowDO -> {
            FlowSetListDTO dto = new FlowSetListDTO();
            dto.setFlowSetId(gztWorkRequestFlowDO.getId());
            dto.setApplyType(gztWorkRequestFlowDO.getApplyType());
            dto.setFlowLevel(gztWorkRequestFlowDO.getFlowLevel());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void editFlowSet(Long companyId,Long userId,Long flowSetId,Integer flowLevel){
        GztWorkRequestFlowDO gztWorkRequestFlowDO = gztWorkRequestFlowMapper.selectByCompanyIdAndId(companyId,flowSetId);
        if(Objects.isNull(gztWorkRequestFlowDO)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "该类型工作请示设置不存在！");
        }
        gztWorkRequestFlowDO.setFlowLevel(flowLevel);
        gztWorkRequestFlowDO.setModifier(userId);
        gztWorkRequestFlowMapper.updateByCompanyIdAndId(gztWorkRequestFlowDO);
    }

    @Override
    @Transactional
    public void submitApply(SubmitWorkApplyAction action){
        String belongModel = "";
        // 根据类型创建工作请示
        GztUserWorkRequestDO gztUserWorkRequestDO = new GztUserWorkRequestDO();
        gztUserWorkRequestDO.setApplyType(action.getApplyType());
        gztUserWorkRequestDO.setCompanyId(action.getCompanyId());
        gztUserWorkRequestDO.setUserId(action.getUserId());
        gztUserWorkRequestDO.setCreator(action.getUserId());
        gztUserWorkRequestDO.setModifier(action.getUserId());
        switch (action.getApplyType()){
            case 1 :
                gztUserWorkRequestDO.setLeaveTypeId(action.getLeaveTypeId());
                gztUserWorkRequestDO.setStartTime(action.getStartTime());
                gztUserWorkRequestDO.setEndTime(action.getEndTime());
                gztUserWorkRequestDO.setOutDays(action.getOutDays());
                gztUserWorkRequestDO.setLeaveReason(action.getLeaveReason());
                belongModel = CollectionBizTypeEnum.QJSQ.getCode();
                break;
            case 2 :
                gztUserWorkRequestDO.setDestination(action.getDestination());
                gztUserWorkRequestDO.setStartTime(action.getStartTime());
                gztUserWorkRequestDO.setEndTime(action.getEndTime());
                gztUserWorkRequestDO.setOutDays(action.getOutDays());
                gztUserWorkRequestDO.setItemDescription(action.getItemDescription());
                belongModel = CollectionBizTypeEnum.CCSQ.getCode();
                break;
            case 3 :
                gztUserWorkRequestDO.setDestination(action.getDestination());
                gztUserWorkRequestDO.setStartTime(action.getStartTime());
                gztUserWorkRequestDO.setEndTime(action.getEndTime());
                gztUserWorkRequestDO.setOutDays(action.getOutDays());
                gztUserWorkRequestDO.setItemDescription(action.getItemDescription());
                belongModel = CollectionBizTypeEnum.WCSQ.getCode();
                break;
            case 4 :
                gztUserWorkRequestDO.setRequestContent(action.getRequestContent());
                gztUserWorkRequestDO.setDocumentId(action.getDocumentId());
                belongModel = CollectionBizTypeEnum.CGQSSQ.getCode();
                break;
            default:
                break;
        }
        this.gztUserWorkRequestMapper.insertSelective(gztUserWorkRequestDO);

        // 生成审核流程主表
        Date date = new Date();
        Long applyId = gztUserWorkRequestDO.getId();
        CommCheckMainDO commCheckMainDO = new CommCheckMainDO();
        commCheckMainDO.setCompanyId(action.getCompanyId());

        commCheckMainDO.setBelongModel(belongModel);
        commCheckMainDO.setModelId(applyId);
        commCheckMainDO.setCheckStatus(CheckMainStatusEnum.ATD.getCode());
        commCheckMainDO.setStartManId(action.getUserId());
        commCheckMainDO.setStartManName(action.getUserName());
        commCheckMainDO.setStartTime(date);
        commCheckMainDO.setCreator(action.getUserId());
        commCheckMainDO.setModifier(action.getUserId());
        commCheckMainMapper.insertSelective(commCheckMainDO);
        Long mainId = commCheckMainDO.getId();

        // 生成审核流程子表,查询当前用户的n级上级
        GztWorkRequestFlowDO gztWorkRequestFlowDO = gztWorkRequestFlowMapper.selectByCompanyIdAndApplyType(action.getCompanyId(),action.getApplyType());
        if(Objects.isNull(gztWorkRequestFlowDO) || gztWorkRequestFlowDO.getFlowLevel() == null){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "该类型工作请示流程配置不存在！");
        }

        Long currentUserId = action.getUserId();
        for(int i=0;i<gztWorkRequestFlowDO.getFlowLevel();i++){
            AccUserDO accUserDO = this.accUserMapper.selectByUserId(action.getCompanyId(),currentUserId);
            currentUserId = accUserDO.getLeaderId();
            if(i==0 && currentUserId == null){
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "用户"+accUserDO.getRealName()+"不存在直属上级！");
            }else if(i>0  && currentUserId == null){
                break;
            }

            CommCheckChildDO commCheckChildDO = new CommCheckChildDO();
            commCheckChildDO.setMainId(mainId);
            commCheckChildDO.setFlowName("领导审核");
            commCheckChildDO.setNodeIndex(i+1);
            if(i == gztWorkRequestFlowDO.getFlowLevel() -1){
                commCheckChildDO.setEndNode(YesOrNoEnum.是.getCode());
            }else{
                commCheckChildDO.setEndNode(YesOrNoEnum.否.getCode());
            }
            if(i == 0){
                commCheckChildDO.setCheckStatus(CheckStatusChildEnum.ATD.getCode());
                commCheckChildDO.setArriveTime(date);
            }else{
                commCheckChildDO.setCheckStatus(CheckStatusChildEnum.NAT.getCode());
            }
            commCheckChildDO.setNeedManId(accUserDO.getLeaderId().toString());
            AccUserDO leaderDO = this.accUserMapper.selectByUserId(action.getCompanyId(),accUserDO.getLeaderId());
            if(Objects.nonNull(leaderDO)){
                commCheckChildDO.setNeedManName(leaderDO.getUserName());
                if(leaderDO.getLeaderId() == null){
                    commCheckChildDO.setEndNode(YesOrNoEnum.是.getCode());
                }
            }else {
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "当前用户直属上级"+accUserDO.getLeaderId()+"不存在！");
            }
            commCheckChildDO.setCreator(action.getUserId());
            commCheckChildDO.setModifier(action.getUserId());
            commCheckChildMapper.insertSelective(commCheckChildDO);
        }
    }

    @Override
    public void checkApply(CheckWorkApplyAction action){
        if(!CheckStatusChildEnum.ATP.getCode().equals(action.getCheckStatus()) && !CheckStatusChildEnum.NATP.getCode().equals(action.getCheckStatus())){
            throw new CommonException(CommonErrorCodeEnum.PARAM_ERROR, "审核状态错误！");
        }
        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByModelAndId(action.getCompanyId(),action.getBelongModel(),action.getModelId());
        if(Objects.isNull(commCheckMainDO)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "审核流程不存在！");
        }
        if(!CheckMainStatusEnum.ATD.getCode().equals(commCheckMainDO.getCheckStatus())){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "当前状态为"+CheckMainStatusEnum.fromCode(commCheckMainDO.getCheckStatus()).getDesc()+"，不可进行审核！");
        }
        CommCheckChildDO currentNodeDO = this.commCheckChildMapper.selectByMainIdAndStatus(commCheckMainDO.getId(),CheckStatusChildEnum.ATD.getCode());
        if(Objects.isNull(currentNodeDO)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "审核流程不存在！");
        }
        Date date = new Date();
        currentNodeDO.setCheckStatus(action.getCheckStatus());
        currentNodeDO.setCheckManId(action.getUserId());
        currentNodeDO.setCheckManName(action.getUserMame());
        currentNodeDO.setCheckTime(date);
        currentNodeDO.setCheckOpinion("");
        currentNodeDO.setModifier(action.getUserId());
        this.commCheckChildMapper.updateByPrimaryKeySelective(currentNodeDO);
        if(CheckStatusChildEnum.ATP.getCode().equals(action.getCheckStatus())) {
            if(currentNodeDO.getEndNode() == 0){
                CommCheckChildDO nextNodeDO = this.commCheckChildMapper.selectByMainIdAndNodeIndex(commCheckMainDO.getId(),currentNodeDO.getNodeIndex()+1);
                nextNodeDO.setCheckStatus(CheckStatusChildEnum.ATD.getCode());
                nextNodeDO.setArriveTime(date);
                this.commCheckChildMapper.updateByPrimaryKeySelective(nextNodeDO);
            }else{
                commCheckMainDO.setCheckStatus(CheckMainStatusEnum.ATP.getCode());
                this.commCheckMainMapper.updateByPrimaryKeySelective(commCheckMainDO);
            }
        }else{
            commCheckMainDO.setCheckStatus(CheckMainStatusEnum.NATP.getCode());
            this.commCheckMainMapper.updateByPrimaryKeySelective(commCheckMainDO);
        }
    }

    @Override
    public List<MyApplyFlowListDTO> myFlowList(Long companyId, Long userId, Integer type, Integer applyType, Byte orderBy, String checkStatus){
        List<MyApplyFlowListDTO> dtoList = Lists.newArrayList();
        switch (type){
            case 1:
                dtoList = myCheckFlowList(companyId, userId,applyType,orderBy);
                break;
            case 2:
                dtoList = myStartFlowList(companyId, userId,applyType,orderBy,checkStatus);
                break;
            case 3:
                dtoList = myHasCheckedFlowList(companyId, userId,applyType,orderBy,checkStatus);
                break;
        }

        return dtoList;
    }

    /**
     * 待我处理的请示列表
     * @param companyId
     * @param userId
     * @param applyType
     * @param orderBy
     * @return
     */
    public List<MyApplyFlowListDTO> myCheckFlowList(Long companyId, Long userId,Integer applyType, Byte orderBy){
        List<MyApplyFlowListDTO> dtoList = Lists.newArrayList();
        String belongModel;
        if(applyType == 1){
            belongModel = CollectionBizTypeEnum.QJSQ.getCode();
        }else if(applyType == 2){
            belongModel = CollectionBizTypeEnum.CCSQ.getCode();
        }else if(applyType == 3){
            belongModel = CollectionBizTypeEnum.WCSQ.getCode();
        }else{
            belongModel = CollectionBizTypeEnum.CGQSSQ.getCode();
        }
        List<MyCheckListDO> myCheckListDOList = this.commCheckChildMapper.queryMyCheckListByModel(userId, CheckStatusChildEnum.ATD.getCode(),belongModel,orderBy);
        if (CollectionUtils.isEmpty(myCheckListDOList)) {
            return dtoList;
        }

        List<Long> modelIds = myCheckListDOList.stream().map(MyCheckListDO::getModelId).collect(Collectors.toList());
        List<GztUserWorkRequestDO> gztUserWorkRequestDOList = gztUserWorkRequestMapper.selectListByCompanyIdAndId(companyId,modelIds);
        Map<Long,GztUserWorkRequestDO> workRequestMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(gztUserWorkRequestDOList)){
            for(GztUserWorkRequestDO gztUserWorkRequestDO : gztUserWorkRequestDOList){
                workRequestMap.put(gztUserWorkRequestDO.getId(),gztUserWorkRequestDO);
            }
        }

        List<Long> leaveTypeIds = gztUserWorkRequestDOList.stream().map(GztUserWorkRequestDO::getLeaveTypeId).collect(Collectors.toList());
        Map<Long,CommClassificationDO> classificationMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(leaveTypeIds)) {
            List<CommClassificationDO> commClassificationDOList = this.commClassificationMapper.listByIds(companyId,leaveTypeIds);
            if (!CollectionUtils.isEmpty(commClassificationDOList)) {
                for (CommClassificationDO commClassificationDO : commClassificationDOList) {
                    classificationMap.put(commClassificationDO.getId(), commClassificationDO);
                }
            }
        }

        return myCheckListDOList.stream().map(myCheckListDO -> {
            MyApplyFlowListDTO dto = new MyApplyFlowListDTO();
            dto.setBelongModel(myCheckListDO.getBelongModel());
            dto.setApplyType(applyType);
            dto.setCheckStatus(myCheckListDO.getCheckStatus());
            dto.setModelId(myCheckListDO.getModelId());
            dto.setMainId(myCheckListDO.getMainId());
            dto.setStartFlowTime(DateUtils.formatDateYYYMMDDHHmmss(myCheckListDO.getStartTime()));
            dto.setStartManName(myCheckListDO.getStartManName());
            if(workRequestMap.containsKey(myCheckListDO.getModelId())){
                GztUserWorkRequestDO gztUserWorkRequestDO = workRequestMap.get(myCheckListDO.getModelId());
                dto.setStartTime(gztUserWorkRequestDO.getStartTime());
                dto.setEndTime(gztUserWorkRequestDO.getEndTime());
                if(gztUserWorkRequestDO.getOutDays() != null) {
                    dto.setOutDays(gztUserWorkRequestDO.getOutDays());
                }
                dto.setDestination(gztUserWorkRequestDO.getDestination());
                dto.setItemDescription(gztUserWorkRequestDO.getItemDescription());
                dto.setLeaveTypeId(gztUserWorkRequestDO.getLeaveTypeId());
                if(classificationMap.containsKey(gztUserWorkRequestDO.getLeaveTypeId())){
                    dto.setLeaveTypeName(classificationMap.get(gztUserWorkRequestDO.getLeaveTypeId()).getName());
                }
                dto.setLeaveReason(gztUserWorkRequestDO.getLeaveReason());
                dto.setRequestContent(gztUserWorkRequestDO.getRequestContent());
                dto.setDocumentId(gztUserWorkRequestDO.getDocumentId());
                if(StringUtils.isNotBlank(gztUserWorkRequestDO.getDocumentId())){
                    List<Long> documentIds = Arrays.stream(gztUserWorkRequestDO.getDocumentId().split(",")).map(Long::parseLong).collect(Collectors.toList());
                    List<CommonDocumentDTO> documentDTOList = documentService.listByDocIds(companyId,documentIds);
                    dto.setDocumentList(documentDTOList);
                }
            }
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 我发起的请示列表
     * @param companyId
     * @param userId
     * @param applyType
     * @param orderBy
     * @return
     */
    public List<MyApplyFlowListDTO> myStartFlowList(Long companyId, Long userId,Integer applyType, Byte orderBy,String checkStatus){
        List<MyApplyFlowListDTO> dtoList = Lists.newArrayList();
        String belongModel;
        if(applyType == 1){
            belongModel = CollectionBizTypeEnum.QJSQ.getCode();
        }else if(applyType == 2){
            belongModel = CollectionBizTypeEnum.CCSQ.getCode();
        }else if(applyType == 3){
            belongModel = CollectionBizTypeEnum.WCSQ.getCode();
        }else{
            belongModel = CollectionBizTypeEnum.CGQSSQ.getCode();
        }
        List<String> belongModels = Lists.newArrayList();
        belongModels.add(belongModel);
        List<CommCheckMainDO> myStartListDOList = this.commCheckMainMapper.queryMyStartList(companyId,userId,checkStatus,orderBy,belongModels);
        if (CollectionUtils.isEmpty(myStartListDOList)) {
            return dtoList;
        }

        List<Long> modelIds = myStartListDOList.stream().map(CommCheckMainDO::getModelId).collect(Collectors.toList());
        List<GztUserWorkRequestDO> gztUserWorkRequestDOList = gztUserWorkRequestMapper.selectListByCompanyIdAndId(companyId,modelIds);
        Map<Long,GztUserWorkRequestDO> workRequestMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(gztUserWorkRequestDOList)){
            for(GztUserWorkRequestDO gztUserWorkRequestDO : gztUserWorkRequestDOList){
                workRequestMap.put(gztUserWorkRequestDO.getId(),gztUserWorkRequestDO);
            }
        }

        List<Long> leaveTypeIds = gztUserWorkRequestDOList.stream().map(GztUserWorkRequestDO::getLeaveTypeId).collect(Collectors.toList());
        Map<Long,CommClassificationDO> classificationMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(leaveTypeIds)) {
            List<CommClassificationDO> commClassificationDOList = this.commClassificationMapper.listByIds(companyId,leaveTypeIds);
            if (!CollectionUtils.isEmpty(commClassificationDOList)) {
                for (CommClassificationDO commClassificationDO : commClassificationDOList) {
                    classificationMap.put(commClassificationDO.getId(), commClassificationDO);
                }
            }
        }

        return myStartListDOList.stream().map(commCheckMainDO -> {
            MyApplyFlowListDTO dto = new MyApplyFlowListDTO();
            dto.setBelongModel(commCheckMainDO.getBelongModel());
            dto.setApplyType(applyType);
            dto.setCheckStatus(commCheckMainDO.getCheckStatus());
            dto.setModelId(commCheckMainDO.getModelId());
            dto.setMainId(commCheckMainDO.getId());
            dto.setStartFlowTime(DateUtils.formatDateYYYMMDDHHmmss(commCheckMainDO.getStartTime()));
            dto.setStartManName(commCheckMainDO.getStartManName());
            if(workRequestMap.containsKey(commCheckMainDO.getModelId())){
                GztUserWorkRequestDO gztUserWorkRequestDO = workRequestMap.get(commCheckMainDO.getModelId());
                dto.setStartTime(gztUserWorkRequestDO.getStartTime());
                dto.setEndTime(gztUserWorkRequestDO.getEndTime());
                if(gztUserWorkRequestDO.getOutDays() != null){
                    dto.setOutDays(gztUserWorkRequestDO.getOutDays());
                }
                dto.setDestination(gztUserWorkRequestDO.getDestination());
                dto.setItemDescription(gztUserWorkRequestDO.getItemDescription());
                dto.setLeaveTypeId(gztUserWorkRequestDO.getLeaveTypeId());
                if(classificationMap.containsKey(gztUserWorkRequestDO.getLeaveTypeId())){
                    dto.setLeaveTypeName(classificationMap.get(gztUserWorkRequestDO.getLeaveTypeId()).getName());
                }
                dto.setLeaveReason(gztUserWorkRequestDO.getLeaveReason());
                dto.setRequestContent(gztUserWorkRequestDO.getRequestContent());
                dto.setDocumentId(gztUserWorkRequestDO.getDocumentId());
                if(StringUtils.isNotBlank(gztUserWorkRequestDO.getDocumentId())){
                    List<Long> documentIds = Arrays.stream(gztUserWorkRequestDO.getDocumentId().split(",")).map(Long::parseLong).collect(Collectors.toList());
                    List<CommonDocumentDTO> documentDTOList = documentService.listByDocIds(companyId,documentIds);
                    dto.setDocumentList(documentDTOList);
                }
            }
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 我已处理的请示列表
     * @param companyId
     * @param userId
     * @param applyType
     * @param orderBy
     * @return
     */
    public List<MyApplyFlowListDTO> myHasCheckedFlowList(Long companyId, Long userId,Integer applyType, Byte orderBy,String checkStatus){
        List<MyApplyFlowListDTO> dtoList = Lists.newArrayList();
        String belongModel;
        if(applyType == 1){
            belongModel = CollectionBizTypeEnum.QJSQ.getCode();
        }else if(applyType == 2){
            belongModel = CollectionBizTypeEnum.CCSQ.getCode();
        }else if(applyType == 3){
            belongModel = CollectionBizTypeEnum.WCSQ.getCode();
        }else{
            belongModel = CollectionBizTypeEnum.CGQSSQ.getCode();
        }
        List<String> belongModels = Lists.newArrayList();
        belongModels.add(belongModel);
        List<MyHasCheckedListDO> myCheckListDOList = this.commCheckChildMapper.queryMyHasCheckedListByModel(userId,checkStatus,belongModels,orderBy);
        if (CollectionUtils.isEmpty(myCheckListDOList)) {
            return dtoList;
        }

        List<Long> modelIds = myCheckListDOList.stream().map(MyHasCheckedListDO::getModelId).collect(Collectors.toList());
        List<GztUserWorkRequestDO> gztUserWorkRequestDOList = gztUserWorkRequestMapper.selectListByCompanyIdAndId(companyId,modelIds);
        Map<Long,GztUserWorkRequestDO> workRequestMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(gztUserWorkRequestDOList)){
            for(GztUserWorkRequestDO gztUserWorkRequestDO : gztUserWorkRequestDOList){
                workRequestMap.put(gztUserWorkRequestDO.getId(),gztUserWorkRequestDO);
            }
        }

        List<Long> leaveTypeIds = gztUserWorkRequestDOList.stream().map(GztUserWorkRequestDO::getLeaveTypeId).collect(Collectors.toList());
        Map<Long,CommClassificationDO> classificationMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(leaveTypeIds)) {
            List<CommClassificationDO> commClassificationDOList = this.commClassificationMapper.listByIds(companyId,leaveTypeIds);
            if (!CollectionUtils.isEmpty(commClassificationDOList)) {
                for (CommClassificationDO commClassificationDO : commClassificationDOList) {
                    classificationMap.put(commClassificationDO.getId(), commClassificationDO);
                }
            }
        }

        return myCheckListDOList.stream().map(myCheckListDO -> {
            MyApplyFlowListDTO dto = new MyApplyFlowListDTO();
            dto.setBelongModel(myCheckListDO.getBelongModel());
            dto.setApplyType(applyType);
            dto.setCheckStatus(myCheckListDO.getCheckStatus());
            dto.setModelId(myCheckListDO.getModelId());
            dto.setMainId(myCheckListDO.getMainId());
            dto.setStartFlowTime(DateUtils.formatDateYYYMMDDHHmmss(myCheckListDO.getStartTime()));
            dto.setStartManName(myCheckListDO.getStartManName());
            if(workRequestMap.containsKey(myCheckListDO.getModelId())){
                GztUserWorkRequestDO gztUserWorkRequestDO = workRequestMap.get(myCheckListDO.getModelId());
                dto.setStartTime(gztUserWorkRequestDO.getStartTime());
                dto.setEndTime(gztUserWorkRequestDO.getEndTime());
                if(gztUserWorkRequestDO.getOutDays() != null){
                    dto.setOutDays(gztUserWorkRequestDO.getOutDays());
                }
                dto.setDestination(gztUserWorkRequestDO.getDestination());
                dto.setItemDescription(gztUserWorkRequestDO.getItemDescription());
                dto.setLeaveTypeId(gztUserWorkRequestDO.getLeaveTypeId());
                if(classificationMap.containsKey(gztUserWorkRequestDO.getLeaveTypeId())){
                    dto.setLeaveTypeName(classificationMap.get(gztUserWorkRequestDO.getLeaveTypeId()).getName());
                }
                dto.setLeaveReason(gztUserWorkRequestDO.getLeaveReason());
                dto.setRequestContent(gztUserWorkRequestDO.getRequestContent());
                dto.setDocumentId(gztUserWorkRequestDO.getDocumentId());
                if(StringUtils.isNotBlank(gztUserWorkRequestDO.getDocumentId())){
                    List<Long> documentIds = Arrays.stream(gztUserWorkRequestDO.getDocumentId().split(",")).map(Long::parseLong).collect(Collectors.toList());
                    List<CommonDocumentDTO> documentDTOList = documentService.listByDocIds(companyId,documentIds);
                    dto.setDocumentList(documentDTOList);
                }
            }
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<MyCheckScheduleDTO> myCheckSchedule(Long companyId, Long mainId,Long userId){
        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByCompanyIdAndId(companyId,mainId);
        if(Objects.isNull(commCheckMainDO)){
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "审核流程不存在！");
        }

        List<MyCheckScheduleDTO> dtoList = Lists.newArrayList();
        List<CommCheckChildDO> doList = this.commCheckChildMapper.selectCheckListByMainId(mainId);
        if(CollectionUtils.isEmpty(doList)){
            return dtoList;
        }

        List<Long> needManIdList = doList.stream().map(CommCheckChildDO::getNeedManId).map(Long::parseLong).collect(Collectors.toList());
        List<AccUserDO> accUserList = accUserMapper.listByIds(companyId,needManIdList);

        Map<String,String> avatarUrlMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(accUserList)){
            List<Long> documentIds = accUserList.stream().map(AccUserDO::getAvatar).collect(Collectors.toList());
            List<CommonDocumentDTO> commonDocumentDTOList = documentService.listByDocIds(companyId,documentIds);
            Map<Long,CommonDocumentDTO> documentDTOMap = new HashMap<>();
            commonDocumentDTOList.forEach(k -> documentDTOMap.put(k.getDocumentId(),k));

            accUserList.forEach(k -> {
                if(documentDTOMap.containsKey(k.getAvatar())){
                    avatarUrlMap.put(k.getId().toString(),documentDTOMap.get(k.getAvatar()).getUrl());
                }
            });
        }

        dtoList = doList.stream().map(commCheckChildDO -> {
            MyCheckScheduleDTO myCheckScheduleDTO = new MyCheckScheduleDTO();
            myCheckScheduleDTO.setNodeIndex(commCheckChildDO.getNodeIndex());
            myCheckScheduleDTO.setCheckStatus(commCheckChildDO.getCheckStatus());
            myCheckScheduleDTO.setCheckManName(commCheckChildDO.getNeedManName());
            if(commCheckChildDO.getCheckTime() != null){
                myCheckScheduleDTO.setCheckTime(DateUtils.formatDateYYYMMDDHHmmss(commCheckChildDO.getCheckTime()));
            }
            if(StringUtils.isNotBlank(commCheckChildDO.getCheckStatus())){
                myCheckScheduleDTO.setCheckContent(CheckStatusChildEnum.fromCode(commCheckChildDO.getCheckStatus()).getDesc());
            }
            if(avatarUrlMap.containsKey(commCheckChildDO.getNeedManId())){
                myCheckScheduleDTO.setAvatarUrl(avatarUrlMap.get(commCheckChildDO.getNeedManId()));
            }
            return myCheckScheduleDTO;
        }).collect(Collectors.toList());
        // 手动添加第一个节点
        MyCheckScheduleDTO dto = new MyCheckScheduleDTO();
        dto.setNodeIndex(0);
        AccUserDO accUserDO = accUserMapper.selectByUserId(companyId,userId);
        if(Objects.nonNull(accUserDO) && accUserDO.getAvatar() != null){
            CommonDocumentDTO documentDTO = documentService.queryByDocId(companyId,accUserDO.getAvatar());
            if(Objects.nonNull(documentDTO)){
                dto.setAvatarUrl(documentDTO.getUrl());
            }
        }
        dto.setCheckManName(commCheckMainDO.getStartManName());
        dto.setCheckStatus(CheckStatusChildEnum.ATP.getCode());
        dto.setCheckContent("发起申请");
        dto.setCheckTime(DateUtils.formatDateYYYMMDDHHmmss(commCheckMainDO.getStartTime()));
        dtoList.add(dto);
        return dtoList.stream().sorted(Comparator.comparing(MyCheckScheduleDTO::getNodeIndex)).collect(Collectors.toList());
    }

}
