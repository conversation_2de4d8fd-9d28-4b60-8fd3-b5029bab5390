package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AppendageQueryPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = 8719894829926565525L;
    private Long companyId;
    private Long collectionId;
    private String sortBy;
}
