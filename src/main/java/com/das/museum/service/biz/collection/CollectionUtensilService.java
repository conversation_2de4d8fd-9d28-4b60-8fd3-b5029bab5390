package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionUtensilAction;
import com.das.museum.service.biz.collection.action.EditCollectionUtensilAction;
import com.das.museum.service.biz.collection.action.UtensilQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionUtensilDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionUtensilPageDTO;
import com.das.museum.common.bo.SimplePageInfo;

public interface CollectionUtensilService {
    Long addCollectionUtensil(AddCollectionUtensilAction action);
    void editCollectionUtensil(EditCollectionUtensilAction action);
    void delByCollectionUtensilId(Long companyId, Long collectionUtensilId);
    SimplePageInfo<CollectionUtensilPageDTO> queryPageByCondition(UtensilQueryPageByConditionAction action);
    CollectionUtensilDetailDTO queryByCollectionUtensilId(Long companyId, Long collectionUtensilId);
}
