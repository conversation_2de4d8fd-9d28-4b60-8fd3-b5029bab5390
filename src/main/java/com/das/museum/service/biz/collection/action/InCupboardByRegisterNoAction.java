package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;

@Data
public class InCupboardByRegisterNoAction implements Serializable {
    private static final long serialVersionUID = -3469481004941613661L;
    private Long companyId;
    private Long registerInfoId;
    private Long warehouseId;
    private Long cupboardId;
    private Long floorId;
    private Long drawerId;
    private Long columnId;
    private Long numberId;
    private Long modifier;
}
