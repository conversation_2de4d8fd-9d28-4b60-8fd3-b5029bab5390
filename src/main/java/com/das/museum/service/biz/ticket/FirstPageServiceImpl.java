package com.das.museum.service.biz.ticket;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.TimeFormatConstant;
import com.das.museum.infr.dataobject.TktOrderBaseDO;
import com.das.museum.infr.dataobject.TktPersonalOrderDetailDO;
import com.das.museum.infr.dataobject.TktTeamOrderDetailDO;
import com.das.museum.infr.dataobject.TktWechatVenueDO;
import com.das.museum.infr.dataobjectexpand.ListGroupByProvinceDO;
import com.das.museum.infr.dataobjectexpand.OrderStatisticsByNumDO;
import com.das.museum.infr.mapper.TktOrderBaseMapper;
import com.das.museum.infr.mapper.TktPersonalOrderDetailMapper;
import com.das.museum.infr.mapper.TktTeamOrderDetailMapper;
import com.das.museum.infr.mapper.TktWechatVenueMapper;
import com.das.museum.service.biz.ticket.dto.*;
import com.das.museum.service.enums.ChannelTypeEnum;
import com.das.museum.service.enums.OrderTypeEnum;
import com.das.museum.service.enums.ProvinceCodeEnum;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @Author: shaochengwei
 * @Date: 2023-01-05
 */
@Service
public class FirstPageServiceImpl implements FirstPageService, InitializingBean {

    private final ConcurrentHashMap<Integer, List<HolidayBO>> holidaysMap = new ConcurrentHashMap<>(16);

    @Resource
    private Environment environment;

    @Resource
    private WechatVenueService wechatVenueService;

    @Override
    public void afterPropertiesSet() throws Exception {
        holidayInit();
    }

    public void holidayInit(){
        // 2022
        List<HolidayBO> l2022 = Lists.newArrayList();
        HolidayBO newYear2022 = new HolidayBO();
        newYear2022.setName("元旦");
        newYear2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.newYearStart")));
        newYear2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.newYearEnd")));
        l2022.add(newYear2022);

        HolidayBO chineseNewYear2022 = new HolidayBO();
        chineseNewYear2022.setName("春节");
        chineseNewYear2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chineseNewYearStart")));
        chineseNewYear2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chineseNewYearEnd")));
        l2022.add(chineseNewYear2022);

        HolidayBO chingMing2022 = new HolidayBO();
        chingMing2022.setName("清明节");
        chingMing2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chingMingStart")));
        chingMing2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chingMingEnd")));
        l2022.add(chingMing2022);

        HolidayBO labor2022 = new HolidayBO();
        labor2022.setName("劳动节");
        labor2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.laborStart")));
        labor2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.laborEnd")));
        l2022.add(labor2022);

        HolidayBO dragonBoat2022 = new HolidayBO();
        dragonBoat2022.setName("劳动节");
        dragonBoat2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.dragonBoatStart")));
        dragonBoat2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.dragonBoatEnd")));
        l2022.add(dragonBoat2022);

        HolidayBO midAutumn2022 = new HolidayBO();
        midAutumn2022.setName("中秋节");
        midAutumn2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.midAutumnStart")));
        midAutumn2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.midAutumnEnd")));
        l2022.add(midAutumn2022);

        HolidayBO national2022 = new HolidayBO();
        national2022.setName("国庆节");
        national2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.nationalStart")));
        national2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.nationalEnd")));
        l2022.add(national2022);
        holidaysMap.put(2022, l2022);
        // 2023
        List<HolidayBO> l2023 = Lists.newArrayList();
        HolidayBO newYear2023 = new HolidayBO();
        newYear2023.setName("元旦");
        newYear2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.newYearStart")));
        newYear2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.newYearEnd")));
        l2023.add(newYear2023);

        HolidayBO chineseNewYear2023 = new HolidayBO();
        chineseNewYear2023.setName("春节");
        chineseNewYear2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chineseNewYearStart")));
        chineseNewYear2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chineseNewYearEnd")));
        l2023.add(chineseNewYear2023);

        HolidayBO chingMing2023 = new HolidayBO();
        chingMing2023.setName("清明节");
        chingMing2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chingMingStart")));
        chingMing2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chingMingEnd")));
        l2023.add(chingMing2023);

        HolidayBO labor2023 = new HolidayBO();
        labor2023.setName("劳动节");
        labor2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.laborStart")));
        labor2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.laborEnd")));
        l2023.add(labor2023);

        HolidayBO dragonBoat2023 = new HolidayBO();
        dragonBoat2023.setName("端午节");
        dragonBoat2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.dragonBoatStart")));
        dragonBoat2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.dragonBoatEnd")));
        l2023.add(dragonBoat2023);

        HolidayBO nationalPlus2023 = new HolidayBO();
        nationalPlus2023.setName("中秋节/国庆节");
        nationalPlus2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.nationalPlusStart")));
        nationalPlus2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.nationalPlusEnd")));
        l2023.add(nationalPlus2023);
        holidaysMap.put(2023, l2023);
    }

    @Data
    static class HolidayBO {
        private String name;

        private Date startTime;

        private Date endTime;
    }

    @Resource
    private TktOrderBaseMapper tktOrderBaseMapper;

    @Resource
    private TktPersonalOrderDetailMapper tktPersonalOrderDetailMapper;

    @Resource
    private TktTeamOrderDetailMapper tktTeamOrderDetailMapper;

    @Resource
    private TktWechatVenueMapper tktWechatVenueMapper;

    @Override
    public VisitorStatisticsDTO visitorStatistics(Long companyId, Integer statisticsWay, String startTime, String endTime){
        VisitorStatisticsDTO visitorStatisticsDTO = new VisitorStatisticsDTO();
        Date timeStart = null;
        Date timeEnd = null;
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = new Date();
        }else if(statisticsWay == 1){
            //本周
            timeStart = DateUtils.getTimesWeekMorning();
            timeEnd = new Date();
        }else if(statisticsWay == 2){
            //本月
            timeStart = DateUtils.getTimesMonthMorning();
            timeEnd = new Date();
        }else if(statisticsWay == 3){
            //本年
            timeStart = DateUtils.getTimesYearMorning();
            timeEnd = new Date();
        }else if(statisticsWay == 4){
            //全部
        }else if(statisticsWay == 6){
            //昨日
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(new Date(),-1,1));
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(new Date(),-1,2));
        }else{
            if(startTime == null){
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "startTime 不能为空！");
            }
            if(endTime == null){
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "endTime 不能为空！");
            }
            //自定义时间
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        }

        //预约信息
        List<TktOrderBaseDO> personalOrderList =  tktOrderBaseMapper.selectListByTime(companyId,timeStart,timeEnd,OrderTypeEnum.个人预约.getCode().byteValue());
        List<TktOrderBaseDO> teamOrderList =  tktOrderBaseMapper.selectListByTime(companyId,timeStart,timeEnd,OrderTypeEnum.团队预约.getCode().byteValue());
        //个人预约
        List<TktPersonalOrderDetailDO> personalList =  tktPersonalOrderDetailMapper.selectListByTime(companyId,timeStart,timeEnd);
        //团队预约
        List<TktTeamOrderDetailDO> teamList = tktTeamOrderDetailMapper.listTeamByTime(companyId, null, timeStart,timeEnd);

        // 微信预约总人数（观众总览）
        long allTotalNum = 0L;
        // 入馆人数（观众总览）
        long allInNum = 0L;
        // 未核销（观众总览）
        long allOutNum = 0L;
        // 微信预约（个人预约）
        long personalTotalNum = 0L;
        // 入馆人数（个人预约）
        long personalInNum = 0L;
        // 未核销（个人预约）
        long personalOutNum = 0L;
        // 微信预约团队个数（团队预约）
        int teamTotalNum = 0;
        // 微信预约人数（团队预约）
        long TeamManTotalNum = 0L;
        // 入馆团队个数（团队预约）
        long teamInNum = 0L;
        // 入馆团队人数（团队预约）
        long teamManInNum = 0L;
        // 未核销团队个数（团队预约）
        long teamOutNum = 0L;
        // 未核销团队人数（团队预约）
        long teamManOutNum = 0L;

        //个人预约总人数
        Optional<Long> personalNum = personalOrderList.stream().map(TktOrderBaseDO::getTotalPeopleCount).reduce(Long::sum);
        if(personalNum.isPresent()){
            personalTotalNum = personalNum.get();
        }
        //个人预约核销总人数
        personalInNum = personalList.stream().filter(tktPersonalOrderDetailDO -> tktPersonalOrderDetailDO.getStatus() == 2).count();
        personalOutNum = personalTotalNum - personalInNum;

        teamTotalNum = teamOrderList.size();
        //团队预约总人数
        Optional<Long> TeamManNum = teamOrderList.stream().map(TktOrderBaseDO::getTotalPeopleCount).reduce(Long::sum);
        if(TeamManNum.isPresent()){
            TeamManTotalNum = TeamManNum.get();
        }

        //团队已核销个数
        teamInNum = teamOrderList.stream().filter(tktOrderBaseDO -> tktOrderBaseDO.getStatus() ==  2).count();
        //团队已核销总人数
        teamManInNum = teamOrderList.stream().filter(tktOrderBaseDO -> tktOrderBaseDO.getStatus() ==  2).mapToLong(TktOrderBaseDO::getTotalPeopleCount).sum();

        teamOutNum = teamTotalNum - teamInNum;
        teamManOutNum = TeamManTotalNum - teamManInNum;

        allTotalNum = personalTotalNum + TeamManTotalNum;
        allInNum = personalInNum + teamManInNum;
        allOutNum = personalOutNum + teamManOutNum;

        visitorStatisticsDTO.setAllTotalNum(allTotalNum);
        visitorStatisticsDTO.setAllInNum(allInNum);
        visitorStatisticsDTO.setAllOutNum(allOutNum);
        visitorStatisticsDTO.setPersonalTotalNum(personalTotalNum);
        visitorStatisticsDTO.setPersonalInNum(personalInNum);
        visitorStatisticsDTO.setPersonalOutNum(personalOutNum);
        visitorStatisticsDTO.setTeamTotalNum(teamTotalNum);
        visitorStatisticsDTO.setTeamManTotalNum(TeamManTotalNum);
        visitorStatisticsDTO.setTeamInNum(teamInNum);
        visitorStatisticsDTO.setTeamManInNum(teamManInNum);
        visitorStatisticsDTO.setTeamOutNum(teamOutNum);
        visitorStatisticsDTO.setTeamManOutNum(teamManOutNum);
        Date ringRatioTimeStart;
        Date ringRatioTimeEnd;
        Date yoyTimeStart = null;
        Date yoyTimeEnd = null;
        if(statisticsWay == 0){
            // 本日
            //昨日同期
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeEnd = calYoy.getTime();

            // 去年同日同期
            Calendar cal = Calendar.getInstance();
            cal.setTime(timeStart);
            cal.add(Calendar.YEAR, -1);
            ringRatioTimeStart = cal.getTime();
            Calendar calEnd = Calendar.getInstance();
            calEnd.setTime(timeEnd);
            calEnd.add(Calendar.YEAR, -1);
            ringRatioTimeEnd = calEnd.getTime();
        }else if(statisticsWay == 1){
            // 本周
            //上周同期
            Calendar cal = Calendar.getInstance();
            cal.setTime(timeStart);
            cal.add(Calendar.DAY_OF_WEEK, -7);
            ringRatioTimeStart = cal.getTime();
            Calendar call = Calendar.getInstance();
            call.setTime(timeEnd);
            call.add(Calendar.DAY_OF_WEEK, -7);
            ringRatioTimeEnd = call.getTime();
        }else if(statisticsWay == 2){
            // 本月
            //上月同期
            Calendar call = Calendar.getInstance();
            call.setTime(timeStart);
            call.add(Calendar.MONTH, -1);
            yoyTimeStart = call.getTime();
            Calendar cal = Calendar.getInstance();
            cal.setTime(timeEnd);
            cal.add(Calendar.MONTH, -1);
            yoyTimeEnd = cal.getTime();

            Calendar callYoy = Calendar.getInstance();
            callYoy.setTime(yoyTimeStart);
            callYoy.add(Calendar.YEAR, -1);
            ringRatioTimeStart = callYoy.getTime();
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(yoyTimeEnd);
            calYoy.add(Calendar.YEAR, -1);
            ringRatioTimeEnd = calYoy.getTime();
        }else if(statisticsWay == 3){
            //本年
            //去年
            Calendar temp = Calendar.getInstance();
            temp.setTime(timeStart);
            temp.add(Calendar.YEAR, -1);
            yoyTimeStart = temp.getTime();
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.YEAR, -1);
            yoyTimeEnd = calYoy.getTime();

            ringRatioTimeStart = yoyTimeStart;
            ringRatioTimeEnd = yoyTimeEnd;
        }else if(statisticsWay == 4){
            return visitorStatisticsDTO;
        }else if(statisticsWay == 6){
            //昨日
            //前日
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeEnd = calYoy.getTime();

            Calendar cal = Calendar.getInstance();
            cal.setTime(timeStart);
            cal.add(Calendar.YEAR, -1);
            ringRatioTimeStart = cal.getTime();
            Calendar calEnd = Calendar.getInstance();
            calEnd.setTime(timeEnd);
            calEnd.add(Calendar.YEAR, -1);
            ringRatioTimeEnd = calEnd.getTime();
        } else {
            return visitorStatisticsDTO;
        }

        // 同比计算
        {
            //预约信息
            List<TktOrderBaseDO> personalOrderTempList =  tktOrderBaseMapper.selectListByTime(companyId,ringRatioTimeStart,ringRatioTimeEnd,OrderTypeEnum.个人预约.getCode().byteValue());
            List<TktOrderBaseDO> teamOrderTempList =  tktOrderBaseMapper.selectListByTime(companyId,ringRatioTimeStart,ringRatioTimeEnd,OrderTypeEnum.团队预约.getCode().byteValue());
            //个人预约
            List<TktPersonalOrderDetailDO> personalTempList =  tktPersonalOrderDetailMapper.selectListByTime(companyId,ringRatioTimeStart,ringRatioTimeEnd);
            // 微信预约总人数（观众总览）
            long allTotalNumTemp = 0L;
            // 入馆人数（观众总览）
            long allInNumTemp = 0L;
            // 未核销（观众总览）
            long allOutNumTemp = 0L;
            // 微信预约（个人预约）
            long personalTotalNumTemp = 0L;
            // 入馆人数（个人预约）
            long personalInNumTemp = 0L;
            // 未核销（个人预约）
            long personalOutNumTemp = 0L;
            // 微信预约人数（团队预约）
            long teamManTotalNumTemp = 0L;
            // 入馆团队人数（团队预约）
            long teamManInNumTemp = 0L;
            // 未核销团队人数（团队预约）
            long teamManOutNumTemp = 0L;

            //个人预约总人数
            Optional<Long> personalNumTemp = personalOrderTempList.stream().map(TktOrderBaseDO::getTotalPeopleCount).reduce(Long::sum);
            if(personalNumTemp.isPresent()){
                personalTotalNumTemp = personalNumTemp.get();
            }
            //个人预约核销总人数
            personalInNumTemp = personalTempList.stream().filter(tktPersonalOrderDetailDO -> tktPersonalOrderDetailDO.getStatus() == 2).count();
            personalOutNumTemp = personalTotalNumTemp - personalInNumTemp;

            //团队预约总人数
            teamManTotalNumTemp = teamOrderTempList.stream().mapToLong(TktOrderBaseDO::getTotalPeopleCount).sum();

            // 团队核销总人数
            teamManInNumTemp =  teamOrderTempList.stream().filter(tktOrderBaseDO -> tktOrderBaseDO.getStatus() == 2).mapToLong(TktOrderBaseDO::getTotalPeopleCount).sum();

            teamManOutNumTemp = teamManTotalNumTemp - teamManInNumTemp;
            allTotalNumTemp = personalTotalNum + TeamManTotalNum;
            allInNumTemp = personalInNumTemp + teamManInNumTemp;
            allOutNumTemp = personalOutNumTemp + teamManOutNumTemp;
            if (allTotalNumTemp == 0) {
                visitorStatisticsDTO.setAllTotalRingRatio(new BigDecimal(allTotalNum));
                visitorStatisticsDTO.setAllTotalRingRatioStr("-");
            } else {
                BigDecimal v1 = new BigDecimal(allTotalNum - allTotalNumTemp).divide(new BigDecimal(allTotalNumTemp), 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(1, RoundingMode.HALF_UP);
                visitorStatisticsDTO.setAllTotalRingRatio(v1);
                visitorStatisticsDTO.setAllTotalRingRatioStr(v1 + "%");
            }
            if (allInNumTemp == 0) {
                visitorStatisticsDTO.setAllInNumRingRatio(new BigDecimal(allInNum));
                visitorStatisticsDTO.setAllInNumRingRatioStr("-");
            } else {
                BigDecimal v1 = new BigDecimal(allInNum - allInNumTemp).divide(new BigDecimal(allInNumTemp), 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(1, RoundingMode.HALF_UP);
                visitorStatisticsDTO.setAllInNumRingRatio(v1);
                visitorStatisticsDTO.setAllInNumRingRatioStr(v1 + "%");
            }
            if (allOutNumTemp == 0) {
                visitorStatisticsDTO.setAllOutNumRingRatio(new BigDecimal(allOutNum));
                visitorStatisticsDTO.setAllOutNumRingRatioStr("-");
            } else {
                BigDecimal v1 = new BigDecimal(allOutNum - allOutNumTemp).divide(new BigDecimal(allOutNumTemp), 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(1, RoundingMode.HALF_UP);
                visitorStatisticsDTO.setAllOutNumRingRatio(v1);
                visitorStatisticsDTO.setAllOutNumRingRatioStr(v1 + "%");
            }
        }

        // 环比计算
        if (Objects.nonNull(yoyTimeStart)) {
            //预约信息
            List<TktOrderBaseDO> personalOrderTempList =  tktOrderBaseMapper.selectListByTime(companyId,yoyTimeStart,yoyTimeEnd,OrderTypeEnum.个人预约.getCode().byteValue());
            List<TktOrderBaseDO> teamOrderTempList =  tktOrderBaseMapper.selectListByTime(companyId,yoyTimeStart,yoyTimeEnd,OrderTypeEnum.团队预约.getCode().byteValue());
            //个人预约
            List<TktPersonalOrderDetailDO> personalTempList =  tktPersonalOrderDetailMapper.selectListByTime(companyId,yoyTimeStart,yoyTimeEnd);
            // 微信预约总人数（观众总览）
            long allTotalNumTemp = 0L;
            // 入馆人数（观众总览）
            long allInNumTemp = 0L;
            // 未核销（观众总览）
            long allOutNumTemp = 0L;
            // 微信预约（个人预约）
            long personalTotalNumTemp = 0L;
            // 入馆人数（个人预约）
            long personalInNumTemp = 0L;
            // 未核销（个人预约）
            long personalOutNumTemp = 0L;
            // 微信预约人数（团队预约）
            long teamManTotalNumTemp = 0L;
            // 入馆团队人数（团队预约）
            long teamManInNumTemp = 0L;
            // 未核销团队人数（团队预约）
            long teamManOutNumTemp = 0L;

            //个人预约总人数
            personalTotalNumTemp = personalOrderTempList.stream().mapToLong(TktOrderBaseDO::getTotalPeopleCount).sum();

            //个人预约核销总人数
            personalInNumTemp = personalTempList.stream().filter(tktPersonalOrderDetailDO -> tktPersonalOrderDetailDO.getStatus() == 2).count();
            //个人未核销
            personalOutNumTemp = personalTotalNumTemp - personalInNumTemp;

            //团队预约总人数
            teamManTotalNumTemp = teamOrderTempList.stream().mapToLong(TktOrderBaseDO::getTotalPeopleCount).sum();

            //团队核销总人数
            teamManInNumTemp =  teamOrderTempList.stream().filter(tktOrderBaseDO -> tktOrderBaseDO.getStatus() == 2).mapToLong(TktOrderBaseDO::getTotalPeopleCount).sum();
            //团队未核销
            teamManOutNumTemp = teamManTotalNumTemp - teamManInNumTemp;

            allTotalNumTemp = personalTotalNumTemp + teamManTotalNumTemp;
            allInNumTemp = personalInNumTemp + teamManInNumTemp;
            allOutNumTemp = personalOutNumTemp + teamManOutNumTemp;
            if (allTotalNumTemp == 0) {
                visitorStatisticsDTO.setAllTotalYoY(new BigDecimal(allTotalNum));
                visitorStatisticsDTO.setAllTotalYoyStr("-");
            } else {
                BigDecimal v1 = new BigDecimal(allTotalNum - allTotalNumTemp).divide(new BigDecimal(allTotalNumTemp), 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(1, RoundingMode.HALF_UP);
                visitorStatisticsDTO.setAllTotalYoY(v1);
                visitorStatisticsDTO.setAllTotalYoyStr(v1 + "%");
            }
            if (allInNumTemp == 0) {
                visitorStatisticsDTO.setAllInNumYoY(new BigDecimal(allInNum));
                visitorStatisticsDTO.setAllInNumYoyStr("-");
            } else {
                BigDecimal v1 = new BigDecimal(allInNum - allInNumTemp).divide(new BigDecimal(allInNumTemp), 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(1, RoundingMode.HALF_UP);
                visitorStatisticsDTO.setAllInNumYoY(v1);
                visitorStatisticsDTO.setAllInNumYoyStr(v1 + "%");
            }
            if (allOutNumTemp == 0) {
                visitorStatisticsDTO.setAllOutNumYoY(new BigDecimal(allOutNum));
                visitorStatisticsDTO.setAllOutNumYoyStr("-");
            } else {
                BigDecimal v1 = new BigDecimal(allOutNum - allOutNumTemp).divide(new BigDecimal(allOutNumTemp), 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(1, RoundingMode.HALF_UP);
                visitorStatisticsDTO.setAllOutNumYoY(v1);
                visitorStatisticsDTO.setAllOutNumYoyStr(v1 + "%");
            }
        }
        return visitorStatisticsDTO;
    }

    @Override
    public VisitorInStatisticsDTO visitorInStatistics(Long companyId,Integer statisticsWay,String startTime,String endTime){
        VisitorInStatisticsDTO dto = new VisitorInStatisticsDTO();

        Date timeStart = null;
        Date timeEnd = null;
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = new Date();
        }else if(statisticsWay == 1){
            //本周
            timeStart = DateUtils.getTimesWeekMorning();
            timeEnd = new Date();
        }else if(statisticsWay == 2){
            //本月
            timeStart = DateUtils.getTimesMonthMorning();
            timeEnd = new Date();
        }else if(statisticsWay == 3){
            //本年
            timeStart = DateUtils.getTimesYearMorning();
            timeEnd = new Date();
        }else if(statisticsWay == 4){
            //全部
        }else if(statisticsWay == 6){
            //昨日
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(new Date(),-1,1));
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(new Date(),-1,2));
        }else{
            if(startTime == null){
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "startTime 不能为空！");
            }
            if(endTime == null){
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "endTime 不能为空！");
            }
            //自定义时间
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        }

        long allInNum;
        long personalInNum;
        long teamNum;
        long teamManInNum;

        //个人核销
        List<TktPersonalOrderDetailDO> personalList = tktPersonalOrderDetailMapper.selectHasWriteOffList(companyId,timeStart,timeEnd);
        //团队核销
        List<TktTeamOrderDetailDO> teamList = tktTeamOrderDetailMapper.selectHasWriteOffList(companyId, timeStart,timeEnd);
        List<TktOrderBaseDO> tktOrderBaseDOList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(teamList)){
            List<Long> orderIds = teamList.stream().map(TktTeamOrderDetailDO::getOrderId).collect(Collectors.toList());
            tktOrderBaseDOList = tktOrderBaseMapper.listByOrderIds(companyId,orderIds);
        }

        teamNum = teamList.size();
        teamManInNum = tktOrderBaseDOList.stream().mapToLong(TktOrderBaseDO::getTotalPeopleCount).sum();
        personalInNum = personalList.size();
        allInNum = teamManInNum + personalInNum;
        dto.setAllInNum(allInNum);
        dto.setPersonalInNum(personalInNum);
        dto.setTeamInNum(teamNum);
        dto.setTeamManInNum(teamManInNum);


        Date yoyTimeStart;
        Date yoyTimeEnd;
        if(statisticsWay == 0){
            // 本日
            //昨日同期
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeEnd = calYoy.getTime();

        }else if(statisticsWay == 1){
            // 本周
            //上周同期
            Calendar cal = Calendar.getInstance();
            cal.setTime(timeStart);
            cal.add(Calendar.DAY_OF_WEEK, -7);
            yoyTimeStart = cal.getTime();
            Calendar call = Calendar.getInstance();
            call.setTime(timeEnd);
            call.add(Calendar.DAY_OF_WEEK, -7);
            yoyTimeEnd = call.getTime();
        }else if(statisticsWay == 2){
            // 本月
            //上月同期
            Calendar call = Calendar.getInstance();
            call.setTime(timeStart);
            call.add(Calendar.MONTH, -1);
            yoyTimeStart = call.getTime();
            Calendar cal = Calendar.getInstance();
            cal.setTime(timeEnd);
            cal.add(Calendar.MONTH, -1);
            yoyTimeEnd = cal.getTime();
        }else if(statisticsWay == 3){
            //本年
            //去年
            Calendar temp = Calendar.getInstance();
            temp.setTime(timeStart);
            temp.add(Calendar.YEAR, -1);
            yoyTimeStart = temp.getTime();
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.YEAR, -1);
            yoyTimeEnd = calYoy.getTime();
        }else if(statisticsWay == 4){
            return dto;
        }else if(statisticsWay == 6){
            //昨日
            //前日
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeEnd = calYoy.getTime();
        } else {
            return dto;
        }

        // 之前同期计算
        //个人核销
        List<TktPersonalOrderDetailDO> personalTempList = tktPersonalOrderDetailMapper.selectHasWriteOffList(companyId, yoyTimeStart, yoyTimeEnd);
        //团队核销
        List<TktTeamOrderDetailDO> teamTempList = tktTeamOrderDetailMapper.selectHasWriteOffList(companyId, yoyTimeStart, yoyTimeEnd);
        List<TktOrderBaseDO> tktOrderBaseDOTempList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(teamTempList)) {
            List<Long> orderIds = teamTempList.stream().map(TktTeamOrderDetailDO::getOrderId).collect(Collectors.toList());
            tktOrderBaseDOTempList = tktOrderBaseMapper.listByOrderIds(companyId, orderIds);
        }

        long teamManInNumTemp = tktOrderBaseDOTempList.stream().mapToLong(TktOrderBaseDO::getTotalPeopleCount).sum();
        long personalInNumTemp = personalTempList.size();
        long allInNumTemp = teamManInNumTemp + personalInNumTemp;

        if (allInNumTemp == 0) {
            dto.setCompareRatio("-");
        } else {
            BigDecimal v1 = new BigDecimal(allInNum - allInNumTemp).divide(new BigDecimal(allInNumTemp), 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(1, RoundingMode.HALF_UP);
            dto.setCompareRatio(v1 + "%");
        }
        return dto;
    }

    @Override
    public List<VisitorListDTO> visitorList(Long companyId, Integer statisticsWay, String startTime, String endTime){
        //查询场馆信息的开闭馆时间
        TktWechatVenueDO tktWechatVenueDO = this.tktWechatVenueMapper.selectByCompanyId(companyId, ChannelTypeEnum.微信小程序.getCode().byteValue());
        if(Objects.isNull(tktWechatVenueDO)){
            return Lists.newArrayList();
        }

        String formatTime = TimeFormatConstant.DATE_DAY_WITH;
        int type = 2;
        int dataType = 10;

        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //本日（开始时间为开馆时间，结束时间为闭馆时间）
            startTime = DateUtils.formatDateYYYMMDD(new Date())+" "+ DateUtils.formatDateHHmm(tktWechatVenueDO.getOpenTime()) + ":00";
            endTime = DateUtils.formatDateYYYMMDD(new Date())+" "+ DateUtils.formatDateHHmm(tktWechatVenueDO.getCloseTime()) + ":00";
            formatTime = TimeFormatConstant.DATE_HOUR_WITH;
            type = 3;
            dataType = 13;
        }else if(statisticsWay == 1){
            //本周
            startTime = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesWeekMorning());
            endTime = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesWeekNight());
        }else if(statisticsWay == 2){
            //本月
            startTime = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesMonthMorning());
            endTime = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesMonthNight());
        }else if(statisticsWay == 3){
            //本年
            startTime = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesYearMorning());
            endTime = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesYearNight());
            type = 1;
            formatTime = TimeFormatConstant.DATE_MONTH_WITH;
            dataType = 7;
        }else if(statisticsWay == 4){
            //至今
            //获取第一条订单时间
            Date firstOrderTime = tktOrderBaseMapper.getFirstOrderTime(companyId);
            if(Objects.isNull(firstOrderTime)){
                startTime = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesDayMorning());
            }else{
                startTime = DateUtils.formatDateYYYMMDDHHmmss(firstOrderTime);
            }
            endTime = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesDayNight());
            int days = DateUtils.calculateDateDif(DateUtils.parseDateYYYMMDDHHmmss(endTime),DateUtils.parseDateYYYMMDDHHmmss(startTime));
            if(days <= 31){
                //按日统计
            }else if(days > 366){
                //按年统计
                type = 4;
                formatTime = TimeFormatConstant.DATE_YEAR_WITH;
                dataType = 4;
            }else{
                //按月统计
                type = 1;
                formatTime = TimeFormatConstant.DATE_MONTH_WITH;
                dataType = 7;
            }
        }else if(statisticsWay == 6){
            //昨日（开始时间为开馆时间，结束时间为闭馆时间）
            startTime = DateUtils.getOrderDay(new Date(),-1,1)+" "+ DateUtils.formatDateHHmm(tktWechatVenueDO.getOpenTime()) + ":00";
            endTime = DateUtils.getOrderDay(new Date(),-1,2)+" "+ DateUtils.formatDateHHmm(tktWechatVenueDO.getCloseTime()) + ":00";
            formatTime = TimeFormatConstant.DATE_HOUR_WITH;
            type = 3;
            dataType = 13;
        } else{
            //自定义时间
            int days = DateUtils.calculateDateDif(DateUtils.parseDateYYYMMDDHHmmss(endTime),DateUtils.parseDateYYYMMDDHHmmss(startTime));
            if(days <= 31){
                //按日统计
            }else if(days > 366){
                //按年统计
                type = 4;
                formatTime = TimeFormatConstant.DATE_YEAR_WITH;
                dataType = 4;
            }else{
                //按月统计
                type = 1;
                formatTime = TimeFormatConstant.DATE_MONTH_WITH;
                dataType = 7;
            }
        }
        //获取所有的时间刻度
        List<String> dateList = DateUtils.getDateList(startTime,endTime,formatTime,type);
        //统计个人预约核销情况
        List<OrderStatisticsByNumDO> personalList = tktPersonalOrderDetailMapper.statisticsOnNumList(companyId,startTime,endTime,dataType);
        Map<String,Integer> personalMap = new HashMap<>();
        for(OrderStatisticsByNumDO orderStatisticsByNumDO : personalList){
            personalMap.put(orderStatisticsByNumDO.getDataTime(),orderStatisticsByNumDO.getManNum());
        }

        //统计团队预约核销情况
        List<OrderStatisticsByNumDO> teamList = tktOrderBaseMapper.statisticsOnNumList(companyId,OrderTypeEnum.团队预约.getCode().byteValue(),startTime,endTime,dataType);
        Map<String,Integer> teamMap = new HashMap<>();
        for(OrderStatisticsByNumDO orderStatisticsByNumDO : teamList){
            teamMap.put(orderStatisticsByNumDO.getDataTime(),orderStatisticsByNumDO.getManNum());
        }

        List<VisitorListDTO> visitorListDTOList = new ArrayList<>();
        for(String dataTime : dateList){
            VisitorListDTO visitorListDTO = new VisitorListDTO();
            int manNum = 0;
            if(personalMap.containsKey(dataTime)){
                manNum += personalMap.get(dataTime);
            }
            if(teamMap.containsKey(dataTime)){
                manNum += teamMap.get(dataTime);
            }
            visitorListDTO.setManNum(manNum);
            if(statisticsWay == 0 && dataTime.length() == 13){
                dataTime = dataTime.substring(10,13) + ":00";
            }
            visitorListDTO.setDataTime(dataTime);
            visitorListDTOList.add(visitorListDTO);
        }

        //根据类型获取不同维度的统计数据
        String yoyStartTime;
        String yoyStartEnd;
        if(statisticsWay == 0){
            return visitorListDTOList;
        }else if(statisticsWay == 1){
            //本周
            Calendar temp = Calendar.getInstance();
            temp.setTime(DateUtils.getTimesWeekMorning());
            temp.add(Calendar.YEAR, -1);
            yoyStartTime = DateUtils.formatDateYYYMMDDHHmmss(temp.getTime());
            temp = Calendar.getInstance();
            temp.setTime(DateUtils.getTimesWeekNight());
            temp.add(Calendar.YEAR, -1);
            yoyStartEnd = DateUtils.formatDateYYYMMDDHHmmss(temp.getTime());
        }else if(statisticsWay == 2){
            //本月
            Calendar temp = Calendar.getInstance();
            temp.setTime(DateUtils.getTimesMonthMorning());
            temp.add(Calendar.YEAR, -1);
            yoyStartTime = DateUtils.formatDateYYYMMDDHHmmss(temp.getTime());
            temp = Calendar.getInstance();
            temp.setTime(DateUtils.getTimesMonthNight());
            temp.add(Calendar.YEAR, -1);
            yoyStartEnd = DateUtils.formatDateYYYMMDDHHmmss(temp.getTime());
        }else if(statisticsWay == 3){
            //本年
            Calendar temp = Calendar.getInstance();
            temp.setTime(DateUtils.getTimesYearMorning());
            temp.add(Calendar.YEAR, -1);
            yoyStartTime = DateUtils.formatDateYYYMMDDHHmmss(temp.getTime());
            temp = Calendar.getInstance();
            temp.setTime(DateUtils.getTimesDayNight());
            temp.add(Calendar.YEAR, -1);
            yoyStartEnd = DateUtils.formatDateYYYMMDDHHmmss(temp.getTime());
            type = 1;
            formatTime = TimeFormatConstant.DATE_MONTH_WITH;
            dataType = 7;
        }else if(statisticsWay == 4){
            return visitorListDTOList;
        }else if(statisticsWay == 6){
            //昨日
            return visitorListDTOList;
        } else {
            return visitorListDTOList;
        }
        {
            //获取所有的时间刻度
            List<String> dateListTemp = DateUtils.getDateList(yoyStartTime,yoyStartEnd,formatTime,type);
            //统计个人预约核销情况
            List<OrderStatisticsByNumDO> personalListTemp = tktPersonalOrderDetailMapper.statisticsOnNumList(companyId,yoyStartTime,yoyStartEnd,dataType);
            Map<String,Integer> personalMapTemp = new HashMap<>();
            for(OrderStatisticsByNumDO orderStatisticsByNumDO : personalListTemp){
                personalMapTemp.put(orderStatisticsByNumDO.getDataTime(),orderStatisticsByNumDO.getManNum());
            }

            //统计团队预约核销情况
            List<OrderStatisticsByNumDO> teamListTemp = tktOrderBaseMapper.statisticsOnNumList(companyId,OrderTypeEnum.团队预约.getCode().byteValue(),yoyStartTime,yoyStartEnd,dataType);
            Map<String,Integer> teamMapTemp = new HashMap<>();
            for(OrderStatisticsByNumDO orderStatisticsByNumDO : teamListTemp){
                teamMapTemp.put(orderStatisticsByNumDO.getDataTime(),orderStatisticsByNumDO.getManNum());
            }

            List<VisitorListDTO> visitorListTemp = new ArrayList<>();
            for(String dataTime : dateListTemp){
                VisitorListDTO visitorListDTO = new VisitorListDTO();
                visitorListDTO.setDataTime(dataTime);
                int manNum = 0;
                if(personalMapTemp.containsKey(dataTime)){
                    manNum += personalMapTemp.get(dataTime);
                }
                if(teamMapTemp.containsKey(dataTime)){
                    manNum += teamMapTemp.get(dataTime);
                }
                visitorListDTO.setManNum(manNum);
                visitorListTemp.add(visitorListDTO);
            }
            for (int i=0; i < visitorListDTOList.size(); i++) {
                VisitorListDTO v1 = visitorListDTOList.get(i);
                if (i>visitorListTemp.size()-1) {
                    v1.setYoy(v1.getManNum() * 1.0);
                    v1.setYoyStr("-");
                    continue;
                }
                VisitorListDTO v2 = visitorListTemp.get(i);
                if (Objects.isNull(v2) || v2.getManNum() == 0) {
                    v1.setYoy(v1.getManNum() * 1.0);
                    v1.setYoyStr("-");
                    continue;
                }
                BigDecimal bigDecimal = BigDecimal.valueOf((v1.getManNum() - v2.getManNum()) / (v2.getManNum() * 1.0));
                double v3 = bigDecimal.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                v1.setYoy(v3);
                v1.setYoyStr(v3 + "%");
            }
        }
        {
            String ringStartTime;
            String ringStartEnd;
            if(statisticsWay == 1){
                //本周
                Calendar temp = Calendar.getInstance();
                temp.setTime(DateUtils.getTimesWeekMorning());
                temp.add(Calendar.DAY_OF_YEAR, -1);
                ringStartTime = DateUtils.formatDateYYYMMDDHHmmss(temp.getTime());
                ringStartEnd = DateUtils.formatDateYYYMMDD(temp.getTime()) + " 23:59:59";
            }else if(statisticsWay == 2){
                //本月
                Calendar temp = Calendar.getInstance();
                temp.setTime(DateUtils.getTimesMonthMorning());
                temp.add(Calendar.DAY_OF_YEAR, -1);
                ringStartTime = DateUtils.formatDateYYYMMDDHHmmss(temp.getTime());
                ringStartEnd = DateUtils.formatDateYYYMMDD(temp.getTime()) + " 23:59:59";
            }else{
                //本年
                Calendar temp = Calendar.getInstance();
                temp.setTime(DateUtils.getTimesYearMorning());
                temp.add(Calendar.DAY_OF_YEAR, -1);
                ringStartTime = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getFirstDayOfMonth((temp.get(Calendar.YEAR)), (temp.get(Calendar.MONTH) + 1)));
                ringStartEnd = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getLastDayOfMonth((temp.get(Calendar.YEAR)), (temp.get(Calendar.MONTH) + 1)));
                type = 1;
                formatTime = TimeFormatConstant.DATE_MONTH_WITH;
                dataType = 7;
            }
            //获取所有的时间刻度
            List<String> dateListTemp = DateUtils.getDateList(ringStartTime,ringStartEnd,formatTime,type);
            //统计个人预约核销情况
            List<OrderStatisticsByNumDO> personalListTemp = tktPersonalOrderDetailMapper.statisticsOnNumList(companyId,ringStartTime,ringStartEnd,dataType);
            Map<String,Integer> personalMapTemp = new HashMap<>();
            for(OrderStatisticsByNumDO orderStatisticsByNumDO : personalListTemp){
                personalMapTemp.put(orderStatisticsByNumDO.getDataTime(),orderStatisticsByNumDO.getManNum());
            }

            //统计团队预约核销情况
            List<OrderStatisticsByNumDO> teamListTemp = tktOrderBaseMapper.statisticsOnNumList(companyId,OrderTypeEnum.团队预约.getCode().byteValue(),ringStartTime,ringStartEnd,dataType);
            Map<String,Integer> teamMapTemp = new HashMap<>();
            for(OrderStatisticsByNumDO orderStatisticsByNumDO : teamListTemp){
                teamMapTemp.put(orderStatisticsByNumDO.getDataTime(),orderStatisticsByNumDO.getManNum());
            }

            List<VisitorListDTO> visitorListTemp = new ArrayList<>();
            for(String dataTime : dateListTemp){
                VisitorListDTO visitorListDTO = new VisitorListDTO();
                visitorListDTO.setDataTime(dataTime);
                int manNum = 0;
                if(personalMapTemp.containsKey(dataTime)){
                    manNum += personalMapTemp.get(dataTime);
                }
                if(teamMapTemp.containsKey(dataTime)){
                    manNum += teamMapTemp.get(dataTime);
                }
                visitorListDTO.setManNum(manNum);
                visitorListTemp.add(visitorListDTO);
            }
            List<VisitorListDTO> resultList = Lists.newArrayList();
            resultList.add(visitorListTemp.get(0));
            resultList.addAll(visitorListDTOList);
            for (int i=0; i < visitorListDTOList.size(); i++) {
                VisitorListDTO v1 = visitorListDTOList.get(i);
                VisitorListDTO v2 = resultList.get(i);
                if (Objects.isNull(v2) || v2.getManNum() == 0) {
                    v1.setRingRatio(v1.getManNum() * 1.0);
                    v1.setRingRatioStr("-");
                    continue;
                }
                BigDecimal bigDecimal = BigDecimal.valueOf((v1.getManNum() - v2.getManNum()) / (v2.getManNum() * 1.0));
                double v3 = bigDecimal.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                v1.setRingRatio(v3);
                v1.setRingRatioStr(v3 + "%");
            }
        }
        return visitorListDTOList;
    }

    @Override
    public List<HolidayDTO> holidayList(Long companyId, Integer year) {
        List<HolidayBO> holidayBOList = holidaysMap.get(year);
        if (CollectionUtils.isEmpty(holidayBOList)) {
            return Lists.newArrayList();
        }
        List<HolidayDTO> result = Lists.newArrayList();
        holidayBOList.forEach(v -> {
            HolidayDTO holidayDTO = new HolidayDTO();
            holidayDTO.setHolidayName(v.getName());
            List<TktPersonalOrderDetailDO> personalOrderList = this.tktPersonalOrderDetailMapper.listByReserveDate(companyId, (byte)2,
                    v.getStartTime(), v.getEndTime());
            long personalCount = CollectionUtils.isEmpty(personalOrderList) ? 0 : personalOrderList.size();
            List<TktOrderBaseDO> teamOrderBaseDOList = this.tktOrderBaseMapper.listByReserveDate(companyId, (byte)2,
                    OrderTypeEnum.团队预约.getCode().byteValue(), v.getStartTime(), v.getEndTime());
            long teamCount = CollectionUtils.isEmpty(teamOrderBaseDOList) ? 0 : teamOrderBaseDOList.size();
            holidayDTO.setInNum(personalCount + teamCount);
            holidayDTO.setYoy((personalCount + teamCount) * 1.0);
            holidayDTO.setYoyStr("-");
            result.add(holidayDTO);
        });
        List<HolidayBO> previousHolidayBOList = holidaysMap.get(year-1);
        if (CollectionUtils.isEmpty(previousHolidayBOList)) {
            return result;
        }
        List<HolidayDTO> resultOld = Lists.newArrayList();
        previousHolidayBOList.forEach(v -> {
            HolidayDTO holidayDTO = new HolidayDTO();
            holidayDTO.setHolidayName(v.getName());
            List<TktPersonalOrderDetailDO> personalOrderList = this.tktPersonalOrderDetailMapper.listByReserveDate(companyId, (byte)2,
                    v.getStartTime(), v.getEndTime());
            long personalCount = CollectionUtils.isEmpty(personalOrderList) ? 0 : personalOrderList.size();
            List<TktOrderBaseDO> teamOrderBaseDOList = this.tktOrderBaseMapper.listByReserveDate(companyId, (byte)2,
                    OrderTypeEnum.团队预约.getCode().byteValue(), v.getStartTime(), v.getEndTime());
            long teamCount = CollectionUtils.isEmpty(teamOrderBaseDOList) ? 0 : teamOrderBaseDOList.size();
            holidayDTO.setInNum(personalCount + teamCount);
            holidayDTO.setYoy((personalCount + teamCount) * 1.0);
            resultOld.add(holidayDTO);
        });
        if (result.size() == resultOld.size()) {
            for (int i=0; i<result.size(); i++) {
                long v1 = result.get(i).getInNum();
                long v2 = resultOld.get(i).getInNum();
                if (v2 == 0) {
                    result.get(i).setYoyStr("-");
                    continue;
                }
                BigDecimal bigDecimal = BigDecimal.valueOf((v1 - v2) / (v2 * 1.0));
                double v3 = bigDecimal.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                result.get(i).setYoy(v3);
                result.get(i).setYoyStr(v3 + "%");
            }
        } else if (result.size() -1 == resultOld.size()) {
            for (int i=0; i<result.size(); i++) {
                if (i == resultOld.size()-1) {
                    result.get(i).setYoy(0.0);
                    result.get(i).setYoyStr("-");
                    continue;
                }
                long v1 = result.get(i).getInNum();
                long v2 = 0L;
                if (i == result.size()-1) {
                    v2 = resultOld.get(i-1).getInNum();
                } else {
                    v2 = resultOld.get(i).getInNum();
                }
                if (v2 == 0) {
                    result.get(i).setYoyStr("-");
                    continue;
                }
                BigDecimal bigDecimal = BigDecimal.valueOf((v1 - v2) / (v2 * 1.0));
                double v3 = bigDecimal.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                result.get(i).setYoy(v3);
                result.get(i).setYoyStr(v3 + "%");
            }
        } else if (result.size() + 1 == resultOld.size()) {
            for (int i=0; i<result.size(); i++) {
                long v1 = result.get(i).getInNum();
                long v2;
                if (i == result.size()-1) {
                    v2 = resultOld.get(i).getInNum() + resultOld.get(i+1).getInNum();
                } else {
                    v2 = resultOld.get(i).getInNum();
                }
                if (v2 == 0) {
                    result.get(i).setYoyStr("-");
                    continue;
                }
                BigDecimal bigDecimal = BigDecimal.valueOf((v1 - v2) / (v2 * 1.0));
                double v3 = bigDecimal.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                result.get(i).setYoy(v3);
                result.get(i).setYoyStr(v3 + "%");
            }
        }
        return result;
    }

    @Override
    public AudiencePortraitAreaDTO audiencePortraitArea(Long companyId, Integer statisticsWay, String startTime, String endTime) {
//        if (StringUtils.isBlank(locationAdCode)) {
//            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "场馆地理位置未配置");
//        }
        // 获取场馆信息
        QueryWechatVenueDTO queryWechatVenueDTO = wechatVenueService.queryWechatVenue(companyId);
        if(Objects.isNull(queryWechatVenueDTO) || StringUtils.isBlank(queryWechatVenueDTO.getAdCodePath())){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "场馆地理位置未配置");
        }

        String locationAdCode = queryWechatVenueDTO.getAdCodePath().split(",")[2];

        Date timeStart = null;
        Date timeEnd = new Date();
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = DateUtils.getTimesDayNight();
        }else if(statisticsWay == 1){
            //本周
            timeStart = DateUtils.getTimesWeekMorning();
        }else if(statisticsWay == 2){
            //本月
            timeStart = DateUtils.getTimesMonthMorning();
        }else if(statisticsWay == 3){
            //本年
            timeStart = DateUtils.getTimesYearMorning();
        }else if(statisticsWay == 4){
            //至今
        }else if(statisticsWay == 6){
            //昨日
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(new Date(),-1,1));
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(new Date(),-1,2));
        }else{
            if(startTime == null){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "开始时间不能为空！");
            }
            if(endTime == null){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "结束时间不能为空！");
            }
            //自定义时间
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        }
        List<TktPersonalOrderDetailDO> personalOrderList = this.tktPersonalOrderDetailMapper.listByReserveDate(companyId, (byte)2, timeStart, timeEnd);
        AudiencePortraitAreaDTO result = new AudiencePortraitAreaDTO();
        if (CollectionUtils.isEmpty(personalOrderList)) {
            // AudiencePortraitAreaDTO audiencePortraitAreaDTO = new AudiencePortraitAreaDTO();
            result.setInMuseumNum(0L);
            result.setProvinceInNum(0L);
            result.setProvinceInNumRatio(0d);
            result.setProvinceInRingRatio(0d);
            result.setProvinceInYoy(0d);
            result.setProvinceOutNum(0L);
            result.setProvinceOutNumRatio(0d);
            result.setProvinceOutRingRatio(0d);
            result.setProvinceOutYoy(0d);
            result.setOverseasNum(0L);
            result.setOverseasNumRatio(0d);
            result.setOverseasRingRatio(0d);
            result.setOverseasYoy(0d);
            //return audiencePortraitAreaDTO;
        } else {
            result.setInMuseumNum((long)personalOrderList.size());
            AtomicLong provinceInNum = new AtomicLong(0L);
            AtomicLong provinceOutNum = new AtomicLong(0L);
            AtomicLong overseasNum = new AtomicLong(0L);
            personalOrderList.forEach(v -> {
                Byte touristCertificateType = v.getTouristCertificateType();
                String touristCertificateNo = v.getTouristCertificateNo();
                if ((byte)1 == touristCertificateType) {
                    if (StringUtils.isEmpty(touristCertificateNo)) {
                        return;
                    }
                    if (touristCertificateNo.startsWith(locationAdCode)) {
                        provinceInNum.getAndIncrement();
                    } else {
                        provinceOutNum.getAndIncrement();
                    }
                } else if ((byte)3 == touristCertificateType) {
                    provinceOutNum.getAndIncrement();
                } else {
                    overseasNum.getAndIncrement();
                }
            });
            result.setProvinceInNum(provinceInNum.get());
            result.setProvinceOutNum(provinceOutNum.get());
            result.setOverseasNum(overseasNum.get());
            // BigDecimal bigDecimal = BigDecimal.valueOf(provinceInNum.get() / (result.getInMuseumNum() * 1.0));
            // BigDecimal.valueOf(provinceInNum.get() / (result.getInMuseumNum() * 1.0)).setScale(4, RoundingMode.HALF_UP).doubleValue() * 100
            result.setProvinceInNumRatio(new BigDecimal(provinceInNum.get()).divide(new BigDecimal(result.getInMuseumNum()), 4, RoundingMode.HALF_UP).doubleValue() * 100);
            result.setProvinceInNumRatioStr(result.getProvinceInNumRatio() + "%");
            result.setProvinceOutNumRatio(BigDecimal.valueOf(provinceOutNum.get() / (result.getInMuseumNum() * 1.0)).setScale(4, RoundingMode.HALF_UP).doubleValue() * 100);
            result.setProvinceOutNumRatioStr(result.getProvinceOutNumRatio() + "%");
            //System.out.println(result.getProvinceInNumRatio() + result.getProvinceOutNumRatio());
            result.setOverseasNumRatio(new BigDecimal("100.0").subtract(BigDecimal.valueOf(result.getProvinceInNumRatio() + result.getProvinceOutNumRatio())).setScale(4, RoundingMode.HALF_UP).doubleValue());
            result.setOverseasNumRatioStr(result.getOverseasNumRatio() + "%");
        }

        Date ringRatioTimeStart;
        Date ringRatioTimeEnd;
        Date yoyTimeStart = null;
        Date yoyTimeEnd = null;
        if(statisticsWay == 0){
            //今日
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeEnd = calYoy.getTime();

            Calendar cal = Calendar.getInstance();
            cal.setTime(timeStart);
            cal.add(Calendar.YEAR, -1);
            ringRatioTimeStart = cal.getTime();
            Calendar calEnd = Calendar.getInstance();
            calEnd.setTime(timeEnd);
            calEnd.add(Calendar.YEAR, -1);
            ringRatioTimeEnd = calEnd.getTime();
        }else if(statisticsWay == 1){
            //本周
            Calendar cal = Calendar.getInstance();
            cal.setTime(DateUtils.getTimesWeekMorning());
            cal.add(Calendar.DAY_OF_WEEK, -7);
            ringRatioTimeStart = cal.getTime();

            Calendar call = Calendar.getInstance();
            call.setTime(DateUtils.getTimesWeekNight());
            call.add(Calendar.DAY_OF_WEEK, -7);
            ringRatioTimeEnd = call.getTime();
        }else if(statisticsWay == 2){
            //本月
            Calendar call = Calendar.getInstance();
            call.setTime(DateUtils.getTimesMonthMorning());
            call.add(Calendar.MONTH, -1);
            ringRatioTimeStart = call.getTime();
            Calendar cal = Calendar.getInstance();
            cal.setTime(DateUtils.getTimesDayNight());
            cal.add(Calendar.MONTH, -1);
            ringRatioTimeEnd = cal.getTime();
            if (DateUtils.getTimesMonthNight().equals(DateUtils.getTimesDayNight())) {
                Calendar calTemp = Calendar.getInstance();
                calTemp.setTime(new Date());
                calTemp.add(Calendar.MONTH, -1);
                ringRatioTimeEnd = DateUtils.getLastDayOfMonth((calTemp.get(Calendar.YEAR)), (calTemp.get(Calendar.MONTH) + 1));
            } else {
                Calendar calTemp = Calendar.getInstance();
                calTemp.setTime(new Date());
                calTemp.add(Calendar.MONTH, -1);
                Date ringRatioTimeEndTemp = DateUtils.getLastDayOfMonth((calTemp.get(Calendar.YEAR)), (calTemp.get(Calendar.MONTH) + 1));
                ringRatioTimeEnd = ringRatioTimeEndTemp.getTime() < ringRatioTimeEnd.getTime() ? ringRatioTimeEndTemp : ringRatioTimeEnd;
            }

            if  (DateUtils.getTimesMonthNight().equals(DateUtils.getTimesDayNight())) {
                Calendar callYoy = Calendar.getInstance();
                callYoy.setTime(new Date());
                yoyTimeStart = DateUtils.getFirstDayOfMonth((callYoy.get(Calendar.YEAR) - 1), (callYoy.get(Calendar.MONTH) + 1));
                yoyTimeEnd = DateUtils.getLastDayOfMonth((callYoy.get(Calendar.YEAR) - 1), (callYoy.get(Calendar.MONTH) + 1));
            } else {
                Calendar callYoy = Calendar.getInstance();
                callYoy.setTime(DateUtils.getTimesMonthMorning());
                callYoy.add(Calendar.YEAR, -1);
                yoyTimeStart = callYoy.getTime();

                Calendar calYoy = Calendar.getInstance();
                calYoy.setTime(DateUtils.getTimesDayNight());
                calYoy.add(Calendar.YEAR, -1);
                yoyTimeEnd = calYoy.getTime();
            }
        }else if(statisticsWay == 3){
            //本年
            Calendar temp = Calendar.getInstance();
            temp.setTime(new Date());
            temp.add(Calendar.YEAR, -1);
            Calendar ringRatioYoy = Calendar.getInstance();
            ringRatioYoy.clear();
            ringRatioYoy.set(Calendar.YEAR, temp.get(Calendar.YEAR));
            ringRatioYoy.roll(Calendar.DAY_OF_YEAR, -1);
            ringRatioTimeEnd = ringRatioYoy.getTime();

            ringRatioYoy = Calendar.getInstance();
            ringRatioYoy.clear();
            ringRatioYoy.set(Calendar.YEAR, temp.get(Calendar.YEAR));
            ringRatioTimeStart = ringRatioYoy.getTime();
        }else if(statisticsWay == 4){
            return result;
        }else if(statisticsWay == 6){
            //昨日
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeEnd = calYoy.getTime();

            Calendar cal = Calendar.getInstance();
            cal.setTime(timeStart);
            cal.add(Calendar.YEAR, -1);
            ringRatioTimeStart = cal.getTime();
            Calendar calEnd = Calendar.getInstance();
            calEnd.setTime(timeEnd);
            calEnd.add(Calendar.YEAR, -1);
            ringRatioTimeEnd = calEnd.getTime();
        } else {
            return result;
        }

        {
            List<TktPersonalOrderDetailDO> personalOrderListTemp = this.tktPersonalOrderDetailMapper.listByReserveDate(companyId, (byte)2, ringRatioTimeStart, ringRatioTimeEnd);
            if (CollectionUtils.isEmpty(personalOrderListTemp)) {
                result.setOverseasRingRatio(result.getOverseasNum() * 1.0);
                result.setOverseasRingRatioStr("-");
                result.setProvinceInRingRatio(result.getProvinceInNum() * 1.0);
                result.setProvinceInRingRatioStr("-");
                result.setProvinceOutRingRatio(result.getProvinceOutNum() * 1.0);
                result.setProvinceOutRingRatioStr("-");
            } else {
                AtomicLong provinceInNumTemp = new AtomicLong(0L);
                AtomicLong provinceOutNumTemp = new AtomicLong(0L);
                AtomicLong overseasNumTemp = new AtomicLong(0L);
                personalOrderListTemp.forEach(v -> {
                    Byte touristCertificateType = v.getTouristCertificateType();
                    String touristCertificateNo = v.getTouristCertificateNo();
                    if ((byte)1 == touristCertificateType) {
                        if (StringUtils.isEmpty(touristCertificateNo)) {
                            return;
                        }
                        if (touristCertificateNo.startsWith(locationAdCode)) {
                            provinceInNumTemp.getAndIncrement();
                        } else {
                            provinceOutNumTemp.getAndIncrement();
                        }
                    } else if ((byte)3 == touristCertificateType) {
                        provinceOutNumTemp.getAndIncrement();
                    } else {
                        overseasNumTemp.getAndIncrement();
                    }
                });
                if (provinceInNumTemp.get() == 0) {
                    result.setProvinceInRingRatio(result.getProvinceInNum() * 1.0);
                    result.setProvinceInRingRatioStr("-");
                } else {
                    BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getProvinceInNum() - provinceInNumTemp.get()) / (provinceInNumTemp.get() * 1.0));
                    double v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                    result.setProvinceInRingRatio(v1);
                    result.setProvinceInRingRatioStr(v1 + "%");
                }
                if (provinceOutNumTemp.get() == 0) {
                    result.setProvinceOutRingRatio(result.getProvinceOutNum() * 1.0);
                    result.setProvinceOutRingRatioStr("-");
                } else {
                    BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getProvinceOutNum() - provinceOutNumTemp.get()) / (provinceOutNumTemp.get() * 1.0));
                    double v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                    result.setProvinceOutRingRatio(v1);
                    result.setProvinceOutRingRatioStr(v1 + "%");
                }
                if (overseasNumTemp.get() == 0) {
                    result.setOverseasRingRatio(result.getOverseasNum() * 1.0);
                    result.setOverseasRingRatioStr("-");
                } else {
                    BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getOverseasNum() - overseasNumTemp.get()) / (overseasNumTemp.get() * 1.0));
                    double v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                    result.setOverseasRingRatio(v1);
                    result.setOverseasRingRatioStr(v1 + "%");
                }
            }
        }
        if (Objects.nonNull(yoyTimeStart)) {
            List<TktPersonalOrderDetailDO> personalOrderListTemp = this.tktPersonalOrderDetailMapper.listByReserveDate(companyId, (byte)2, yoyTimeStart, yoyTimeEnd);
            if (CollectionUtils.isEmpty(personalOrderListTemp)) {
                result.setOverseasYoy(result.getOverseasNum() * 1.0);
                result.setOverseasYoyStr("-");
                result.setProvinceInYoy(result.getProvinceInNum() * 1.0);
                result.setProvinceInYoyStr("-");
                result.setProvinceOutYoy(result.getProvinceOutNum() * 1.0);
                result.setProvinceOutYoyStr("-");
            } else {
                AtomicLong provinceInNumTemp = new AtomicLong(0L);
                AtomicLong provinceOutNumTemp = new AtomicLong(0L);
                AtomicLong overseasNumTemp = new AtomicLong(0L);
                personalOrderListTemp.forEach(v -> {
                    Byte touristCertificateType = v.getTouristCertificateType();
                    String touristCertificateNo = v.getTouristCertificateNo();
                    if ((byte)1 == touristCertificateType) {
                        if (StringUtils.isEmpty(touristCertificateNo)) {
                            return;
                        }
                        if (touristCertificateNo.startsWith(locationAdCode)) {
                            provinceInNumTemp.getAndIncrement();
                        } else {
                            provinceOutNumTemp.getAndIncrement();
                        }
                    } else if ((byte)3 == touristCertificateType) {
                        provinceOutNumTemp.getAndIncrement();
                    } else {
                        overseasNumTemp.getAndIncrement();
                    }
                });
                if (provinceInNumTemp.get() == 0) {
                    result.setProvinceInYoy(result.getProvinceInNum() * 1.0);
                    result.setProvinceInYoyStr("-");
                } else {
                    BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getProvinceInNum() - provinceInNumTemp.get()) / (provinceInNumTemp.get() * 1.0));
                    double v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                    result.setProvinceInYoy(v1);
                    result.setProvinceInYoyStr(v1 + "%");
                }
                if (provinceOutNumTemp.get() == 0) {
                    result.setProvinceOutYoy(result.getProvinceOutNum() * 1.0);
                    result.setProvinceInYoyStr("-");
                } else {
                    BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getProvinceOutNum() - provinceOutNumTemp.get()) / (provinceOutNumTemp.get() * 1.0));
                    double v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                    result.setProvinceOutYoy(v1);
                    result.setProvinceInYoyStr(v1 + "%");
                }
                if (overseasNumTemp.get() == 0) {
                    result.setOverseasYoy(result.getOverseasNum() * 1.0);
                    result.setOverseasYoyStr("-");
                } else {
                    BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getOverseasNum() - overseasNumTemp.get()) / (overseasNumTemp.get() * 1.0));
                    double v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                    result.setOverseasYoy(v1);
                    result.setOverseasYoyStr(v1 + "%");
                }
            }
        }
        return result;
    }

    @Override
    public AudiencePortraitAgeDTO audiencePortraitAge(Long companyId, Integer statisticsWay, String startTime, String endTime) {
//        if (StringUtils.isBlank(locationAdCode)) {
//            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "场馆地理位置未配置");
//        }
        Date timeStart = null;
        Date timeEnd = new Date();
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = DateUtils.getTimesDayNight();
        }else if(statisticsWay == 1){
            //本周
            timeStart = DateUtils.getTimesWeekMorning();
        }else if(statisticsWay == 2){
            //本月
            timeStart = DateUtils.getTimesMonthMorning();
        }else if(statisticsWay == 3){
            //本年
            timeStart = DateUtils.getTimesYearMorning();
        }else if(statisticsWay == 4){
            //至今
        }else if(statisticsWay == 6){
            //昨日
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(new Date(),-1,1));
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(new Date(),-1,2));
        }else{
            if(startTime == null){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "开始时间不能为空！");
            }
            if(endTime == null){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "结束时间不能为空！");
            }
            //自定义时间
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        }
        List<TktPersonalOrderDetailDO> personalOrderList = this.tktPersonalOrderDetailMapper.listByReserveDate(companyId, (byte)2, timeStart, timeEnd);
        AudiencePortraitAgeDTO result = new AudiencePortraitAgeDTO();
        if (CollectionUtils.isEmpty(personalOrderList)) {
            // AudiencePortraitAgeDTO audiencePortraitAgeDTO = new AudiencePortraitAgeDTO();
            result.setAdultNum(0L);
            result.setAdultNumRatio(0d);
            result.setAdultNumRingRatio(0d);
            result.setAdultNumYoy(0d);
            result.setNoAdultNum(0L);
            result.setNoAdultNumRatio(0d);
            result.setNoAdultNumRingRatio(0d);
            result.setNoAdultNumYoy(0d);
            // return audiencePortraitAgeDTO;
        } else {
            result.setInMuseumNum((long)personalOrderList.size());
            AtomicLong adultNum = new AtomicLong(0L);
            AtomicLong noAdultNum = new AtomicLong(0L);
            personalOrderList.forEach(v -> {
                Byte touristCertificateType = v.getTouristCertificateType();
                String touristCertificateNo = v.getTouristCertificateNo();
                if ((byte)1 != touristCertificateType) {
                    return;
                }
                int age = countAge(touristCertificateNo);
                if (age == -1) {
                    return;
                }
                if (age < 18) {
                    noAdultNum.getAndIncrement();
                } else {
                    adultNum.getAndIncrement();
                }
            });
            result.setInMuseumNum((long)personalOrderList.size());
            result.setAdultNum(adultNum.get());
            result.setNoAdultNum(noAdultNum.get());
            BigDecimal bigDecimal = BigDecimal.valueOf(adultNum.get() / (result.getInMuseumNum() * 1.0));
            result.setAdultNumRatio(bigDecimal.setScale(2, RoundingMode.HALF_UP).doubleValue() * 100);
            result.setAdultNumRatioStr(result.getAdultNumRatio() + "%");
            result.setNoAdultNumRatio(new BigDecimal(100).subtract(BigDecimal.valueOf(result.getAdultNumRatio())).doubleValue());
            result.setNoAdultNumRatioStr(result.getNoAdultNumRatio() + "%");
        }

        Date ringRatioTimeStart;
        Date ringRatioTimeEnd;
        Date yoyTimeStart = null;
        Date yoyTimeEnd = null;
        if(statisticsWay == 0){
            //今日
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeEnd = calYoy.getTime();

            Calendar cal = Calendar.getInstance();
            cal.setTime(timeStart);
            cal.add(Calendar.YEAR, -1);
            ringRatioTimeStart = cal.getTime();
            Calendar calEnd = Calendar.getInstance();
            calEnd.setTime(timeEnd);
            calEnd.add(Calendar.YEAR, -1);
            ringRatioTimeEnd = calEnd.getTime();
        }else if(statisticsWay == 1){
            //本周
            Calendar cal = Calendar.getInstance();
            cal.setTime(DateUtils.getTimesWeekMorning());
            cal.add(Calendar.DAY_OF_WEEK, -7);
            ringRatioTimeStart = cal.getTime();

            Calendar call = Calendar.getInstance();
            call.setTime(DateUtils.getTimesWeekNight());
            call.add(Calendar.DAY_OF_WEEK, -7);
            ringRatioTimeEnd = call.getTime();
        }else if(statisticsWay == 2){
            //本月
            Calendar call = Calendar.getInstance();
            call.setTime(DateUtils.getTimesMonthMorning());
            call.add(Calendar.MONTH, -1);
            ringRatioTimeStart = call.getTime();
            Calendar cal = Calendar.getInstance();
            cal.setTime(DateUtils.getTimesDayNight());
            cal.add(Calendar.MONTH, -1);
            ringRatioTimeEnd = cal.getTime();
            if (DateUtils.getTimesMonthNight().equals(DateUtils.getTimesDayNight())) {
                Calendar calTemp = Calendar.getInstance();
                calTemp.setTime(new Date());
                calTemp.add(Calendar.MONTH, -1);
                ringRatioTimeEnd = DateUtils.getLastDayOfMonth((calTemp.get(Calendar.YEAR)), (calTemp.get(Calendar.MONTH) + 1));
            } else {
                Calendar calTemp = Calendar.getInstance();
                calTemp.setTime(new Date());
                calTemp.add(Calendar.MONTH, -1);
                Date ringRatioTimeEndTemp = DateUtils.getLastDayOfMonth((calTemp.get(Calendar.YEAR)), (calTemp.get(Calendar.MONTH) + 1));
                ringRatioTimeEnd = ringRatioTimeEndTemp.getTime() < ringRatioTimeEnd.getTime() ? ringRatioTimeEndTemp : ringRatioTimeEnd;
            }

            if  (DateUtils.getTimesMonthNight().equals(DateUtils.getTimesDayNight())) {
                Calendar callYoy = Calendar.getInstance();
                callYoy.setTime(new Date());
                yoyTimeStart = DateUtils.getFirstDayOfMonth((callYoy.get(Calendar.YEAR) - 1), (callYoy.get(Calendar.MONTH) + 1));
                yoyTimeEnd = DateUtils.getLastDayOfMonth((callYoy.get(Calendar.YEAR) - 1), (callYoy.get(Calendar.MONTH) + 1));
            } else {
                Calendar callYoy = Calendar.getInstance();
                callYoy.setTime(DateUtils.getTimesMonthMorning());
                callYoy.add(Calendar.YEAR, -1);
                yoyTimeStart = callYoy.getTime();

                Calendar calYoy = Calendar.getInstance();
                calYoy.setTime(DateUtils.getTimesDayNight());
                calYoy.add(Calendar.YEAR, -1);
                yoyTimeEnd = calYoy.getTime();
            }
        }else if(statisticsWay == 3){
            //本年
            Calendar temp = Calendar.getInstance();
            temp.setTime(new Date());
            temp.add(Calendar.YEAR, -1);
            Calendar ringRatioYoy = Calendar.getInstance();
            ringRatioYoy.clear();
            ringRatioYoy.set(Calendar.YEAR, temp.get(Calendar.YEAR));
            ringRatioYoy.roll(Calendar.DAY_OF_YEAR, -1);
            ringRatioTimeEnd = ringRatioYoy.getTime();

            ringRatioYoy = Calendar.getInstance();
            ringRatioYoy.clear();
            ringRatioYoy.set(Calendar.YEAR, temp.get(Calendar.YEAR));
            ringRatioTimeStart = ringRatioYoy.getTime();
        }else if(statisticsWay == 4){
            return result;
        }else if(statisticsWay == 6){
            //昨日
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            yoyTimeEnd = calYoy.getTime();

            Calendar cal = Calendar.getInstance();
            cal.setTime(timeStart);
            cal.add(Calendar.YEAR, -1);
            ringRatioTimeStart = cal.getTime();
            Calendar calEnd = Calendar.getInstance();
            calEnd.setTime(timeEnd);
            calEnd.add(Calendar.YEAR, -1);
            ringRatioTimeEnd = calEnd.getTime();
        } else {
            return result;
        }

        {
            List<TktPersonalOrderDetailDO> personalOrderListTemp = this.tktPersonalOrderDetailMapper.listByReserveDate(companyId, (byte)2, ringRatioTimeStart, ringRatioTimeEnd);
            if (CollectionUtils.isEmpty(personalOrderListTemp)) {
                result.setAdultNumRingRatio(result.getAdultNum() * 1.0);
                result.setAdultNumRingRatioStr("-");
                result.setNoAdultNumRingRatio(result.getNoAdultNum() * 1.0);
                result.setNoAdultNumRingRatioStr("-");
            } else {
                AtomicLong adultNumTemp = new AtomicLong(0L);
                AtomicLong noAdultNumTemp = new AtomicLong(0L);
                personalOrderListTemp.forEach(v -> {
                    Byte touristCertificateType = v.getTouristCertificateType();
                    String touristCertificateNo = v.getTouristCertificateNo();
                    if ((byte)1 != touristCertificateType) {
                        return;
                    }
                    int age = countAge(touristCertificateNo);
                    if (age == -1) {
                        return;
                    }
                    if (age < 18) {
                        noAdultNumTemp.getAndIncrement();
                    } else {
                        adultNumTemp.getAndIncrement();
                    }
                });
                if (adultNumTemp.get() == 0) {
                    result.setAdultNumRingRatio(result.getAdultNum() * 1.0);
                    result.setAdultNumRingRatioStr("-");
                } else {
                    BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getAdultNum() - adultNumTemp.get()) / (adultNumTemp.get() * 1.0));
                    double v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                    result.setAdultNumRingRatio(v1);
                    result.setAdultNumRingRatioStr(v1 + "%");
                }
                if (noAdultNumTemp.get() == 0) {
                    result.setNoAdultNumRingRatio(result.getNoAdultNum() * 1.0);
                    result.setNoAdultNumRingRatioStr("-");
                } else {
                    BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getNoAdultNum() - noAdultNumTemp.get()) / (noAdultNumTemp.get() * 1.0));
                    double v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                    result.setNoAdultNumRingRatio(v1);
                    result.setNoAdultNumRingRatioStr(v1 + "%");
                }
            }
        }
        if (Objects.nonNull(yoyTimeStart)) {
            List<TktPersonalOrderDetailDO> personalOrderListTemp = this.tktPersonalOrderDetailMapper.listByReserveDate(companyId, (byte)2, yoyTimeStart, yoyTimeEnd);
            if (CollectionUtils.isEmpty(personalOrderListTemp)) {
                result.setAdultNumYoy(result.getAdultNum() * 1.0);
                result.setAdultNumYoyStr("-");
                result.setNoAdultNumYoy(result.getNoAdultNum() * 1.0);
                result.setNoAdultNumYoyStr("-");
            } else {
                AtomicLong adultNumTemp = new AtomicLong(0L);
                AtomicLong noAdultNumTemp = new AtomicLong(0L);
                personalOrderListTemp.forEach(v -> {
                    Byte touristCertificateType = v.getTouristCertificateType();
                    String touristCertificateNo = v.getTouristCertificateNo();
                    if ((byte)1 != touristCertificateType) {
                        return;
                    }
                    int age = countAge(touristCertificateNo);
                    if (age == -1) {
                        return;
                    }
                    if (age < 18) {
                        noAdultNumTemp.getAndIncrement();
                    } else {
                        adultNumTemp.getAndIncrement();
                    }
                });
                if (adultNumTemp.get() == 0) {
                    result.setAdultNumYoy(result.getAdultNum() * 1.0);
                    result.setAdultNumYoyStr("-");
                } else {
                    BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getAdultNum() - adultNumTemp.get()) / (adultNumTemp.get() * 1.0));
                    double v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                    result.setAdultNumYoy(v1);
                    result.setAdultNumYoyStr(v1 + "%");
                }
                if (noAdultNumTemp.get() == 0) {
                    result.setNoAdultNumYoy(result.getNoAdultNum() * 1.0);
                    result.setNoAdultNumYoyStr("-");
                } else {
                    BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getNoAdultNum() - noAdultNumTemp.get()) / (noAdultNumTemp.get() * 1.0));
                    double v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                    result.setNoAdultNumYoy(v1);
                    result.setNoAdultNumYoyStr(v1 + "%");
                }
            }
        }
        return result;
    }

    @Override
    public List<StatisticsByProvinceDTO> statisticsByProvince(Long companyId, Integer statisticsWay, String startTime, String endTime){
        Date timeStart = null;
        Date timeEnd = null;
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = DateUtils.getTimesDayNight();
        }else if(statisticsWay == 1){
            //本周
            timeStart = DateUtils.getTimesWeekMorning();
        }else if(statisticsWay == 2){
            //本月
            timeStart = DateUtils.getTimesMonthMorning();
        }else if(statisticsWay == 3){
            //本年
            timeStart = DateUtils.getTimesYearMorning();
        }else if(statisticsWay == 4){
            //至今
        }else if(statisticsWay == 6){
            //昨日
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(new Date(),-1,1));
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(new Date(),-1,2));
        }else{
            if(startTime == null){
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "startTime 不能为空！");
            }
            if(endTime == null){
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "endTime 不能为空！");
            }
            //自定义时间
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        }

        List<ListGroupByProvinceDO> listGroupByProvinceDO = this.tktPersonalOrderDetailMapper.listGroupByProvince(companyId, (byte) 2,timeStart,timeEnd);
        if(CollectionUtils.isEmpty(listGroupByProvinceDO)){
            return Lists.newArrayList();
        }

        return listGroupByProvinceDO.stream().map(groupByProvinceDO -> {
            StatisticsByProvinceDTO dto = new StatisticsByProvinceDTO();
            dto.setProvinceCode(groupByProvinceDO.getProvinceCode());
            try {
                dto.setProvinceName(ProvinceCodeEnum.fromCode(groupByProvinceDO.getProvinceCode()).getDesc());
            }catch (Exception e){
                dto.setProvinceName("未知省份");
            }
            dto.setVisitorNum(groupByProvinceDO.getVisitorNum());
            return dto;
        }).collect(Collectors.toList());

    }


    /**
     * 根据身份证的号码算出当前身份证持有者的年龄
     *
     * @return
     */
    public int countAge(String idNumber) {
        if(idNumber.length() != 18 && idNumber.length() != 15){
            return -1;
        }
        String year;
        String yue;
        String day;
        if(idNumber.length() == 18){
            year = idNumber.substring(6).substring(0, 4);// 得到年份
            yue = idNumber.substring(10).substring(0, 2);// 得到月份
            day = idNumber.substring(12).substring(0,2);//得到日
        }else{
            year = "19" + idNumber.substring(6, 8);// 年份
            yue = idNumber.substring(8, 10);// 月份
            day = idNumber.substring(10, 12);//日
        }
        Date date = new Date();// 得到当前的系统时间
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String fyear = format.format(date).substring(0, 4);// 当前年份
        String fyue = format.format(date).substring(5, 7);// 月份
        String fday=format.format(date).substring(8,10);//
        int age = 0;
        if(Integer.parseInt(yue) == Integer.parseInt(fyue)){//如果月份相同
            //说明已经过了生日或者今天是生日
            if (Integer.parseInt(day) <= Integer.parseInt(fday)) {
                age = Integer.parseInt(fyear) - Integer.parseInt(year);
            } else {
                age = Integer.parseInt(fyear) - Integer.parseInt(year) - 1;
            }
        }else{

            if(Integer.parseInt(yue) < Integer.parseInt(fyue)){
                //如果当前月份大于出生月份
                age = Integer.parseInt(fyear) - Integer.parseInt(year);
            }else{
                //如果当前月份小于出生月份,说明生日还没过
                age = Integer.parseInt(fyear) - Integer.parseInt(year) - 1;
            }
        }
        return age;
    }

    public static void main(String[] args) {
        BigDecimal v1 = new BigDecimal(4 - 7).divide(new BigDecimal(7), 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(1,RoundingMode.HALF_UP);
        System.out.println(v1);
    }
}
