package com.das.museum.service.biz.collection.action;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class EditCollectionRegisterAction extends AddCollectionRegisterAction {
    private static final long serialVersionUID = -5222835952730441175L;
    private Long collectionRegisterId;
    private Long modifier;
    private String modifierName;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}
