package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.util.List;

@Data
public class IdentifyCollectionInfoPageDTO {
    private Long collectionId;
    private String name;
    private String documentId;
    private List<CommonDocumentDTO> commonDocumentList;
    private Integer collectionCount;
    private Long countUnit;
    private String countUnitName;
    private Integer actualCount;
    private Long appraiserId;
    private String appraiserName;
    private String identifyDate;
    private String identifyAgency;
    private String identifyRemark;
    private List<CommonDocumentDTO> identifyDocumentList;
    private String identifyDocumentId;
    private String identifyOpinion;
    private String era;
    private String textureCategory;
    private String textureCategoryName;
    private String texture;
    private String textureName;
    private Long initialLevel;
    private Double amount;
    private String initialLevelName;
    private String size;
    private Long sizeUnit;
    private String sizeUnitName;
    private String completeDegree;
    private String completeDegreeName;
    private String completeInfo;
    private String holder;
    private Long collectType;
    private String collectTypeName;
    private String collectDate;
    private String payVoucherNo;
    private String introduce;
    private String inputMode;
    private String inputType;
    private String remark;
    private Boolean checked;
    private String checkStatus;
    private String checkStatusDesc;
}
