package com.das.museum.service.biz.collection;

import cn.hutool.core.util.ZipUtil;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.UUIDUtils;
import com.das.museum.domain.enums.RegisterStatusEnum;
import com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO;
import com.das.museum.infr.dataobject.ScsCollectionTwoImageDO;
import com.das.museum.infr.mapper.ScsCollectionRegisterInfoMapper;
import com.das.museum.infr.mapper.ScsCollectionTwoImageMapper;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.enums.FileTypeEnum;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.common.document.response.DocumentVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

import static cn.hutool.core.util.CharsetUtil.GBK;

/**
 * 导入二维影像
 * <AUTHOR>
 * @date 2023/6/25
 */
@Slf4j
@Service
public class CollectionTwoImageImportServiceImpl implements CollectionTwoImageImportService {

    @Resource
    private ScsCollectionTwoImageMapper scsCollectionTwoImageMapper;

    @Resource
    private ScsCollectionRegisterInfoMapper scsCollectionRegisterInfoMapper;

    @Resource
    private DocumentService documentService;

    private static final Map<String, List<ErrResultBO>> ERR_RESULT_MAP = Maps.newConcurrentMap();

    private static final Map<String, ImportResult> IMPORT_RESULT = Maps.newConcurrentMap();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importTwoImageData(String importUniqueCode, MultipartFile file, LoginUserInfoBO userBO) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)) {
            return;
        }
        try {
            Long companyId = userBO.getCompanyId();
            String fileDir = UUIDUtils.generateUUID();
            String targetFileName = fileDir + "_" + fileName;
            String osName = System.getProperties().get("os.name").toString().toLowerCase(Locale.ROOT);
            String targetPath = "";
            if (osName.startsWith("win")) {
                targetPath = "D:\\data\\collection\\images\\" + fileDir + "\\";
            } else {
                targetPath = "/media/vdb/collection/images/" + fileDir + "/";
            }
            File targetFile = new File(targetPath + targetFileName);
            if (!targetFile.getParentFile().exists()) {
                targetFile.getParentFile().mkdirs();
            }
            FileUtils.copyInputStreamToFile(file.getInputStream(),targetFile);
            File unzip;
            try {
                unzip = ZipUtil.unzip(targetFile.getPath(), targetPath);
            } catch (IllegalArgumentException e) {
                log.error("CollectionTwoImageImportServiceImpl importTwoImageData importUniqueCode: {} source unzip Exception: ", importUniqueCode, e);
                unzip = ZipUtil.unzip(targetFile.getPath(), targetPath, Charset.forName(GBK));
            }
            /*if (osName.startsWith("win")) {
                unzip = ZipUtil.unzip(targetFile.getPath(), targetPath, Charset.forName(GBK));
            } else {
                unzip = ZipUtil.unzip(targetFile.getPath(), targetPath);
            }*/
            if (!unzip.exists()) {
                ERR_RESULT_MAP.put(importUniqueCode,
                        Lists.newArrayList(new ErrResultBO(fileName, "文件解析失败")));
                IMPORT_RESULT.put(importUniqueCode, new ImportResult(2, 0, 0, 0));
                return;
            }
            File zipFile = new File(unzip.getPath() + File.separator +fileName.substring(0, fileName.lastIndexOf(".")));
            if (!zipFile.exists()) {
                zipFile = new File(unzip.getPath());
                if (!zipFile.exists()) {
                    ERR_RESULT_MAP.put(importUniqueCode,
                            Lists.newArrayList(new ErrResultBO(fileName, "文件解析失败")));
                    IMPORT_RESULT.put(importUniqueCode, new ImportResult(2, 0, 0, 0));
                    return;
                }
            }
            File[] files = zipFile.listFiles();
            if (Objects.isNull(files) || files.length == 0) {
                ERR_RESULT_MAP.put(importUniqueCode,
                        Lists.newArrayList(new ErrResultBO(fileName, "文件为空, 解析失败")));
                IMPORT_RESULT.put(importUniqueCode, new ImportResult(2, 0, 0, 0));
                return;
            }
            List<ErrResultBO> errResultBOList = Lists.newArrayList();
            int successNum = 0;
            int failedNum = 0;
            for (File f : files) {
                if (f.getName().equalsIgnoreCase(targetFileName)) {
                    continue;
                }
                if (f.isDirectory()) {
                    File[] fileList = f.listFiles();
                    if (Objects.isNull(fileList) || fileList.length == 0) {
                        errResultBOList.add(new ErrResultBO(f.getName(), "文件夹为空"));
                        failedNum++;
                        continue;
                    }
                    //System.out.println(f.getName());
                    //String registerNo = f.getName().substring(0, f.getName().lastIndexOf("."));
                    ScsCollectionRegisterInfoDO collectionRegisterInfoDO = this.scsCollectionRegisterInfoMapper
                            .selectByRegisterNo(companyId, f.getName());
                    if (Objects.isNull(collectionRegisterInfoDO)
                            || RegisterStatusEnum.ALGF.getCode().equals(collectionRegisterInfoDO.getRegisterStatus())) {
                        errResultBOList.add(new ErrResultBO(f.getName(), "未找到与文件夹名称匹配的藏品总登记号"));
                        failedNum += fileList.length;
                        continue;
                    }
                    Long documentId = null;
                    for (File lf : fileList) {
                        if (lf.isDirectory()) {
                            errResultBOList.add(new ErrResultBO(lf.getName(), "文件夹中含有不支持格式的文件或其他文件夹"));
                            failedNum++;
                        } else {
                            try {
                                String saveFileName = lf.getName().toLowerCase();
                                if (!(saveFileName.endsWith(".jpg") || saveFileName.endsWith("jpeg")
                                        || saveFileName.endsWith("png") || saveFileName.endsWith("raw") || saveFileName.endsWith("tiff") || saveFileName.endsWith("tif"))) {
                                    errResultBOList.add(new ErrResultBO(lf.getName(), "文件夹中含有不支持格式的文件或其他文件夹"));
                                    failedNum++;
                                    continue;
                                }
                                DocumentVO documentVO = this.documentService
                                        .uploadFile(lf, FileTypeEnum.IMAGE.getCode(), true, 0.5D, userBO);
                                ScsCollectionTwoImageDO scsCollectionTwoImageDO = new ScsCollectionTwoImageDO();
                                scsCollectionTwoImageDO.setCompanyId(companyId);
                                scsCollectionTwoImageDO.setDocumentId(documentVO.getDocumentId());
                                scsCollectionTwoImageDO.setRegisterInfoId(collectionRegisterInfoDO.getId());
                                scsCollectionTwoImageDO.setCreator(userBO.getUserId());
                                scsCollectionTwoImageDO.setModifier(userBO.getUserId());
                                scsCollectionTwoImageMapper.insertSelective(scsCollectionTwoImageDO);
                                if(documentId == null){
                                    documentId = documentVO.getDocumentId();
                                }
                                successNum++;
                            } catch (Exception e) {
                                errResultBOList.add(new ErrResultBO(lf.getName(), "文件上传失败"));
                                failedNum++;
                            }
                        }
                    }
                    // 判断藏品登录信息是否有封面图片，如果没有则填充
                    if(StringUtils.isBlank(collectionRegisterInfoDO.getDocumentId()) && documentId != null){
                        this.scsCollectionRegisterInfoMapper.updateDocumentIdById(companyId,collectionRegisterInfoDO.getId(),String.valueOf(documentId));
                    }
                } else {
                    errResultBOList.add(new ErrResultBO(f.getName(), "文件未存在以藏品总登记号命名的文件夹中，无法导入"));
                    failedNum++;
                }
            }
            IMPORT_RESULT.put(importUniqueCode, new ImportResult(1, successNum, failedNum, 0));
            if (failedNum != 0) {
                IMPORT_RESULT.put(importUniqueCode, new ImportResult(2, successNum, failedNum, 0));
            }
            ERR_RESULT_MAP.put(importUniqueCode, errResultBOList);
            try {
                if (targetFile.exists()) {
                    FileUtils.deleteDirectory(new File(targetPath));
                    /*FileUtils.delete(targetFile);
                    FileUtils.deleteDirectory(new File(unzip.getPath() +  File.separator +fileName.substring(0, fileName.lastIndexOf("."))));*/
                }
            } catch (Exception e) {
                log.error("CollectionTwoImageImportServiceImpl importTwoImageData importUniqueCode: {} source delete Exception: ", importUniqueCode, e);
            }
        } catch (Exception e) {
            log.error("CollectionTwoImageImportServiceImpl  importTwoImageData importUniqueCode: {} Exception: ", importUniqueCode, e);
            ERR_RESULT_MAP.put(importUniqueCode,
                    Lists.newArrayList(new ErrResultBO(fileName, "文件解析失败")));
            IMPORT_RESULT.put(importUniqueCode, new ImportResult(2, 0, 0, 0));
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
    }

    public static Map<String, ImportResult> getImportResult() {
        return IMPORT_RESULT;
    }

    public static Map<String, List<ErrResultBO>> getErrResultMap() {
        return ERR_RESULT_MAP;
    }

    @Data
    public static class ErrResultBO {

        private String fileName;

        private String errorMessage;

        ErrResultBO(String fileName, String errorMessage) {
            this.fileName = fileName;
            this.errorMessage = errorMessage;
        }
    }

    @Data
    public static class ImportResult {
        /**
         * 导入状态：0 导入中，1 导入成功 , 2 导入失败
         */
        private Integer importStatus;

        /**
         * 成功数量
         */
        private Integer successNum;

        /**
         * 失败数量
         */
        private Integer failedNum;

        /**
         * 导入结果是否已确认：0 否，1 是
         */
        private Integer resultConfirm;

        public ImportResult(Integer importStatus, Integer successNum, Integer failedNum, Integer resultConfirm) {
            this.importStatus = importStatus;
            this.successNum = successNum;
            this.failedNum = failedNum;
            this.resultConfirm = resultConfirm;
        }
    }
}
