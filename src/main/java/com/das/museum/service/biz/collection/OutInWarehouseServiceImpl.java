package com.das.museum.service.biz.collection;

import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.constants.CollectionConstant;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.PaginationUtils;
import com.das.museum.domain.collection.entity.OutInWarehouseCertificateEntity;
import com.das.museum.domain.collection.repository.CollectionRepository;
import com.das.museum.domain.collection.repository.OutInWarehouseCertificateRepository;
import com.das.museum.domain.enums.CheckMainStatusEnum;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.domain.enums.OutInWarehouseStatusEnum;
import com.das.museum.domain.enums.RegisterStatusEnum;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.mapper.*;
import com.das.museum.infr.query.OutInWarehouseConditionQuery;
import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.*;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.service.biz.user.UserService;
import com.das.museum.service.biz.user.dto.UserDTO;
import com.das.museum.service.enums.YesOrNoEnum;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class OutInWarehouseServiceImpl implements OutInWarehouseService {

    @Resource
    private OutInWarehouseCertificateRepository outInWarehouseCertificateRepository;

    @Resource
    private ScsOutInWarehouseCertificateInfoMapper scsOutInWarehouseCertificateInfoMapper;

    @Resource
    private ScsOutWarehouseLogMapper scsOutWarehouseLogMapper;

    @Resource
    private ScsInWarehouseLogMapper scsInWarehouseLogMapper;

    @Resource
    private CollectionService collectionService;

    @Resource
    private DocumentService documentService;

    @Resource
    private ScsBizCollectionRelMapper scsBizCollectionRelMapper;

    @Resource
    private ScsCollectionRegisterInfoMapper scsCollectionRegisterInfoMapper;

    @Resource
    private ScsCollectionInfoMapper scsCollectionInfoMapper;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private CollectionRegisterService collectionRegisterService;

    @Resource
    private CheckService checkService;

    @Resource
    private CollectionRepository collectionRepository;

    @Resource
    private UserService userService;

    @Resource
    private CommIncrSegmentMapper commIncrSegmentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addOutInWarehouseCertificate(AddOutInWarehouseAction action) {
        String bizDimension = DateUtils.formatDateYYYMMDD().substring(0, 4);
        CommIncrSegmentDO commIncrSegmentDO = this.commIncrSegmentMapper
                .selectByCompanyIdAndBizCode(action.getCompanyId(), CollectionBizTypeEnum.OUTIN.getCode(), bizDimension);
        if (Objects.isNull(commIncrSegmentDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "出入库凭证自动编号配置不存在");
        }
        Long bizCurrIndex = commIncrSegmentDO.getBizCurrIndex();
        OutInWarehouseCertificateEntity entity = this.convertOutInWarehouseCertificateEntity(action);
        entity.setAutoAudit(action.getAutoAudit());
        String outInWarehouseCertificateNo = entity.getPrefixName()
                + CollectionConstant.OUT_IN_WAREHOUSE_CERTIFICATE_MIDDLE + (bizCurrIndex + 1)
                + CollectionConstant.CERTIFICATE_TAIL;
        entity.setOutInWarehouseCertificateNo(outInWarehouseCertificateNo);
        entity.setSuffixNumber(bizCurrIndex + 1);
        this.outInWarehouseCertificateRepository.saveOutInWarehouseCertificate(entity);
        Long outInWarehouseId = entity.getOutInWarehouseId();
        /*OutInWarehouseCertificateEntity updateEntity = new OutInWarehouseCertificateEntity();
        updateEntity.setOutInWarehouseId(outInWarehouseId);
        updateEntity.setSuffixNumber(outInWarehouseId);
        updateEntity.setOutInWarehouseCertificateNo(entity.getPrefixName()
                + CollectionConstant.OUT_IN_WAREHOUSE_CERTIFICATE_MIDDLE + updateEntity.getSuffixNumber()
                + CollectionConstant.CERTIFICATE_TAIL);
        updateEntity.setAutoAudit(action.getAutoAudit());
        this.outInWarehouseCertificateRepository.editOutInWarehouseCertificate(updateEntity);*/
        this.collectionRegisterService.updateRegisterStatuesByCollectionIds(action.getCompanyId(), RegisterStatusEnum.PPOWH, action.getCollectionIds(),action.getCreator());
        int row = this.commIncrSegmentMapper.updateByBizCurrIndex(commIncrSegmentDO.getId(), bizCurrIndex);
        if (row <= 0) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "出入库凭证自动获取编号失败");
        }
        // 自动提审
        if(action.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(action.getCreator());
            submitCheckAction.setCompanyId(action.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.OUTIN);
            submitCheckAction.setModelId(outInWarehouseId);
            submitCheckAction.setCreatorName(action.getCreatorName());
            this.checkService.submitCheck(submitCheckAction);
        }
        return entity.getOutInWarehouseId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editOutInWarehouseCertificate(EditOutInWarehouseAction action) {
        OutInWarehouseInfoDetailDTO outInWarehouseDTO = this.queryById(action.getCompanyId(), action.getOutInWarehouseId());
        if (Objects.isNull(outInWarehouseDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "出入库凭证信息不存在");
        }
        CheckStatusByModelIdAction checkStatusByModelIdAction = new CheckStatusByModelIdAction();
        checkStatusByModelIdAction.setBelongModel(CollectionBizTypeEnum.OUTIN);
        checkStatusByModelIdAction.setCompanyId(action.getCompanyId());
        checkStatusByModelIdAction.setModelId(action.getOutInWarehouseId());
        this.checkService.checkStatusByModelId(checkStatusByModelIdAction);

        // 原有的藏品状态还原
        List<Long> collectionIds = collectionRepository.queryByBizIdAndBizType(action.getOutInWarehouseId(), CollectionBizTypeEnum.OUTIN.getCode());
        this.collectionRegisterService.updateRegisterStatuesByCollectionIds(action.getCompanyId(), RegisterStatusEnum.ARTS, collectionIds, action.getModifier());

        this.scsBizCollectionRelMapper.deleteByBizIdAndBizType(action.getOutInWarehouseId(), CollectionBizTypeEnum.OUTIN.getCode());
        OutInWarehouseCertificateEntity entity = this.convertOutInWarehouseCertificateEntity(action);
        this.outInWarehouseCertificateRepository.editOutInWarehouseCertificate(entity);
        this.collectionRegisterService.updateRegisterStatuesByCollectionIds(action.getCompanyId(), RegisterStatusEnum.PPOWH, action.getCollectionIds(),action.getModifier());

        // 自动提审
        if(action.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(action.getModifier());
            submitCheckAction.setCompanyId(action.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.OUTIN);
            submitCheckAction.setModelId(action.getOutInWarehouseId());
            submitCheckAction.setCreatorName(action.getModeifierName());
            this.checkService.submitCheck(submitCheckAction);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delOutInWarehouseCertificateById(Long companyId, Long outInWarehouseId) {
        OutInWarehouseInfoDetailDTO outInWarehouseDTO = this.queryById(companyId, outInWarehouseId);
        if (Objects.isNull(outInWarehouseDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "出入库凭证信息不存在");
        }
        CheckStatusByModelIdAction checkStatusByModelIdAction = new CheckStatusByModelIdAction();
        checkStatusByModelIdAction.setBelongModel(CollectionBizTypeEnum.OUTIN);
        checkStatusByModelIdAction.setCompanyId(companyId);
        checkStatusByModelIdAction.setModelId(outInWarehouseId);
        this.checkService.checkStatusByModelId(checkStatusByModelIdAction);
        List<Long> collectionIds = collectionRepository.queryByBizIdAndBizType(outInWarehouseId,CollectionBizTypeEnum.OUTIN.getCode());
        if(!collectionIds.isEmpty()) {
            this.collectionRegisterService.updateRegisterStatusByCollectionId(companyId, collectionIds, RegisterStatusEnum.ARTS);
        }
        this.outInWarehouseCertificateRepository.delOutInWarehouseCertificateById(companyId, outInWarehouseId);

    }

    @Override
    public OutInWarehouseInfoDetailDTO queryById(Long companyId, Long outInWarehouseId) {
        ScsOutInWarehouseCertificateInfoDO outInWarehouseDO = this.scsOutInWarehouseCertificateInfoMapper.selectById(companyId, outInWarehouseId);
        if (Objects.isNull(outInWarehouseDO)) {
            return null;
        }
        OutInWarehouseInfoDetailDTO outInWarehouseDTO = BeanCopyUtils.copyByJSON(outInWarehouseDO, OutInWarehouseInfoDetailDTO.class);
        outInWarehouseDTO.setOutInWarehouseId(outInWarehouseDO.getId());

        if(StringUtils.isNotBlank(outInWarehouseDTO.getUseId())){
            UserDTO userDTO = this.userService.queryByLoginAccount(companyId, Long.parseLong(outInWarehouseDTO.getUseId()));
            if(Objects.nonNull(userDTO)){
                outInWarehouseDTO.setUseName(userDTO.getUserName());
            }
        }

        List<Long> commIds = new ArrayList<>();

        if(StringUtils.isNotBlank(outInWarehouseDTO.getUserDepartmentId())){
            commIds.add(Long.parseLong(outInWarehouseDTO.getUserDepartmentId()));
        }
        if(Objects.nonNull(outInWarehouseDO.getReason())){
            commIds.add(outInWarehouseDO.getReason());
        }

        List<CommClassificationDO> commClassificationDOList = this.commClassificationMapper.listByIds(companyId, commIds);
        if(CollectionUtils.isNotEmpty(commClassificationDOList)){
            Map<Long, String> commMap = commClassificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId, CommClassificationDO::getName, (key1, key2)->key2));
            outInWarehouseDTO.setUserDepartmentName(commMap.get(Long.parseLong(outInWarehouseDO.getUserDepartmentId())));
            outInWarehouseDTO.setReasonName(commMap.get(outInWarehouseDO.getReason()));
        }

        return outInWarehouseDTO;
    }

    @Override
    public SimplePageInfo<OutInWarehouseInfoPageDTO> queryPageByConditionQuery(QueryOutInPageByConditionAction action) {
        // action.startPage();
        OutInWarehouseConditionQuery query = new OutInWarehouseConditionQuery();
        query.setCompanyId(action.getCompanyId());
        query.setOutInWarehouseCertificateNo(action.getOutInWarehouseCertificateNo());
        query.setRegisterNo(action.getRegisterNo());
        query.setUseId(action.getUseId());
        query.setUserDepartmentId(action.getUserDepartmentId());
        query.setSortBy(action.getSortBy());
        List<ScsOutInWarehouseCertificateInfoDO> outInWarehouseDOList = this.scsOutInWarehouseCertificateInfoMapper.listPageByCondition(action.getCompanyId(), action.getUseId(), action.getUserDepartmentId(), action.getRegisterNo(),
                action.getOutInWarehouseCertificateNo(), action.getCollectionName(), action.getReason(), action.getSortBy(), query);
        if (CollectionUtils.isEmpty(outInWarehouseDOList)) {
            return new SimplePageInfo<>();
        }
        /*SimplePageInfo<OutInWarehouseInfoPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(outInWarehouseDOList, OutInWarehouseInfoPageDTO.class);*/
        List<OutInWarehouseInfoPageDTO> outInWarehouseInfoPageDTOList = this.setOutInWarehouseAttributes(action.getCompanyId(), outInWarehouseDOList, action.getCheckStatus(), action.getIsUserChecked());
        // pageInfo.setList(outInWarehouseInfoPageDTOList);
        return PaginationUtils.pagination(outInWarehouseInfoPageDTOList, action.getPageNum(), action.getPageSize());
    }

    @Override
    public SimplePageInfo<OutInWarehouseCollectionDTO> queryCollectionPageById(Long companyId, Long outInWarehouseId, String collectionName, String registerNo,
                                                                               List<RegisterStatusEnum> registerStatus, Integer pageNum, Integer pageSize) {
        List<ScsBizCollectionRelDO> collectionRelDOList = this.scsBizCollectionRelMapper.listByBizIdAndBizType(outInWarehouseId, CollectionBizTypeEnum.OUTIN.getCode());
        if (CollectionUtils.isEmpty(collectionRelDOList)) {
            return new SimplePageInfo<>();
        }
        List<Long> collectionIds = collectionRelDOList.stream().map(ScsBizCollectionRelDO::getCollectionId).collect(Collectors.toList());
        // 查询藏品
        List<ScsCollectionInfoDO> collectionInfoList = this.scsCollectionInfoMapper.listByIds(companyId, collectionIds);
        if (CollectionUtils.isEmpty(collectionInfoList)) {
            return new SimplePageInfo<>();
        }
        Map<Long, ScsCollectionInfoDO> collectionMap = Maps.newHashMap();
        collectionInfoList.forEach(collection -> collectionMap.put(collection.getId(), collection));
        RegisterQueryPageByConditionAction action = new RegisterQueryPageByConditionAction();
        action.setCompanyId(companyId);
        action.setSortBy("gmt_create desc");
        action.setCollectionIds(collectionIds);
//        action.setName(collectionName);
//        action.setRegisterNo(registerNo);
        action.setRegisterStatus(registerStatus);
        action.setPageNum(1);
        action.setPageSize(100000);
        SimplePageInfo<CollectionRegisterPageDTO> pageInfo = this.collectionRegisterService.queryPageByCondition(action);
        if (Objects.isNull(pageInfo) || CollectionUtils.isEmpty(pageInfo.getList())) {
            return new SimplePageInfo<>();
        }
        List<ScsOutWarehouseLogDO> outWarehouseLogDOList = this.scsOutWarehouseLogMapper.listByOutInWarehouseId(companyId, outInWarehouseId);
        Map<Long, List<ScsOutWarehouseLogDO>> outWarehouseLogMap = null;
        StringBuilder documentBuilder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(outWarehouseLogDOList)) {
            outWarehouseLogMap = outWarehouseLogDOList.stream().collect(Collectors.groupingBy(ScsOutWarehouseLogDO::getCollectionId));
            outWarehouseLogDOList.forEach(outWarehouseLog -> {
                if (StringUtils.isNotBlank(outWarehouseLog.getDocumentId())) {
                    documentBuilder.append(outWarehouseLog.getDocumentId()).append(',');
                }
            });
        }
        List<ScsInWarehouseLogDO> inWarehouseLogDOList = this.scsInWarehouseLogMapper.listByOutInWarehouseId(companyId, outInWarehouseId);
        Map<Long, List<ScsInWarehouseLogDO>> inWarehouseLogMap = null;
        if (CollectionUtils.isNotEmpty(inWarehouseLogDOList)) {
            inWarehouseLogMap = inWarehouseLogDOList.stream().collect(Collectors.groupingBy(ScsInWarehouseLogDO::getCollectionId));
            inWarehouseLogDOList.forEach(inWarehouseLog -> {
                if (StringUtils.isNotBlank(inWarehouseLog.getDocumentId())) {
                    documentBuilder.append(inWarehouseLog.getDocumentId()).append(',');
                }
            });
        }
        Map<Long, CommonDocumentDTO> documentMap = null;
        if (documentBuilder.length() != 0) {
            String documentStr = documentBuilder.deleteCharAt(documentBuilder.toString().length() - 1).toString();
            List<CommonDocumentDTO> documentDTOList = this.documentService.listByDocIds(companyId, documentStr);
            documentMap = documentDTOList.stream().collect(Collectors.toMap(CommonDocumentDTO::getDocumentId,
                    commDocumentDO -> commDocumentDO));
        }
        //SimplePageInfo<OutInWarehouseCollectionDTO> pageInfoResult = new SimplePageInfo<>();
        Map<Long, List<ScsOutWarehouseLogDO>> finalOutWarehouseLogMap = outWarehouseLogMap;
        Map<Long, List<ScsInWarehouseLogDO>> finalInWarehouseLogMap = inWarehouseLogMap;
        Map<Long, CommonDocumentDTO> finalDocumentMap = documentMap;
        List<OutInWarehouseCollectionDTO> outInWarehouseCollectionDTOList = pageInfo.getList().stream().map(collection -> {
            OutInWarehouseCollectionDTO outInWarehouseCollectionDTO = BeanCopyUtils.copyByJSON(collection, OutInWarehouseCollectionDTO.class);
            if (finalOutWarehouseLogMap != null && finalOutWarehouseLogMap.containsKey(collection.getCollectionId())) {
                outInWarehouseCollectionDTO.setRegisterStatus(RegisterStatusEnum.AOWH.getCode());
                List<ScsOutWarehouseLogDO> outWarehouseLogTempList = finalOutWarehouseLogMap.get(collection.getCollectionId());
                ScsOutWarehouseLogDO outWarehouseLogDO = outWarehouseLogTempList.get(0);
                outInWarehouseCollectionDTO.setOutDocumentId(outWarehouseLogDO.getDocumentId());
                if (finalDocumentMap != null) {
                    String documentId = outWarehouseLogDO.getDocumentId();
                    if (StringUtils.isNotBlank(documentId)) {
                        List<Long> documentTempIds = Stream.of(documentId.split(",")).map(Long::parseLong)
                                .distinct().collect(Collectors.toList());
                        List<CommonDocumentDTO> commonDocumentList = Lists.newArrayList();
                        documentTempIds.forEach(docId -> {
                            CommonDocumentDTO commDocumentDTO = finalDocumentMap.get(docId);
                            if (Objects.isNull(commDocumentDTO)) {
                                return;
                            }
                            commonDocumentList.add(commDocumentDTO);
                        });
                        outInWarehouseCollectionDTO.setOutDocumentList(commonDocumentList);
                    }
                }
                if (collectionMap.containsKey(collection.getCollectionId())) {
                    ScsCollectionInfoDO collectionInfoDO = collectionMap.get(collection.getCollectionId());
                    outInWarehouseCollectionDTO.setOutWarehouseDate(collectionInfoDO.getOutWarehouseDate());
                }
            }
            if (finalInWarehouseLogMap != null && finalInWarehouseLogMap.containsKey(collection.getCollectionId())) {
                List<ScsInWarehouseLogDO> inWarehouseLogTempList = finalInWarehouseLogMap.get(collection.getCollectionId());
                ScsInWarehouseLogDO inWarehouseLogDO = inWarehouseLogTempList.get(0);
                outInWarehouseCollectionDTO.setTransferor(inWarehouseLogDO.getTransferor());
                outInWarehouseCollectionDTO.setTransferorName(inWarehouseLogDO.getTransferorName());
                outInWarehouseCollectionDTO.setReceiver(inWarehouseLogDO.getReceiver());
                outInWarehouseCollectionDTO.setReceiverName(inWarehouseLogDO.getReceiverName());
                outInWarehouseCollectionDTO.setReturnAddress(inWarehouseLogDO.getReturnAddress());
                outInWarehouseCollectionDTO.setReturnRemark(inWarehouseLogDO.getReturnRemark());
                outInWarehouseCollectionDTO.setCollectionRemark(inWarehouseLogDO.getCollectionRemark());
                outInWarehouseCollectionDTO.setInDocumentId(inWarehouseLogDO.getDocumentId());
                outInWarehouseCollectionDTO.setRegisterStatus(RegisterStatusEnum.AIWH.getCode());
                // 附件
                if (finalDocumentMap != null) {
                    String documentId = inWarehouseLogDO.getDocumentId();
                    if (StringUtils.isNotBlank(documentId)) {
                        List<Long> documentTempIds = Stream.of(documentId.split(",")).map(Long::parseLong)
                                .distinct().collect(Collectors.toList());
                        List<CommonDocumentDTO> commonDocumentList = Lists.newArrayList();
                        documentTempIds.forEach(docId -> {
                            CommonDocumentDTO commDocumentDTO = finalDocumentMap.get(docId);
                            if (Objects.isNull(commDocumentDTO)) {
                                return;
                            }
                            commonDocumentList.add(commDocumentDTO);
                        });
                        outInWarehouseCollectionDTO.setInDocumentList(commonDocumentList);
                    }
                }
                if (collectionMap.containsKey(collection.getCollectionId())) {
                    ScsCollectionInfoDO collectionInfoDO = collectionMap.get(collection.getCollectionId());
                    outInWarehouseCollectionDTO.setInWarehouseDate(collectionInfoDO.getInWarehouseDate());
                }
            }
            return outInWarehouseCollectionDTO;
        }).collect(Collectors.toList());
//        BeanUtils.copyProperties(pageInfo,pageInfoResult);
//        pageInfoResult.setList(outInWarehouseCollectionDTOList);
//        return pageInfoResult;
        if(StringUtils.isNotBlank(collectionName)){
            outInWarehouseCollectionDTOList = outInWarehouseCollectionDTOList.stream().filter(s->s.getName().contains(collectionName) || s.getRegisterNo().contains(collectionName)).collect(Collectors.toList());
        }
        return PaginationUtils.pagination(outInWarehouseCollectionDTOList, action.getPageNum(), action.getPageSize());
    }

    @Override
    public List<CommonDocumentDTO> queryAttachmentByOutInWarehouseId(Long companyId, Long outInWarehouseId) {
        OutInWarehouseInfoDetailDTO outInWarehouseDTO = this.queryById(companyId, outInWarehouseId);
        if (Objects.isNull(outInWarehouseDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "出入库凭证信息不存在");
        }
        String documentId = outInWarehouseDTO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return Lists.newArrayList();
        }
        return this.documentService.listByDocIds(companyId, documentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void collectionOutWarehouse(CollectionOutWarehouseAction action) {
        Long companyId = action.getCompanyId();
        Long creator = action.getCreator();
        Long outInWarehouseId = action.getOutInWarehouseId();
        OutInWarehouseInfoDetailDTO outInWarehouseDTO = this.queryById(companyId, outInWarehouseId);
        if (Objects.isNull(outInWarehouseDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "出入库凭证信息不存在");
        }
        List<OutWarehouseCollectionAction> collectionList = action.getOutWarehouseCollection();
        if (CollectionUtils.isEmpty(collectionList)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "出库藏品信息为空");
        }
        QueryCheckStatusAction queryCheckStatusAction = new QueryCheckStatusAction();
        queryCheckStatusAction.setBelongModel(CollectionBizTypeEnum.OUTIN);
        queryCheckStatusAction.setCompanyId(companyId);
        queryCheckStatusAction.setModelId(outInWarehouseId);
        CheckMainStatusEnum statusEnum = this.checkService.queryCheckStatus(queryCheckStatusAction);
        if(!CheckMainStatusEnum.ATP.equals(statusEnum)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "出库凭证审核未通过，无法出库！");
        }
        Date outWarehouseDate = Objects.isNull(action.getOutWarehouseDate()) ? new Date() : action.getOutWarehouseDate();
        collectionList.forEach(collection -> {
            String registerNo = collection.getRegisterNo();
            String documentIds = collection.getDocumentId();
            if (StringUtils.isBlank(documentIds)) {
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "出库藏品照片为空, 请按规则上传出库藏品照片");
            }
            List<CommonDocumentDTO> documentDTOList = this.documentService.listByDocIds(companyId, documentIds);
            if (CollectionUtils.isEmpty(documentDTOList)) {
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "总登记号为[" + registerNo + "]藏品出库照片未上传, 请按规则上传出库藏品照片");
            }
            ScsOutWarehouseLogDO outWarehouseLogDO = new ScsOutWarehouseLogDO();
            outWarehouseLogDO.setCompanyId(companyId);
            outWarehouseLogDO.setCollectionId(collection.getCollectionId());
            outWarehouseLogDO.setDocumentId(documentIds);
            outWarehouseLogDO.setCreator(creator);
            outWarehouseLogDO.setModifier(creator);
            outWarehouseLogDO.setOutInWarehouseId(outInWarehouseId);
            outWarehouseLogDO.setOutInWarehouseCertificateNo(outInWarehouseDTO.getOutInWarehouseCertificateNo());
            this.scsOutWarehouseLogMapper.insertSelective(outWarehouseLogDO);
            ScsCollectionInfoDO collectionInfoDO = new ScsCollectionInfoDO();
            collectionInfoDO.setId(collection.getCollectionId());
            collectionInfoDO.setWarehouseStatus(OutInWarehouseStatusEnum.OWH.getCode());
            collectionInfoDO.setOutWarehouseDate(outWarehouseDate);
            this.scsCollectionInfoMapper.updateByPrimaryKeySelective(collectionInfoDO);
            List<Long> collectionIdList = new ArrayList<>();
            collectionIdList.add(collection.getCollectionId());
            this.collectionRegisterService.updateRegisterStatusByCollectionId(companyId, collectionIdList, RegisterStatusEnum.AOWH);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void collectionInWarehouse(CollectionInWarehouseAction action) {
        Long companyId = action.getCompanyId();
        Long creator = action.getCreator();
        Long outInWarehouseId = action.getOutInWarehouseId();
        OutInWarehouseInfoDetailDTO outInWarehouseDTO = this.queryById(companyId, outInWarehouseId);
        if (Objects.isNull(outInWarehouseDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "出入库凭证信息不存在");
        }
        List<InWarehouseCollectionAction> collectionList = action.getInWarehouseCollection();
        if (CollectionUtils.isEmpty(collectionList)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "入库藏品信息为空");
        }
        QueryCheckStatusAction queryCheckStatusAction = new QueryCheckStatusAction();
        queryCheckStatusAction.setBelongModel(CollectionBizTypeEnum.OUTIN);
        queryCheckStatusAction.setCompanyId(companyId);
        queryCheckStatusAction.setModelId(outInWarehouseId);
        CheckMainStatusEnum statusEnum = this.checkService.queryCheckStatus(queryCheckStatusAction);
        if(!CheckMainStatusEnum.ATP.equals(statusEnum)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "出入库凭证审核未通过，无法入库！");
        }
        collectionList.forEach(collection -> {
            String registerNo = collection.getRegisterNo();
            String documentIds = collection.getDocumentId();
            if (StringUtils.isBlank(documentIds)) {
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "总登记号为[" + registerNo + "]入库藏品照片未上传, 请按规则上传入库藏品照片");
            }
            List<CommonDocumentDTO> documentDTOList = this.documentService.listByDocIds(companyId, documentIds);
            if (CollectionUtils.isEmpty(documentDTOList)) {
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "总登记号为[" + registerNo + "]入库藏品照片未上传, 请按规则上传入库藏品照片");
            }
            ScsInWarehouseLogDO inWarehouseLogDO = new ScsInWarehouseLogDO();
            inWarehouseLogDO.setCompanyId(companyId);
            inWarehouseLogDO.setOutInWarehouseId(outInWarehouseId);
            inWarehouseLogDO.setOutInWarehouseCertificateNo(outInWarehouseDTO.getOutInWarehouseCertificateNo());
            inWarehouseLogDO.setCollectionId(collection.getCollectionId());
            inWarehouseLogDO.setTransferor(action.getTransferor());
            inWarehouseLogDO.setTransferorName(action.getTransferorName());
            inWarehouseLogDO.setReceiver(action.getReceiver());
            inWarehouseLogDO.setReceiverName(action.getReceiverName());
            inWarehouseLogDO.setReturnAddress(action.getReturnAddress());
            inWarehouseLogDO.setReturnDate(action.getReturnDate());
            inWarehouseLogDO.setReturnRemark(action.getReturnRemark());
            inWarehouseLogDO.setCollectionRemark(action.getCollectionRemark());
            inWarehouseLogDO.setDocumentId(documentIds);
            inWarehouseLogDO.setCreator(creator);
            inWarehouseLogDO.setModifier(creator);
            this.scsInWarehouseLogMapper.insertSelective(inWarehouseLogDO);
            ScsCollectionInfoDO collectionInfoDO = new ScsCollectionInfoDO();
            collectionInfoDO.setId(collection.getCollectionId());
            collectionInfoDO.setWarehouseStatus(OutInWarehouseStatusEnum.IWH.getCode());
            collectionInfoDO.setInWarehouseDate(action.getReturnDate());
            this.scsCollectionInfoMapper.updateByPrimaryKeySelective(collectionInfoDO);
            List<Long> collectionIdList = new ArrayList<>();
            collectionIdList.add(collection.getCollectionId());
            this.collectionRegisterService.updateRegisterStatusByCollectionId(companyId, collectionIdList, RegisterStatusEnum.AIWH);
        });
    }
    private List<OutInWarehouseInfoPageDTO> setOutInWarehouseAttributes(Long companyId, List<ScsOutInWarehouseCertificateInfoDO> outInWarehouseDOList, String checkStatus, Integer isUserChecked) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<Long> outInWarehouseIds = outInWarehouseDOList.stream()
                .map(ScsOutInWarehouseCertificateInfoDO::getId).collect(Collectors.toList());

        List<Long> useIds = outInWarehouseDOList.stream().map(scsOutInWarehouseCertificateInfoDO ->{
                if(StringUtils.isNotBlank(scsOutInWarehouseCertificateInfoDO.getUseId())){
                    return Long.parseLong(scsOutInWarehouseCertificateInfoDO.getUseId());
                }
                return null;
        }).collect(Collectors.toList());

        // 根据出入库凭证id查询出库、入库详情
        List<ScsInWarehouseLogDO> scsInWarehouseLogDOList = scsInWarehouseLogMapper.listByOutInWarehouseIds(companyId,outInWarehouseIds);
        Map<String,ScsInWarehouseLogDO> inLogMap = new HashMap<>();
        scsInWarehouseLogDOList.forEach(scsInWarehouseLogDO -> inLogMap.put(scsInWarehouseLogDO.getOutInWarehouseId()+"-"+scsInWarehouseLogDO.getCollectionId(), scsInWarehouseLogDO));

        List<ScsOutWarehouseLogDO> scsOutWarehouseLogDOList = scsOutWarehouseLogMapper.listByOutInWarehouseIds(companyId,outInWarehouseIds);
        Map<String,ScsOutWarehouseLogDO> outLogMap = new HashMap<>();
        scsOutWarehouseLogDOList.forEach(scsOutWarehouseLogDO -> outLogMap.put(scsOutWarehouseLogDO.getOutInWarehouseId()+"-"+scsOutWarehouseLogDO.getCollectionId(), scsOutWarehouseLogDO));

        Map<Long, String> userNameMap = this.userService.queryUserNameMap(companyId, useIds);

        List<Long> departmentIds = outInWarehouseDOList.stream().map(scsOutInWarehouseCertificateInfoDO ->{
            if(StringUtils.isNotBlank(scsOutInWarehouseCertificateInfoDO.getUserDepartmentId())){
                Long id = Long.parseLong(scsOutInWarehouseCertificateInfoDO.getUserDepartmentId());
                return id;
            }
            return null;
        }).collect(Collectors.toList());

        List<CommClassificationDO> commonDocumentDOList = this.commClassificationMapper.listByIds(companyId, departmentIds);
        Map<Long, String> departmentNameMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(commonDocumentDOList)){
            departmentNameMap = commonDocumentDOList.stream()
                    .collect(Collectors.toMap(CommClassificationDO::getId, CommClassificationDO::getName, (key1, key2) -> key2));
        }
        // 出入库事由
        List<Long> reasonIds = outInWarehouseDOList.stream().map(scsOutInWarehouseCertificateInfoDO ->{
            if(scsOutInWarehouseCertificateInfoDO.getReason() != null){
                return scsOutInWarehouseCertificateInfoDO.getReason();
            }
            return null;
        }).collect(Collectors.toList());
        List<CommClassificationDO> reasonList = this.commClassificationMapper.listByIds(companyId, reasonIds);
        Map<Long, String> reasonInfoMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(commonDocumentDOList)){
            reasonInfoMap = reasonList.stream()
                    .collect(Collectors.toMap(CommClassificationDO::getId, CommClassificationDO::getName, (key1, key2) -> key2));
        }

        List<BizCollectionRelDTO> collectionRelList = this.collectionService.listByBizIdsAndBizType(outInWarehouseIds, CollectionBizTypeEnum.OUTIN);
        Map<Long, List<BizCollectionRelDTO>> bizCollectionRelMap = Maps.newHashMap();
        Map<Long, ScsCollectionRegisterInfoDO> bizCollectionMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(collectionRelList)) {
            bizCollectionRelMap = collectionRelList.stream().collect(Collectors.groupingBy(BizCollectionRelDTO::getBizId));
            List<Long> collectionIds = collectionRelList.stream().map(BizCollectionRelDTO::getCollectionId).collect(Collectors.toList());
            // 查询藏品信息
            List<ScsCollectionRegisterInfoDO> collectionList = this.scsCollectionRegisterInfoMapper.listByIds(companyId, collectionIds);
            collectionList.forEach(collection -> bizCollectionMap.put(collection.getCollectionId(), collection));
        }
        QueryBatchCheckStatusAction batchCheckStatusAction = new QueryBatchCheckStatusAction();
        batchCheckStatusAction.setBelongModel(CollectionBizTypeEnum.OUTIN);
        batchCheckStatusAction.setCompanyId(companyId);
        batchCheckStatusAction.setModelIds(outInWarehouseIds);
        Map<Long, CheckCurrentInfoDTO> checkInfoMap = this.checkService.queryBatchCheckStatus(batchCheckStatusAction);

        Map<Long, List<BizCollectionRelDTO>> finalBizCollectionRelMap = bizCollectionRelMap;
        Map<Long, String> finalDepartmentNameMap = departmentNameMap;
        Map<Long, String> finalReasonInfoMap = reasonInfoMap;
        return outInWarehouseDOList.stream().map(outInWarehouseDO -> {
            OutInWarehouseInfoPageDTO outInWarehouseInfoPageDTO = BeanCopyUtils.copyByJSON(outInWarehouseDO, OutInWarehouseInfoPageDTO.class);
            outInWarehouseInfoPageDTO.setOutInWarehouseId(outInWarehouseDO.getId());
            if (checkInfoMap != null && checkInfoMap.containsKey(outInWarehouseDO.getId())) {
                CheckCurrentInfoDTO checkCurrentInfoDTO = checkInfoMap.get(outInWarehouseDO.getId());
                outInWarehouseInfoPageDTO.setCheckStatus(checkCurrentInfoDTO.getCheckStatus().getCode());
                outInWarehouseInfoPageDTO.setCheckStatusDesc(checkCurrentInfoDTO.getCheckStatus().getDesc());
                outInWarehouseInfoPageDTO.setNeedManName(checkCurrentInfoDTO.getNeedManName());
                outInWarehouseInfoPageDTO.setHasChecked(YesOrNoEnum.否.getCode().byteValue());
                if(Objects.nonNull(checkCurrentInfoDTO.getNeedManId()) && checkCurrentInfoDTO.getNeedManId().contains(userBO.getUserId())){
                    outInWarehouseInfoPageDTO.setHasChecked(YesOrNoEnum.是.getCode().byteValue());
                }

                if(userNameMap.size() > 0 && StringUtils.isNotBlank(outInWarehouseDO.getUseId()) && userNameMap.containsKey(Long.parseLong(outInWarehouseDO.getUseId()))){
                    outInWarehouseInfoPageDTO.setUseIdName(userNameMap.get(Long.parseLong(outInWarehouseDO.getUseId())));
                }

                if(finalDepartmentNameMap.size() > 0 && StringUtils.isNotBlank(outInWarehouseDO.getUserDepartmentId()) && finalDepartmentNameMap.containsKey(Long.parseLong(outInWarehouseDO.getUserDepartmentId()))){
                    outInWarehouseInfoPageDTO.setUserDepartmentName(finalDepartmentNameMap.get(Long.parseLong(outInWarehouseDO.getUserDepartmentId())));
                }

            }
            if(!Objects.isNull(outInWarehouseDO.getGmtCreate())){
                outInWarehouseInfoPageDTO.setGmtCreate(DateUtils.formatDateYYYMMDDHHmmss(outInWarehouseDO.getGmtCreate()));
            }
            if (finalBizCollectionRelMap != null && !finalBizCollectionRelMap.isEmpty()) {
                List<BizCollectionRelDTO> collectionRelDTOList = finalBizCollectionRelMap.get(outInWarehouseDO.getId());
                if (CollectionUtils.isNotEmpty(collectionRelDTOList)) {
                    outInWarehouseInfoPageDTO.setCollectionCount(collectionRelDTOList.size());
                } else {
                    outInWarehouseInfoPageDTO.setCollectionCount(0);
                }
                AtomicLong powhCount = new AtomicLong(0L);
                AtomicLong aowhCount = new AtomicLong(0L);
                AtomicLong aiwhCount = new AtomicLong(0L);
                // 此凭证入库数量
                powhCount.set(collectionRelDTOList.size());
                collectionRelDTOList.forEach(collect -> {
                    if (!bizCollectionMap.containsKey(collect.getCollectionId())) {
                        return;
                    }

                    if (outLogMap.containsKey(outInWarehouseDO.getId() + "-" + collect.getCollectionId())) {
                        powhCount.decrementAndGet();
                        if (inLogMap.containsKey(outInWarehouseDO.getId() + "-" + collect.getCollectionId())) {
                            aiwhCount.incrementAndGet();
                        } else {
                            aowhCount.incrementAndGet();
                        }
                    }
                });
                CollectionStatisticalDTO collectionStatistical = new CollectionStatisticalDTO();
                collectionStatistical.setPowhCode(RegisterStatusEnum.POWH.getCode());
                collectionStatistical.setPowhCount(powhCount.get());
                collectionStatistical.setAowhCode(RegisterStatusEnum.AOWH.getCode());
                collectionStatistical.setAowhCount(aowhCount.get());
                collectionStatistical.setAiwhCode(RegisterStatusEnum.AIWH.getCode());
                collectionStatistical.setAiwhCount(aiwhCount.get());
                outInWarehouseInfoPageDTO.setCollectionStatistical(collectionStatistical);
            }

            if(finalReasonInfoMap.containsKey(outInWarehouseDO.getReason())){
                outInWarehouseInfoPageDTO.setReasonInfo(finalReasonInfoMap.get(outInWarehouseDO.getReason()));
            }

            if (StringUtils.isNotBlank(checkStatus)) {
                if (checkStatus.equals(outInWarehouseInfoPageDTO.getCheckStatus())) {
                    return outInWarehouseInfoPageDTO;
                } else {
                    return null;
                }
            }
            if (isUserChecked == 1) {
                if (outInWarehouseInfoPageDTO.getHasChecked() == YesOrNoEnum.否.getCode().byteValue()) {
                    return null;
                }
            }
            return outInWarehouseInfoPageDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private OutInWarehouseCertificateEntity convertOutInWarehouseCertificateEntity(AddOutInWarehouseAction action) {
        OutInWarehouseCertificateEntity entity = new OutInWarehouseCertificateEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setOutInWarehouseCertificateNo(action.getPrefixName()
                + CollectionConstant.OUT_IN_WAREHOUSE_CERTIFICATE_MIDDLE);
        entity.setPrefixName(action.getPrefixName());
        entity.setSuffixNumber(action.getSuffixNumber());
        entity.setReason(action.getReason());
        entity.setReasonInfo(action.getReasonInfo());
        entity.setUseId(action.getUseId());
        entity.setUserDepartmentId(action.getUserDepartmentId());
        entity.setUseAddress(action.getUseAddress());
        entity.setRemark(action.getRemark());
        entity.setUseDate(action.getUseDate());
        entity.setPreReturnDate(action.getPreReturnDate());
        entity.setDocumentId(action.getDocumentId());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        entity.setCollectionIds(action.getCollectionIds());
        entity.setAutoAudit(action.getAutoAudit());
        return entity;
    }
    private OutInWarehouseCertificateEntity convertOutInWarehouseCertificateEntity(EditOutInWarehouseAction action) {
        OutInWarehouseCertificateEntity entity = new OutInWarehouseCertificateEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setOutInWarehouseId(action.getOutInWarehouseId());
//        entity.setOutInWarehouseCertificateNo(action.getOutInWarehouseCertificateNo());
//        entity.setPrefixName(action.getPrefixName());
//        entity.setSuffixNumber(action.getSuffixNumber());
        entity.setReason(action.getReason());
        entity.setReasonInfo(action.getReasonInfo());
        entity.setUseId(action.getUseId());
        entity.setUserDepartmentId(action.getUserDepartmentId());
        entity.setUseAddress(action.getUseAddress());
        entity.setRemark(action.getRemark());
        entity.setUseDate(action.getUseDate());
        entity.setPreReturnDate(action.getPreReturnDate());
        entity.setDocumentId(action.getDocumentId());
        entity.setModifier(action.getModifier());
        entity.setCollectionIds(action.getCollectionIds());
        entity.setAutoAudit(action.getAutoAudit());
        return entity;
    }
}
