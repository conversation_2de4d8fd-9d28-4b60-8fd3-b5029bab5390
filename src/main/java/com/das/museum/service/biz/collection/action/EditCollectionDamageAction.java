package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/2
 */
@Data
public class EditCollectionDamageAction implements Serializable {
    private static final long serialVersionUID = 1158356197343210270L;
    private Long collectionDamageId;
    private Long companyId;
    private String address;
    private Long responsibleId;
    private String responsibleName;
    private String damageInfo;
    private String reason;
    private String handleInfo;
    private String processInfo;
    private String remark;
    private Date damageDate;
    private String documentId;
    private Long modifier;
}
