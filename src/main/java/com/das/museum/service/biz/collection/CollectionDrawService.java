package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionDrawAction;
import com.das.museum.service.biz.collection.action.DrawQueryPageByConditionAction;
import com.das.museum.service.biz.collection.action.EditCollectionDrawAction;
import com.das.museum.service.biz.collection.dto.CollectionDrawDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionDrawPageDTO;
import com.das.museum.common.bo.SimplePageInfo;

public interface CollectionDrawService {
    Long addCollectionDraw(AddCollectionDrawAction action);
    void editCollectionDraw(EditCollectionDrawAction action);
    void delByCollectionDrawId(Long companyId, Long collectionDrawIdId);
    SimplePageInfo<CollectionDrawPageDTO> queryPageByCondition(DrawQueryPageByConditionAction action);
    CollectionDrawDetailDTO queryByCollectionDrawId(Long companyId, Long collectionDrawId);
}
