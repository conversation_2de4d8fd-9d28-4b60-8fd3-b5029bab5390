package com.das.museum.service.biz.collection;

import cn.hutool.core.codec.Base64Encoder;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.ExportUtil;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.mapper.*;
import com.das.museum.service.biz.collection.dto.*;
import com.das.museum.web.controller.collection.wordexport.response.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Author: Lee
 * @Date: 2023/2/15
 */
@Slf4j
@Service
public class WorldExportServiceImpl implements WordExportService{

    @Resource
    private CollectionService collectionService;

    @Resource
    private CollectCertificateService collectCertificateService;

    @Resource
    private IdentifyCertificateService identifyCertificateService;

    @Resource
    private InTibetanService inTibetanService;

    @Resource
    private CollectionRegisterService collectionRegisterService;

    @Resource
    private ScsCollectionAppendageMapper scsCollectionAppendageMapper;
    @Resource
    private ScsCollectionRubbingMapper scsCollectionRubbingMapper;
    @Resource
    private ScsCollectionMountMapper scsCollectionMountMapper;
    @Resource
    private ScsCollectionBookMapper scsCollectionBookMapper;
    @Resource
    private ScsCollectionIdentifyMapper scsCollectionIdentifyMapper;
    @Resource
    private ScsCollectionDrawMapper scsCollectionDrawMapper;


    @Resource
    private CommClassificationMapper commClassificationMapper;
    @Resource
    private CommDocumentMapper commDocumentMapper;
    @Resource
    private AccCompanyMapper accCompanyMapper;

    @Value("${minio.endpoint}")
    private String pathHead;

    private String defaultStr = "-";
    private String defaultSort = "gmt_create desc";

    @Override
    public Map<String, Object> exportCollectWord(Long companyId, Long collectId) {
        Map<String, Object> dataMap = new HashMap<>();
        List<CollectionInfoDetailDTO> collectionInfoDOList = this.collectionService.queryByBizIdAndBizType(companyId, collectId, CollectionBizTypeEnum.CLT);
        CollectInfoDetailDTO collectInfoDetailDTO = this.collectCertificateService.queryDetailInfoById(companyId, collectId);
        if(CollectionUtils.isEmpty(collectionInfoDOList)){
            return null;
        }

        List<CollectDetailListForExportWordVO> detailedListDataList = new ArrayList<>();
        List<CollectDetailForExportWordVO> detailDataList = new ArrayList<>();
        List<String> imageList = new ArrayList<>();
        AtomicReference<Long> allNumAto = new AtomicReference<>(0L);
        AtomicReference<Double> allMoneyAto = new AtomicReference<>(0d);
        collectionInfoDOList.forEach(scsCollectionInfoDO -> {
            CollectDetailListForExportWordVO detailedListExportVO = new CollectDetailListForExportWordVO();
            CollectDetailForExportWordVO detailExportVO = new CollectDetailForExportWordVO();

            detailedListExportVO.setCollName(scsCollectionInfoDO.getName());
            detailedListExportVO.setCollLevel(setStringValue(scsCollectionInfoDO.getInitialLevelName()));
            Double amount = Objects.isNull(scsCollectionInfoDO.getAmount()) ? 0d : scsCollectionInfoDO.getAmount();
            String moneyStr = amount == 0d ? "0" : amount.toString();
            detailedListExportVO.setMoney(moneyStr);
            detailedListExportVO.setPreOwner(setStringValue(scsCollectionInfoDO.getHolder()));
            detailedListExportVO.setCollectType(setStringValue(scsCollectionInfoDO.getCollectTypeName()));
            detailedListExportVO.setRemarks(setStringValue(scsCollectionInfoDO.getRemark()));

            detailExportVO.setName(scsCollectionInfoDO.getName());
            detailExportVO.setYear(setStringValue(scsCollectionInfoDO.getEra()));
            detailExportVO.setTexture(setStringValue(scsCollectionInfoDO.getTextureName()));
            detailExportVO.setSize(setStringValue(scsCollectionInfoDO.getSize()));
            detailExportVO.setSizeUnit(setStringValue(scsCollectionInfoDO.getSizeUnitName()));
            int actCount = Objects.isNull(scsCollectionInfoDO.getActualCount()) ? 0 : scsCollectionInfoDO.getActualCount();
            detailExportVO.setCount(actCount == 0 ? defaultStr : String.valueOf(actCount));
            detailExportVO.setDegree(setStringValue(scsCollectionInfoDO.getCompleteDegreeName()));
            detailExportVO.setDesc(setStringValue(scsCollectionInfoDO.getIntroduce()));

            if(CollectionUtils.isNotEmpty(scsCollectionInfoDO.getImageDocumentList())){
                try {
                    CommDocumentDO commDocumentDO = this.commDocumentMapper.selectById(companyId, scsCollectionInfoDO.getImageDocumentList().get(0).getDocumentId());
                    String imgBase64 = getAndTransImage(pathHead + "/" + commDocumentDO.getBucket() + "/" + commDocumentDO.getPath());
                    imageList.add(imgBase64);
                    detailExportVO.setImage(String.valueOf(imageList.size() - 1));
                }
                catch (IOException e){
                    log.error(e.toString());
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "获取图片出错，导出失败");
                }
            }

            detailedListDataList.add(detailedListExportVO);
            detailDataList.add(detailExportVO);
            Long addCount = actCount == 0 ? 1L : actCount;
            allNumAto.getAndSet(allNumAto.get() + addCount);
            allMoneyAto.getAndSet(allMoneyAto.get() + amount);
        });

        dataMap.put("fileName", collectInfoDetailDTO.getCollectCertificateNo());
        dataMap.put("CPzjb", detailedListDataList);
        dataMap.put("CPzjxxb", detailDataList);
        dataMap.put("imageList", imageList);
        dataMap.put("allMoney", allMoneyAto.get().toString());
        dataMap.put("allNum", allNumAto.get().toString());
        return dataMap;
    }

    @Override
    public Map<String, Object> exportIdentifyWord(Long companyId, Long identifyId) {
        List<IdentifyCollectionInfoPageDTO> identifyInfoList = this.collectionService.queryCollectionInfoByIdentifyIdNoPage(companyId, identifyId);
        IdentifyInfoDetailDTO identifyInfoDetailDTO = this.identifyCertificateService.queryDetailInfoById(companyId, identifyId);
        if(CollectionUtils.isEmpty(identifyInfoList)){
            return null;
        }
        Map<String, Object> dataMap = new HashMap<>();
        AtomicReference<Long> allNumAto = new AtomicReference<>(0L);
        List<IdentifyListForExportWordVO> dataList = new ArrayList<>();
        identifyInfoList.forEach(identify ->{
            IdentifyListForExportWordVO exportVO = new IdentifyListForExportWordVO();

            exportVO.setCollName(identify.getName());
            exportVO.setCountUnit(setStringValue(identify.getCountUnitName()));
            int actCount = Objects.isNull(identify.getActualCount()) ? 0 : identify.getActualCount();
            exportVO.setCount(actCount == 0 ? defaultStr : String.valueOf(actCount));
            exportVO.setYear(setStringValue(identify.getEra()));
            exportVO.setTexture(setStringValue(identify.getTextureName()));
            exportVO.setSize(setStringValue(identify.getSize()));
            exportVO.setDegree(setStringValue(identify.getCompleteDegreeName()));
            exportVO.setCollectType(setStringValue(identify.getCollectTypeName()));
            exportVO.setAppraiserName(setStringValue(identify.getAppraiserName()));
            exportVO.setIdentifyAgency(setStringValue(identify.getIdentifyAgency()));
            exportVO.setIdentifyOpinion(setStringValue(identify.getIdentifyOpinion()));

            dataList.add(exportVO);
            Long addCount = actCount == 0 ? 1L : actCount;
            allNumAto.getAndSet(allNumAto.get() + addCount);
        });
        dataMap.put("fileName", identifyInfoDetailDTO.getIdentifyCertificateNo());

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        dataMap.put("nowYear", String.valueOf(calendar.get(Calendar.YEAR)));
        dataMap.put("nowMonth", calendar.get(Calendar.MONTH) + 1);
        dataMap.put("nowDay", calendar.get(Calendar.DATE));

        dataMap.put("CPjdb", dataList);
        dataMap.put("num", allNumAto.get().toString());
        dataMap.put("year", identifyInfoDetailDTO.getPrefixName());
        dataMap.put("no", identifyInfoDetailDTO.getSuffixNumber());

        return dataMap;
    }

    @Override
    public Map<String, Object> exportInTibetanWord(Long companyId, Long inTibetanId) {
        SimplePageInfo<CollectCheckedCollectionInfoDTO> pageInfo = this.collectionService.queryPageByBizIdAndBizType(companyId,
                inTibetanId, CollectionBizTypeEnum.TBT, null, null, null, 1, 10000);
        if(CollectionUtils.isEmpty(pageInfo.getList())){
            return null;
        }
        List<CollectCheckedCollectionInfoDTO> collectCheckedCollectionInfoDTOList = pageInfo.getList();
        InTibetanInfoDetailDTO inTibetanInfoDetailDTO = this.inTibetanService.queryDetailInfoById(companyId, inTibetanId);

        Map<String, Object> dataMap = new HashMap<>();
        AtomicReference<Long> allNumAto = new AtomicReference<>(0L);
        List<InTibetanListForExportWordVO> dataList = new ArrayList<>();

        collectCheckedCollectionInfoDTOList.forEach(infoDTO ->{
            InTibetanListForExportWordVO exportVO = new InTibetanListForExportWordVO();

            exportVO.setRegNo(setStringValue(infoDTO.getRegisterNo()));
            exportVO.setCollName(infoDTO.getName());
            int actCount = Objects.isNull(infoDTO.getActualCount()) ? 0 : infoDTO.getActualCount();
            exportVO.setCount(actCount == 0 ? defaultStr : String.valueOf(actCount));
            exportVO.setCountUnit(setStringValue(infoDTO.getCountUnitName()));
            exportVO.setYear(setStringValue(infoDTO.getEra()));
            exportVO.setTexture(setStringValue(infoDTO.getTextureName()));
            exportVO.setSize(setStringValue(infoDTO.getSize()));
            exportVO.setDegree(setStringValue(infoDTO.getCompleteDegreeName()));
            exportVO.setLevel(setStringValue(infoDTO.getInitialLevelName()));
            exportVO.setAppraiserName(setStringValue(infoDTO.getAppraiserName()));
            exportVO.setCollectType(setStringValue(infoDTO.getCollectTypeName()));
            exportVO.setInMuDate(setStringValue(infoDTO.getInMuseumDate()));
            exportVO.setInCollDate(setStringValue(infoDTO.getInTibetanDate()));
            exportVO.setRemarks(setStringValue(infoDTO.getRemark()));

            dataList.add(exportVO);
            Long addCount = actCount == 0 ? 1L : actCount;
            allNumAto.getAndSet(allNumAto.get() + addCount);
        });

        dataMap.put("fileName", inTibetanInfoDetailDTO.getInTibetanCertificateNo());

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        dataMap.put("nowYear", String.valueOf(calendar.get(Calendar.YEAR)));
        dataMap.put("nowMonth", calendar.get(Calendar.MONTH) + 1);
        dataMap.put("nowDay", calendar.get(Calendar.DATE));

        dataMap.put("CPrcb", dataList);
        dataMap.put("num", allNumAto.get().toString());
        dataMap.put("year", inTibetanInfoDetailDTO.getPrefixName());
        dataMap.put("no", inTibetanInfoDetailDTO.getSuffixNumber());

        return dataMap;
    }

    @Override
    public Map<String, Object> exportCollectionRecordWord(Long companyId, String registerNo) {
        CollectionRegisterDetailDTO registerDetailDTO = this.collectionRegisterService.queryByRegisterNo(companyId, registerNo);
        if(Objects.isNull(registerDetailDTO)){
            return null;
        }
        Map<String, Object> dataMap = new HashMap<>();
        List<CommClassificationDO> commClassDOList = this.commClassificationMapper.listByIds(companyId,
                Lists.newArrayList(registerDetailDTO.getIdentifyLevel(),
                        registerDetailDTO.getAge(),
                        registerDetailDTO.getQualityUnit()));
        Map<Long, String> commClassMap = commClassDOList.stream()
                .collect(Collectors.toMap(CommClassificationDO::getId, CommClassificationDO::getName, (key1, key2) -> key2));

        // 装载数据
        dataMap.put("collName", setStringValue(registerDetailDTO.getName()));
        dataMap.put("level", setStringValue(commClassMap.get(registerDetailDTO.getIdentifyLevel())));
        dataMap.put("regNo", setStringValue(registerDetailDTO.getRegisterNo()));
        dataMap.put("classifyId", setStringValue(registerDetailDTO.getClassifyDesc()));
        dataMap.put("preName", setStringValue(registerDetailDTO.getOriginalName()));

        String yearStr = StringUtils.isBlank(registerDetailDTO.getEra()) ? defaultStr : "（公元）" + registerDetailDTO.getEra();
        dataMap.put("year", yearStr);

        dataMap.put("author", setStringValue(registerDetailDTO.getAuthor()));
        dataMap.put("era", setStringValue(commClassMap.get(registerDetailDTO.getAge())));
        dataMap.put("count", Objects.isNull(registerDetailDTO.getCollectionCount()) ? defaultStr : registerDetailDTO.getCollectionCount().toString());
        dataMap.put("texture", setStringValue(registerDetailDTO.getTextureName()));
        dataMap.put("color", setStringValue(registerDetailDTO.getColor()));
        dataMap.put("use", setStringValue(registerDetailDTO.getUse()));
        dataMap.put("authorBiography", setStringNullValue(registerDetailDTO.getAuthorBiography()));

        dataMap.put("exploreAddr", setStringNullValue(registerDetailDTO.getExcavatedLocation()));
        dataMap.put("exploreDate", setDateNullValue(registerDetailDTO.getExcavationDate()));
        dataMap.put("excavator", setStringNullValue(registerDetailDTO.getExcavator()));
        dataMap.put("collectAddr", setStringNullValue(registerDetailDTO.getCollectAddress()));
        dataMap.put("collectDate", setDateNullValue(registerDetailDTO.getCollectDate()));
        dataMap.put("collector", setStringNullValue(registerDetailDTO.getCollector()));
        dataMap.put("approCom", setStringNullValue(registerDetailDTO.getAppropriationUnit()));
        dataMap.put("approDate", setDateNullValue(registerDetailDTO.getAppropriationDate()));
        dataMap.put("approHandBy", setStringNullValue(registerDetailDTO.getAppropriationHandler()));
        dataMap.put("exchaCom", setStringNullValue(registerDetailDTO.getTransferUnit()));
        dataMap.put("exchaDate", setDateNullValue(registerDetailDTO.getTransferDate()));
        dataMap.put("exchaHandBy", setStringNullValue(registerDetailDTO.getTransferHandler()));
        dataMap.put("donateName", setStringNullValue(registerDetailDTO.getDonors()));
        dataMap.put("donateDate", setDateNullValue(registerDetailDTO.getDonateDate()));
        dataMap.put("donateHandBy", setStringNullValue(registerDetailDTO.getDonateHandler()));
        dataMap.put("donateAddr", setStringNullValue(registerDetailDTO.getDonateAddress()));
        dataMap.put("donateMoney", "");
        dataMap.put("sellName", setStringNullValue(registerDetailDTO.getSeller()));
        dataMap.put("sellDate", setDateNullValue(registerDetailDTO.getBuyDate()));
        dataMap.put("sellHandBy", setStringNullValue(registerDetailDTO.getBuyHandler()));
        dataMap.put("sellAddr", setStringNullValue(registerDetailDTO.getBuyAddress()));
        dataMap.put("sellMoney", "");

        dataMap.put("sizeAndWeight", setSizeAndWeight(registerDetailDTO, commClassMap.get(registerDetailDTO.getQualityUnit())));
        dataMap.put("appendageList", setAppendageList(companyId, registerDetailDTO.getCollectionId()));
        dataMap.put("inCollDate", setDateValue(registerDetailDTO.getInTibetanDate()));
        dataMap.put("inMuCerNo", setStringValue(registerDetailDTO.getInMuseumCertificateNo()));
        dataMap.put("desc", setStringNullValue(registerDetailDTO.getDescription()));
        dataMap.put("process", setStringNullValue(registerDetailDTO.getProcess()));
        dataMap.put("postList", setPostScript(companyId, registerDetailDTO.getCollectionId()));

        dataMap.put("bookList", setAboutBook(companyId, registerDetailDTO.getCollectionId()));
        dataMap.put("handDown", setStringNullValue(registerDetailDTO.getHandDown()));
        dataMap.put("identifyList", setIdentifyOpinion(companyId, registerDetailDTO.getCollectionId()));
        dataMap.put("mountList", setMountRecord(companyId, registerDetailDTO.getCollectionId()));
        List<String> drawList = setDrawList(companyId, registerDetailDTO.getCollectionId());
        dataMap.put("drawList", drawList);
        dataMap.put("statusRecord", setStringNullValue(registerDetailDTO.getStatusRecord()));
        dataMap.put("remarks", setRemarks(registerDetailDTO));
        dataMap.put("appendix", setStringNullValue(registerDetailDTO.getAppendix()));

        List<String> imageList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(registerDetailDTO.getCommDocumentList())){
            registerDetailDTO.getCommDocumentList().forEach(commonDTO->{
                if("IMAGE".equals(commonDTO.getFileType())){
                    try {
                        CommDocumentDO commDocumentDO = this.commDocumentMapper.selectById(companyId, commonDTO.getDocumentId());
                        String imageBase64 = getAndTransImage(pathHead + "/" + commDocumentDO.getBucket() + "/" + commDocumentDO.getPath());
                        imageList.add(imageBase64);
                    }
                    catch (IOException e){
                        log.error(e.toString());
                        throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "获取图片出错，导出失败");
                    }
                }
            });
        }
        if(CollectionUtils.isNotEmpty(imageList)){
            dataMap.put("imageList", imageList);
        }

        dataMap.put("fileName", registerNo);
        return dataMap;
    }

    @Override
    public Map<String, Object> exportCollectionCardWord(Long companyId, String registerNo) {
        CollectionRegisterDetailDTO registerDetailDTO = this.collectionRegisterService.queryByRegisterNo(companyId, registerNo);
        if(Objects.isNull(registerDetailDTO)){
            return null;
        }

        AccCompanyDO companyDO = this.accCompanyMapper.selectByPrimaryKey(companyId);
        String companyName = companyDO.getCompanyName();

        List<CommClassificationDO> commClassDOList = this.commClassificationMapper.listByIds(companyId,
                Lists.newArrayList(registerDetailDTO.getIdentifyLevel(),
                        registerDetailDTO.getAge(),
                        registerDetailDTO.getSource(),
                        registerDetailDTO.getCompleteDegree(),
                        registerDetailDTO.getQualityUnit(),
                        registerDetailDTO.getIdentifyLevel()));
        Map<Long, String> commClassMap = commClassDOList.stream()
                .collect(Collectors.toMap(CommClassificationDO::getId, CommClassificationDO::getName, (key1, key2) -> key2));

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("regNo", setStringValue(registerDetailDTO.getRegisterNo()));
        dataMap.put("classifyId", setStringValue(registerDetailDTO.getClassifyDesc()));
        dataMap.put("originId", setStringValue(registerDetailDTO.getOriginalNo()));
        dataMap.put("collName", setStringValue(registerDetailDTO.getName()));
        dataMap.put("preName", setStringValue(registerDetailDTO.getOriginalName()));
        dataMap.put("author", setStringValue(registerDetailDTO.getAuthor()));
        dataMap.put("era", setStringValue(registerDetailDTO.getEra()));
        dataMap.put("color", setStringValue(registerDetailDTO.getColor()));
        dataMap.put("use", setStringValue(registerDetailDTO.getUse()));
        dataMap.put("authorBiography", setStringValue(registerDetailDTO.getAuthorBiography()));

        dataMap.put("exploreAddr", setStringValue(registerDetailDTO.getExcavatorAddress()));
        dataMap.put("exploreDate", setDateValue(registerDetailDTO.getExcavationDate()));
        dataMap.put("excavator", setStringValue(registerDetailDTO.getExcavator()));
        dataMap.put("collectAddr", setStringValue(registerDetailDTO.getCollectAddress()));
        dataMap.put("collectDate", setDateValue(registerDetailDTO.getCollectDate()));
        dataMap.put("collector", setStringValue(registerDetailDTO.getCollector()));
        dataMap.put("approCom", setStringValue(registerDetailDTO.getAppropriationUnit()));
        dataMap.put("approDate", setDateValue(registerDetailDTO.getAppropriationDate()));
        dataMap.put("approHandBy", setStringValue(registerDetailDTO.getAppropriationHandler()));
        dataMap.put("exchaCom", setStringValue(registerDetailDTO.getExchangeUnit()));
        dataMap.put("exchaDate", setDateValue(registerDetailDTO.getExchangeDate()));
        dataMap.put("exchaHandBy", setStringValue(registerDetailDTO.getExchangeHandler()));
        dataMap.put("donateName", setStringValue(registerDetailDTO.getDonors()));
        dataMap.put("donateDate", setDateValue(registerDetailDTO.getDonateDate()));
        dataMap.put("donateHandBy", setStringValue(registerDetailDTO.getDonateHandler()));
        dataMap.put("donateAddr", setStringValue(registerDetailDTO.getDonateAddress()));
        dataMap.put("donateMoney", "");
        dataMap.put("sellName", setStringValue(registerDetailDTO.getSeller()));
        dataMap.put("sellDate", setDateValue(registerDetailDTO.getBuyDate()));
        dataMap.put("sellHandBy", setStringValue(registerDetailDTO.getBuyHandler()));
        dataMap.put("sellAddr", setStringValue(registerDetailDTO.getBuyAddress()));
        dataMap.put("sellMoney", "");
        dataMap.put("postList", setPostScript(companyId, registerDetailDTO.getCollectionId()));

        String qualityUnit = commClassMap.get(registerDetailDTO.getQualityUnit());
        if(StringUtils.isBlank(qualityUnit)){
            qualityUnit = "";
        }

        String sizeAndWeight = "";
        if(StringUtils.isNotBlank(registerDetailDTO.getSize())){
            sizeAndWeight += "尺寸为：" + registerDetailDTO.getSize();
        }
        if(StringUtils.isNotBlank(sizeAndWeight)){
            sizeAndWeight += "，";
        }
        if(Objects.nonNull(registerDetailDTO.getSpecificQuality())){
            sizeAndWeight += "重量为：" + registerDetailDTO.getSpecificQuality()/100f + qualityUnit;
        }
        dataMap.put("sizeAndWeight",setStringValue(sizeAndWeight));
        // 附属物
        dataMap.put("appendageList", "");
        dataMap.put("inMuCerNo", setStringValue(registerDetailDTO.getInMuseumCertificateNo()));
        dataMap.put("desc", setStringValue(registerDetailDTO.getDescription()));
        dataMap.put("process", setStringValue(registerDetailDTO.getProcess()));
        dataMap.put("statusRecord", "");
        dataMap.put("remarks", "");
        dataMap.put("appendix", setStringValue(registerDetailDTO.getAppendix()));

        dataMap.put("companyName", companyName);
        dataMap.put("year", setStringValue(commClassMap.get(registerDetailDTO.getAge())));
        dataMap.put("degree", setStringValue(commClassMap.get(registerDetailDTO.getCompleteDegree())));
        dataMap.put("level", setStringValue(commClassMap.get(registerDetailDTO.getIdentifyLevel())));
        dataMap.put("inCollDate", setDateValue(registerDetailDTO.getCollectDate()));
        dataMap.put("count", Objects.isNull(registerDetailDTO.getCollectionCount()) ? defaultStr : registerDetailDTO.getCollectionCount().toString());

        if (Objects.nonNull(registerDetailDTO.getSpecificQuality())){
            String weight = registerDetailDTO.getSpecificQuality()/100f + qualityUnit;
            dataMap.put("weight", setStringValue(weight));
        }else{
            dataMap.put("weight", "");
        }
        dataMap.put("texture", setStringValue(registerDetailDTO.getTextureName()));
        dataMap.put("collectType", setStringValue(commClassMap.get(registerDetailDTO.getSource())));
        dataMap.put("size", setStringNullValue(registerDetailDTO.getSize()));
        dataMap.put("desc", setStringNullValue(registerDetailDTO.getDescription()));
        dataMap.put("postList", setPostScript(companyId, registerDetailDTO.getCollectionId()));
        dataMap.put("handDown", setStringNullValue(registerDetailDTO.getHandDown()));
        dataMap.put("identifyAgency", "");
        dataMap.put("identifyOpinion", "");
        dataMap.put("appraiserName", "");
        dataMap.put("synopsis", setStringNullValue(registerDetailDTO.getSynopsis()));

        AtomicReference<String> image = new AtomicReference<>("");
        if(CollectionUtils.isNotEmpty(registerDetailDTO.getCommDocumentList())){
                for(int i = 0; i < registerDetailDTO.getCommDocumentList().size(); i++){
                if("IMAGE".equals(registerDetailDTO.getCommDocumentList().get(i).getFileType())){
                    try {
                        CommDocumentDO commDocumentDO = this.commDocumentMapper.selectById(companyId, registerDetailDTO.getCommDocumentList().get(i).getDocumentId());
                        image.set(getAndTransImage(pathHead + "/" + commDocumentDO.getBucket() + "/" + commDocumentDO.getPath()));
                    }
                    catch (IOException e){
                        log.error(e.toString());
                        throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "获取图片出错，导出失败");
                    }
                    break;
                }
            }
        }
        if(StringUtils.isNotBlank(image.get())){
            dataMap.put("image", image.get());
        }

        dataMap.put("fileName", registerNo);
        return dataMap;
    }

    @Override
    public Map<String, Object> exportCollectionIdentifyBookWord(Long companyId, String registerNo) {
        CollectionRegisterDetailDTO registerDetailDTO = this.collectionRegisterService.queryByRegisterNo(companyId, registerNo);
        if(Objects.isNull(registerDetailDTO)){
            return null;
        }

        AccCompanyDO companyDO = this.accCompanyMapper.selectByPrimaryKey(companyId);
        String companyName = companyDO.getCompanyName();

        List<CommClassificationDO> commClassDOList = this.commClassificationMapper.listByIds(companyId,
                Lists.newArrayList(registerDetailDTO.getIdentifyLevel(),
                        registerDetailDTO.getAge(),
                        registerDetailDTO.getSource(),
                        registerDetailDTO.getCompleteDegree(),
                        registerDetailDTO.getInTibetanAgeRange(),
                        registerDetailDTO.getIdentifyLevel()));
        Map<Long, String> commClassMap = commClassDOList.stream()
                .collect(Collectors.toMap(CommClassificationDO::getId, CommClassificationDO::getName, (key1, key2) -> key2));

        Map<String, Object> dataMap = new HashMap<>();

        dataMap.put("collName", setStringValue(registerDetailDTO.getName()));
        dataMap.put("collectType", setStringValue(commClassMap.get(registerDetailDTO.getSource())));
        dataMap.put("comName", companyName);
        dataMap.put("regNo", setStringValue(registerDetailDTO.getRegisterNo()));
        dataMap.put("processTime", setDateNullValue(registerDetailDTO.getCollectDate()));
        dataMap.put("year", setStringValue(commClassMap.get(registerDetailDTO.getAge())));
        dataMap.put("texture", setStringValue(registerDetailDTO.getTextureName()));

        dataMap.put("description", setStringValue(registerDetailDTO.getDescription()));
        if(StringUtils.isNotBlank(commClassMap.get(registerDetailDTO.getInTibetanAgeRange()))){
            dataMap.put("inTibetanAgeRange", commClassMap.get(registerDetailDTO.getInTibetanAgeRange()));
        }else{
            dataMap.put("inTibetanAgeRange", "");
        }

        dataMap.put("specificInfo", setStringValue(registerDetailDTO.getSpecificInfo()));
        dataMap.put("use", setStringValue(registerDetailDTO.getUse()));

        AtomicReference<String> image = new AtomicReference<>("");
        if(CollectionUtils.isNotEmpty(registerDetailDTO.getCommDocumentList())){
            for(int i = 0; i < registerDetailDTO.getCommDocumentList().size(); i++){
                if("IMAGE".equals(registerDetailDTO.getCommDocumentList().get(i).getFileType())){
                    try {
                        CommDocumentDO commDocumentDO = this.commDocumentMapper.selectById(companyId, registerDetailDTO.getCommDocumentList().get(i).getDocumentId());
                        image.set(getAndTransImage(pathHead + "/" + commDocumentDO.getBucket() + "/" + commDocumentDO.getPath()));
                    }
                    catch (IOException e){
                        log.error(e.toString());
                        throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "获取图片出错，导出失败");
                    }
                    break;
                }
            }
        }
        if(StringUtils.isNotBlank(image.get())){
            dataMap.put("image", image.get());
        }

        dataMap.put("fileName", registerNo);
        return dataMap;
    }

    @Override
    public Map<String, Object> exportCollectionLedgerWord(Long companyId,List<String> registerNos,String registerNo,
                                                          String classifyDesc,String name,Long age,Long identifyLevel,String texture,
                                                          Long source,Long category,Long completeDegree) {
        List<CollectionLedgerDTO> ledgerDTOList = this.collectionRegisterService.queryLedgerByRegisterNo(companyId,registerNos,registerNo,
                classifyDesc,name,age,identifyLevel,texture,source,category,completeDegree);
        if(CollectionUtils.isEmpty(ledgerDTOList)){
            return null;
        }
        List<Long> ids = new ArrayList<>();
        ledgerDTOList.forEach(ledgerDTO->{
            if(Objects.nonNull(ledgerDTO.getYear()) && !ids.contains(ledgerDTO.getYear())){
                ids.add(ledgerDTO.getYear());
            }
            if(Objects.nonNull(ledgerDTO.getCountUnit()) && !ids.contains(ledgerDTO.getCountUnit())){
                ids.add(ledgerDTO.getCountUnit());
            }
            if(Objects.nonNull(ledgerDTO.getDegree()) && !ids.contains(ledgerDTO.getDegree())){
                ids.add(ledgerDTO.getDegree());
            }
            if(StringUtils.isNotEmpty(ledgerDTO.getTexture()) && !ids.contains(Long.parseLong(ledgerDTO.getTexture()))){
                ids.add(Long.parseLong(ledgerDTO.getTexture()));
            }
            if(Objects.nonNull(ledgerDTO.getCollectType()) && !ids.contains(ledgerDTO.getCollectType())){
                ids.add(ledgerDTO.getCollectType());
            }
            if(Objects.nonNull(ledgerDTO.getLevel()) && !ids.contains(ledgerDTO.getLevel())){
                ids.add(ledgerDTO.getLevel());
            }
            if(Objects.nonNull(ledgerDTO.getQualityUnit()) && !ids.contains(ledgerDTO.getQualityUnit())){
                ids.add(ledgerDTO.getQualityUnit());
            }
        });
        List<CommClassificationDO> commClassDOList = this.commClassificationMapper.listByIds(companyId, ids);
        Map<Long, String> commClassMap = commClassDOList.stream()
                .collect(Collectors.toMap(CommClassificationDO::getId, CommClassificationDO::getName, (key1, key2) -> key2));

        List<CollectionLedgerForExportWordVO> ledgerVOList = ledgerDTOList.stream().map(ledgerDTO->{
            CollectionLedgerForExportWordVO exportWordVO = new CollectionLedgerForExportWordVO();
            String[] dateArray = ledgerDTO.getRegDate().split("-");
            exportWordVO.setRegYear(dateArray[0]);
            exportWordVO.setRegMonth(dateArray[1]);
            exportWordVO.setRegDate(dateArray[2]);
            exportWordVO.setRegNo(ledgerDTO.getRegNo());
            exportWordVO.setClassifyId(ledgerDTO.getClassifyDesc() == null ? defaultStr:ledgerDTO.getClassifyDesc());
            exportWordVO.setCollName(setStringValue(ledgerDTO.getCollName()));
            exportWordVO.setYear(Objects.isNull(ledgerDTO.getYear()) ? defaultStr : commClassMap.get(ledgerDTO.getYear()));
            exportWordVO.setCount(Objects.isNull(ledgerDTO.getCount()) ? defaultStr : String.valueOf(ledgerDTO.getCount()));
            exportWordVO.setCountUnit(Objects.isNull(ledgerDTO.getCountUnit()) ? defaultStr : commClassMap.get(ledgerDTO.getCountUnit()));
            exportWordVO.setActCount(Objects.isNull(ledgerDTO.getActCount()) ? defaultStr : String.valueOf(ledgerDTO.getActCount()));

            String qualityUnit = commClassMap.get(ledgerDTO.getQualityUnit());
            if(StringUtils.isBlank(qualityUnit)){
                qualityUnit = "";
            }

            String sizeAndWeight = "";
            if(StringUtils.isNotBlank(ledgerDTO.getSize())){
                sizeAndWeight += "尺寸为：" + ledgerDTO.getSize();
            }
            if(StringUtils.isNotBlank(sizeAndWeight)){
                sizeAndWeight += "，";
            }
            if(Objects.nonNull(ledgerDTO.getWeight())){
                sizeAndWeight += "重量为：" + String.format("%.2f", ledgerDTO.getWeight() / 100f) + qualityUnit;
            }
            exportWordVO.setSizeAndWeight(setStringValue(sizeAndWeight));
            exportWordVO.setTexture(StringUtils.isEmpty(ledgerDTO.getTexture()) || StringUtils.isBlank(commClassMap.get(Long.parseLong(ledgerDTO.getTexture()))) ? defaultStr : commClassMap.get(Long.parseLong(ledgerDTO.getTexture())));
            exportWordVO.setDegree(Objects.isNull(ledgerDTO.getDegree()) ? defaultStr : commClassMap.get(ledgerDTO.getDegree()));
            exportWordVO.setCollectType(Objects.isNull(ledgerDTO.getCollectType()) ? defaultStr : commClassMap.get(ledgerDTO.getCollectType()));
            exportWordVO.setInMuCerNo(setStringValue(ledgerDTO.getInMuCerNo()));
            exportWordVO.setLogoffCerNo(setStringValue(ledgerDTO.getLogoffCerNo()));
            exportWordVO.setLevel(Objects.isNull(ledgerDTO.getLevel()) ? defaultStr : commClassMap.get(ledgerDTO.getLevel()));
            exportWordVO.setRemarks(setStringValue(ledgerDTO.getRemarks()));
            return exportWordVO;
        }).collect(Collectors.toList());

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("CPzzb", ledgerVOList);
        return dataMap;
    }

    @Override
    public List<File> batchExportCollectWord(Long companyId, List<Long> collectIds) {
        List<File> fileList = new ArrayList<>();
        collectIds.forEach(id ->{
            Map<String, Object> dataMap = this.exportCollectWord(companyId, id);
            File file = ExportUtil.GenerateWordDoc(dataMap, "藏品征集凭证" + dataMap.get("fileName"), "CPZJ.ftl");
            if(Objects.nonNull(file)){
                fileList.add(file);
            }
        });
        return fileList;
    }

    @Override
    public List<File> batchExportIdentifyWord(Long companyId, List<Long> identifyIds) {
        List<File> fileList = new ArrayList<>();
        identifyIds.forEach(id ->{
            Map<String, Object> dataMap = this.exportIdentifyWord(companyId, id);
            File file = ExportUtil.GenerateWordDoc(dataMap, "藏品鉴定凭证" + dataMap.get("fileName"), "CPJD.ftl");
            if(Objects.nonNull(file)){
                fileList.add(file);
            }
        });
        return fileList;
    }

    @Override
    public List<File> batchExportInTibetanWord(Long companyId, List<Long> inTibetanIds) {
        List<File> fileList = new ArrayList<>();
        inTibetanIds.forEach(id ->{
            Map<String, Object> dataMap = this.exportInTibetanWord(companyId, id);
            File file = ExportUtil.GenerateWordDoc(dataMap, "藏品入藏凭证" + dataMap.get("fileName"), "CPRC.ftl");
            if(Objects.nonNull(file)){
                fileList.add(file);
            }
        });
        return fileList;
    }

    @Override
    public List<File> batchExportCollectionRecordWord(Long companyId, List<String> registerNoList) {
        List<File> fileList = new ArrayList<>();
        registerNoList.forEach(regNo ->{
            Map<String, Object> dataMap = this.exportCollectionRecordWord(companyId, regNo);
            File file = ExportUtil.GenerateWordDoc(dataMap, "藏品档案" + dataMap.get("fileName"), "CPDAN.ftl");
            if(Objects.nonNull(file)){
                fileList.add(file);
            }
        });
        return fileList;
    }

    @Override
    public List<File> batchExportCollectionCardWord(Long companyId, List<String> registerNoList) {
        List<File> fileList = new ArrayList<>();
        registerNoList.forEach(regNo ->{
            Map<String, Object> dataMap = this.exportCollectionCardWord(companyId, regNo);
            File file = ExportUtil.GenerateWordDoc(dataMap, "藏品卡片" + dataMap.get("fileName"), "CPKP.ftl");
            if(Objects.nonNull(file)){
                fileList.add(file);
            }
        });
        return fileList;
    }

    @Override
    public List<File> batchExportCollectionIdentifyBookWord(Long companyId, List<String> registerNoList) {
        List<File> fileList = new ArrayList<>();
        registerNoList.forEach(regNo ->{
            Map<String, Object> dataMap = this.exportCollectionIdentifyBookWord(companyId, regNo);
            File file = ExportUtil.GenerateWordDoc(dataMap, "评审鉴定书" + dataMap.get("fileName"), "CPJDS.ftl");
            if(Objects.nonNull(file)){
                fileList.add(file);
            }
        });
        return fileList;
    }

    // 一些特殊数据装载类
    private String setSizeAndWeight(CollectionRegisterDetailDTO sizeWeightDTO, String qualityUnit){
        // 装载尺寸质量数据
        String sizeWeightStr = "", cmStr = "厘米，";
        if(Objects.nonNull(sizeWeightDTO.getLength())){
            sizeWeightStr += "通长：" + sizeWeightDTO.getLength() /100.0 + cmStr;
        }
        if(Objects.nonNull(sizeWeightDTO.getWidth())){
            sizeWeightStr += "通宽：" + sizeWeightDTO.getWidth()/100.0 + cmStr;
        }
        if(Objects.nonNull(sizeWeightDTO.getHeight())){
            sizeWeightStr += "通高：" + sizeWeightDTO.getHeight()/100.0 + cmStr;
        }
        if(Objects.nonNull(sizeWeightDTO.getPresence())){
            sizeWeightStr += "腹围：" + sizeWeightDTO.getPresence()/100.0 + cmStr;
        }
        if(Objects.nonNull(sizeWeightDTO.getCaliber())){
            sizeWeightStr += "口径：" + sizeWeightDTO.getCaliber()/100.0 + cmStr;
        }
        if(Objects.nonNull(sizeWeightDTO.getBottomDiameter())){
            sizeWeightStr += "底径：" + sizeWeightDTO.getBottomDiameter()/100.0 + cmStr;
        }
        if(StringUtils.isNotBlank(sizeWeightDTO.getSize())){
            sizeWeightStr += "具体尺寸为：" + sizeWeightDTO.getSize() + "，";
        }

        if(StringUtils.isBlank(qualityUnit)){
            qualityUnit ="";
        }
        if (Objects.nonNull(sizeWeightDTO.getSpecificQuality())){
            sizeWeightStr += "具体质量为：" + sizeWeightDTO.getSpecificQuality()/100.0
                    + qualityUnit + "。";
        }
        // 如有内容，则将最后的逗号更换为句号
        if(StringUtils.isNotBlank(sizeWeightStr)){
            StringBuffer sb = new StringBuffer(sizeWeightStr);
            sb.replace(sb.length() - 1, sb.length(), "。");
            sizeWeightStr = sb.toString();
        }
        return  sizeWeightStr;
    }

    private String setRemarks(CollectionRegisterDetailDTO registerDetailDTO){
        String remarkStr = "";
        if(StringUtils.isNotBlank(registerDetailDTO.getCollectionRemark())){
            remarkStr += "来源备注： " + registerDetailDTO.getCollectionRemark();
        }
        if(StringUtils.isNotBlank(registerDetailDTO.getCountRemark())){
            remarkStr += "数量备注：" + registerDetailDTO.getCountRemark();
        }
        if(StringUtils.isNotBlank(registerDetailDTO.getTextureRemark())){
            remarkStr += "质地备注：" + registerDetailDTO.getTextureRemark();
        }
        return remarkStr;
    }

    private String setAppendageList(Long companyId, Long collectionId){
        List<ScsCollectionAppendageDO> appendageDOList = this.scsCollectionAppendageMapper.listPageByCondition(companyId, collectionId, defaultSort);
        if(CollectionUtils.isEmpty(appendageDOList)){
            return "";
        }
        StringBuffer sb = new StringBuffer();
        appendageDOList.forEach(appendageDO ->{
            sb.append(appendageDO.getName());
            sb.append("、");
        });
        String resStr = sb.toString();
        return resStr.substring(0, resStr.length() - 1);
    }

    private List<String> setPostScript(Long companyId, Long collectionId){
        List<ScsCollectionRubbingDO> rubbingDOList = this.scsCollectionRubbingMapper.listPageByCondition(companyId, collectionId, null, null, defaultSort);
        if(CollectionUtils.isEmpty(rubbingDOList)){
            return null;
        }

        return rubbingDOList.stream().filter(s -> StringUtils.isNotBlank(s.getPostscript())).map(rubbingDO-> rubbingDO.getPostscript()).collect(Collectors.toList());
    }

    private List<CollectionMountDTO> setMountRecord(Long companyId, Long collectionId){
        List<ScsCollectionMountDO> mountDOList = this.scsCollectionMountMapper.listPageByCondition(companyId, collectionId, defaultSort);
        if(CollectionUtils.isEmpty(mountDOList)){
            return null;
        }

        return mountDOList.stream().map(mountDO->{
            CollectionMountDTO mountDTO = new CollectionMountDTO();
            mountDTO.setContractUnit(mountDO.getContractUnit());
            mountDTO.setProducerName(mountDO.getProducerName());
            mountDTO.setMountDate(DateUtils.formatDateYYYMMDD(mountDO.getMountDate()));
            return mountDTO;
        }).collect(Collectors.toList());
    }

    private List<String> setDrawList(Long companyId, Long collectionId){
        List<String> imageList = Lists.newArrayList();
        List<String> documentIdsList = Lists.newArrayList();
        List<String> docIdList = Lists.newArrayList();
        // 绘图
        List<ScsCollectionDrawDO> drawDOList = this.scsCollectionDrawMapper.listPageByCondition(companyId,collectionId,null,null,"gmt_create desc");
        documentIdsList.addAll(drawDOList.stream().map(ScsCollectionDrawDO::getDocumentId).collect(Collectors.toList()));
        // 拓片
        List<ScsCollectionRubbingDO> rubbingDOList = this.scsCollectionRubbingMapper.listPageByCondition(companyId, collectionId, null, null, defaultSort);
        documentIdsList.addAll(rubbingDOList.stream().map(ScsCollectionRubbingDO::getDocumentId).collect(Collectors.toList()));

        documentIdsList.forEach(k -> {
            List<String> idList = Arrays.asList(k.split(","));
            docIdList.addAll(idList);
        });

        List<Long> documentList = docIdList.stream().filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(documentList)){
            List<CommDocumentDO> commDocumentDOList = this.commDocumentMapper.listByIds(companyId,documentList);
            commDocumentDOList.forEach(k -> {
                String imageBase64 = null;
                try {
                    imageBase64 = getAndTransImage(pathHead + "/" + k.getBucket() + "/" + k.getPath());
                } catch (IOException e) {
                    e.printStackTrace();
                }
                imageList.add(imageBase64);
            });
        }
        return imageList;
    }

    private List<CollectionAboutBookDTO> setAboutBook(Long companyId, Long collectionId){
        List<ScsCollectionBookDO> bookDOList = this.scsCollectionBookMapper.listPageByCondition(companyId, collectionId, defaultSort);
        if(CollectionUtils.isEmpty(bookDOList)){
            return null;
        }
        List<Long> levelIds = new ArrayList<>();
        bookDOList.forEach(bookDO->{
            if(!levelIds.contains(bookDO.getBookLevel())){
                levelIds.add(bookDO.getBookLevel());
            }
        });

        List<CommClassificationDO> commClassDOList = this.commClassificationMapper.listByIds(companyId, levelIds);
        Map<Long, String> commClassMap = commClassDOList.stream()
                .collect(Collectors.toMap(CommClassificationDO::getId, CommClassificationDO::getName, (key1, key2) -> key2));

        return bookDOList.stream().map(bookDO ->{
            CollectionAboutBookDTO aboutBookDTO = new CollectionAboutBookDTO();
            aboutBookDTO.setAuthor(bookDO.getAuthor());
            aboutBookDTO.setPressName(setStringValue(bookDO.getPressName()));
            aboutBookDTO.setArticle(setStringValue(bookDO.getArticle()));
            aboutBookDTO.setBookLevel(Objects.isNull(bookDO.getBookLevel()) ? defaultStr : commClassMap.get(bookDO.getBookLevel()));
            aboutBookDTO.setRecordDate(DateUtils.formatDateYYYMMDD(bookDO.getRecordDate()));
            return aboutBookDTO;
        }).collect(Collectors.toList());
    }

    private List<CollectionIdentifyOpinionDTO> setIdentifyOpinion(Long companyId, Long collectionId){
        List<ScsCollectionIdentifyDO> identifyDOList = this.scsCollectionIdentifyMapper.listPageByCondition(companyId, collectionId, defaultSort);
        if(CollectionUtils.isEmpty(identifyDOList)){
            return null;
        }

        return identifyDOList.stream().map(identifyDO->{
            CollectionIdentifyOpinionDTO opinionDTO = new CollectionIdentifyOpinionDTO();
            opinionDTO.setApprName(identifyDO.getAppraiserName());
            opinionDTO.setOrgAgency(setStringValue(identifyDO.getOrgAgency()));
            opinionDTO.setIdentAgency(setStringValue(identifyDO.getIdentifyAgency()));
            opinionDTO.setIdentOpinion(identifyDO.getIdentifyOpinion());
            opinionDTO.setIdentDate(DateUtils.formatDateYYYMMDD(identifyDO.getIdentifyDate()));
            return opinionDTO;
        }).collect(Collectors.toList());
    }


    /**
     * 获取图片并转换为Base64
     * @param imgUrl 图片路径
     * @return
     * @throws IOException
     */
    private String getAndTransImage(String imgUrl) throws IOException {
        URL url = new URL(imgUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setConnectTimeout(5000);
        InputStream is = conn.getInputStream();
        byte[] imgData = readInputStream(is);
        Base64Encoder encoder = new Base64Encoder();
        return encoder.encode(imgData);
    }

    private byte[] readInputStream(InputStream inputStream) throws IOException{
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        while ((len = inputStream.read(buffer)) != -1){
            outputStream.write(buffer, 0, len);
        }

        inputStream.close();
        return outputStream.toByteArray();
    }

    private String setStringValue(String value){
        return StringUtils.isBlank(value) ? defaultStr : value;
    }
    private String setStringNullValue(String value){
        return StringUtils.isBlank(value) ? "" : value;
    }
    private String setDateValue(Date value){
        return Objects.isNull(value) ? defaultStr : DateUtils.formatDateYYYMMDD(value);
    }
    private String setDateNullValue(Date value){
        return Objects.isNull(value) ? "" : DateUtils.formatDateYYYMMDD(value);
    }
    private String setLongValue(Long value){
        return Objects.isNull(value) ? defaultStr:value.toString();
    }

}
