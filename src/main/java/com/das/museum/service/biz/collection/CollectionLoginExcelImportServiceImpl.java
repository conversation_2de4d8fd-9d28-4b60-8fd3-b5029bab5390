package com.das.museum.service.biz.collection;

import com.alibaba.fastjson.JSON;
import com.das.museum.common.utils.ImportExcelUtil;
import com.das.museum.domain.enums.RegisterStatusEnum;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.mapper.*;
import com.das.museum.service.biz.collection.action.AddCollectionRegisterAction;
import com.das.museum.service.biz.collection.dto.GetImportResultDTO;
import com.das.museum.service.enums.ImportStatusEnum;
import com.das.museum.service.enums.YesOrNoEnum;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.collection.login.request.CollectionImportReq;
import com.das.museum.web.controller.collection.login.response.CollectionImportErrorVO;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: shaochengwei
 * @Date: 2023-01-07
 */
@Slf4j
@Service
public class CollectionLoginExcelImportServiceImpl implements CollectionLoginExcelImportService{

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private ScsCollectionLoginImportMapper scsCollectionLoginImportMapper;

    @Resource
    private CollectionRegisterService collectionRegisterService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void importData(MultipartFile file,Long companyId,Long userId, Byte importType) {
        //开始导入时间
        Long startTime = System.currentTimeMillis();
        List<CollectionImportReq> collectionImportList = new ArrayList<>();
        ScsCollectionLoginImportDO scsCollectionLoginImportDO = scsCollectionLoginImportMapper.selectByParams(companyId,userId,YesOrNoEnum.否.getCode().byteValue());
        if(Objects.isNull(scsCollectionLoginImportDO)){
            scsCollectionLoginImportDO = new ScsCollectionLoginImportDO();
            scsCollectionLoginImportDO.setCompanyId(companyId);
            scsCollectionLoginImportDO.setUserId(userId);
            scsCollectionLoginImportDO.setImportStatus(ImportStatusEnum.导入中.getCode().byteValue());
            scsCollectionLoginImportDO.setResultConfirm(YesOrNoEnum.否.getCode().byteValue());
            scsCollectionLoginImportDO.setCreator(userId);
            scsCollectionLoginImportDO.setModifier(userId);
            scsCollectionLoginImportMapper.insertSelective(scsCollectionLoginImportDO);
        }

        //清空上一次导入错误列表
        redisTemplate.delete("collectionImportError-"+companyId+"-"+userId);

        //获取数据
        List<List<Object>> oList = Lists.newArrayList();
        try {
            oList = ImportExcelUtil.getListByExcel(file.getInputStream(), file.getOriginalFilename());
        }catch (Exception e){
            scsCollectionLoginImportDO.setImportStatus(ImportStatusEnum.导入失败.getCode().byteValue());
            scsCollectionLoginImportDO.setFailedReason((byte)0);
            log.error("Excel解析失败！"+e.getMessage());
        }

        //获取导入的参数字典
        String[] bizCodesArr = {"NDLB","WWLB","ZHIDILEIBIE","ZLFW","ZLDW","CPJB","ZJFS","WCCD"};
        List<CommClassificationDO> commClassificationDOList = commClassificationMapper.selectBybizCodeList(companyId, (byte) 1,Arrays.asList(bizCodesArr));
        Map<String,Long> bizCodeMap = new HashMap<>();
        for(CommClassificationDO commClassificationDO : commClassificationDOList){
            bizCodeMap.put(commClassificationDO.getBizCode(),commClassificationDO.getId());
        }

        //年代
        Map<String,Long> ageMap = new HashMap<>();
        if(bizCodeMap.containsKey("NDLB")){
            List<CommClassificationDO> ageList = commClassificationMapper.selectChildById(companyId,bizCodeMap.get("NDLB"));
            for(CommClassificationDO commClassificationDO : ageList){
                ageMap.put(commClassificationDO.getName(),commClassificationDO.getId());
            }
        }

        //文物类别
        Map<String,Long> categoryMap = new HashMap<>();
        if(bizCodeMap.containsKey("WWLB")){
            List<CommClassificationDO> ageList = commClassificationMapper.selectChildById(companyId,bizCodeMap.get("WWLB"));
            for(CommClassificationDO commClassificationDO : ageList){
                categoryMap.put(commClassificationDO.getName(),commClassificationDO.getId());
            }
        }

        //质地类别、单一质地
        Map<String,Long> textureCategoryMap = new HashMap<>();
        // 无机复合质地
        Map<String,Long> wjTextureCategoryMap = new HashMap<>();
        // 有机复合质地
        Map<String,Long> yjTextureCategoryMap = new HashMap<>();
        // 有机无机复合质地
        Map<String,Long> hhTextureCategoryMap = new HashMap<>();
        if(bizCodeMap.containsKey("ZHIDILEIBIE")){
            List<CommClassificationDO> ageList = commClassificationMapper.selectChildById(companyId,bizCodeMap.get("ZHIDILEIBIE"));
            for(CommClassificationDO commClassificationDO : ageList){
                // 单一质地
                if(!commClassificationDO.getName().contains("WJ-") && !commClassificationDO.getName().contains("YJ-") && !commClassificationDO.getName().contains("HH-")){
                    textureCategoryMap.put(commClassificationDO.getName(),commClassificationDO.getId());
                }
                if(commClassificationDO.getName().contains("WJ-")){
                    wjTextureCategoryMap.put(commClassificationDO.getName(),commClassificationDO.getId());
                }
                if(commClassificationDO.getName().contains("YJ-")){
                    yjTextureCategoryMap.put(commClassificationDO.getName(),commClassificationDO.getId());
                }
                if(commClassificationDO.getName().contains("HH-")){
                    hhTextureCategoryMap.put(commClassificationDO.getName(),commClassificationDO.getId());
                }
            }
        }


        //质量范围
        Map<String,Long> qualityRangeMap = new HashMap<>();
        if(bizCodeMap.containsKey("ZLFW")){
            List<CommClassificationDO> ageList = commClassificationMapper.selectChildById(companyId,bizCodeMap.get("ZLFW"));
            for(CommClassificationDO commClassificationDO : ageList){
                qualityRangeMap.put(commClassificationDO.getName(),commClassificationDO.getId());
            }
        }

        //质量单位
        Map<String,Long> qualityUnitMap = new HashMap<>();
        if(bizCodeMap.containsKey("ZLDW")){
            List<CommClassificationDO> ageList = commClassificationMapper.selectChildById(companyId,bizCodeMap.get("ZLDW"));
            for(CommClassificationDO commClassificationDO : ageList){
                qualityUnitMap.put(commClassificationDO.getName(),commClassificationDO.getId());
            }
        }

        //文物级别
        Map<String,Long> identifyLevelMap = new HashMap<>();
        if(bizCodeMap.containsKey("CPJB")){
            List<CommClassificationDO> ageList = commClassificationMapper.selectChildById(companyId,bizCodeMap.get("CPJB"));
            for(CommClassificationDO commClassificationDO : ageList){
                identifyLevelMap.put(commClassificationDO.getName(),commClassificationDO.getId());
            }
        }

        //文物来源
        Map<String,Long> sourceMap = new HashMap<>();
        if(bizCodeMap.containsKey("ZJFS")){
            List<CommClassificationDO> ageList = commClassificationMapper.selectChildById(companyId,bizCodeMap.get("ZJFS"));
            for(CommClassificationDO commClassificationDO : ageList){
                sourceMap.put(commClassificationDO.getName(),commClassificationDO.getId());
            }
        }

        //完残程度
        Map<String,Long> completeDegreeMap = new HashMap<>();
        if(bizCodeMap.containsKey("WCCD")){
            List<CommClassificationDO> ageList = commClassificationMapper.selectChildById(companyId,bizCodeMap.get("WCCD"));
            for(CommClassificationDO commClassificationDO : ageList){
                completeDegreeMap.put(commClassificationDO.getName(),commClassificationDO.getId());
            }
        }

        //缓存本次导入的所有数据
        Map<Integer,CollectionImportErrorVO> collectionMap = new HashMap<>();

        List<CollectionImportErrorVO> collectionImportErrorVOList = Lists.newArrayList();
        //从第4行开始，第1、2、3行是表头
        if(oList.size() >= 2) {
            for (int i = 1; i < oList.size(); i++) {
                //错误信息
                List<String> errorList = Lists.newArrayList();

                List<Object> list = oList.get(i);
                String registerNo = String.valueOf(list.get(1));
                if(StringUtils.isNotBlank(registerNo) && registerNo.contains(".0")){
                    registerNo = registerNo.replace(".0","");
                }
                String name = String.valueOf(list.get(2));
                String originalName = String.valueOf(list.get(3));
                String age1 = String.valueOf(list.get(4));
                String age2 = String.valueOf(list.get(5));
                String age3 = String.valueOf(list.get(6));
                String age4 = String.valueOf(list.get(7));
                String era = String.valueOf(list.get(8));
                String category = String.valueOf(list.get(9));
                String textureCategory1 = String.valueOf(list.get(10));
                String textureCategory2 = String.valueOf(list.get(11));
                String texture = String.valueOf(list.get(12));
                String actualCount = String.valueOf(list.get(13));
                String length = String.valueOf(list.get(14));
                String width = String.valueOf(list.get(15));
                String height = String.valueOf(list.get(16));
                String size = String.valueOf(list.get(17));
                String qualityRange = String.valueOf(list.get(18));
                String specificQuality = String.valueOf(list.get(19));
                String qualityUnit = String.valueOf(list.get(20));
                String identifyLevel = String.valueOf(list.get(21));
                String source = String.valueOf(list.get(22));
                String completeDegree = String.valueOf(list.get(23));

                CollectionImportErrorVO collectionImportErrorVO = new CollectionImportErrorVO();
                collectionImportErrorVO.setRegisterNo(registerNo);
                collectionImportErrorVO.setName(name);
                collectionImportErrorVO.setOriginalName(originalName);
                collectionImportErrorVO.setAge1(age1);
                collectionImportErrorVO.setAge2(age2);
                collectionImportErrorVO.setAge3(age3);
                collectionImportErrorVO.setAge4(age4);
                collectionImportErrorVO.setEra(era);
                collectionImportErrorVO.setCategory(category);
                collectionImportErrorVO.setTextureCategory1(textureCategory1);
                collectionImportErrorVO.setTextureCategory2(textureCategory2);
                collectionImportErrorVO.setTexture(texture);
                collectionImportErrorVO.setActualCount(actualCount);
                collectionImportErrorVO.setLength(length);
                collectionImportErrorVO.setWidth(width);
                collectionImportErrorVO.setHeight(height);
                collectionImportErrorVO.setSize(size);
                collectionImportErrorVO.setQualityRange(qualityRange);
                if(StringUtils.isNotBlank(specificQuality)){
                    try {
                        collectionImportErrorVO.setSpecificQuality(Double.parseDouble(specificQuality));
                    }catch (Exception e){
                        errorList.add("具体质量只能时数字！");
                    }
                }
                collectionImportErrorVO.setQualityUnit(qualityUnit);
                collectionImportErrorVO.setIdentifyLevel(identifyLevel);
                collectionImportErrorVO.setSource(source);
                collectionImportErrorVO.setCompleteDegree(completeDegree);

                CollectionImportReq collectionImportReq = new CollectionImportReq();
                collectionImportReq.setDataNumber(i);
                if (StringUtils.isNotBlank(registerNo)) {
                    collectionImportReq.setRegisterNo(registerNo);
                } else {
                    errorList.add("登录号不能为空");
                }

                if (StringUtils.isNotBlank(name)) {
                    collectionImportReq.setName(name);
                } else {
                    errorList.add("名称不能为空");
                }

                collectionImportReq.setOriginalName(originalName);

                String age = age1;
                if(StringUtils.isNotBlank(age2)){
                    age = age2;
                }
                if(StringUtils.isNotBlank(age3)){
                    age = age3;
                }
                if(StringUtils.isNotBlank(age4)){
                    age = age4;
                }

                if (StringUtils.isNotBlank(age)) {
                    if (ageMap.containsKey(age)) {
                        collectionImportReq.setAge4(ageMap.get(age));
                    } else {
                        errorList.add("年代无法解析");
                    }
                }
//                else {
//                    errorList.add("年代不能为空");
//                }

                collectionImportReq.setEra(era);

                if (StringUtils.isNotBlank(category)) {
                    if (categoryMap.containsKey(category)) {
                        collectionImportReq.setCategory(categoryMap.get(category));
                    } else {
                        errorList.add("文物类别无法解析");
                    }
                }
//                else {
//                    errorList.add("文物类别不能为空");
//                }

                if (StringUtils.isNotBlank(textureCategory2)) {
                    if (textureCategoryMap.containsKey(textureCategory2)) {
                        collectionImportReq.setTextureCategory2(textureCategoryMap.get(textureCategory2));
                    } else {
                        errorList.add("质地类别2无法解析");
                    }
                }
//                else {
//                    errorList.add("质地类别2不能为空");
//                }

                if (StringUtils.isNotBlank(texture)) {
                    List<Long> textureList = Lists.newArrayList();
                    String[] textureArr = texture.split("，");

                    if("无机质".equals(textureCategory2) || "有机质".equals(textureCategory2)){
                        for (int k = 0; k < textureArr.length; k++) {
                            if (textureCategoryMap.containsKey(textureArr[k])) {
                                textureList.add(textureCategoryMap.get(textureArr[k]));
                            } else {
                                errorList.add("质地无法解析");
                            }
                        }
                    }else if("无机复合或组合".equals(textureCategory2)){
                        for (int k = 0; k < textureArr.length; k++) {
                            if (wjTextureCategoryMap.containsKey(textureArr[k])) {
                                textureList.add(wjTextureCategoryMap.get(textureArr[k]));
                            } else {
                                errorList.add("质地无法解析");
                            }
                        }
                    }else if("有机复合或组合".equals(textureCategory2)){
                        for (int k = 0; k < textureArr.length; k++) {
                            if (yjTextureCategoryMap.containsKey(textureArr[k])) {
                                textureList.add(yjTextureCategoryMap.get(textureArr[k]));
                            } else {
                                errorList.add("质地无法解析");
                            }
                        }
                    }else if("有机无机复合或组合".equals(textureCategory2)){
                        for (int k = 0; k < textureArr.length; k++) {
                            if (hhTextureCategoryMap.containsKey(textureArr[k])) {
                                textureList.add(hhTextureCategoryMap.get(textureArr[k]));
                            } else {
                                errorList.add("质地无法解析");
                            }
                        }
                    }
                    List<String> textureListStr = textureList.stream().sorted().map(String::valueOf).collect(Collectors.toList());
                    collectionImportReq.setTexture(String.join(",", textureListStr));
                }
//                else {
//                    errorList.add("质地不能为空");
//                }

                if (StringUtils.isNotBlank(actualCount)) {
                    try {
                        collectionImportReq.setActualCount(Double.parseDouble(actualCount));
                    } catch (Exception e) {
                        errorList.add("实际数量只能是数字");
                    }
                }
//                else {
//                    errorList.add("实际数量不能为空");
//                }

                if (StringUtils.isNotBlank(length)) {
                    try {
                        collectionImportReq.setLength(Double.parseDouble(length));
                    } catch (Exception e) {
                        errorList.add("通长只能是数字");
                    }
                }

                if (StringUtils.isNotBlank(width)) {
                    try {
                        collectionImportReq.setWidth(Double.parseDouble(width));
                    } catch (Exception e) {
                        errorList.add("通宽只能是数字");
                    }
                }

                if (StringUtils.isNotBlank(height)) {
                    try {
                        collectionImportReq.setHeight(Double.parseDouble(height));
                    } catch (Exception e) {
                        errorList.add("通高只能是数字");
                    }
                }

                collectionImportReq.setSize(size);

                if (StringUtils.isNotBlank(qualityRange)) {
                    if (qualityRangeMap.containsKey(qualityRange)) {
                        collectionImportReq.setQualityRange(qualityRangeMap.get(qualityRange));
                    } else {
                        errorList.add("质量范围无法解析");
                    }
                }
//                else {
//                    errorList.add("质量范围不能为空");
//                }

                if (StringUtils.isNotBlank(specificQuality)) {
                    try {
                        collectionImportReq.setSpecificQuality(Double.parseDouble(specificQuality));
                    } catch (Exception e) {
                        errorList.add("具体质量只能是数字");
                    }
                }

                if (StringUtils.isNotBlank(qualityUnit)) {
                    if (qualityUnitMap.containsKey(qualityUnit)) {
                        collectionImportReq.setQualityUnit(qualityUnitMap.get(qualityUnit));
                    } else {
                        errorList.add("单位无法解析");
                    }
                }

                if (StringUtils.isNotBlank(identifyLevel)) {
                    if (identifyLevelMap.containsKey(identifyLevel)) {
                        collectionImportReq.setIdentifyLevel(identifyLevelMap.get(identifyLevel));
                    } else {
                        errorList.add("文物级别无法解析");
                    }
                }
//                else {
//                    errorList.add("文物级别不能为空");
//                }

                if (StringUtils.isNotBlank(source)) {
                    if (sourceMap.containsKey(source)) {
                        collectionImportReq.setSource(sourceMap.get(source));
                    } else {
                        errorList.add("文物来源无法解析");
                    }
                }
//                else {
//                    errorList.add("文物来源不能为空");
//                }

                if (StringUtils.isNotBlank(completeDegree)) {
                    if (completeDegreeMap.containsKey(completeDegree)) {
                        collectionImportReq.setCompleteDegree(completeDegreeMap.get(completeDegree));
                    } else {
                        errorList.add("完残程度无法解析");
                    }
                }
//                else {
//                    errorList.add("完残程度不能为空");
//                }

                if (CollectionUtils.isNotEmpty(errorList)) {
                    collectionImportErrorVO.setErrorBack(String.join("，", errorList));
                    collectionImportErrorVOList.add(collectionImportErrorVO);
                } else {
                    collectionImportList.add(collectionImportReq);
                }

                collectionMap.put(i, collectionImportErrorVO);
            }
        }

        //将解析成功的数据插入数据库
        if(CollectionUtils.isNotEmpty(collectionImportList)){
            for(CollectionImportReq req : collectionImportList){
                AddCollectionRegisterAction action = new AddCollectionRegisterAction();
                action.setCompanyId(companyId);
                action.setCreator(userId);
                action.setRegisterNo(req.getRegisterNo());
                action.setName(req.getName());
                action.setOriginalName(req.getOriginalName());
                action.setAge(req.getAge4());
                action.setEra(req.getEra());
                action.setCategory(req.getCategory());
                action.setTextureCategory(req.getTextureCategory2());
                action.setTexture(req.getTexture());
                if(req.getActualCount() != null){
                    action.setActualCount(req.getActualCount().intValue());
                }

                if(req.getLength()!=null) {
                    action.setLength((long)(req.getLength()*100));
                }
                if(req.getWidth()!=null) {
                    action.setWidth((long)(req.getWidth()*100));
                }
                if(req.getHeight()!=null) {
                    action.setHeight((long)(req.getHeight()*100));
                }
                action.setQualityRange(req.getQualityRange());
                if(req.getSpecificQuality() != null) {
                    action.setSpecificQuality((long) (req.getSpecificQuality() * 100));
                }
                action.setQualityUnit(req.getQualityUnit());
                action.setIdentifyLevel(req.getIdentifyLevel());
                action.setSource(req.getSource());
                action.setSize(req.getSize());
                action.setCompleteDegree(req.getCompleteDegree());
                if(importType == 2){
                    action.setRegisterStatus(RegisterStatusEnum.ARTS.getCode());
                }else{
                    action.setRegisterStatus(RegisterStatusEnum.NRTS.getCode());
                }
                try{
                    collectionRegisterService.addCollectionRegister(action);
                }catch (Exception e){
                    //将保存失败的数据保存
                    CollectionImportErrorVO vo = collectionMap.get(req.getDataNumber());
                    vo.setErrorBack(e.getMessage());
                    collectionImportErrorVOList.add(vo);
                }
            }
        }

        //将解析失败和保存失败的数据存入redis缓存,key值加上companyId和userId
        redisTemplate.opsForValue().set("collection—import-error-"+companyId+"-"+userId, JSON.toJSON(collectionImportErrorVOList).toString());

        //更新导入结果
        Long endTime = System.currentTimeMillis();
        scsCollectionLoginImportDO.setUseTime(endTime-startTime);
        if(CollectionUtils.isNotEmpty(collectionImportErrorVOList)){
            scsCollectionLoginImportDO.setImportStatus(ImportStatusEnum.导入失败.getCode().byteValue());
            scsCollectionLoginImportDO.setFailedReason((byte)1);
        }else{
            scsCollectionLoginImportDO.setImportStatus(ImportStatusEnum.导入成功.getCode().byteValue());
        }

        scsCollectionLoginImportDO.setFailedNum((long) collectionImportErrorVOList.size());
        if(oList.size() >= 2){
            // 2行表头
            scsCollectionLoginImportDO.setSuccessNum((long)oList.size()- 1 - collectionImportErrorVOList.size());
        }
        scsCollectionLoginImportMapper.updateByPrimaryKeySelective(scsCollectionLoginImportDO);
    }


    @Override
    public GetImportResultDTO getImportResult(){
        GetImportResultDTO getImportResultDTO = new GetImportResultDTO();
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        Long companyId = userBO.getCompanyId();
        Long userId = userBO.getUserId();
        ScsCollectionLoginImportDO scsCollectionLoginImportDO = scsCollectionLoginImportMapper.selectByParams(companyId,userId,YesOrNoEnum.否.getCode().byteValue());
        if(Objects.nonNull(scsCollectionLoginImportDO)){
            BeanUtils.copyProperties(scsCollectionLoginImportDO,getImportResultDTO);
        }
        return getImportResultDTO;
    }

    @Override
    public void confirmKnow(Long id){
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        this.scsCollectionLoginImportMapper.updateByCompanyId(userBO.getCompanyId(),id,YesOrNoEnum.是.getCode().byteValue());
    }

}
