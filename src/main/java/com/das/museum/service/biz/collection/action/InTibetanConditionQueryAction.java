package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;

@Data
public class InTibetanConditionQueryAction extends PageInfoBaseReq {
    private static final long serialVersionUID = -9096667604501823066L;
    private Long companyId;
    private String inTibetanCertificateNo;
    private String registerNo;
    private String collectionName;
    private String checkStatus;
    private String sortBy;
    private Integer isUserChecked;
}
