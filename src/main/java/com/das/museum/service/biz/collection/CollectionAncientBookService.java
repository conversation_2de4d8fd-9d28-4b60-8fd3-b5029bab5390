package com.das.museum.service.biz.collection;

import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.service.biz.collection.action.AddCollectionAncientBookAction;
import com.das.museum.service.biz.collection.action.AncientBookQueryPageByConditionAction;
import com.das.museum.service.biz.collection.action.EditCollectionAncientBookAction;
import com.das.museum.service.biz.collection.dto.CollectionAncientBookDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionAncientBookPageDTO;

public interface CollectionAncientBookService {
    Long addCollectionAncientBook(AddCollectionAncientBookAction action);
    void editCollectionAncientBook(EditCollectionAncientBookAction action);
    void delByCollectionAncientBookId(Long companyId, Long collectionAncientBookId);
    SimplePageInfo<CollectionAncientBookPageDTO> queryPageByCondition(AncientBookQueryPageByConditionAction action);
    CollectionAncientBookDetailDTO queryByCollectionAncientBookId(Long companyId, Long collectionAncientBookId);
}
