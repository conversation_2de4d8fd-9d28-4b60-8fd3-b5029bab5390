package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionRegisterIdentifyDetailDTO implements Serializable {
    private static final long serialVersionUID = -3777044022014271824L;
    private Long registerIdentifyId;
    private Long collectionId;
    private Long identifyType;
    private Long appraiserId;
    private String appraiserName;
    private String orgAgency;
    private String identifyAgency;
    private String approveAgency;
    private String identifyOpinion;
    private String identifyRemark;
    private Date identifyDate;
    private String documentId;
    private List<CommonDocumentDTO> commDocumentList;
}