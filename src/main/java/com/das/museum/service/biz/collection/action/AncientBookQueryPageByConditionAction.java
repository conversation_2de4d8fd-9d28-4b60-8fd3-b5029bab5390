package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AncientBookQueryPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = 2408454700481386510L;
    private Long companyId;
    private Long collectionId;
    private String sortBy;
}
