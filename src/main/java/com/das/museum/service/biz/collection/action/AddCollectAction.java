package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AddCollectAction implements Serializable {
    private static final long serialVersionUID = -5558154371239286676L;
    private String collectCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private String batch;
    private String transferor;
    private String departmentHead;
    private String documentId;
    private Long companyId;
    private Long creator;
    private String creatorName;
    private List<AddCollectCollectionAction> collectCollectionList;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}
