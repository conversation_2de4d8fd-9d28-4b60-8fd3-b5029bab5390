package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AddCollectionDrawAction implements Serializable {
    private static final long serialVersionUID = -4057465126565655286L;
    private Long companyId;
    private Long collectionId;
    private String title;
    private String drawNo;
    private Long draughtsmanId;
    private String draughtsmanName;
    private String proportion;
    private Date drawDate;
    private String documentId;
    private Long creator;
}
