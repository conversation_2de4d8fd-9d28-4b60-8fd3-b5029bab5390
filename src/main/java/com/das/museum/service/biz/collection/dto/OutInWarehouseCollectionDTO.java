package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class OutInWarehouseCollectionDTO implements Serializable {
    private static final long serialVersionUID = 8095485916776084199L;
    private Long collectionRegisterId;
    private Long companyId;
    private Long collectionId;
    private String registerNo;
    private String name;
    private Long classify;
    private String classifyName;
    private String classifyDesc;
    private Long identifyLevel;
    private String identifyLevelName;
    private String era;
    private Long age;
    private String ageName;
    private String registerStatus;
    private String documentId;
    private List<CommonDocumentDTO> commonDocumentList;
    private Long completeDegree;
    private String completeDegreeName;
    private Long warehouseId;
    private String warehouseName;
    private Integer actualCount;
    private Long countUnit;
    private String countUnitName;
    private Integer collectionCount;
    private String texture;
    private String textureName;
    private Long source;
    private String sourceName;
    private String specificSource;
    private String size;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inWarehouseDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outWarehouseDate;
    private String outDocumentId;
    private List<CommonDocumentDTO> outDocumentList;
    private Long transferor;
    private String transferorName;
    private Long receiver;
    private String receiverName;
    private String returnAddress;
    private String collectionRemark;
    private String returnRemark;
    private String inDocumentId;
    private List<CommonDocumentDTO> inDocumentList;
}
