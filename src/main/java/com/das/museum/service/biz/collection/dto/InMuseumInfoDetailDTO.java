package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class InMuseumInfoDetailDTO implements Serializable {
    private static final long serialVersionUID = 6045845239480963451L;
    private Long inMuseumId;
    private Long companyId;
    private String inMuseumCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private String batch;
    private Long submitter;
    private String submitterName;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private List<CommonDocumentDTO> commDocumentList;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}