package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionBookAction;
import com.das.museum.service.biz.collection.action.BookQueryPageByConditionAction;
import com.das.museum.service.biz.collection.action.EditCollectionBookAction;
import com.das.museum.service.biz.collection.dto.CollectionBookDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionBookPageDTO;
import com.das.museum.common.bo.SimplePageInfo;

public interface CollectionBookService {
    Long addCollectionBook(AddCollectionBookAction action);
    void editCollectionBook(EditCollectionBookAction action);
    void delByCollectionBookId(Long companyId, Long collectionBookId);
    SimplePageInfo<CollectionBookPageDTO> queryPageByCondition(BookQueryPageByConditionAction action);
    CollectionBookDetailDTO queryByCollectionBookId(Long companyId, Long collectionBookId);
}
