package com.das.museum.service.biz.ticket.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: shaochengwei
 * @Date: 2023-01-05
 */
@Data
public class VisitorStatisticsDTO {

    /**
     * 微信预约总人数（观众总览）
     */
    private Long allTotalNum;

    /**
     * 微信预约总人数（观众总览）环比
     */
    private BigDecimal allTotalRingRatio;

    /**
     * 微信预约总人数（观众总览）环比
     */
    private String allTotalRingRatioStr;

    /**
     * 微信预约总人数（观众总览）同比
     */
    private BigDecimal allTotalYoY;

    /**
     * 微信预约总人数（观众总览）同比
     */
    private String allTotalYoyStr;

    /**
     * 入馆人数（观众总览）
     */
    private Long allInNum;

    /**
     * 入馆人数（观众总览）环比
     */
    private BigDecimal allInNumRingRatio;

    /**
     * 入馆人数（观众总览）环比
     */
    private String allInNumRingRatioStr;

    /**
     * 入馆人数（观众总览）同比
     */
    private BigDecimal allInNumYoY;

    /**
     * 入馆人数（观众总览）同比
     */
    private String allInNumYoyStr;

    /**
     * 未核销（观众总览）
     */
    private Long allOutNum;

    /**
     * 未核销（观众总览）环比
     */
    private BigDecimal allOutNumRingRatio;

    /**
     * 未核销（观众总览）环比
     */
    private String allOutNumRingRatioStr;

    /**
     * 未核销（观众总览）同比
     */
    private BigDecimal allOutNumYoY;

    /**
     * 未核销（观众总览）同比
     */
    private String allOutNumYoyStr;

    /**
     * 微信预约（个人预约）
     */
    private Long personalTotalNum;

    /**
     * 入馆人数（个人预约）
     */
    private Long personalInNum;

    /**
     * 未核销（个人预约）
     */
    private Long personalOutNum;

    /**
     * 微信预约团队个数（团队预约）
     */
    private Integer teamTotalNum;

    /**
     * 微信预约人数（团队预约）
     */
    private Long teamManTotalNum;

    /**
     * 入馆团队个数（团队预约）
     */
    private Long teamInNum;

    /**
     * 入馆团队人数（团队预约）
     */
    private Long teamManInNum;

    /**
     * 未核销团队个数（团队预约）
     */
    private Long teamOutNum;

    /**
     * 未核销团队人数（团队预约）
     */
    private Long teamManOutNum;

}
