package com.das.museum.service.biz.collection.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectionAppendagePageDTO implements Serializable {
    private static final long serialVersionUID = -3680566391916086926L;
    private Long collectionAppendageId;
    private Long companyId;
    private Long collectionId;
    private String name;
    private Integer actualCount;
    private Long meteringUnit;
    private String meteringUnitName;
    private String shape;
    private Long completeDegree;
    private String completeDegreeName;
    private String size;
    private String texture;
    private String weight;
    private String currentSituation;
    private String remark;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}
