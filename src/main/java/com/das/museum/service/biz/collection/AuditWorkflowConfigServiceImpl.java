package com.das.museum.service.biz.collection;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.domain.classification.entity.AuditWorkflowConfigEntity;
import com.das.museum.domain.classification.repository.AuditWorkflowConfigRepository;
import com.das.museum.domain.enums.AuditWorkflowBizTypeEnum;
import com.das.museum.domain.enums.ClassificationBizTypeEnum;
import com.das.museum.infr.dataobject.CommWorkflowConfigDO;
import com.das.museum.infr.dataobject.CommAuditWorkflowConfigDO;
import com.das.museum.infr.mapper.CommWorkflowConfigMapper;
import com.das.museum.infr.mapper.CommAuditWorkflowConfigMapper;
import com.das.museum.service.biz.collection.action.AddAuditWorkflowConfigAction;
import com.das.museum.service.biz.collection.action.EditAuditWorkflowConfigAction;
import com.das.museum.service.biz.collection.dto.AuditWorkflowConfigDTO;
import com.das.museum.service.biz.common.ClassificationService;
import com.das.museum.service.biz.common.dto.ClassificationDTO;
import com.das.museum.web.controller.collection.process.response.GetWorkflowConfigByBizIdVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AuditWorkflowConfigServiceImpl implements AuditWorkflowConfigService {

    @Resource
    private AuditWorkflowConfigRepository auditWorkflowConfigRepository;

    @Resource
    private CommAuditWorkflowConfigMapper commAuditWorkflowConfigMapper;

    @Resource
    private ClassificationService classificationService;

    @Resource
    private CommWorkflowConfigMapper commWorkflowConfigMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addAuditWorkflowConfig(AddAuditWorkflowConfigAction action) {
        Long companyId = action.getCompanyId();
        Boolean check = this.duplicateFlowNameCheck(companyId, action.getBizId(), action.getFlowName());
        if (!check) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "流程[" + action.getFlowName() + "]节点已存在");
        }
        List<CommAuditWorkflowConfigDO> auditWorkflowConfigDOList = this.commAuditWorkflowConfigMapper.listByBizId(companyId, action.getBizId());
        if (CollectionUtils.isNotEmpty(auditWorkflowConfigDOList) && Objects.isNull(action.getNextNodeId()) && Objects.isNull(action.getPrevNodeId())) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "流程[" + action.getFlowName() + "]节点不存在关联节点");
        }

        CommAuditWorkflowConfigDO auditWorkflowConfigDOPrev = null;
        if (Objects.nonNull(action.getPrevNodeId())) {
            auditWorkflowConfigDOPrev = this.commAuditWorkflowConfigMapper.selectById(companyId, action.getPrevNodeId());
            if (Objects.isNull(auditWorkflowConfigDOPrev)) {
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "流程[" + action.getFlowName() + "]节点前置节点不存在");
            }
        }
        if (Objects.nonNull(action.getNextNodeId())) {
            CommAuditWorkflowConfigDO auditWorkflowConfigDO = this.commAuditWorkflowConfigMapper.selectById(companyId, action.getNextNodeId());
            if (Objects.isNull(auditWorkflowConfigDO)) {
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "流程[" + action.getFlowName() + "]节点后置节点不存在");
            }
        }
        AuditWorkflowConfigEntity entity = BeanCopyUtils.copyByJSON(action, AuditWorkflowConfigEntity.class);
        entity.setModifier(action.getCreator());

        if(Objects.isNull(action.getPrevNodeId()) && Objects.isNull(action.getNextNodeId())){
            // 如果是第一个节点
            entity.setSortIndex((byte)1);
        }else if(Objects.nonNull(action.getPrevNodeId())) {
            entity.setSortIndex((byte) (auditWorkflowConfigDOPrev.getSortIndex() + 1));
        }

        this.auditWorkflowConfigRepository.saveAuditWorkflowConfig(entity);
        if (Objects.nonNull(action.getPrevNodeId())) {
            CommAuditWorkflowConfigDO auditWorkflowConfigDO = new CommAuditWorkflowConfigDO();
            auditWorkflowConfigDO.setId(action.getPrevNodeId());
            auditWorkflowConfigDO.setNextNodeId(entity.getAuditWorkflowConfigId());
            auditWorkflowConfigDO.setModifier(action.getCreator());
            this.commAuditWorkflowConfigMapper.updateByPrimaryKeySelective(auditWorkflowConfigDO);
        }
        if (Objects.nonNull(action.getNextNodeId())) {
            CommAuditWorkflowConfigDO auditWorkflowConfigDO = new CommAuditWorkflowConfigDO();
            auditWorkflowConfigDO.setId(action.getNextNodeId());
            auditWorkflowConfigDO.setPrevNodeId(entity.getAuditWorkflowConfigId());
            auditWorkflowConfigDO.setModifier(action.getCreator());
            this.commAuditWorkflowConfigMapper.updateByPrimaryKeySelective(auditWorkflowConfigDO);
        }
        return entity.getAuditWorkflowConfigId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editAuditWorkflowConfig(EditAuditWorkflowConfigAction action) {
        Long auditWorkflowConfigId = action.getAuditWorkflowConfigId();
        AuditWorkflowConfigDTO auditWorkflowConfigDTO = this.queryByBizIdAndFlowName(action.getCompanyId(), action.getBizId(), action.getFlowName());
        if (Objects.nonNull(auditWorkflowConfigDTO) && !auditWorkflowConfigDTO.getAuditWorkflowConfigId().equals(auditWorkflowConfigId)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "流程[" + action.getFlowName() + "]节点已存在");
        }
        AuditWorkflowConfigEntity entity = BeanCopyUtils.copyByJSON(action, AuditWorkflowConfigEntity.class);
        this.auditWorkflowConfigRepository.editAuditWorkflowConfig(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delAuditWorkflowConfig(Long companyId, Long auditWorkflowConfigId) {
        CommAuditWorkflowConfigDO auditWorkflowConfigDO = this.commAuditWorkflowConfigMapper.selectById(companyId, auditWorkflowConfigId);
        if (Objects.isNull(auditWorkflowConfigDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "流程节点不存在");
        }
        this.commAuditWorkflowConfigMapper.deleteById(companyId, auditWorkflowConfigId);
        Long prevNodeId = auditWorkflowConfigDO.getPrevNodeId();
        Long nextNodeId = auditWorkflowConfigDO.getNextNodeId();
        if (Objects.nonNull(prevNodeId) && Objects.isNull(nextNodeId)) {
            this.commAuditWorkflowConfigMapper.updateNextNodeIsNull(companyId, prevNodeId);
        } else if (Objects.isNull(prevNodeId) && Objects.nonNull(nextNodeId)) {
            this.commAuditWorkflowConfigMapper.updatePrevNodeIsNull(companyId, nextNodeId);
        } else {
            AuditWorkflowConfigDTO prevNodeConfig = this.queryByAuditWorkflowConfigId(companyId, prevNodeId);
            if (Objects.nonNull(prevNodeConfig)) {
                CommAuditWorkflowConfigDO updateDo = new CommAuditWorkflowConfigDO();
                updateDo.setId(prevNodeId);
                updateDo.setNextNodeId(nextNodeId);
                this.commAuditWorkflowConfigMapper.updateByPrimaryKeySelective(updateDo);
            }
            AuditWorkflowConfigDTO nextNodeConfig = this.queryByAuditWorkflowConfigId(companyId, nextNodeId);
            if (Objects.nonNull(nextNodeConfig)) {
                CommAuditWorkflowConfigDO updateDo = new CommAuditWorkflowConfigDO();
                updateDo.setId(nextNodeId);
                updateDo.setSortIndex((byte) (nextNodeConfig.getSortIndex() - 1));
                updateDo.setPrevNodeId(prevNodeId);
                this.commAuditWorkflowConfigMapper.updateByPrimaryKeySelective(updateDo);
            }

        }
    }

    @Override
    public AuditWorkflowConfigDTO queryByAuditWorkflowConfigId(Long companyId, Long auditWorkflowConfigId) {
        CommAuditWorkflowConfigDO auditWorkflowConfigDO = this.commAuditWorkflowConfigMapper.selectById(companyId, auditWorkflowConfigId);
        if (Objects.isNull(auditWorkflowConfigDO)) {
            return null;
        }
        AuditWorkflowConfigDTO auditWorkflowConfigDTO = BeanCopyUtils.copyByJSON(auditWorkflowConfigDO, AuditWorkflowConfigDTO.class);
        auditWorkflowConfigDTO.setAuditWorkflowConfigId(auditWorkflowConfigDO.getId());
        return auditWorkflowConfigDTO;
    }

    @Override
    public AuditWorkflowConfigDTO queryByBizIdAndFlowName(Long companyId, Long bizId, String flowName) {
        CommAuditWorkflowConfigDO auditWorkflowConfigDO = this.commAuditWorkflowConfigMapper.selectByBizIdAndFlowName(companyId, bizId, flowName);
        if (Objects.isNull(auditWorkflowConfigDO)) {
            return null;
        }
        AuditWorkflowConfigDTO auditWorkflowConfigDTO = BeanCopyUtils.copyByJSON(auditWorkflowConfigDO, AuditWorkflowConfigDTO.class);
        auditWorkflowConfigDTO.setAuditWorkflowConfigId(auditWorkflowConfigDO.getId());
        return auditWorkflowConfigDTO;
    }

    @Override
    public Boolean duplicateFlowNameCheck(Long companyId, Long bizId, String flowName) {
        AuditWorkflowConfigDTO auditWorkflowConfigDTO = this.queryByBizIdAndFlowName(companyId, bizId, flowName);
        return Objects.isNull(auditWorkflowConfigDTO);
    }

    @Override
    public List<AuditWorkflowConfigDTO> queryByBizId(Long companyId, Long bizId) {
        List<CommAuditWorkflowConfigDO> auditWorkflowConfigDOList = this.commAuditWorkflowConfigMapper.listByBizId(companyId, bizId);
        if (CollectionUtils.isEmpty(auditWorkflowConfigDOList)) {
            return Lists.newArrayList();
        }
        return auditWorkflowConfigDOList.stream().map(auditWorkflowConfig -> {
            AuditWorkflowConfigDTO auditWorkflowConfigDTO = BeanCopyUtils.copyByJSON(auditWorkflowConfig, AuditWorkflowConfigDTO.class);
            auditWorkflowConfigDTO.setAuditWorkflowConfigId(auditWorkflowConfig.getId());
            return auditWorkflowConfigDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AuditWorkflowConfigDTO> queryByClassificationBizCode(Long companyId, AuditWorkflowBizTypeEnum auditEnum) {
        ClassificationDTO classificationDTO = this.classificationService.queryByTypeAndCode(companyId,
                ClassificationBizTypeEnum.DIC, auditEnum.getCode());
        if (Objects.isNull(classificationDTO)) {
            return Lists.newArrayList();
        }
        return this.queryByBizId(companyId, classificationDTO.getClassificationId());
    }

    @Override
    public GetWorkflowConfigByBizIdVO getWorkflowConfigByBizId(Long companyId, Long bizId){
        GetWorkflowConfigByBizIdVO vo = new GetWorkflowConfigByBizIdVO();
        CommWorkflowConfigDO commWorkflowConfigDO = commWorkflowConfigMapper.selectByCompanyIdAndBizId(companyId,bizId);
        if(Objects.isNull(commWorkflowConfigDO)){
            return vo;
        }
        vo.setConfigId(commWorkflowConfigDO.getId());
        vo.setBizId(bizId);
        vo.setAutoRemove(commWorkflowConfigDO.getAutoRemove());
        return vo;
    }

    @Override
    public void editWorkflowConfig(Long companyId, Long userId, Long configId, Byte autoRemove){
        CommWorkflowConfigDO commWorkflowConfigDO = commWorkflowConfigMapper.selectByCompanyIdAndId(companyId,configId);
        if(Objects.isNull(commWorkflowConfigDO)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "流程配置不存在！");
        }
        commWorkflowConfigDO.setAutoRemove(autoRemove);
        commWorkflowConfigDO.setModifier(userId);
        commWorkflowConfigDO.setGmtModified(new Date());
        commWorkflowConfigMapper.updateByCompanyIdAndId(commWorkflowConfigDO);
    }
}
