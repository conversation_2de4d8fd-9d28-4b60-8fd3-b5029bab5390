package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EditCollectionDrawAction implements Serializable {
    private static final long serialVersionUID = -958151786083355041L;
    private Long collectionDrawId;
    private Long companyId;
    private Long collectionId;
    private String title;
    private String drawNo;
    private Long draughtsmanId;
    private String draughtsmanName;
    private String proportion;
    private Date drawDate;
    private String documentId;
    private Long modifier;
}
