package com.das.museum.service.biz.collection.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OutInWarehouseInfoPageDTO implements Serializable {
    private static final long serialVersionUID = -2220395425149273083L;
    private Long outInWarehouseId;
    private Long companyId;
    private String outInWarehouseCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private Long reason;
    private String reasonInfo;
    private String useId;
    private String useIdName;
    private String userDepartmentId;
    private String userDepartmentName;
    private String useAddress;
    private String remark;
    private Date useDate;
    private Date preReturnDate;
    private String documentId;
    private String gmtCreate;
    private String checkStatus;
    private String checkStatusDesc;
    private String needManName;
    /**
     * 是否有审核权限 0 否 1 是
     */
    private Byte hasChecked;
    private Integer collectionCount;
    private CollectionStatisticalDTO collectionStatistical;
}
