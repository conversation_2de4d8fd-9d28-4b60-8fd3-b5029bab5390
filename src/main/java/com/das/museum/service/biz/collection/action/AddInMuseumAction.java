package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AddInMuseumAction implements Serializable {
    private static final long serialVersionUID = 3699546449751024502L;
    private String inMuseumCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private String batch;
    private String documentId;
    private Long companyId;
    private Long creator;
    private String creatorName;
    private List<AddInMuseumCollectionAction> inMuseumCollectionList;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}
