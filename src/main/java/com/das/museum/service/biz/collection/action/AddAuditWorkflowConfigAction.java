package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;

@Data
public class AddAuditWorkflowConfigAction implements Serializable {
    private static final long serialVersionUID = 4860312477708817987L;
    private Long companyId;
    private Long bizId;
    private String flowName;
    private String reviewer;
    private String reviewerName;
    private Byte sortIndex;
    private Long prevNodeId;
    private Long nextNodeId;
    private String remark;
    private Long creator;
}
