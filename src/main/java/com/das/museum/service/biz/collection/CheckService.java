package com.das.museum.service.biz.collection;

import com.das.museum.domain.enums.CheckMainStatusEnum;
import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.CheckCurrentInfoDTO;
import com.das.museum.service.biz.collection.dto.CheckFlowDTO;
import com.das.museum.service.biz.collection.dto.CheckRecordDTO;
import com.das.museum.web.controller.collection.check.response.BatchToCancelApplyDTO;
import com.das.museum.web.controller.collection.check.response.BatchToSubmitDTO;

import java.util.List;
import java.util.Map;

public interface CheckService {
    void submitCheck(SubmitCheckAction submitCheckAction);
    void check(CheckAction checkAction);
    void cancelApply(CancelApplyAction cancelApplyAction);
    void deleteFlow(DeleteFlowAction deleteFlowAction);
    List<CheckFlowDTO> queryFlowList(QueryCheckFlowAction queryCheckFlowAction);
    List<CheckRecordDTO> queryRecordList(QueryCheckRecordAction queryCheckRecordAction);
    CheckMainStatusEnum queryCheckStatus(QueryCheckStatusAction queryCheckStatusAction);
    void checkStatusByModelId(CheckStatusByModelIdAction queryBatchCheckStatusAction);
    Map<Long, CheckCurrentInfoDTO> queryBatchCheckStatus(QueryBatchCheckStatusAction queryBatchCheckStatusAction);
    List<Long> queryCheckedCollectionId(QueryCheckedCollectionIdAction queryCheckedCollectionIdAction);

    /**
     * 批量申请
     * @param submitCheckAction
     */
    BatchToSubmitDTO batchToSubmitCheck(BatchToSubmitCheckAction submitCheckAction);

    /**
     * 批量撤销申请
     * @param cancelApplyAction
     */
    BatchToCancelApplyDTO batchToCancelApply(BatchToCancelApplyAction cancelApplyAction);

    /**
     * 自动提交审批的方法
     * @param action
     */
//    void autoSubmitCheck(SubmitCheckAction action);
}
