package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;

@Data
public class AddCollectionUtensilAction implements Serializable {
    private static final long serialVersionUID = -644702912106018061L;
    private Long companyId;
    private Long collectionId;
    private String inscription;
    private String ornament;
    private String documentId;
    private Long creator;
}
