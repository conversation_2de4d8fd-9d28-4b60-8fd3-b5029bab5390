package com.das.museum.service.biz.collection;

import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.domain.collection.entity.CollectionRegisterEntity;
import com.das.museum.domain.collection.repository.CollectionRegisterRepository;
import com.das.museum.domain.collection.valueobject.CollectionRegisterAttrExVO;
import com.das.museum.domain.collection.valueobject.CollectionRegisterAttrInfoVO;
import com.das.museum.domain.collection.valueobject.CollectionRegisterBaseInfoVO;
import com.das.museum.domain.collection.valueobject.CollectionRegisterManageInfoVO;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.domain.enums.InCupboardStatusEnum;
import com.das.museum.infr.dataobject.ScsCollectionInfoDO;
import com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO;
import com.das.museum.infr.mapper.ScsCollectionInfoMapper;
import com.das.museum.infr.mapper.ScsCollectionRegisterInfoMapper;
import com.das.museum.service.biz.collection.CheckService;
import com.das.museum.service.biz.collection.action.AddCollectionRegisterAction;
import com.das.museum.service.biz.collection.action.SubmitCheckAction;
import com.das.museum.service.biz.collection.dto.AddCollectionRegisterDTO;
import com.das.museum.service.enums.InputModeEnum;
import com.das.museum.service.enums.InputTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 藏品登录公共方法类
 */

@Slf4j
@Component
public class CollectionLogin {

    @Resource
    private ScsCollectionInfoMapper scsCollectionInfoMapper;

    @Resource
    private CollectionRegisterRepository collectionRegisterRepository;

    @Resource
    private ScsCollectionRegisterInfoMapper scsCollectionRegisterInfoMapper;

    @Resource
    private CheckService checkService;

    @Transactional(rollbackFor = Exception.class)
    public AddCollectionRegisterDTO addCollectionRegister(AddCollectionRegisterAction action,String registerStatus){
        AddCollectionRegisterDTO addCollectionRegisterDTO = new AddCollectionRegisterDTO();
        // 新增数据到藏品表
        Long collectionId =  addCollectionInfo(action);
        // 保存藏品登录信息
        Long collectionRegisterId = addRegisterInfo(action,collectionId,registerStatus);
        // 自动提审
        autoSubmit(action);
        addCollectionRegisterDTO.setCollectionId(collectionId);
        addCollectionRegisterDTO.setCollectionRegisterId(collectionRegisterId);
        return addCollectionRegisterDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long addCollectionInfo(AddCollectionRegisterAction action){
        Long companyId = action.getCompanyId();
        Long userId = action.getCreator();
        ScsCollectionInfoDO collectionInfoDO = this.scsCollectionInfoMapper.selectByRegisterNo(companyId, action.getRegisterNo());
        if (Objects.nonNull(collectionInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品总登记号已存在");
        }

        ScsCollectionInfoDO insertCollectionInfoDO = this.buildCollectionInfoDO(action);
        insertCollectionInfoDO.setInputMode(InputModeEnum.REG.getCode());
        insertCollectionInfoDO.setInputType(InputTypeEnum.MLE.getCode());
        insertCollectionInfoDO.setCompanyId(companyId);
        insertCollectionInfoDO.setCreator(userId);
        insertCollectionInfoDO.setModifier(userId);
        this.scsCollectionInfoMapper.insertSelective(insertCollectionInfoDO);
        return insertCollectionInfoDO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Long addRegisterInfo(AddCollectionRegisterAction action,Long collectionId,String registerStatus){
        Long companyId = action.getCompanyId();
        Long userId = action.getCreator();
        CollectionRegisterEntity registerEntity = new CollectionRegisterEntity();
        CollectionRegisterBaseInfoVO registerBaseInfo = BeanCopyUtils.copyByJSON(action, CollectionRegisterBaseInfoVO.class);
        registerBaseInfo.setCollectionId(collectionId);
        registerBaseInfo.setRegisterStatus(registerStatus);
        registerBaseInfo.setInCupboardStatus(InCupboardStatusEnum.NCBD.getCode());
        registerBaseInfo.setCompanyId(companyId);
        registerBaseInfo.setCreator(userId);
        registerBaseInfo.setModifier(userId);
        registerEntity.setCollectionRegisterBaseInfo(registerBaseInfo);
        CollectionRegisterManageInfoVO registerManageInfo = BeanCopyUtils.copyByJSON(action, CollectionRegisterManageInfoVO.class);
        registerManageInfo.setCompanyId(companyId);
        registerManageInfo.setCreator(userId);
        registerManageInfo.setModifier(userId);
        registerEntity.setCollectionRegisterManageInfo(registerManageInfo);
        CollectionRegisterAttrInfoVO registerAttrInfoVO = BeanCopyUtils.copyByJSON(action, CollectionRegisterAttrInfoVO.class);
        registerAttrInfoVO.setCompanyId(companyId);
        registerAttrInfoVO.setCreator(userId);
        registerAttrInfoVO.setModifier(userId);
        if (Objects.nonNull(action.getAmount())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getAmount());
            registerAttrInfoVO.setAmount((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue() * 100));
        }
        if (Objects.nonNull(action.getLength())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getLength());
            registerAttrInfoVO.setLength((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue() * 100));
        }
        if (Objects.nonNull(action.getWidth())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getWidth());
            registerAttrInfoVO.setWidth((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue() * 100));
        }
        if (Objects.nonNull(action.getHeight())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getHeight());
            registerAttrInfoVO.setHeight((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue() * 100));
        }
        if (Objects.nonNull(action.getPresence())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getPresence());
            registerAttrInfoVO.setPresence((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue() * 100));
        }
        if (Objects.nonNull(action.getCaliber())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getCaliber());
            registerAttrInfoVO.setCaliber((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue() * 100));
        }

        if (Objects.nonNull(action.getBottomDiameter())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getBottomDiameter());
            registerAttrInfoVO.setBottomDiameter((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue() * 100));
        }
        registerEntity.setCollectionRegisterAttrInfo(registerAttrInfoVO);
        CollectionRegisterAttrExVO registerAttrExVO = BeanCopyUtils.copyByJSON(action, CollectionRegisterAttrExVO.class);
        registerAttrExVO.setCompanyId(companyId);
        registerAttrExVO.setCreator(userId);
        registerAttrExVO.setModifier(userId);
        registerAttrExVO.setCollectsDate(action.getCollectsDate());
        registerEntity.setCollectionRegisterAttrExVO(registerAttrExVO);
        registerEntity.setAutoAudit(action.getAutoAudit());
        this.collectionRegisterRepository.saveCollectionRegister(registerEntity);
        return registerEntity.getCollectionRegisterId();
    }


    @Transactional(rollbackFor = Exception.class)
    public void autoSubmit(AddCollectionRegisterAction action){
        Long companyId = action.getCompanyId();
        Long userId = action.getCreator();
        // 自动提审（excel表格导入的没有autoAudit字段，不做处理）
        if(Objects.nonNull(action.getAutoAudit()) && action.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(userId);
            submitCheckAction.setCompanyId(companyId);
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.LOGIN);
            ScsCollectionRegisterInfoDO registerInfoDO = this.scsCollectionRegisterInfoMapper.selectByRegisterNo(companyId, action.getRegisterNo());
            Long registerInfoId = registerInfoDO.getId();
            submitCheckAction.setModelId(registerInfoId);
            submitCheckAction.setCreatorName(action.getCreatorName());
            this.checkService.submitCheck(submitCheckAction);
        }
    }

    private ScsCollectionInfoDO buildCollectionInfoDO(AddCollectionRegisterAction action) {
        ScsCollectionInfoDO collectionInfoDO = new ScsCollectionInfoDO();
        collectionInfoDO.setRegisterNo(action.getRegisterNo());
        collectionInfoDO.setClassify(action.getClassify());
        collectionInfoDO.setName(action.getName());
        collectionInfoDO.setInitialLevel(action.getIdentifyLevel());
        collectionInfoDO.setInTibetanDate(action.getInTibetanDate());
        collectionInfoDO.setAge(action.getAge());
        collectionInfoDO.setEra(action.getEra());
        collectionInfoDO.setAppendageDesc(action.getAttachmentDesc());
        collectionInfoDO.setCollectDate(action.getCollectDate());
        collectionInfoDO.setInMuseumDate(action.getInMuseumDate());
        collectionInfoDO.setInTibetanDate(action.getInTibetanDate());
        collectionInfoDO.setWarehouseId(action.getWarehouseId());
        collectionInfoDO.setCollectionCount(action.getCollectionCount());
        collectionInfoDO.setCountUnit(action.getCountUnit());
        collectionInfoDO.setActualCount(action.getActualCount());
        collectionInfoDO.setCompleteDegree(action.getCompleteDegree());
        collectionInfoDO.setCompleteInfo(action.getCompleteInfo());
        collectionInfoDO.setTexture(action.getTexture());
        collectionInfoDO.setTextureCategory(action.getTextureCategory());
        collectionInfoDO.setCollectType(action.getSource());
        collectionInfoDO.setHolder(action.getHolder());
        collectionInfoDO.setPayVoucherNo(action.getPayVoucherNo());
        if (Objects.nonNull(action.getAmount())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getAmount());
            collectionInfoDO.setAmount((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue() * 100));
        }
        collectionInfoDO.setRemark(action.getCollectionRemark());
        return collectionInfoDO;
    }

}
