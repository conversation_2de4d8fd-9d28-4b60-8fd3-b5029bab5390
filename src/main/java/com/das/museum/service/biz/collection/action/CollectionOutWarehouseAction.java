package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionOutWarehouseAction implements Serializable {
    private static final long serialVersionUID = 1827335798546336934L;
    private Long companyId;
    private Long outInWarehouseId;
    private List<OutWarehouseCollectionAction> outWarehouseCollection;
    private Date outWarehouseDate;
    private Long creator;
}
