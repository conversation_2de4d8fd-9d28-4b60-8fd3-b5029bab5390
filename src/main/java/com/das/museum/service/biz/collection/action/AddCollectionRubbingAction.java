package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;

@Data
public class AddCollectionRubbingAction implements Serializable {
    private static final long serialVersionUID = 5084114802491041648L;
    private Long companyId;
    private Long collectionId;
    private String bookStyle;
    private String mountSituation;
    private String rubbingNo;
    private String recognition;
    private String seal;
    private String postscript;
    private String interpretation;
    private String documentId;
    private Long creator;
}
