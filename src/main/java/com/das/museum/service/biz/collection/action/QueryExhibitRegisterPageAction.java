package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryExhibitRegisterPageAction extends PageInfoBaseReq {

    private static final long serialVersionUID = -8623339597121872400L;

    private Long companyId;

    private String keyword;

    private Long age;

    private Long identifyLevel;

    private Long texture;

    private Long source;

    private Long completeDegree;

    //private List<Integer> digitalType;

    /**
     * 数字化类型 1 仅二维 2、仅三维 3、二维和三维
     */
    private Integer digitalType;

    /**
     * dataFlag 0 显示全部 1 过滤已展示
     */
    private Integer dataFlag;
}
