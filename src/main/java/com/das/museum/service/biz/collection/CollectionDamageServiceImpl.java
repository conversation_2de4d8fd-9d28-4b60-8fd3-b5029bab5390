package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionDamageAction;
import com.das.museum.service.biz.collection.action.DamageQueryPageByConditionAction;
import com.das.museum.service.biz.collection.action.EditCollectionDamageAction;
import com.das.museum.service.biz.collection.dto.CollectionDamageDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionDamagePageDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectionDamageEntity;
import com.das.museum.domain.collection.repository.CollectionDamageRepository;
import com.das.museum.infr.dataobject.ScsCollectionDamageDO;
import com.das.museum.infr.mapper.ScsCollectionDamageMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CollectionDamageServiceImpl implements CollectionDamageService {

    @Resource
    private CollectionDamageRepository collectionDamageRepository;

    @Resource
    private ScsCollectionDamageMapper scsCollectionDamageMapper;

    @Resource
    private DocumentService documentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCollectionDamage(AddCollectionDamageAction action) {
        CollectionDamageEntity entity = this.convertCollectionDamageEntity(action);
        this.collectionDamageRepository.saveCollectionDamage(entity);
        return entity.getCollectionDamageId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionDamage(EditCollectionDamageAction action) {
        Long companyId = action.getCompanyId();
        Long collectionDamageId = action.getCollectionDamageId();
        CollectionDamageDetailDTO collectionDamageDTO = this.queryByCollectionDamageId(companyId, collectionDamageId);
        if (Objects.isNull(collectionDamageDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "损坏记录不存在");
        }
        CollectionDamageEntity entity = this.convertCollectionDamageEntity(action);
        this.collectionDamageRepository.editCollectionDamage(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByCollectionDamageId(Long companyId, Long collectionDamageId) {
        CollectionDamageDetailDTO collectionDamageDTO = this.queryByCollectionDamageId(companyId, collectionDamageId);
        if (Objects.isNull(collectionDamageDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "损坏记录不存在");
        }
        this.collectionDamageRepository.delByCollectionDamageId(companyId, collectionDamageId);
        if (StringUtils.isBlank(collectionDamageDTO.getDocumentId())) {
            return;
        }
        this.documentService.delByDocIds(companyId, collectionDamageDTO.getDocumentId());
    }

    @Override
    public SimplePageInfo<CollectionDamagePageDTO> queryPageByCondition(DamageQueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionDamageDO> collectionDamageDOList = this.scsCollectionDamageMapper.listPageByCondition(companyId,
                action.getCollectionId(), action.getSortBy());
        if (CollectionUtils.isEmpty(collectionDamageDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<CollectionDamagePageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectionDamageDOList, CollectionDamagePageDTO.class);
        List<CollectionDamagePageDTO> collectionDamagePageDTOList = collectionDamageDOList.stream().map(collectionDamageDO -> {
            CollectionDamagePageDTO collectionDamagePageDTO = BeanCopyUtils.copyByJSON(collectionDamageDO, CollectionDamagePageDTO.class);
            collectionDamagePageDTO.setCollectionDamageId(collectionDamageDO.getId());
            return collectionDamagePageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectionDamagePageDTOList);
        return pageInfo;
    }

    @Override
    public CollectionDamageDetailDTO queryByCollectionDamageId(Long companyId, Long collectionDamageId) {
        ScsCollectionDamageDO collectionDamageDO = this.scsCollectionDamageMapper.selectById(companyId, collectionDamageId);
        if (Objects.isNull(collectionDamageDO)) {
            return null;
        }
        CollectionDamageDetailDTO collectionDamageDTO = BeanCopyUtils.copyByJSON(collectionDamageDO, CollectionDamageDetailDTO.class);
        collectionDamageDTO.setCollectionDamageId(collectionDamageDO.getId());
        String documentId = collectionDamageDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return collectionDamageDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        collectionDamageDTO.setCommDocumentList(commDocumentList);
        return collectionDamageDTO;
    }
    private CollectionDamageEntity convertCollectionDamageEntity(AddCollectionDamageAction action) {
        CollectionDamageEntity entity = new CollectionDamageEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setCollectionId(action.getCollectionId());
        entity.setAddress(action.getAddress());
        entity.setResponsibleId(action.getResponsibleId());
        entity.setResponsibleName(action.getResponsibleName());
        entity.setDamageInfo(action.getDamageInfo());
        entity.setReason(action.getReason());
        entity.setHandleInfo(action.getHandleInfo());
        entity.setProcessInfo(action.getProcessInfo());
        entity.setRemark(action.getRemark());
        entity.setDamageDate(action.getDamageDate());
        entity.setDocumentId(action.getDocumentId());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        return entity;
    }

    private CollectionDamageEntity convertCollectionDamageEntity(EditCollectionDamageAction action) {
        CollectionDamageEntity entity = new CollectionDamageEntity();
        entity.setCollectionDamageId(action.getCollectionDamageId());
        entity.setCompanyId(action.getCompanyId());
        entity.setAddress(action.getAddress());
        entity.setResponsibleId(action.getResponsibleId());
        entity.setResponsibleName(action.getResponsibleName());
        entity.setDamageInfo(action.getDamageInfo());
        entity.setReason(action.getReason());
        entity.setHandleInfo(action.getHandleInfo());
        entity.setProcessInfo(action.getProcessInfo());
        entity.setRemark(action.getRemark());
        entity.setDamageDate(action.getDamageDate());
        entity.setDocumentId(action.getDocumentId());
        entity.setModifier(action.getModifier());
        return entity;
    }
}
