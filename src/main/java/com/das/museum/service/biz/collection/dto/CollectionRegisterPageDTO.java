package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionRegisterPageDTO implements Serializable {
    private static final long serialVersionUID = 3359650488212437569L;
    private Long index;
    private Long registerInfoId;
    private Long companyId;
    private Long collectionId;
    private String registerName;
    private String registerNo;
    private String name;
    private String originalNo;
    private String originalName;
    private Long classify;
    private String classifyName;
    private String classifyDesc;
    private Long identifyLevel;
    private String identifyLevelName;
    private Long category;
    private String categoryName;
    private String era;
    private Long age;
    private String ageName;
    private String registerStatus;
    private String documentId;
    private Long completeDegree;
    private String completeDegreeName;
    private Long warehouseId;
    private String warehouseName;
    private Long cupboardId;
    private String cupboardName;
    private Long floorId;
    private String floorName;
    private Long drawerId;
    private String drawerName;
    private Long columnId;
    private String columnName;
    private Long numberId;
    private String numberName;
    private Integer actualCount;
    private Long countUnit;
    private String countUnitName;
    private Integer collectionCount;
    private String texture;
    private String textureName;
    private Long source;
    private String sourceName;
    private String specificSource;
    private String size;
    private Long creator;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModified;
    private String creatorName;
    private String checkStatus;
    private String checkStatusDesc;
    private String needManName;
    /**
     * 是否有审核权限 0 否 1 是
     */
    private Byte hasChecked;
    private Double length;
    private Double width;
    private Double height;
    private Long qualityRange;
    private String qualityRangeName;
    private Long qualityUnit;
    private String qualityUnitName;
    private Double specificQuality;
    private List<CommonDocumentDTO> commonDocumentList;

    /**
     * 入馆凭证号
     */
    private String inMuseumCertificateNo;
    /**
     * 注销凭证号
     */
    private String logoffCerNo;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 二维影像个数
     */
    private Integer twoModelCount;

    /**
     * 三维模型个数
     */
    private Integer threeModelCount;
}
