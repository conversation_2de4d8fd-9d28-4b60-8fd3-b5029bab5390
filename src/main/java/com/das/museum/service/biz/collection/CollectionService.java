package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.dto.BizCollectionRelDTO;
import com.das.museum.service.biz.collection.dto.CollectCheckedCollectionInfoDTO;
import com.das.museum.service.biz.collection.dto.CollectionInfoDetailDTO;
import com.das.museum.service.biz.collection.dto.IdentifyCollectionInfoPageDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.domain.enums.CollectionBizTypeEnum;

import java.util.List;
import java.util.Map;

public interface CollectionService {
    SimplePageInfo<CollectCheckedCollectionInfoDTO> queryPageByBizIdAndBizType(Long companyId, Long bizId, CollectionBizTypeEnum bizType, String collectionName, String era, String payVoucherNo,
                                                                               Integer pageNum, Integer pageSize);
    List<CollectionInfoDetailDTO> queryByBizIdAndBizType(Long companyId, Long bizId, CollectionBizTypeEnum bizType);
    SimplePageInfo<IdentifyCollectionInfoPageDTO> queryCollectionInfoByIdentifyId(Long companyId, Long identifyId,
                                                                                  Integer pageNum, Integer pageSize);
    List<IdentifyCollectionInfoPageDTO> queryCollectionInfoByIdentifyIdNoPage(Long companyId, Long identifyId);
    CollectionInfoDetailDTO queryCollectionInfoById(Long companyId, Long collectionId);
    List<BizCollectionRelDTO> listByBizIdsAndBizType(List<Long> bizIds, CollectionBizTypeEnum bizType);
    List<BizCollectionRelDTO> listByBizIdsAndBizTypeExclude(List<Long> bizIds, CollectionBizTypeEnum bizType, List<Long> collectionIds);
    List<BizCollectionRelDTO> listByBizIdAndBizType(Long companyId, Long bizId, CollectionBizTypeEnum bizType);
    List<BizCollectionRelDTO> listByBizType(Long companyId, CollectionBizTypeEnum bizType);
    Map<Long, List<BizCollectionRelDTO>> queryBizCollectionRelMap(Long companyId, List<Long> bizIds, CollectionBizTypeEnum bizType);
    void assignWarehouseByCollectionIds(Long companyId, Long bizId, String bizType, List<Long> collectionIds, Long warehouseId, Long modifier);
}
