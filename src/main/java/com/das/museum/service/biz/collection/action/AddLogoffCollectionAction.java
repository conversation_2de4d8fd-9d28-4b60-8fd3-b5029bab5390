package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;

@Data
public class AddLogoffCollectionAction implements Serializable {
    private static final long serialVersionUID = 3970334548376135757L;
    private Long companyId;
    private Long logoffId;
    private String logoffCertificateNo;
    private Long collectionId;
    private String registerNo;
    private Long classify;
    private String classifyName;
    private String collectionName;
    private String era;
    private Long age;
    private String ageName;
    private Integer collectionCount;
    private Long countUnit;
    private String countUnitName;
    private Long identifyLevel;
    private String identifyLevelName;
    private String situation;
    private String reason;
    private String whereabouts;
    private String remark;
    private Long creator;
    private Long modifier;
}
