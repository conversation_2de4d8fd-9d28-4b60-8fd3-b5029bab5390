package com.das.museum.service.biz.collection.action;

import lombok.Data;

@Data
public class EditThreeModelInfoAction {

    /**
     * 自增ID
     */
    private Long threeModelId;

    /**
     * 模型名称
     */
    private String threeModelName;

    /**
     * 应用等级，1展示级，2存储级，3复制级
     */
    private Byte applicationLevel;

    /**
     * 批次名称
     */
    private String fileBatch;

    /**
     * 制作单位
     */
    private String makeUnit;

    /**
     * 制作日期
     */
    private String makeTime;

    /**
     * 采集设备
     */
    private String collectDevice;

    /**
     * 顶点数
     */
    private Integer topTip;

    /**
     * 三角面数
     */
    private Integer trianglePlane;

    /**
     * 模型坐标系
     */
    private String coordinateSystem;

    /**
     * 纹理格式
     */
    private String textureFormat;

    /**
     * 色彩模式
     */
    private String colorMode;

    /**
     * 色彩位数
     */
    private String colorBits;

    /**
     * 贴图分辨率
     */
    private String resolutionRatio;

    /**
     * 几何精度
     */
    private String geometricAccuracy;

    /**
     * 说明备注
     */
    private String remark;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 更新人
     */
    private Long modifier;

}
