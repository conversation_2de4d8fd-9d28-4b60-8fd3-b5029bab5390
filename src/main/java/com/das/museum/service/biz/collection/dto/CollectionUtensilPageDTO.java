package com.das.museum.service.biz.collection.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectionUtensilPageDTO implements Serializable {
    private static final long serialVersionUID = -2443665590615446935L;
    private Long collectionUtensilId;
    private Long companyId;
    private Long collectionId;
    private String inscription;
    private String ornament;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}