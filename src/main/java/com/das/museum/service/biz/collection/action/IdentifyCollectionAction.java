package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.util.Date;

@Data
public class IdentifyCollectionAction {
    private Long companyId;
    private Long identifyCertificateId;
    private Long collectionId;
    private Long appraiserId;
    private String appraiserName;
    private String identifyAgency;
    private String identifyOpinion;
    private String identifyRemark;
    private Date identifyDate;
    private String documentId;
    private Long creator;
    private Long modifier;

}
