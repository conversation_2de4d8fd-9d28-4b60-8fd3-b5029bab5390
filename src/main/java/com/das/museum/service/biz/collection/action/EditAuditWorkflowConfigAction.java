package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;

@Data
public class EditAuditWorkflowConfigAction implements Serializable {
    private static final long serialVersionUID = -3068545892892999507L;
    private Long auditWorkflowConfigId;
    private Long companyId;
    private Long bizId;
    private String flowName;
    private String reviewer;
    private String reviewerName;
    private Byte sortIndex;
    private Long prevNodeId;
    private Long nextNodeId;
    private String remark;
    private Long modifier;
}
