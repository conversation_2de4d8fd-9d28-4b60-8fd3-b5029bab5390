package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.*;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.domain.enums.RegisterStatusEnum;

import java.util.List;

public interface CollectionRegisterService {
    void generateCollectionRegister(Long companyId, Long userId, Long collectionId);
    AddCollectionRegisterDTO addCollectionRegister(AddCollectionRegisterAction action);
    void editCollectionRegister(EditCollectionRegisterAction action);
    void delCollectionRegisterByRegisterNo(Long companyId, String registerNo);
    SimplePageInfo<CollectionRegisterPageDTO> queryPageByCondition(RegisterQueryPageByConditionAction action);
    CollectionRegisterDetailDTO queryByRegisterNo(Long companyId, String registerNo);
    CollectionRegisterDetailDTO queryById(Long companyId,Long registerInfoId);
    SimplePageInfo<CollectionRegisterDetailSnapshotDTO> queryByRegisterInfoId(Long companyId, Long registerInfoId, Integer pageNum, Integer pageSize);
    CollectionRegisterDetailDTO queryByRegisterInfoSnapshotId(Long companyId, Long registerInfoSnapshotId);
    void delByRegisterInfoSnapshotId(Long companyId, Long registerInfoSnapshotId);
    void inCupboardByRegisterInfoId(InCupboardByRegisterNoAction action);
    void updateRegisterStatusById(Long companyId, List<Long> ids, RegisterStatusEnum registerStatus);
    void updateRegisterStatusByCollectionId(Long companyId, List<Long> collectionIds, RegisterStatusEnum registerStatus);
    void updateRegisterStatuesByCollectionIds(Long companyId, RegisterStatusEnum registerStatusEnum, List<Long> collectionIds,Long modifier);

    /**
     * 导出藏品总账的方法
     * @param companyId 公司id
     * @return
     */
    List<CollectionLedgerDTO> queryLedgerByRegisterNo(Long companyId,List<String> registerNos,String registerNo,
                                                      String classifyDesc,String name,Long age,Long identifyLevel,String texture,
                                                      Long source,Long category,Long completeDegree);

    void batchCheck(Integer isAllFlag, List<Long> checkIds);

    void updateRegisterStatus(Long collectionRegisterId, String registerStatus);

    SimplePageInfo<ExhibitRegisterPageDTO> queryExhibitRegisterPage(QueryExhibitRegisterPageAction action);
}
