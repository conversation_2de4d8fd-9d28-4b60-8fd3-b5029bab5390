package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AddCollectionDamageAction implements Serializable {
    private static final long serialVersionUID = -6683538770260423542L;
    private Long companyId;
    private Long collectionId;
    private String address;
    private Long responsibleId;
    private String responsibleName;
    private String damageInfo;
    private String reason;
    private String handleInfo;
    private String processInfo;
    private String remark;
    private Date damageDate;
    private String documentId;
    private Long creator;
}
