package com.das.museum.service.biz.collection;

import com.das.museum.common.utils.PaginationUtils;
import com.das.museum.domain.enums.CheckMainStatusEnum;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.mapper.*;
import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.*;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.constants.CollectionConstant;
import com.das.museum.service.biz.user.UserService;
import com.das.museum.service.enums.InMuseumStatusEnum;
import com.das.museum.service.enums.InputModeEnum;
import com.das.museum.service.enums.InputTypeEnum;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.InMuseumCertificateEntity;
import com.das.museum.domain.collection.repository.CollectionRepository;
import com.das.museum.domain.collection.repository.InMuseumCertificateRepository;
import com.das.museum.domain.collection.valueobject.CollectionVO;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.infr.query.InMuseumConditionQuery;
import com.das.museum.service.enums.YesOrNoEnum;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class InMuseumServiceImpl implements InMuseumService {

    @Resource
    private InMuseumCertificateRepository inMuseumCertificateRepository;

    @Resource
    private CollectionRepository collectionRepository;

    @Resource
    private ScsInMuseumCertificateInfoMapper scsInMuseumCertificateInfoMapper;

    @Resource
    private DocumentService documentService;

    @Resource
    private UserService userService;

    @Resource
    private CollectionService collectionService;

    @Resource
    private CheckService checkService;

    @Resource
    private CommCheckMainMapper commCheckMainMapper;

    @Resource
    private CommCheckChildMapper commCheckChildMapper;

    @Resource
    private ScsBizCollectionRelMapper scsBizCollectionRelMapper;

    @Resource
    private CommIncrSegmentMapper commIncrSegmentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addInMuseumCertificateInfo(AddInMuseumAction addInMuseumAction) {
        Long companyId = addInMuseumAction.getCompanyId();
        String bizDimension = DateUtils.formatDateYYYMMDD().substring(0, 4);
        CommIncrSegmentDO commIncrSegmentDO = this.commIncrSegmentMapper
                .selectByCompanyIdAndBizCode(companyId, CollectionBizTypeEnum.MSM.getCode(), bizDimension);
        if (Objects.isNull(commIncrSegmentDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "入馆凭证自动编号配置不存在");
        }
        Long bizCurrIndex = commIncrSegmentDO.getBizCurrIndex();
        String inMuseumCertificateNo = addInMuseumAction.getPrefixName()
                + CollectionConstant.IN_MUSEUM_CERTIFICATE_MIDDLE + (bizCurrIndex + 1)
                + CollectionConstant.CERTIFICATE_TAIL;
        InMuseumCertificateEntity entity = this.convertInMuseumCertificateEntity(addInMuseumAction);
        entity.setSuffixNumber(bizCurrIndex + 1);
        entity.setInMuseumCertificateNo(inMuseumCertificateNo);
        entity.setAutoAudit(addInMuseumAction.getAutoAudit());
        this.inMuseumCertificateRepository.saveInMuseumCertificate(entity);
        Long inMuseumId = entity.getInMuseumId();
        this.collectionRepository.saveByBizIdAndBizType(companyId, inMuseumId,
                CollectionBizTypeEnum.MSM.getCode(),
                entity.getInMuseumCollectionList());

        /*InMuseumCertificateEntity updateEntity = new InMuseumCertificateEntity();
        updateEntity.setInMuseumId(inMuseumId);
        updateEntity.setSuffixNumber(inMuseumId);
        updateEntity.setInMuseumCertificateNo(entity.getPrefixName()
                + CollectionConstant.IN_MUSEUM_CERTIFICATE_MIDDLE + updateEntity.getSuffixNumber()
                + CollectionConstant.CERTIFICATE_TAIL);
        updateEntity.setAutoAudit(addInMuseumAction.getAutoAudit());
        this.inMuseumCertificateRepository.editInMuseumCertificate(updateEntity);*/

        int row = this.commIncrSegmentMapper.updateByBizCurrIndex(commIncrSegmentDO.getId(), bizCurrIndex);
        if (row <= 0) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "入馆凭证自动获取编号失败");
        }
        // 自动提审
        if(addInMuseumAction.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(addInMuseumAction.getCreator());
            submitCheckAction.setCompanyId(addInMuseumAction.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.MSM);
            submitCheckAction.setModelId(inMuseumId);
            submitCheckAction.setCreatorName(addInMuseumAction.getCreatorName());
            this.checkService.submitCheck(submitCheckAction);
        }

        return inMuseumId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editInMuseumCertificateInfo(Long companyId, Long inMuseumId, EditInMuseumAction editInMuseumAction) {
        ScsInMuseumCertificateInfoDO certificateInfoDO = this.inMuseumCertificateInfoCheck(companyId, inMuseumId);
        InMuseumCertificateEntity entity = this.convertInMuseumCertificateEntity(editInMuseumAction);
        entity.setSuffixNumber(certificateInfoDO.getSuffixNumber());
        entity.setInMuseumCertificateNo(editInMuseumAction.getPrefixName()
                + CollectionConstant.IN_MUSEUM_CERTIFICATE_MIDDLE + certificateInfoDO.getSuffixNumber()
                + CollectionConstant.CERTIFICATE_TAIL);
        List<Long> excludeCollectionIds = null;
        if (CollectionUtils.isNotEmpty(entity.getInMuseumCollectionList())) {
            excludeCollectionIds = entity.getInMuseumCollectionList().stream()
                    .map(CollectionVO::getCollectionId).distinct().collect(Collectors.toList());
        }
        this.collectionRepository.delByBizIdAndBizTypeExcludeCLT(companyId, inMuseumId, CollectionBizTypeEnum.MSM.getCode(),
                excludeCollectionIds, InputModeEnum.ALY.getCode(), InputTypeEnum.MLE.getCode());
        this.inMuseumCertificateRepository.editInMuseumCertificate(entity);
        this.collectionRepository.saveByBizIdAndBizType(companyId, inMuseumId,
                CollectionBizTypeEnum.MSM.getCode(), entity.getInMuseumCollectionList());

        // 自动提审
        if(editInMuseumAction.getAutoAudit() == (byte)1) {
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(editInMuseumAction.getModifier());
            submitCheckAction.setCompanyId(editInMuseumAction.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.MSM);
            submitCheckAction.setModelId(inMuseumId);
            submitCheckAction.setCreatorName(editInMuseumAction.getModifierName());
            this.checkService.submitCheck(submitCheckAction);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBynMuseumCertificateById(Long companyId, Long inMuseumId) {
        ScsInMuseumCertificateInfoDO certificateInfoDO = this.inMuseumCertificateInfoCheck(companyId, inMuseumId);
        this.inMuseumCertificateRepository.delInMuseumCertificateById(companyId, inMuseumId);
        collectionRepository.delByBizIdAndBizTypeExcludeCLT(companyId, inMuseumId, CollectionBizTypeEnum.MSM.getCode(),
                Lists.newArrayList(), InputModeEnum.ALY.getCode(), InputTypeEnum.MLE.getCode());
        this.documentService.delByDocIds(companyId, certificateInfoDO.getDocumentId());
    }

    @Override
    public InMuseumInfoDetailDTO queryDetailInfoById(Long companyId, Long inMuseumId) {
        ScsInMuseumCertificateInfoDO inMuseumDO = this.scsInMuseumCertificateInfoMapper.selectById(companyId, inMuseumId);
        if (Objects.isNull(inMuseumDO)) {
            return null;
        }
        InMuseumInfoDetailDTO inMuseumDTO = BeanCopyUtils.copyByJSON(inMuseumDO, InMuseumInfoDetailDTO.class);
        inMuseumDTO.setInMuseumId(inMuseumDO.getId());
        String documentId = inMuseumDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return inMuseumDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        inMuseumDTO.setCommDocumentList(commDocumentList);
        return inMuseumDTO;
    }

    @Override
    public SimplePageInfo<InMuseumInfoPageDTO> queryPageByConditionQuery(InMuseumConditionQueryAction action) {
        Long companyId = action.getCompanyId();
        // action.startPage();
        InMuseumConditionQuery query = new InMuseumConditionQuery();
        query.setCollectionName(action.getCollectionName());
        query.setCollectType(action.getCollectType());
        List<ScsInMuseumCertificateInfoDO> inMuseumList = this.scsInMuseumCertificateInfoMapper
                .listPageByCondition(companyId, action.getInMuseumCertificateNo(), action.getSortBy(), query);
        if (CollectionUtils.isEmpty(inMuseumList)) {
            return new SimplePageInfo<>();
        }
       /* SimplePageInfo<InMuseumInfoPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(inMuseumList, InMuseumInfoPageDTO.class);*/
        List<InMuseumInfoPageDTO> inMuseumInfoPageDTOList = this.setInMuseumAttributes(companyId, inMuseumList, action.getCheckStatus(), action.getIsUserChecked());
        // pageInfo.setList(inMuseumInfoPageDTOList);
        return PaginationUtils.pagination(inMuseumInfoPageDTOList, action.getPageNum(), action.getPageSize());
    }

    @Override
    public List<CommonDocumentDTO> queryAttachmentByInMuseumId(Long companyId, Long inMuseumId) {
        ScsInMuseumCertificateInfoDO inMuseumDO = this.scsInMuseumCertificateInfoMapper.selectById(companyId, inMuseumId);
        if (Objects.isNull(inMuseumDO)) {
            return Lists.newArrayList();
        }
        String documentId = inMuseumDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return Lists.newArrayList();
        }
        return this.documentService.listByDocIds(companyId, documentId);
    }

    @Override
    public SimplePageInfo<InMuseumInfoPageDTO> queryPageInTibetanList(PageInTibetanAction action) {
        Long companyId = action.getCompanyId();
        List<BizCollectionRelDTO> idyRelDTOList = this.collectionService.listByBizType(companyId, CollectionBizTypeEnum.TBT);
        List<Long> collectionIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(idyRelDTOList)) {
            collectionIds = idyRelDTOList.stream().map(BizCollectionRelDTO::getCollectionId).distinct().collect(Collectors.toList());
        }
        List<String> atpCollectionList = this.commCheckMainMapper.listByBelongModel(companyId, CollectionBizTypeEnum.MSM.getCode());
        if (CollectionUtils.isNotEmpty(atpCollectionList)) {
            List<Long> aptCollectionIds = Lists.newArrayList();
            atpCollectionList.forEach(k -> {
                if (StringUtils.isBlank(k)) {
                    return;
                }
                List<Long> atpCollectionIds = Stream.of(k.split(",")).map(id -> {
                    if (StringUtils.isBlank(id)) {
                        return null;
                    }
                    return Long.parseLong(id);
                }).distinct().filter(Objects::nonNull).collect(Collectors.toList());
                aptCollectionIds.addAll(atpCollectionIds);
            });
            if (CollectionUtils.isNotEmpty(aptCollectionIds)) {
                List<ScsBizCollectionRelDO> nAtpCollectionList = this.scsBizCollectionRelMapper.listByBizTypeExclude(CollectionBizTypeEnum.MSM.getCode(), aptCollectionIds);
                if (CollectionUtils.isNotEmpty(nAtpCollectionList)) {
                    List<Long> nAtpCollectionIds = nAtpCollectionList.stream().map(ScsBizCollectionRelDO::getCollectionId).distinct().collect(Collectors.toList());
                    collectionIds.addAll(nAtpCollectionIds);
                }
            }
        }
        if (CollectionUtils.isEmpty(collectionIds)) {
            collectionIds = null;
        }
        action.startPage();
        List<ScsInMuseumCertificateInfoDO> inMuseumList = this.scsInMuseumCertificateInfoMapper
                .listPageByInTibetan(companyId, action.getInMuseumCertificateNo(), collectionIds);
        if (CollectionUtils.isEmpty(inMuseumList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<InMuseumInfoPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(inMuseumList, InMuseumInfoPageDTO.class);
        List<InMuseumInfoPageDTO> inMuseumInfoPageDTOList = this.setInMuseumAttributes(companyId, inMuseumList, null, 0);
        pageInfo.setList(inMuseumInfoPageDTOList);
        return pageInfo;
    }

    @Override
    public List<CollectCheckedCollectionInfoDTO> queryCheckedCollectionInfoList(Long companyId, Long inMuseumId) {
        Map<Long,Object> collectionMap = new HashMap<>();
        List<CollectionInfoDetailDTO> collectionList = this.collectionService.queryByBizIdAndBizType(companyId,inMuseumId,CollectionBizTypeEnum.MSM);
        List<Long> collectionIds = collectionList.stream().map(CollectionInfoDetailDTO::getCollectionId).collect(Collectors.toList());
        QueryCheckedCollectionIdAction action  = new QueryCheckedCollectionIdAction();
        action.setCompanyId(companyId);
        action.setBelongModel(CollectionBizTypeEnum.MSM);
        action.setModelId(inMuseumId);
        action.setCollectionIds(collectionIds);
        List<Long> checkCollectionIdList =  checkService.queryCheckedCollectionId(action);
        for(Long id:checkCollectionIdList){
            collectionMap.put(id,"");
        }

        return collectionList.stream().map(collectionInfoDetailDTO -> {
            CollectCheckedCollectionInfoDTO collectCheckedCollectionInfoDTO =  BeanCopyUtils.copyByJSON(collectionInfoDetailDTO, CollectCheckedCollectionInfoDTO.class);
            collectCheckedCollectionInfoDTO.setChecked(collectionMap.containsKey(collectionInfoDetailDTO.getCollectionId()));
            return collectCheckedCollectionInfoDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public SimplePageInfo<CollectCheckedCollectionInfoDTO> queryAtpCollectionInfoList(Long companyId, Long inMuseumId, String collectionName, String payVoucherNo, Integer pageNum, Integer pageSize) {
        CommCheckMainDO checkMainDO = this.commCheckMainMapper.selectByModelAndId(companyId, CollectionBizTypeEnum.MSM.getCode(), inMuseumId);
        if (Objects.isNull(checkMainDO)
                || StringUtils.isBlank(checkMainDO.getCheckStatus())
                || !CheckMainStatusEnum.ATP.getCode().equals(checkMainDO.getCheckStatus())) {
            return new SimplePageInfo<>();
        }
        CommCheckChildDO endCheckChildDO = this.commCheckChildMapper.selectEndNodeByMainId(checkMainDO.getId());
        if (Objects.isNull(endCheckChildDO)) {
            return new SimplePageInfo<>();
        }
        Set<Long> atpCollectionIds = Sets.newHashSet();
        if (StringUtils.isNotBlank(endCheckChildDO.getCollectionId())) {
            Stream.of(endCheckChildDO.getCollectionId().split(",")).forEach(id -> {
                if (StringUtils.isBlank(id)) {
                    return;
                }
                atpCollectionIds.add(Long.parseLong(id));
            });
        }
        List<ScsBizCollectionRelDO> tbtCollections = this.scsBizCollectionRelMapper.listByBizType(CollectionBizTypeEnum.TBT.getCode());
        if (CollectionUtils.isNotEmpty(tbtCollections)) {
            List<Long> tbtCollectionIds = tbtCollections.stream().map(ScsBizCollectionRelDO::getCollectionId).collect(Collectors.toList());
            tbtCollectionIds.forEach(atpCollectionIds::remove);
        }
        List<CollectionInfoDetailDTO> collectionList = this.collectionService.queryByBizIdAndBizType(companyId, inMuseumId, CollectionBizTypeEnum.MSM);
        if (CollectionUtils.isEmpty(collectionList)) {
            return new SimplePageInfo<>();
        }
        List<CollectCheckedCollectionInfoDTO> result = collectionList.stream().map(collectionInfoDetailDTO -> {
            CollectCheckedCollectionInfoDTO collectCheckedCollectionInfoDTO = BeanCopyUtils.copyByJSON(collectionInfoDetailDTO, CollectCheckedCollectionInfoDTO.class);
            if(atpCollectionIds.contains(collectionInfoDetailDTO.getCollectionId())){
                return collectCheckedCollectionInfoDTO;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if(StringUtils.isNotBlank(collectionName)){
            result = result.stream().filter(k -> StringUtils.isNotBlank(k.getName()) && k.getName().contains(collectionName)).collect(Collectors.toList());
        }
        if(StringUtils.isNotBlank(payVoucherNo)){
            result = result.stream().filter(k -> StringUtils.isNotBlank(k.getPayVoucherNo()) && k.getPayVoucherNo().contains(payVoucherNo)).collect(Collectors.toList());
        }
        return PaginationUtils.pagination(result, pageNum, pageSize);
    }

    private ScsInMuseumCertificateInfoDO inMuseumCertificateInfoCheck(Long companyId, Long inMuseumId) {
        ScsInMuseumCertificateInfoDO certificateInfoDO =
                this.scsInMuseumCertificateInfoMapper.selectByPrimaryKey(inMuseumId);
        if (Objects.isNull(certificateInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "入馆凭证信息不存在");
        }
        CheckStatusByModelIdAction checkStatusByModelIdAction = new CheckStatusByModelIdAction();
        checkStatusByModelIdAction.setBelongModel(CollectionBizTypeEnum.TBT);
        checkStatusByModelIdAction.setCompanyId(companyId);
        checkStatusByModelIdAction.setModelId(inMuseumId);
        this.checkService.checkStatusByModelId(checkStatusByModelIdAction);
        return certificateInfoDO;
    }
    private List<InMuseumInfoPageDTO> setInMuseumAttributes(Long companyId, List<ScsInMuseumCertificateInfoDO> inMuseumList, String checkStatus, Integer isUserChecked) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<Long> userIds = inMuseumList.stream()
                .map(ScsInMuseumCertificateInfoDO::getCreator).distinct().collect(Collectors.toList());
        Map<Long, String> userNameMap = this.userService.queryUserNameMap(companyId, userIds);

        List<Long> inMuseumIds = inMuseumList.stream()
                .map(ScsInMuseumCertificateInfoDO::getId).collect(Collectors.toList());
        Map<Long, List<BizCollectionRelDTO>> bizCollectionRelMap = this.collectionService
                .queryBizCollectionRelMap(companyId, inMuseumIds, CollectionBizTypeEnum.MSM);
        QueryBatchCheckStatusAction batchCheckStatusAction = new QueryBatchCheckStatusAction();
        batchCheckStatusAction.setBelongModel(CollectionBizTypeEnum.MSM);
        batchCheckStatusAction.setCompanyId(companyId);
        batchCheckStatusAction.setModelIds(inMuseumIds);
        Map<Long, CheckCurrentInfoDTO> checkInfoMap = this.checkService.queryBatchCheckStatus(batchCheckStatusAction);
        return inMuseumList.stream().map(inMuseumDO -> {
            InMuseumInfoPageDTO inMuseumInfoPageDTO = BeanCopyUtils.copyByJSON(inMuseumDO, InMuseumInfoPageDTO.class);
            inMuseumInfoPageDTO.setInMuseumId(inMuseumDO.getId());
            if (checkInfoMap != null && checkInfoMap.containsKey(inMuseumDO.getId())) {
                CheckCurrentInfoDTO checkCurrentInfoDTO = checkInfoMap.get(inMuseumDO.getId());
                inMuseumInfoPageDTO.setCheckStatus(checkCurrentInfoDTO.getCheckStatus().getCode());
                inMuseumInfoPageDTO.setCheckStatusDesc(checkCurrentInfoDTO.getCheckStatus().getDesc());
                inMuseumInfoPageDTO.setNeedManName(checkCurrentInfoDTO.getNeedManName());
                inMuseumInfoPageDTO.setHasChecked(YesOrNoEnum.否.getCode().byteValue());
                if(Objects.nonNull(checkCurrentInfoDTO.getNeedManId()) && checkCurrentInfoDTO.getNeedManId().contains(userBO.getUserId())){
                    inMuseumInfoPageDTO.setHasChecked(YesOrNoEnum.是.getCode().byteValue());
                }
            }
            if (userNameMap != null && !userNameMap.isEmpty()) {
                inMuseumInfoPageDTO.setCreatorName(userNameMap.get(inMuseumDO.getCreator()));
            }
            if(!Objects.isNull(inMuseumDO.getGmtCreate())){
                inMuseumInfoPageDTO.setGmtCreate(DateUtils.formatDateYYYMMDDHHmmss(inMuseumDO.getGmtCreate()));
            }
            if (bizCollectionRelMap != null && !bizCollectionRelMap.isEmpty()) {
                List<BizCollectionRelDTO> collectionRelDTOList = bizCollectionRelMap.get(inMuseumDO.getId());
                if (CollectionUtils.isNotEmpty(collectionRelDTOList)) {
                    inMuseumInfoPageDTO.setCollectionCount(collectionRelDTOList.size());
                } else {
                    inMuseumInfoPageDTO.setCollectionCount(0);
                }
            }
            if (StringUtils.isNotBlank(checkStatus)) {
                if (checkStatus.equals(inMuseumInfoPageDTO.getCheckStatus())) {
                    return inMuseumInfoPageDTO;
                } else {
                    return null;
                }
            }
            if (isUserChecked == 1) {
                if (inMuseumInfoPageDTO.getHasChecked() == YesOrNoEnum.否.getCode().byteValue()) {
                    return null;
                }
            }
            return inMuseumInfoPageDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
    private InMuseumCertificateEntity convertInMuseumCertificateEntity(AddInMuseumAction addInMuseumAction) {
        InMuseumCertificateEntity entity = new InMuseumCertificateEntity();
        entity.setCompanyId(addInMuseumAction.getCompanyId());
        entity.setInMuseumCertificateNo(addInMuseumAction.getPrefixName()
                + CollectionConstant.IN_MUSEUM_CERTIFICATE_MIDDLE);
        entity.setPrefixName(addInMuseumAction.getPrefixName());
        entity.setSuffixNumber(addInMuseumAction.getSuffixNumber());
        entity.setBatch(addInMuseumAction.getBatch());
        entity.setCreator(addInMuseumAction.getCreator());
        entity.setModifier(addInMuseumAction.getCreator());
        entity.setDocumentId(addInMuseumAction.getDocumentId());
        entity.setAutoAudit(addInMuseumAction.getAutoAudit());
        List<CollectionVO> collectionVOList = this.convertInMuseumCollectionVO(addInMuseumAction.getInMuseumCollectionList());
        entity.setInMuseumCollectionList(collectionVOList);
        return entity;
    }
    private InMuseumCertificateEntity convertInMuseumCertificateEntity(EditInMuseumAction editInMuseumAction) {
        InMuseumCertificateEntity entity = new InMuseumCertificateEntity();
        entity.setInMuseumId(editInMuseumAction.getInMuseumId());
        entity.setInMuseumCertificateNo(editInMuseumAction.getInMuseumCertificateNo());
        entity.setPrefixName(editInMuseumAction.getPrefixName());
        entity.setSuffixNumber(editInMuseumAction.getSuffixNumber());
        entity.setBatch(editInMuseumAction.getBatch());
        entity.setModifier(editInMuseumAction.getModifier());
        entity.setDocumentId(editInMuseumAction.getDocumentId());
        entity.setAutoAudit(editInMuseumAction.getAutoAudit());
        List<CollectionVO> collectionVOList = this.convertInMuseumCollectionVO(editInMuseumAction.getInMuseumCollectionList());
        entity.setInMuseumCollectionList(collectionVOList);
        return entity;
    }
    private List<CollectionVO> convertInMuseumCollectionVO(List<AddInMuseumCollectionAction> inMuseumCollectionList) {
        if (CollectionUtils.isEmpty(inMuseumCollectionList)) {
            return Lists.newArrayList();
        }
        return inMuseumCollectionList.stream().map(collection -> {
            CollectionVO collectionVO = new CollectionVO();
            collectionVO.setCompanyId(collectionVO.getCompanyId());
            collectionVO.setCollectionId(collection.getCollectionId());
            collectionVO.setName(collection.getName());
            collectionVO.setActualCount(collection.getActualCount());
            collectionVO.setCountUnit(collection.getCountUnit());
            collectionVO.setCollectionCount(collection.getCollectionCount());
            collectionVO.setEra(collection.getEra());
            collectionVO.setAge(collection.getAge());
            collectionVO.setTextureCategory(collection.getTextureCategory());
            collectionVO.setTexture(collection.getTexture());
            collectionVO.setSize(collection.getSize());
            collectionVO.setSizeUnit(collection.getSizeUnit());
            collectionVO.setCompleteDegree(collection.getCompleteDegree());
            collectionVO.setCompleteInfo(collection.getCompleteInfo());
            collectionVO.setInitialLevel(collection.getInitialLevel());
            collectionVO.setAmount(collection.getAmount());
            collectionVO.setCollectType(collection.getCollectType());
            collectionVO.setCollectDate(collection.getCollectDate());
            collectionVO.setInMuseumDate(collection.getInMuseumDate());
            collectionVO.setInMuseumStatus(InMuseumStatusEnum.NMSM.getCode());
            collectionVO.setHolder(collection.getHolder());
            collectionVO.setPayVoucherNo(collection.getPayVoucherNo());
            collectionVO.setRemark(collection.getRemark());
            collectionVO.setAppendageDesc(collection.getAppendageDesc());
            collectionVO.setSourceInfo(collection.getSourceInfo());
            collectionVO.setIntroduce(collection.getIntroduce());
            collectionVO.setInputMode(collection.getInputMode().getCode());
            collectionVO.setInputType(collection.getInputType().getCode());
            collectionVO.setDocumentId(collection.getDocumentId());
            collectionVO.setCreator(collection.getCreator());
            collectionVO.setModifier(collection.getModifier());
            return collectionVO;
        }).collect(Collectors.toList());
    }
}
