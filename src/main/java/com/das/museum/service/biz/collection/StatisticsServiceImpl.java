package com.das.museum.service.biz.collection;

import cn.hutool.json.JSONObject;
import com.das.museum.domain.enums.ClassificationBizTypeEnum;
import com.das.museum.infr.dataobject.CommClassificationDO;
import com.das.museum.infr.dataobject.ScsOutInWarehouseCertificateInfoDO;
import com.das.museum.infr.dataobject.ScsOutWarehouseLogDO;
import com.das.museum.infr.mapper.*;
import com.das.museum.service.biz.collection.dto.*;
import com.das.museum.service.enums.CollectionLevelEnum;
import com.das.museum.service.enums.OutWarehouseReasonEnum;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Author: Lee
 * @Date: 2023/1/12
 */
@Slf4j
@Service
public class StatisticsServiceImpl implements StatisticsService{

    @Resource
    ScsCollectionRegisterInfoMapper scsCollectionRegisterInfoMapper;
    @Resource
    ScsOutInWarehouseCertificateInfoMapper scsOutInWarehouseCertificateInfoMapper;

    @Resource
    ScsOutWarehouseLogMapper scsOutWarehouseLogMapper;

    @Resource
    CommClassificationMapper commClassificationMapper;
    @Resource
    SzzyResourceMapper szzyResourceMapper;

    @Override
    public StatisticsAllCollectionDTO statisticsAllCollection() {
        LoginUserInfoBO userInfoBO = LoginUserInfoUtils.getLoginUserInfo();
        Long companyId = userInfoBO.getCompanyId();
        // 统计出已注销外所有藏品
        Long collectionCount = this.scsCollectionRegisterInfoMapper.statisticsAllByCompanyId(companyId);
        if(Objects.isNull(collectionCount)){
            collectionCount = 0L;
        }
        // 统计在库藏品
        Long inWarehouseCount = this.scsCollectionRegisterInfoMapper.statisticsInWarehouseByCompanyId(companyId);
        if(Objects.isNull(inWarehouseCount)){
            inWarehouseCount = 0L;
        }
        // 统计出库藏品
        Long outWarehouseCount = this.scsCollectionRegisterInfoMapper.statisticsOutWarehouseByCompanyId(companyId);
        if(Objects.isNull(outWarehouseCount)){
            outWarehouseCount = 0L;
        }
        // 统计在展藏品
        List<CommClassificationDO> commClassificationReasonList = this.commClassificationMapper.selectBybizCodeList(companyId,
                ClassificationBizTypeEnum.DIC.getCode().byteValue(), Lists.newArrayList(OutWarehouseReasonEnum.GNZS.getCode(),
                        OutWarehouseReasonEnum.GWZL.getCode(), OutWarehouseReasonEnum.JWZL.getCode()));
        Long inShowCount = 0L;
        if (CollectionUtils.isNotEmpty(commClassificationReasonList)) {
            List<Long> outReasonIds = commClassificationReasonList.stream().map(CommClassificationDO::getId).collect(Collectors.toList());
            // 统计在展藏品
            inShowCount = this.scsCollectionRegisterInfoMapper.statisticsInShowByCompanyId(companyId, outReasonIds);
        }
        AtomicReference<Long> hasLevelCount = new AtomicReference<>(0L);
        AtomicReference<Long> oneLevelCount = new AtomicReference<>(0L);
        AtomicReference<Long> twoLevelCount = new AtomicReference<>(0L);
        AtomicReference<Long> thrLevelCount = new AtomicReference<>(0L);
        AtomicReference<Long> unTypeCount = new AtomicReference<>(0L);
        StatisticsAllCollectionDTO allCollectionDTO = new StatisticsAllCollectionDTO();
        allCollectionDTO.setCollectionCount(collectionCount);
        allCollectionDTO.setInWarehouseCount(inWarehouseCount);
        allCollectionDTO.setOutWarehouseCount(outWarehouseCount);
        allCollectionDTO.setInShowCount(inShowCount);
        allCollectionDTO.setHasLevelCount(hasLevelCount.get());
        allCollectionDTO.setNoLevelCount(unTypeCount.get());
        allCollectionDTO.setFirLevelCount(oneLevelCount.get());
        allCollectionDTO.setSecLevelCount(twoLevelCount.get());
        allCollectionDTO.setThrLevelCount(thrLevelCount.get());
        // 统计定级、未定级、一级、二级、三级藏品集合数据
        List<Long> collectionLevelIds = this.scsCollectionRegisterInfoMapper.listIdentifyLevelIds(companyId);
        if(CollectionUtils.isEmpty(collectionLevelIds)){
            return allCollectionDTO;
        }
        List<CommClassificationDO> commClassificationDOList = this.commClassificationMapper.selectByIds(collectionLevelIds);
        if (CollectionUtils.isEmpty(commClassificationDOList)) {
            return allCollectionDTO;
        }
        Map<Long, String> commClassificationMap = new HashMap<>();
        commClassificationDOList.forEach(c -> commClassificationMap.put(c.getId(), c.getName()));
        collectionLevelIds.forEach(c -> {
            String levelName = commClassificationMap.get(c);
            if (CollectionLevelEnum.一级.getCode().equals(levelName)) {
                hasLevelCount.getAndSet(hasLevelCount.get() + 1);
                oneLevelCount.getAndSet(oneLevelCount.get() + 1);
            } else if (CollectionLevelEnum.二级.getCode().equals(levelName)) {
                hasLevelCount.getAndSet(hasLevelCount.get() + 1);
                twoLevelCount.getAndSet(twoLevelCount.get() + 1);
            } else if (CollectionLevelEnum.三级.getCode().equals(levelName)) {
                hasLevelCount.getAndSet(hasLevelCount.get() + 1);
                thrLevelCount.getAndSet(thrLevelCount.get() + 1);
            } else if (CollectionLevelEnum.未定级.getCode().equals(levelName)) {
                unTypeCount.getAndSet(unTypeCount.get() + 1);
            } else {
                unTypeCount.getAndSet(unTypeCount.get() + 1);
            }
        });
        allCollectionDTO.setHasLevelCount(hasLevelCount.get());
        allCollectionDTO.setNoLevelCount(unTypeCount.get());
        allCollectionDTO.setFirLevelCount(oneLevelCount.get());
        allCollectionDTO.setSecLevelCount(twoLevelCount.get());
        allCollectionDTO.setThrLevelCount(thrLevelCount.get());
        return allCollectionDTO;
    }

    @Override
    public OutWarehouseTrendDTO getOutWarehouseTrend() {
        LoginUserInfoBO userInfoBO = LoginUserInfoUtils.getLoginUserInfo();
        Long companyId = userInfoBO.getCompanyId();

        OutWarehouseTrendDTO outWarehouseTrendDTO = new OutWarehouseTrendDTO();

        List<Long> outWarehouseCollectionIds = this.scsCollectionRegisterInfoMapper.selectOutWarehouseByCompanyId(companyId);
        if(CollectionUtils.isEmpty(outWarehouseCollectionIds)){
            return outWarehouseTrendDTO;
        }

        List<ScsOutWarehouseLogDO> outWarehouseDOs = this.scsOutWarehouseLogMapper.listCertIdByCollectIds(companyId, outWarehouseCollectionIds);
        List<Long> hasOutIds = new ArrayList<>();
        Map<Long, ScsOutWarehouseLogDO> outWarehouseLogDOMap = new HashMap<>();
        outWarehouseDOs.forEach(outWarehouseDO ->{
            if(outWarehouseLogDOMap.containsKey(outWarehouseDO.getCollectionId())){
                if(outWarehouseLogDOMap.get(outWarehouseDO.getCollectionId()).getGmtCreate().before(outWarehouseDO.getGmtCreate())){
                    outWarehouseLogDOMap.replace(outWarehouseDO.getCollectionId(), outWarehouseDO);
                }
            }else {
                outWarehouseLogDOMap.put(outWarehouseDO.getCollectionId(), outWarehouseDO);
            }
        });

        outWarehouseLogDOMap.forEach((aLong, scsOutWarehouseLogDO) -> hasOutIds.add(scsOutWarehouseLogDO.getOutInWarehouseId()));

        List<ScsOutInWarehouseCertificateInfoDO> reasonList = this.scsOutInWarehouseCertificateInfoMapper.selectReasonByCollectionId(companyId, hasOutIds);
        Map<Long, Long> reasonMap = reasonList.stream().collect(Collectors.toMap(ScsOutInWarehouseCertificateInfoDO::getId, ScsOutInWarehouseCertificateInfoDO::getReason, (key1, key2) -> key2));
        // 统计数量
        AtomicReference<Long> inMuseumShowCount = new AtomicReference<>(0L);
        AtomicReference<Long> forStudyCount = new AtomicReference<>(0L);
        AtomicReference<Long> othersCount = new AtomicReference<>(0L);

        hasOutIds.forEach(id ->{
            String reason = reasonMap.get(id).toString();
            switch (reason){
                case "311":
                    inMuseumShowCount.getAndSet(inMuseumShowCount.get() + 1L);
                    break;
                case "310":
                case "317":
                case "318":
                    forStudyCount.getAndSet(forStudyCount.get() + 1L);
                    break;
                case "309":
                case "312":
                case "313":
                case "314":
                case "315":
                case "316":
                case "319":
                case "320":
                default:
                    othersCount.getAndSet(othersCount.get() + 1L);
                    break;
            }
        });

        outWarehouseTrendDTO.setInMuseumShow(inMuseumShowCount.get());
        outWarehouseTrendDTO.setForStudy(forStudyCount.get());
        outWarehouseTrendDTO.setOthers(othersCount.get());
        return outWarehouseTrendDTO;
    }

    @Override
    public List<StatisticsByTypeDTO> getStatisticsByType(Byte type) {
        LoginUserInfoBO userInfoBO = LoginUserInfoUtils.getLoginUserInfo();
        Long companyId = userInfoBO.getCompanyId();
        List<StatisticsByTypeDTO> statisticsByTypeDTOList = new ArrayList<>();
        if(type == (byte) 0){ // 质地
            statisticsByTypeDTOList = this.scsCollectionRegisterInfoMapper.statisticsByTexture(companyId);
        }else if(type == (byte) 1){ // 年代
            statisticsByTypeDTOList = this.scsCollectionRegisterInfoMapper.statisticsByAge(companyId);
        }else if(type == (byte) 2){ // 级别
            statisticsByTypeDTOList = this.scsCollectionRegisterInfoMapper.statisticsByIdentify(companyId);
        }

        if(CollectionUtils.isEmpty(statisticsByTypeDTOList)){
            return new ArrayList<>();
        }

        List<Long> ids = new ArrayList<>();
        statisticsByTypeDTOList.forEach(statisticsByTypeDTO -> {
            if (type == (byte) 0) {
                if(StringUtils.isNotBlank(statisticsByTypeDTO.getTypeName())){
                    String[] textureIds = statisticsByTypeDTO.getTypeName().split(",");
                    for (String textureId : textureIds) {
                        if (StringUtils.isNotBlank(textureId)) {
                            ids.add(Long.parseLong(textureId));
                        }
                    }
                }
            } else {
                if(StringUtils.isNotBlank(statisticsByTypeDTO.getTypeName())){
                    ids.add(Long.parseLong(statisticsByTypeDTO.getTypeName()));
                }
            }
        });

        List<CommClassificationDO> commClassificationDOList = this.commClassificationMapper.listByIds(companyId, ids);
        Map<Long, CommClassificationDO> commMap = commClassificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId, commClassificationDO -> commClassificationDO));

        List<StatisticsByTypeDTO> resultList = new ArrayList<>();
        AtomicReference<Long> unTypeCount = new AtomicReference<>(0L);
        StatisticsByTypeDTO unTypeDTO = new StatisticsByTypeDTO();
        unTypeDTO.setTypeName("未定义类型");
        unTypeDTO.setCollectionCount(0L);
        statisticsByTypeDTOList.forEach(statisticsByTypeDTO -> {
            StatisticsByTypeDTO resultDTO = new StatisticsByTypeDTO();
            if(StringUtils.isNotBlank(statisticsByTypeDTO.getTypeName())){
                if (type == (byte) 0) {
                    String[] textureIds = statisticsByTypeDTO.getTypeName().split(",");
                    for (String textureId : textureIds) {
                        if (StringUtils.isNotBlank(textureId)) {
                            Long commId = Long.parseLong(textureId);
                            if(commMap.containsKey(commId)){
                                resultDTO.setTypeName(commMap.get(commId).getName());
                                resultDTO.setCollectionCount(statisticsByTypeDTO.getCollectionCount());
                                resultList.add(resultDTO);
                            }else {
                                unTypeCount.getAndSet(unTypeCount.get() + statisticsByTypeDTO.getCollectionCount());
                            }
                        }
                    }
                } else {
                    Long commId = Long.parseLong(statisticsByTypeDTO.getTypeName());
                    if(commMap.containsKey(commId)){
                        resultDTO.setTypeName(commMap.get(commId).getName());
                        resultDTO.setCollectionCount(statisticsByTypeDTO.getCollectionCount());
                        resultList.add(resultDTO);
                    }else {
                        unTypeCount.getAndSet(unTypeCount.get() + statisticsByTypeDTO.getCollectionCount());
                    }
                }
            }else { // 未定义类型
                unTypeCount.getAndSet(unTypeCount.get() + statisticsByTypeDTO.getCollectionCount());
            }
        });
        if(unTypeCount.get() > 0){
            unTypeDTO.setCollectionCount(unTypeCount.get());
            resultList.add(unTypeDTO);
        }
        return resultList;
    }

    @Override
    public StatisticDigitalDTO getStatisticDigital() {
        LoginUserInfoBO userInfoBO = LoginUserInfoUtils.getLoginUserInfo();
        Long companyId = userInfoBO.getCompanyId();
        StatisticDigitalDTO digitalDTO = new StatisticDigitalDTO();
        List<String> twoImageResources = this.szzyResourceMapper.statisticsDigital(companyId, 10L, "CP_TWO_IMAGE");
        List<String> threeModelResources = this.szzyResourceMapper.statisticsDigital(companyId, 11L, "CP_3D_MODEL");

        Long hasTwoImageCount, hasThreeModelCount;
        if(Objects.isNull(twoImageResources)){
            hasTwoImageCount = 0L;
        }else {
            AtomicReference<Long> twoImageCount = new AtomicReference<>(0L);
            List<String> registerList = new ArrayList<>();
            twoImageResources.forEach(resBody ->{
                JSONObject json = new JSONObject(resBody);
                String register = json.get("registerNo").toString();
                if(!registerList.contains(register)){
                    log.info("----" + register + "----");
                    registerList.add(register);
                    twoImageCount.getAndSet(twoImageCount.get() + 1L);
                }
            });
            hasTwoImageCount = twoImageCount.get();
        }
        if (Objects.isNull(threeModelResources)){
            hasThreeModelCount = 0L;
        }else {
            AtomicReference<Long> threeImageCount = new AtomicReference<>(0L);
            List<String> registerList = new ArrayList<>();
            threeModelResources.forEach(resBody ->{
                JSONObject json = new JSONObject(resBody);
                String register = json.get("registerNo").toString();
                if(!registerList.contains(register)){
                    registerList.add(register);
                    threeImageCount.getAndSet(threeImageCount.get() + 1L);
                }
            });
            hasThreeModelCount = threeImageCount.get();
        }

        // 统计出已注销外所有藏品
        Long collectionCount = this.scsCollectionRegisterInfoMapper.statisticsAllByCompanyId(companyId);
        if(Objects.isNull(collectionCount)){
            return digitalDTO;
        }
        float hasTwoImagePresent = 0.0f;
        float hasThreeModelPresent = 0.0f;
        if(collectionCount != 0 ) {
            hasTwoImagePresent = (float) hasTwoImageCount / collectionCount;
            hasThreeModelPresent = (float) hasThreeModelCount / collectionCount;
        }

        digitalDTO.setTwoImagePresent(convertFloatToLong(hasTwoImagePresent));
        digitalDTO.setTwoImageCount(hasTwoImageCount);
        digitalDTO.setThreeModelPresent(convertFloatToLong(hasThreeModelPresent));
        digitalDTO.setThreeModelCount(hasThreeModelCount);

        return digitalDTO;
    }

    private Long convertFloatToLong(float value){
        return (long) Math.round(value * 100);
    }
}
