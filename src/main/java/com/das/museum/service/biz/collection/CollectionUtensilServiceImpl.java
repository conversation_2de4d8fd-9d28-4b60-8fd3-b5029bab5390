package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionUtensilAction;
import com.das.museum.service.biz.collection.action.EditCollectionUtensilAction;
import com.das.museum.service.biz.collection.action.UtensilQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionUtensilDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionUtensilPageDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectionUtensilEntity;
import com.das.museum.domain.collection.repository.CollectionUtensilRepository;
import com.das.museum.infr.dataobject.ScsCollectionUtensilDO;
import com.das.museum.infr.mapper.ScsCollectionUtensilMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CollectionUtensilServiceImpl implements CollectionUtensilService {

    @Resource
    private CollectionUtensilRepository collectionUtensilRepository;

    @Resource
    private ScsCollectionUtensilMapper scsCollectionUtensilMapper;

    @Resource
    private DocumentService documentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCollectionUtensil(AddCollectionUtensilAction action) {
        CollectionUtensilEntity entity = this.convertCollectionUtensilEntity(action);
        this.collectionUtensilRepository.saveCollectionUtensil(entity);
        return entity.getCollectionUtensilId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionUtensil(EditCollectionUtensilAction action) {
        Long companyId = action.getCompanyId();
        Long collectionUtensilId = action.getCollectionUtensilId();
        CollectionUtensilDetailDTO collectionUtensilDTO = this.queryByCollectionUtensilId(companyId, collectionUtensilId);
        if (Objects.isNull(collectionUtensilDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "器物信息不存在");
        }
        CollectionUtensilEntity entity = this.convertCollectionUtensilEntity(action);
        this.collectionUtensilRepository.editCollectionUtensil(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByCollectionUtensilId(Long companyId, Long collectionUtensilId) {
        CollectionUtensilDetailDTO collectionUtensilDTO = this.queryByCollectionUtensilId(companyId, collectionUtensilId);
        if (Objects.isNull(collectionUtensilDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "器物信息不存在");
        }
        this.collectionUtensilRepository.delByCollectionUtensilId(companyId, collectionUtensilId);
        if (StringUtils.isBlank(collectionUtensilDTO.getDocumentId())) {
            return;
        }
        this.documentService.delByDocIds(companyId, collectionUtensilDTO.getDocumentId());
    }

    @Override
    public SimplePageInfo<CollectionUtensilPageDTO> queryPageByCondition(UtensilQueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionUtensilDO> collectionUtensilDOList = this.scsCollectionUtensilMapper.listPageByCondition(companyId,
                action.getCollectionId(), action.getSortBy());
        if (CollectionUtils.isEmpty(collectionUtensilDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<CollectionUtensilPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectionUtensilDOList, CollectionUtensilPageDTO.class);
        List<CollectionUtensilPageDTO> collectionUtensilPageDTOList = collectionUtensilDOList.stream().map(collectionUtensilDO -> {
            CollectionUtensilPageDTO collectionUtensilPageDTO = BeanCopyUtils.copyByJSON(collectionUtensilDO, CollectionUtensilPageDTO.class);
            collectionUtensilPageDTO.setCollectionUtensilId(collectionUtensilDO.getId());
            return collectionUtensilPageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectionUtensilPageDTOList);
        return pageInfo;
    }

    @Override
    public CollectionUtensilDetailDTO queryByCollectionUtensilId(Long companyId, Long collectionUtensilId) {
        ScsCollectionUtensilDO collectionUtensilDO = this.scsCollectionUtensilMapper.selectById(companyId, collectionUtensilId);
        if (Objects.isNull(collectionUtensilDO)) {
            return null;
        }
        CollectionUtensilDetailDTO collectionUtensilDTO = BeanCopyUtils.copyByJSON(collectionUtensilDO, CollectionUtensilDetailDTO.class);
        collectionUtensilDTO.setCollectionUtensilId(collectionUtensilDTO.getCollectionUtensilId());
        String documentId = collectionUtensilDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return collectionUtensilDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        collectionUtensilDTO.setCommDocumentList(commDocumentList);
        return collectionUtensilDTO;
    }

    private CollectionUtensilEntity convertCollectionUtensilEntity(AddCollectionUtensilAction action) {
        CollectionUtensilEntity entity = new CollectionUtensilEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setCollectionId(action.getCollectionId());
        entity.setInscription(action.getInscription());
        entity.setOrnament(action.getOrnament());
        entity.setDocumentId(action.getDocumentId());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        return entity;
    }

    private CollectionUtensilEntity convertCollectionUtensilEntity(EditCollectionUtensilAction action) {
        CollectionUtensilEntity entity = new CollectionUtensilEntity();
        entity.setCollectionUtensilId(action.getCollectionUtensilId());
        entity.setCompanyId(action.getCompanyId());
        entity.setInscription(action.getInscription());
        entity.setOrnament(action.getOrnament());
        entity.setDocumentId(action.getDocumentId());
        entity.setModifier(action.getModifier());
        return entity;
    }
}
