package com.das.museum.service.biz.collection.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectionMountPageDTO implements Serializable {
    private static final long serialVersionUID = -1076949857063864272L;
    private Long collectionMountId;
    private Long companyId;
    private Long collectionId;
    private String contractUnit;
    private Long producerId;
    private String producerName;
    private String remark;
    private String mountDate;
    private String documentId;
    private Long creator;
    private Long modifier;
    private String gmtCreate;
    private String gmtModified;
}