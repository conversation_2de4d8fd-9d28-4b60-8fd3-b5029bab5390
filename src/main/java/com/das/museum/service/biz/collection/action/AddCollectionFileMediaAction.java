package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AddCollectionFileMediaAction implements Serializable {
    private static final long serialVersionUID = -4897501698885985335L;
    private Long companyId;
    private Long collectionId;
    private String mediaType;
    private String fileName;
    private String fileNo;
    private String producerName;
    private String proportion;
    private Date shotDate;
    private Date productionDate;
    private Byte clarity;
    private String remark;
    private String documentId;
    private Long creator;
}
