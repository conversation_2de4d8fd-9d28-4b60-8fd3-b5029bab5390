package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionFileMediaPageDTO implements Serializable {
    private static final long serialVersionUID = 116912091992214451L;
    private Long collectionFileMediaId;
    private Long companyId;
    private Long collectionId;
    private String mediaType;
    private String fileName;
    private String fileNo;
    private String producerName;
    private String proportion;
    private Date shotDate;
    private Date productionDate;
    private Byte clarity;
    private String remark;
    private String documentId;
    private Long creator;
    private String creatorName;
    private Long modifier;
    private String gmtCreate;
    private Date gmtModified;
    private List<CommonDocumentDTO> commonDocumentList;
}