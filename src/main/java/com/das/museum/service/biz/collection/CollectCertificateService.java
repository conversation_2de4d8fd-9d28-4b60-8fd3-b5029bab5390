package com.das.museum.service.biz.collection;

import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.service.biz.collection.action.AddCollectAction;
import com.das.museum.service.biz.collection.action.CollectConditionQueryAction;
import com.das.museum.service.biz.collection.action.EditCollectAction;
import com.das.museum.service.biz.collection.dto.CollectCheckedCollectionInfoDTO;
import com.das.museum.service.biz.collection.dto.CollectInfoDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectInfoPageDTO;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;

import java.util.List;

public interface CollectCertificateService {
    Long addCollectCertificateInfo(AddCollectAction addCollectAction);
    void editCollectCertificateInfo(Long companyId, Long creator, String creatorName, Long collectId, EditCollectAction editCollectAction);
    void delByCollectCertificateInfoById(Long companyId, Long collectId);
    CollectInfoDetailDTO queryDetailInfoById(Long companyId, Long collectId);
    List<CommonDocumentDTO> queryAttachmentByCollectId(Long companyId, Long collectId);
    SimplePageInfo<CollectInfoPageDTO> queryPageByConditionQuery(CollectConditionQueryAction collectConditionQueryAction);
    SimplePageInfo<CollectInfoPageDTO> queryPageByBizTypeList(Long companyId, String collectCertificateNo, String sortBy,
                                                              CollectionBizTypeEnum bizType, CollectionBizTypeEnum sourceBizType, Integer pageNum, Integer pageSize);
    List<CollectCheckedCollectionInfoDTO> queryCheckedCollectionInfoList(Long companyId, Long collectId);

    SimplePageInfo<CollectCheckedCollectionInfoDTO> queryAtpCollectionInfoList(Long companyId, Long collectId, CollectionBizTypeEnum targetBizType, String collectionName, String era, Integer pageNum, Integer pageSize);
}
