package com.das.museum.service.biz.collection;

import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.service.biz.collection.action.EditTwoImageInfoAction;
import com.das.museum.service.biz.collection.action.QueryTwoImagePageAction;
import com.das.museum.service.biz.collection.dto.QueryTwoImageInfoDTO;
import com.das.museum.service.biz.collection.dto.QueryTwoImagePageListDTO;

public interface CollectionTwoImageService {

    SimplePageInfo<QueryTwoImagePageListDTO> queryTwoImagePageList(QueryTwoImagePageAction action);

    QueryTwoImageInfoDTO queryTwoImagePageList(Long companyId,Long twoImageId);

    void editTwoImageInfo(EditTwoImageInfoAction action);

    void delTwoImageInfo(Long companyId,Long twoImageId);
}
