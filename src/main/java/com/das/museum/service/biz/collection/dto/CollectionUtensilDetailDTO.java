package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionUtensilDetailDTO implements Serializable {
    private static final long serialVersionUID = 4788424243868199644L;
    private Long collectionUtensilId;
    private Long companyId;
    private Long collectionId;
    private String inscription;
    private String ornament;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private List<CommonDocumentDTO> commDocumentList;
}