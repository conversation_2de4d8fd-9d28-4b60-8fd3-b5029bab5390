package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;

@Data
public class AddCollectionAncientBookAction implements Serializable {
    private static final long serialVersionUID = -6892367796976516654L;
    private Long companyId;
    private Long collectionId;
    private String classification;
    private String version;
    private String archive;
    private String frame;
    private String format;
    private String documentId;
    private Long creator;
}
