package com.das.museum.service.biz.collection;

import com.das.museum.infr.dataobject.AccUserDO;
import com.das.museum.infr.mapper.AccUserMapper;
import com.das.museum.service.biz.collection.action.AddCollectionFileMediaAction;
import com.das.museum.service.biz.collection.action.EditCollectionFileMediaAction;
import com.das.museum.service.biz.collection.action.MediaQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionFileMediaDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionFileMediaPageDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectionFileMediaEntity;
import com.das.museum.domain.collection.repository.CollectionFileMediaRepository;
import com.das.museum.infr.dataobject.ScsCollectionFileMediaDO;
import com.das.museum.infr.mapper.ScsCollectionFileMediaMapper;
import com.das.museum.service.enums.DeleteStatusEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class CollectionFileMediaServiceImpl implements CollectionFileMediaService {

    @Resource
    private CollectionFileMediaRepository collectionFileMediaRepository;

    @Resource
    private ScsCollectionFileMediaMapper scsCollectionFileMediaMapper;

    @Resource
    private AccUserMapper accUserMapper;

    @Resource
    private DocumentService documentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCollectionFileMedia(AddCollectionFileMediaAction action) {
        CollectionFileMediaEntity entity = this.convertScsCollectionFileMediaDO(action);
        this.collectionFileMediaRepository.saveCollectionFileMedia(entity);
        return entity.getCollectionFileMediaId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionFileMedia(EditCollectionFileMediaAction action) {
        Long companyId = action.getCompanyId();
        Long collectionFileMediaId = action.getCollectionFileMediaId();
        CollectionFileMediaDetailDTO collectionFileMediaDTO = this.queryByCollectionFileMediaId(companyId, collectionFileMediaId);
        if (Objects.isNull(collectionFileMediaDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "流媒体信息不存在");
        }
        CollectionFileMediaEntity entity = this.convertScsCollectionFileMediaDO(action);
        this.collectionFileMediaRepository.editCollectionFileMedia(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByCollectionFileMediaId(Long companyId, Long collectionFileMediaId) {
        CollectionFileMediaDetailDTO collectionFileMediaDTO = this.queryByCollectionFileMediaId(companyId, collectionFileMediaId);
        if (Objects.isNull(collectionFileMediaDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "流媒体信息不存在");
        }
        this.collectionFileMediaRepository.delByCollectionFileMediaId(companyId, collectionFileMediaId);
        if (StringUtils.isBlank(collectionFileMediaDTO.getDocumentId())) {
            return;
        }
        this.documentService.delByDocIds(companyId, collectionFileMediaDTO.getDocumentId());
    }

    @Override
    public SimplePageInfo<CollectionFileMediaPageDTO> queryPageByCondition(MediaQueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionFileMediaDO> collectionFileMediaDOList = this.scsCollectionFileMediaMapper.listPageByCondition(companyId,
                action.getCollectionId(), action.getMediaType().getCode(), action.getSortBy());
        if (CollectionUtils.isEmpty(collectionFileMediaDOList)) {
            return new SimplePageInfo<>();
        }
        StringBuilder documentIdBuilder = new StringBuilder();
        collectionFileMediaDOList.forEach(fileMediaDO -> {
            if (StringUtils.isNotBlank(fileMediaDO.getDocumentId())) {
                documentIdBuilder.append(fileMediaDO.getDocumentId()).append(",");
            }
        });
        Map<Long, CommonDocumentDTO> documentMap = Maps.newHashMap();
        if (documentIdBuilder.length() != 0) {
            String documentStr = documentIdBuilder.deleteCharAt(documentIdBuilder.length() - 1).toString();
            List<CommonDocumentDTO> documentDTOList = this.documentService.listByDocIds(companyId, documentStr);
            if (CollectionUtils.isNotEmpty(documentDTOList)) {
                documentDTOList.forEach(document -> {
                    documentMap.put(document.getDocumentId(), document);
                });
            }
        }
        SimplePageInfo<CollectionFileMediaPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectionFileMediaDOList, CollectionFileMediaPageDTO.class);
        List<CollectionFileMediaPageDTO> collectionFileMediaPageDTOList = collectionFileMediaDOList.stream().map(collectionFileMediaDO -> {
            CollectionFileMediaPageDTO collectionFileMediaPageDTO = BeanCopyUtils.copyByJSON(collectionFileMediaDO, CollectionFileMediaPageDTO.class);
            collectionFileMediaPageDTO.setCollectionFileMediaId(collectionFileMediaDO.getId());
            collectionFileMediaPageDTO.setGmtCreate(DateUtils.formatDateYYYMMDDHHmmss(collectionFileMediaDO.getGmtCreate()));
            AccUserDO accUserInfoDO = this.accUserMapper.selectByUserIdAndIsDelete(collectionFileMediaDO.getCreator(), DeleteStatusEnum.否.getCode().byteValue());
            if (Objects.nonNull(accUserInfoDO)) {
                collectionFileMediaPageDTO.setCreatorName(accUserInfoDO.getUserName());
            }
            List<CommonDocumentDTO> documentDTOList = Lists.newArrayList();
            if (StringUtils.isNotBlank(collectionFileMediaDO.getDocumentId())) {
                List<Long> documentIds = Stream.of(collectionFileMediaDO.getDocumentId().split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                documentIds.forEach(documentId -> documentDTOList.add(documentMap.get(documentId)));
            }
            collectionFileMediaPageDTO.setCommonDocumentList(documentDTOList);
            return collectionFileMediaPageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectionFileMediaPageDTOList);
        return pageInfo;
    }

    @Override
    public CollectionFileMediaDetailDTO queryByCollectionFileMediaId(Long companyId, Long collectionFileMediaId) {
        ScsCollectionFileMediaDO collectionFileMediaDO = this.scsCollectionFileMediaMapper.selectById(companyId, collectionFileMediaId);
        if (Objects.isNull(collectionFileMediaDO)) {
            return null;
        }
        CollectionFileMediaDetailDTO collectionFileMediaDTO = BeanCopyUtils.copyByJSON(collectionFileMediaDO, CollectionFileMediaDetailDTO.class);
        collectionFileMediaDTO.setCollectionFileMediaId(collectionFileMediaDTO.getCollectionFileMediaId());
        String documentId = collectionFileMediaDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return collectionFileMediaDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        collectionFileMediaDTO.setCommDocumentList(commDocumentList);
        return collectionFileMediaDTO;
    }

    private CollectionFileMediaEntity convertScsCollectionFileMediaDO(AddCollectionFileMediaAction action) {
        CollectionFileMediaEntity entity = new CollectionFileMediaEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setCollectionId(action.getCollectionId());
        entity.setMediaType(action.getMediaType());
        entity.setFileName(action.getFileName());
        entity.setFileNo(action.getFileNo());
        entity.setProducerName(action.getProducerName());
        entity.setProductionDate(action.getProductionDate());
        entity.setProportion(action.getProportion());
        entity.setShotDate(action.getShotDate());
        entity.setClarity(action.getClarity());
        entity.setRemark(action.getRemark());
        entity.setDocumentId(action.getDocumentId());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        return entity;
    }
    private CollectionFileMediaEntity convertScsCollectionFileMediaDO(EditCollectionFileMediaAction action) {
        CollectionFileMediaEntity entity = new CollectionFileMediaEntity();
        entity.setCollectionFileMediaId(action.getCollectionFileMediaId());
        entity.setCompanyId(action.getCompanyId());
        entity.setMediaType(action.getMediaType());
        entity.setFileName(action.getFileName());
        entity.setFileNo(action.getFileNo());
        entity.setProducerName(action.getProducerName());
        entity.setProductionDate(action.getProductionDate());
        entity.setProportion(action.getProportion());
        entity.setShotDate(action.getShotDate());
        entity.setClarity(action.getClarity());
        entity.setRemark(action.getRemark());
        entity.setDocumentId(action.getDocumentId());
        entity.setModifier(action.getModifier());
        return entity;
    }
}
