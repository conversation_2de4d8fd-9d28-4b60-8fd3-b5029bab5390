package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/18
 */
@Data
public class EditCollectAction implements Serializable {
    private static final long serialVersionUID = -6533778058741939154L;
    private Long collectId;
    private String collectCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private String batch;
    private String transferor;
    private String departmentHead;
    private String documentId;
    private Long companyId;
    private Long modifier;
    private List<EditCollectCollectionAction> collectCollectionList;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}
