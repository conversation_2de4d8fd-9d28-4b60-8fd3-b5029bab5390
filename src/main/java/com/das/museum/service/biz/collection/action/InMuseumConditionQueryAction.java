package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class InMuseumConditionQueryAction extends PageInfoBaseReq {
    private static final long serialVersionUID = -8768522507485157496L;
    private Long companyId;
    private String inMuseumCertificateNo;
    private Long collectType;
    private String collectionName;
    private String checkStatus;
    private String sortBy;
    private Integer isUserChecked;
}
