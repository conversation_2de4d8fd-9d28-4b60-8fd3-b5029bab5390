package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionRegisterIdentifyAction;
import com.das.museum.service.biz.collection.action.EditCollectionRegisterIdentifyAction;
import com.das.museum.service.biz.collection.action.RegisterIdentifyQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionRegisterIdentifyDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionRegisterIdentifyPageDTO;
import com.das.museum.common.bo.SimplePageInfo;

public interface CollectionRegisterIdentifyService {
    Long addCollectionRegisterIdentify(AddCollectionRegisterIdentifyAction action);
    void editCollectionRegisterIdentify(EditCollectionRegisterIdentifyAction action);
    void delByRegisterIdentifyId(Long companyId, Long registerIdentifyId);
    SimplePageInfo<CollectionRegisterIdentifyPageDTO> queryPageByCondition(RegisterIdentifyQueryPageByConditionAction action);
    CollectionRegisterIdentifyDetailDTO queryByRegisterIdentifyId(Long companyId, Long registerIdentifyId);
}
