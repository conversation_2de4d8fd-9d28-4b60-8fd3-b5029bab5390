package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class InTibetanInfoDetailDTO implements Serializable {
    private static final long serialVersionUID = 5889857150326982102L;
    private Long inTibetanId;
    private Long companyId;
    private String inTibetanCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private Long submitter;
    private String submitterName;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private List<CommonDocumentDTO> commDocumentList;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}
