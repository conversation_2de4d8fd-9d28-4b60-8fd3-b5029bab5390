package com.das.museum.service.biz.collection.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectionAncientBookPageDTO implements Serializable {
    private static final long serialVersionUID = 8299621377138512574L;
    private Long collectionAncientBookId;
    private Long companyId;
    private Long collectionId;
    private String classification;
    private String version;
    private String archive;
    private String frame;
    private String format;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}