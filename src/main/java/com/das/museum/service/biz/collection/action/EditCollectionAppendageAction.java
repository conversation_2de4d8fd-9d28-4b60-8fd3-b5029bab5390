package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;

@Data
public class EditCollectionAppendageAction implements Serializable {
    private static final long serialVersionUID = -6947702553856595476L;
    private Long collectionAppendageId;
    private Long companyId;
    private String name;
    private Integer actualCount;
    private Long meteringUnit;
    private String meteringUnitName;
    private String shape;
    private Long completeDegree;
    private String size;
    private String texture;
    private String weight;
    private String currentSituation;
    private String remark;
    private String documentId;
    private Long modifier;
}
