package com.das.museum.service.biz.collection.action;

import com.das.museum.service.enums.InputModeEnum;
import com.das.museum.service.enums.InputTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AddInTibetanCollectionAction implements Serializable {
    private static final long serialVersionUID = -3116438707312618702L;
    private Long collectionId;
    private String registerNo;
    private Long classify;
    private String name;
    private Integer actualCount;
    private Long countUnit;
    private Integer collectionCount;
    private String era;
    private Long age;
    private Long textureCategory;
    private String texture;
    private String size;
    private Long sizeUnit;
    private Long completeDegree;
    private String completeInfo;
    private Long initialLevel;
    private Long amount;
    private Long collectType;
    private Date collectDate;
    private String holder;
    private String payVoucherNo;
    private String remark;
    private String appendageDesc;
    private String sourceInfo;
    private String introduce;
    private String inMuseumCertificateNo;
    private Date inMuseumDate;
    private Date inTibetanDate;
    private String appraiserName;
    private InputModeEnum inputMode;
    private InputTypeEnum inputType;
    private String documentId;
    private Long creator;
    private Long modifier;

    /**
     * 文物类别：classification_id
     */
    private Long category;

    /**
     * 质量范围:classification_id
     */
    private Long qualityRange;

    /**
     * 库房id
     */
    private Long warehouseId;
}
