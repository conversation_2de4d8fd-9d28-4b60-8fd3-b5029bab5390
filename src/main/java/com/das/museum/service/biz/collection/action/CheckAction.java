package com.das.museum.service.biz.collection.action;

import com.das.museum.domain.enums.CollectionBizTypeEnum;
import lombok.Data;

@Data
public class CheckAction {
    private Long companyId;
    private Long creator;
    private String creatorName;
    private Long modelId;
    private CollectionBizTypeEnum belongModel;
    private String checkStatus;
    private String checkOpinion;
    private String collectionId;

    /**
     * 是否自动通过 0 否 1 是
     */
    private byte autoRemove;
}
