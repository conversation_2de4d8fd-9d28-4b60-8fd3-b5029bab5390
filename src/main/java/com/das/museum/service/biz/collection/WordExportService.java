package com.das.museum.service.biz.collection;

import java.util.List;
import java.util.Map;
import java.io.File;

public interface WordExportService {
    /**
     * 导出藏品征集凭证
     * @param companyId 公司id
     * @param collectId 藏品凭证编号
     * @return
     */
    Map<String, Object> exportCollectWord(Long companyId, Long collectId);

    /**
     * 导出藏品鉴定凭证
     * @param companyId 公司id
     * @param collectId 藏品凭证编号
     * @return
     */
    Map<String, Object> exportIdentifyWord(Long companyId, Long identifyId);

    /**
     * 导出藏品入藏凭证
     * @param companyId 公司id
     * @param inTibetanId 凭证id
     * @return
     */
    Map<String, Object> exportInTibetanWord(Long companyId, Long inTibetanId);

    Map<String, Object> exportCollectionRecordWord(Long companyId, String registerNo);
    Map<String, Object> exportCollectionCardWord(Long companyId, String registerNo);
    Map<String, Object> exportCollectionIdentifyBookWord(Long companyId, String registerNo);

    Map<String, Object> exportCollectionLedgerWord(Long companyId,List<String> registerNos,String registerNo,
                                                   String classifyDesc,String name,Long age,Long identifyLevel,String texture,
                                                   Long source,Long category,Long completeDegree);

    List<File> batchExportCollectWord(Long companyId, List<Long> collectIds);
    List<File> batchExportIdentifyWord(Long companyId, List<Long> identifyIds);
    List<File> batchExportInTibetanWord(Long companyId, List<Long> inTibetanIds);

    List<File> batchExportCollectionRecordWord(Long companyId, List<String> registerNoList);
    List<File> batchExportCollectionCardWord(Long companyId, List<String> registerNoList);
    List<File> batchExportCollectionIdentifyBookWord(Long companyId, List<String> registerNoList);
}
