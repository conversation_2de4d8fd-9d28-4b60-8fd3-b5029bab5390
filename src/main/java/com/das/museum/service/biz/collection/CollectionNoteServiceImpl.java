package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionNoteAction;
import com.das.museum.service.biz.collection.action.EditCollectionNoteAction;
import com.das.museum.service.biz.collection.action.NoteQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionNoteDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionNotePageDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectionNoteEntity;
import com.das.museum.domain.collection.repository.CollectionNoteRepository;
import com.das.museum.infr.dataobject.ScsCollectionNoteDO;
import com.das.museum.infr.mapper.ScsCollectionNoteMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CollectionNoteServiceImpl implements CollectionNoteService {

    @Resource
    private CollectionNoteRepository collectionNoteRepository;

    @Resource
    private ScsCollectionNoteMapper scsCollectionNoteMapper;

    @Resource
    private DocumentService documentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCollectionNote(AddCollectionNoteAction action) {
        CollectionNoteEntity entity = this.convertCollectionNoteEntity(action);
        this.collectionNoteRepository.saveCollectionNote(entity);
        return entity.getCollectionNoteId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionNote(EditCollectionNoteAction action) {
        Long companyId = action.getCompanyId();
        Long collectionNoteId = action.getCollectionNoteId();
        CollectionNoteDetailDTO collectionNoteDTO = this.queryByCollectionNoteId(companyId, collectionNoteId);
        if (Objects.isNull(collectionNoteDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "便签不存在");
        }
        CollectionNoteEntity entity = this.convertCollectionNoteEntity(action);
        this.collectionNoteRepository.editCollectionNote(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByCollectionNoteId(Long companyId, Long collectionNoteId) {
        CollectionNoteDetailDTO collectionNoteDTO = this.queryByCollectionNoteId(companyId, collectionNoteId);
        if (Objects.isNull(collectionNoteDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "便签不存在");
        }
        this.collectionNoteRepository.delByCollectionNoteId(companyId, collectionNoteId);
        if (StringUtils.isBlank(collectionNoteDTO.getDocumentId())) {
            return;
        }
        this.documentService.delByDocIds(companyId, collectionNoteDTO.getDocumentId());
    }

    @Override
    public SimplePageInfo<CollectionNotePageDTO> queryPageByCondition(NoteQueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionNoteDO> collectionNoteDOList = this.scsCollectionNoteMapper.listPageByCondition(companyId,
                action.getCollectionId(), action.getSortBy());
        if (CollectionUtils.isEmpty(collectionNoteDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<CollectionNotePageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectionNoteDOList, CollectionNotePageDTO.class);
        List<CollectionNotePageDTO> collectionNotePageDTOList = collectionNoteDOList.stream().map(collectionNoteDO -> {
            CollectionNotePageDTO collectionNotePageDTO = BeanCopyUtils.copyByJSON(collectionNoteDO, CollectionNotePageDTO.class);
            collectionNotePageDTO.setCollectionNoteId(collectionNoteDO.getId());
            return collectionNotePageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectionNotePageDTOList);
        return pageInfo;
    }

    @Override
    public CollectionNoteDetailDTO queryByCollectionNoteId(Long companyId, Long collectionNoteId) {
        ScsCollectionNoteDO collectionNoteDO = this.scsCollectionNoteMapper.selectById(companyId, collectionNoteId);
        if (Objects.isNull(collectionNoteDO)) {
            return null;
        }
        CollectionNoteDetailDTO collectionNoteDTO = BeanCopyUtils.copyByJSON(collectionNoteDO, CollectionNoteDetailDTO.class);
        collectionNoteDTO.setCollectionNoteId(collectionNoteDO.getId());
        return collectionNoteDTO;
    }
    private CollectionNoteEntity convertCollectionNoteEntity(AddCollectionNoteAction action) {
        CollectionNoteEntity entity = new CollectionNoteEntity();
        entity.setCollectionId(action.getCollectionId());
        entity.setCompanyId(action.getCompanyId());
        entity.setContent(action.getContent());
        entity.setDocumentId(action.getDocumentId());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        return entity;
    }
    private CollectionNoteEntity convertCollectionNoteEntity(EditCollectionNoteAction action) {
        CollectionNoteEntity entity = new CollectionNoteEntity();
        entity.setCollectionNoteId(action.getCollectionNoteId());
        entity.setCompanyId(action.getCompanyId());
        entity.setContent(action.getContent());
        entity.setDocumentId(action.getDocumentId());
        entity.setModifier(action.getModifier());
        return entity;
    }
}
