package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/16
 */
@Data
public class ExhibitRegisterPageDTO implements Serializable {

    private static final long serialVersionUID = -5100168185967799787L;

    private Long registerInfoId;
    private Long companyId;
    private Long collectionId;
    private String registerName;
    private String registerNo;
    private String name;
    private Long identifyLevel;
    private String identifyLevelName;
    private Long age;
    private String ageName;
    private Long completeDegree;
    private String completeDegreeName;
    private String texture;
    private String textureName;
    private Long source;
    private String sourceName;
    private String size;
    private Integer twoImageCount;
    private Integer threeCount;
    private List<CommonDocumentDTO> commonDocumentList;
    private Integer isExistSame;
    private Date gmtCreate;
}
