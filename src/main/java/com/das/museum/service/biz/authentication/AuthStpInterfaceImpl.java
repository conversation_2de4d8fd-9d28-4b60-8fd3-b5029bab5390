package com.das.museum.service.biz.authentication;

import cn.dev33.satoken.stp.StpInterface;
import com.das.museum.infr.dataobject.AccRoleDO;
import com.das.museum.infr.dataobject.AccRolePermissionRelDO;
import com.das.museum.infr.dataobject.AccUserDO;
import com.das.museum.infr.dataobject.AccUserRoleRelDO;
import com.das.museum.infr.mapper.AccRoleMapper;
import com.das.museum.infr.mapper.AccRolePermissionRelMapper;
import com.das.museum.infr.mapper.AccUserMapper;
import com.das.museum.infr.mapper.AccUserRoleRelMapper;
import com.das.museum.service.enums.DeleteStatusEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AuthStpInterfaceImpl implements StpInterface {

    @Resource
    private AccUserMapper accUserMapper;

    @Resource
    private AccUserRoleRelMapper accUserRoleRelMapper;

    @Resource
    private AccRolePermissionRelMapper accRolePermissionRelMapper;

    @Resource
    private AccRoleMapper accRoleMapper;

    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        if (Objects.isNull(loginId)) {
            return Lists.newArrayList();
        }
        Long userId;
        if (loginId instanceof String) {
            String userIdTemp = (String) loginId;
            userId = Long.valueOf(userIdTemp);
        } else {
            userId = (Long) loginId;
        }

        AccUserDO accUserDO = this.accUserMapper.selectByUserIdAndIsDelete(userId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(accUserDO)) {
            return Lists.newArrayList();
        }
        List<AccUserRoleRelDO> userRoleRelDOList = this.accUserRoleRelMapper.listByUserId(null, userId);
        if (CollectionUtils.isEmpty(userRoleRelDOList)) {
            return Lists.newArrayList();
        }
        List<Long> roleIds = userRoleRelDOList.stream().map(AccUserRoleRelDO::getRoleId).collect(Collectors.toList());
        //获取角色状态，只保留 未删除状态和启动状态的角色
        List<AccRoleDO> accRoleDOList = accRoleMapper.listByRoleIds(null,roleIds,DeleteStatusEnum.否.getCode().byteValue());
        List<Long> usedRoleIds = accRoleDOList.stream().filter(accRoleDO -> accRoleDO.getStatus() == 1).map(AccRoleDO::getId).collect(Collectors.toList());

        List<AccRolePermissionRelDO> rolePermissionRelDOList = this.accRolePermissionRelMapper.listByRoleIds(null, usedRoleIds);
        if (CollectionUtils.isEmpty(rolePermissionRelDOList)) {
            return Lists.newArrayList();
        }
        return rolePermissionRelDOList.stream().map(AccRolePermissionRelDO::getPermissionCode).distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        return Lists.newArrayList();
    }
}
