package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionMountDetailDTO implements Serializable {
    private static final long serialVersionUID = -3704071782463759169L;
    private Long collectionMountId;
    private Long companyId;
    private Long collectionId;
    private String contractUnit;
    private Long producerId;
    private String producerName;
    private String remark;
    private Date mountDate;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private List<CommonDocumentDTO> commDocumentList;
}