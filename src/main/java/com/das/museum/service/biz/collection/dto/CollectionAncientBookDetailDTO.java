package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionAncientBookDetailDTO implements Serializable {
    private static final long serialVersionUID = -5666276125231276739L;
    private Long collectionAncientBookId;
    private Long companyId;
    private Long collectionId;
    private String classification;
    private String version;
    private String archive;
    private String frame;
    private String format;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private List<CommonDocumentDTO> commDocumentList;
}