package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/25
 */
@Data
public class AddLogoffAction implements Serializable {
    private static final long serialVersionUID = 7607794756336883858L;
    private Long companyId;
    private String logoffCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private Date logoffDate;
    private String approveNo;
    private String documentId;
    private Long creator;
    private String creatorName;
    private List<AddLogoffCollectionAction> logoffCollectionList;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}
