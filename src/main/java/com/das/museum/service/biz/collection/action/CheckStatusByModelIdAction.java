package com.das.museum.service.biz.collection.action;

import com.das.museum.domain.enums.CollectionBizTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class CheckStatusByModelIdAction implements Serializable {
    private static final long serialVersionUID = -7421947169380953027L;
    private Long companyId;
    private Long modelId;
    private CollectionBizTypeEnum belongModel;
}
