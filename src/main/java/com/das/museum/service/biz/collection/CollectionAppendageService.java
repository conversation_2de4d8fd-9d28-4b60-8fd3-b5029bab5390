package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionAppendageAction;
import com.das.museum.service.biz.collection.action.AppendageQueryPageByConditionAction;
import com.das.museum.service.biz.collection.action.EditCollectionAppendageAction;
import com.das.museum.service.biz.collection.dto.CollectionAppendageDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionAppendagePageDTO;
import com.das.museum.common.bo.SimplePageInfo;

public interface CollectionAppendageService {
    Long addCollectionAppendage(AddCollectionAppendageAction action);
    void editCollectionAppendage(EditCollectionAppendageAction action);
    void delByCollectionAppendageId(Long companyId, Long collectionAppendageId);
    SimplePageInfo<CollectionAppendagePageDTO> queryPageByCondition(AppendageQueryPageByConditionAction action);
    CollectionAppendageDetailDTO queryByCollectionAppendageId(Long companyId, Long collectionAppendageId);
}
