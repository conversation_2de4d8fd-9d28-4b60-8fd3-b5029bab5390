package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionDamageDetailDTO implements Serializable {
    private static final long serialVersionUID = -7762790280729852588L;
    private Long collectionDamageId;
    private Long collectionId;
    private String address;
    private Long responsibleId;
    private String responsibleName;
    private String damageInfo;
    private String reason;
    private String handleInfo;
    private String processInfo;
    private String remark;
    private Date damageDate;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private List<CommonDocumentDTO> commDocumentList;
}