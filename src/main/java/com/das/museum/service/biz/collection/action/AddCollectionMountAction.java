package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AddCollectionMountAction implements Serializable {
    private static final long serialVersionUID = -182486225125995862L;
    private Long companyId;
    private Long collectionId;
    private String contractUnit;
    private Long producerId;
    private String producerName;
    private String remark;
    private Date mountDate;
    private String documentId;
    private Long creator;
}
