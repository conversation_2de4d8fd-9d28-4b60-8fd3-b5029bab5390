package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AddOutInWarehouseAction implements Serializable {
    private static final long serialVersionUID = 7026920264450651818L;
    private Long companyId;
    private String outInWarehouseCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private Long reason;
    private String reasonInfo;
    private String useId;
    private String userDepartmentId;
    private String useAddress;
    private String remark;
    private Date useDate;
    private Date preReturnDate;
    private String documentId;
    private List<Long> collectionIds;
    private Long creator;
    private String creatorName;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;

}
