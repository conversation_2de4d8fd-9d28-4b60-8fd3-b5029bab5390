package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionRubbingDetailDTO implements Serializable {
    private static final long serialVersionUID = 623704937564745066L;
    private Long collectionRubbingId;
    private Long companyId;
    private Long collectionId;
    private String bookStyle;
    private String mountSituation;
    private String rubbingNo;
    private String recognition;
    private String seal;
    private String postscript;
    private String interpretation;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private List<CommonDocumentDTO> commDocumentList;
}