package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import com.das.museum.domain.enums.RegisterStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class RegisterQueryPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = -5715121492648590684L;
    private Long companyId;
    private String registerNo;
    private Long classify;
    private String classifyDesc;
    private String name;
    private String sortBy;
    private List<RegisterStatusEnum> registerStatus;
    private List<Long> collectionIds;
    private Date startModified;
    private Date endModified;
    private String checkStatus;
    private Long age;
    private Long identifyLevel;
    private String texture;
    private Long source;
    private Long warehouseId;
    private Long category;
    private Long completeDegree;
    private Integer isUserChecked;
}
