package com.das.museum.service.biz.collection;

import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.constants.CollectionConstant;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.PaginationUtils;
import com.das.museum.domain.collection.entity.InTibetanCertificateEntity;
import com.das.museum.domain.collection.repository.CollectionRepository;
import com.das.museum.domain.collection.repository.InTibetanCertificateRepository;
import com.das.museum.domain.collection.valueobject.CollectionVO;
import com.das.museum.domain.enums.CheckMainStatusEnum;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.infr.dataobject.CommIncrSegmentDO;
import com.das.museum.infr.dataobject.ScsInTibetanCertificateInfoDO;
import com.das.museum.infr.mapper.CommIncrSegmentMapper;
import com.das.museum.infr.mapper.ScsInTibetanCertificateInfoMapper;
import com.das.museum.infr.query.InTibetanConditionQuery;
import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.*;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.service.biz.user.UserService;
import com.das.museum.service.enums.InputModeEnum;
import com.das.museum.service.enums.InputTypeEnum;
import com.das.museum.service.enums.ProcessStatusEnum;
import com.das.museum.service.enums.YesOrNoEnum;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InTibetanServiceImpl implements InTibetanService {

    @Resource
    private InTibetanCertificateRepository inTibetanCertificateRepository;

    @Resource
    private CollectionRepository collectionRepository;

    @Resource
    private ScsInTibetanCertificateInfoMapper scsInTibetanCertificateInfoMapper;

    @Resource
    private DocumentService documentService;

    @Resource
    private UserService userService;

    @Resource
    private CollectionService collectionService;

    @Resource
    private CollectionRegisterService collectionRegisterService;

    @Resource
    private CheckService checkService;

    @Resource
    private CommIncrSegmentMapper commIncrSegmentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addInTibetanCertificate(AddInTibetanAction action) {
        Long companyId = action.getCompanyId();
        String bizDimension = DateUtils.formatDateYYYMMDD().substring(0, 4);
        CommIncrSegmentDO commIncrSegmentDO = this.commIncrSegmentMapper
                .selectByCompanyIdAndBizCode(action.getCompanyId(), CollectionBizTypeEnum.TBT.getCode(), bizDimension);
        if (Objects.isNull(commIncrSegmentDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "入藏凭证自动编号配置不存在");
        }
        Long bizCurrIndex = commIncrSegmentDO.getBizCurrIndex();
        String inTibetanCertificateNo = action.getPrefixName()
                + CollectionConstant.IN_TIBETAN_CERTIFICATE_MIDDLE + (bizCurrIndex + 1)
                + CollectionConstant.CERTIFICATE_TAIL;
        InTibetanCertificateEntity entity = this.convertInTibetanCertificateEntity(action);
        entity.setSuffixNumber(bizCurrIndex + 1);
        entity.setInTibetanCertificateNo(inTibetanCertificateNo);
        entity.setAutoAudit(action.getAutoAudit());
        this.inTibetanCertificateRepository.saveInTibetanCertificate(entity);
        Long inTibetanId = entity.getInTibetanId();
        this.collectionRepository.saveByBizIdAndBizType(companyId, inTibetanId,
                CollectionBizTypeEnum.TBT.getCode(), entity.getInTibetanCollectionList());

        /*InTibetanCertificateEntity updateEntity = new InTibetanCertificateEntity();
        updateEntity.setInTibetanId(inTibetanId);
        updateEntity.setSuffixNumber(inTibetanId);
        updateEntity.setInTibetanCertificateNo(entity.getPrefixName()
                + CollectionConstant.IN_TIBETAN_CERTIFICATE_MIDDLE + updateEntity.getSuffixNumber()
                + CollectionConstant.CERTIFICATE_TAIL);
        updateEntity.setAutoAudit(action.getAutoAudit());
        this.inTibetanCertificateRepository.editInTibetanCertificate(updateEntity);*/

        int row = this.commIncrSegmentMapper.updateByBizCurrIndex(commIncrSegmentDO.getId(), bizCurrIndex);
        if (row <= 0) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "入藏凭证自动获取编号失败");
        }
        // 自动提审
        if(action.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(action.getCreator());
            submitCheckAction.setCompanyId(action.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.TBT);
            submitCheckAction.setModelId(inTibetanId);
            submitCheckAction.setCreatorName(action.getCreatorName());
            this.checkService.submitCheck(submitCheckAction);
        }

        return inTibetanId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editInTibetanCertificate(Long companyId, Long inTibetanId, EditInTibetanAction action) {
        ScsInTibetanCertificateInfoDO certificateInfoDO = this.inTibetanCertificateCheck(companyId, inTibetanId);
        InTibetanCertificateEntity entity = this.convertInTibetanCertificateEntity(action);
        entity.setSuffixNumber(certificateInfoDO.getSuffixNumber());
        entity.setInTibetanCertificateNo(action.getPrefixName()
                + CollectionConstant.IN_TIBETAN_CERTIFICATE_MIDDLE + certificateInfoDO.getSuffixNumber()
                + CollectionConstant.CERTIFICATE_TAIL);
        List<Long> excludeCollectionIds = null;
        if (CollectionUtils.isNotEmpty(entity.getInTibetanCollectionList())) {
            excludeCollectionIds = entity.getInTibetanCollectionList().stream()
                    .map(CollectionVO::getCollectionId).distinct().collect(Collectors.toList());
        }
        this.collectionRepository.delByBizIdAndBizTypeExcludeCLT(companyId, inTibetanId, CollectionBizTypeEnum.TBT.getCode(),
                excludeCollectionIds, InputModeEnum.CNE.getCode(), InputTypeEnum.MLE.getCode());
        this.inTibetanCertificateRepository.editInTibetanCertificate(entity);
        this.collectionRepository.saveByBizIdAndBizType(companyId, inTibetanId,
                CollectionBizTypeEnum.TBT.getCode(), entity.getInTibetanCollectionList());

        // 自动提审
        if(action.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(action.getModifier());
            submitCheckAction.setCompanyId(action.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.TBT);
            submitCheckAction.setModelId(inTibetanId);
            submitCheckAction.setCreatorName(action.getModifierName());
            this.checkService.submitCheck(submitCheckAction);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByInTibetanCertificateById(Long companyId, Long inTibetanId) {
        ScsInTibetanCertificateInfoDO certificateInfoDO = this.inTibetanCertificateCheck(companyId, inTibetanId);
        this.inTibetanCertificateRepository.delInTibetanCertificateById(companyId, inTibetanId);
        collectionRepository.delByBizIdAndBizTypeExcludeCLT(companyId, inTibetanId, CollectionBizTypeEnum.TBT.getCode(),
                Lists.newArrayList(), InputModeEnum.CNE.getCode(), InputTypeEnum.MLE.getCode());
        this.documentService.delByDocIds(companyId, certificateInfoDO.getDocumentId());
    }

    @Override
    public InTibetanInfoDetailDTO queryDetailInfoById(Long companyId, Long inTibetanId) {
        ScsInTibetanCertificateInfoDO inTibetanDO = this.scsInTibetanCertificateInfoMapper.selectById(companyId, inTibetanId);
        if (Objects.isNull(inTibetanDO)) {
            return null;
        }
        InTibetanInfoDetailDTO inTibetanDTO = BeanCopyUtils.copyByJSON(inTibetanDO, InTibetanInfoDetailDTO.class);
        inTibetanDTO.setInTibetanId(inTibetanDO.getId());
        String documentId = inTibetanDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return inTibetanDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        inTibetanDTO.setCommDocumentList(commDocumentList);
        return inTibetanDTO;
    }

    @Override
    public SimplePageInfo<InTibetanInfoPageDTO> queryPageByConditionQuery(InTibetanConditionQueryAction action) {
        Long companyId = action.getCompanyId();
        // action.startPage();
        InTibetanConditionQuery query = new InTibetanConditionQuery();
        query.setCollectionName(action.getCollectionName());
        query.setRegisterNo(action.getRegisterNo());
        query.setInTibetanCertificateNo(action.getInTibetanCertificateNo());
        query.setSortBy(action.getSortBy());
        List<ScsInTibetanCertificateInfoDO> inTibetanList = this.scsInTibetanCertificateInfoMapper
                .listPageByCondition(companyId, action.getInTibetanCertificateNo(), action.getSortBy(), query);
        if (CollectionUtils.isEmpty(inTibetanList)) {
            return new SimplePageInfo<>();
        }
        /*SimplePageInfo<InTibetanInfoPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(inTibetanList, InTibetanInfoPageDTO.class);*/
        List<InTibetanInfoPageDTO> inTibetanInfoPageDTOList = this.setInTibetanAttributes(companyId, inTibetanList, action.getCheckStatus(), action.getIsUserChecked());
        // pageInfo.setList(inTibetanInfoPageDTOList);
        return PaginationUtils.pagination(inTibetanInfoPageDTOList, action.getPageNum(), action.getPageSize());
    }

    @Override
    public List<CommonDocumentDTO> queryAttachmentByInTibetanId(Long companyId, Long inTibetanId) {
        ScsInTibetanCertificateInfoDO inTibetanDO = this.scsInTibetanCertificateInfoMapper.selectById(companyId, inTibetanId);
        if (Objects.isNull(inTibetanDO)) {
            return Lists.newArrayList();
        }
        String documentId = inTibetanDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return Lists.newArrayList();
        }
        return this.documentService.listByDocIds(companyId, documentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignWarehouseByCollectionIds(AssignWarehouseAction action) {
        Long companyId = action.getCompanyId();
        Long inTibetanId = action.getInTibetanId();
        List<Long> collectionIds = action.getCollectionIds();
        Long warehouseId = action.getWarehouseId();
        Long modifier = action.getModifier();
        ScsInTibetanCertificateInfoDO inTibetanDO = this.scsInTibetanCertificateInfoMapper.selectById(companyId, inTibetanId);
        if (Objects.isNull(inTibetanDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "入藏凭证信息不存在");
        }
        QueryCheckStatusAction queryCheckStatusAction = new QueryCheckStatusAction();
        queryCheckStatusAction.setBelongModel(CollectionBizTypeEnum.TBT);
        queryCheckStatusAction.setCompanyId(companyId);
        queryCheckStatusAction.setModelId(inTibetanId);
        CheckMainStatusEnum checkMainStatusEnum = this.checkService.queryCheckStatus(queryCheckStatusAction);
        if (Objects.isNull(checkMainStatusEnum) || !checkMainStatusEnum.equals(CheckMainStatusEnum.ATP)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "入藏凭证信息审核未通过");
        }
        if (Objects.nonNull(warehouseId)) {
            this.collectionService.assignWarehouseByCollectionIds(companyId, inTibetanId, CollectionBizTypeEnum.TBT.getCode(),
                    collectionIds, warehouseId, modifier);
        }
        collectionIds.forEach(collectionId -> this.collectionRegisterService.generateCollectionRegister(companyId, modifier, collectionId));

    }

    @Override
    public List<CollectCheckedCollectionInfoDTO> queryCheckedCollectionInfoList(Long companyId, Long inTibetanId) {
        Map<Long,Object> collectionMap = new HashMap<>();
        List<CollectionInfoDetailDTO> collectionList = this.collectionService.queryByBizIdAndBizType(companyId,inTibetanId,CollectionBizTypeEnum.TBT);
        List<Long> collectionIds = collectionList.stream().map(CollectionInfoDetailDTO::getCollectionId).collect(Collectors.toList());
        QueryCheckedCollectionIdAction action  = new QueryCheckedCollectionIdAction();
        action.setCompanyId(companyId);
        action.setBelongModel(CollectionBizTypeEnum.TBT);
        action.setModelId(inTibetanId);
        action.setCollectionIds(collectionIds);
        List<Long> checkCollectionIdList =  checkService.queryCheckedCollectionId(action);
        for(Long id:checkCollectionIdList){
            collectionMap.put(id,"");
        }

        List<CollectCheckedCollectionInfoDTO> resultList = collectionList.stream().map(collectionInfoDetailDTO -> {
            CollectCheckedCollectionInfoDTO collectCheckedCollectionInfoDTO =  BeanCopyUtils.copyByJSON(collectionInfoDetailDTO, CollectCheckedCollectionInfoDTO.class);
            if(collectionMap.containsKey(collectionInfoDetailDTO.getCollectionId())){
                collectCheckedCollectionInfoDTO.setChecked(true);
            }else{
                collectCheckedCollectionInfoDTO.setChecked(false);
            }
            return collectCheckedCollectionInfoDTO;
        }).collect(Collectors.toList());
        return resultList;
    }

    private ScsInTibetanCertificateInfoDO inTibetanCertificateCheck(Long companyId, Long inTibetanId) {
        ScsInTibetanCertificateInfoDO certificateInfoDO =
                this.scsInTibetanCertificateInfoMapper.selectByPrimaryKey(inTibetanId);
        if (Objects.isNull(certificateInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "入馆凭证信息不存在");
        }
        CheckStatusByModelIdAction checkStatusByModelIdAction = new CheckStatusByModelIdAction();
        checkStatusByModelIdAction.setBelongModel(CollectionBizTypeEnum.TBT);
        checkStatusByModelIdAction.setCompanyId(companyId);
        checkStatusByModelIdAction.setModelId(inTibetanId);
        this.checkService.checkStatusByModelId(checkStatusByModelIdAction);
        return certificateInfoDO;
    }
    private List<InTibetanInfoPageDTO> setInTibetanAttributes(Long companyId, List<ScsInTibetanCertificateInfoDO> inTibetanList, String checkStatus, Integer isUserChecked) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<Long> userIds = inTibetanList.stream()
                .map(ScsInTibetanCertificateInfoDO::getCreator).distinct().collect(Collectors.toList());
        Map<Long, String> userNameMap = this.userService.queryUserNameMap(companyId, userIds);
        List<Long> inMuseumIds = inTibetanList.stream()
                .map(ScsInTibetanCertificateInfoDO::getId).collect(Collectors.toList());
        Map<Long, List<BizCollectionRelDTO>> bizCollectionRelMap = this.collectionService.queryBizCollectionRelMap(companyId,
                inMuseumIds, CollectionBizTypeEnum.TBT);
        QueryBatchCheckStatusAction batchCheckStatusAction = new QueryBatchCheckStatusAction();
        batchCheckStatusAction.setBelongModel(CollectionBizTypeEnum.TBT);
        batchCheckStatusAction.setCompanyId(companyId);
        batchCheckStatusAction.setModelIds(inMuseumIds);
        Map<Long, CheckCurrentInfoDTO> checkInfoMap = this.checkService.queryBatchCheckStatus(batchCheckStatusAction);
        return inTibetanList.stream().map(inTibetanDO -> {
            InTibetanInfoPageDTO inTibetanInfoPageDTO = BeanCopyUtils.copyByJSON(inTibetanDO, InTibetanInfoPageDTO.class);
            inTibetanInfoPageDTO.setInTibetanId(inTibetanDO.getId());
            if (checkInfoMap != null && checkInfoMap.containsKey(inTibetanDO.getId())) {
                CheckCurrentInfoDTO checkCurrentInfoDTO = checkInfoMap.get(inTibetanDO.getId());
                inTibetanInfoPageDTO.setCheckStatus(checkCurrentInfoDTO.getCheckStatus().getCode());
                inTibetanInfoPageDTO.setCheckStatusDesc(checkCurrentInfoDTO.getCheckStatus().getDesc());
                inTibetanInfoPageDTO.setNeedManName(checkCurrentInfoDTO.getNeedManName());
                inTibetanInfoPageDTO.setHasChecked(YesOrNoEnum.否.getCode().byteValue());
                if(Objects.nonNull(checkCurrentInfoDTO.getNeedManId()) && checkCurrentInfoDTO.getNeedManId().contains(userBO.getUserId())){
                    inTibetanInfoPageDTO.setHasChecked(YesOrNoEnum.是.getCode().byteValue());
                }
            }
            if (userNameMap != null && !userNameMap.isEmpty()) {
                inTibetanInfoPageDTO.setCreatorName(userNameMap.get(inTibetanDO.getCreator()));
            }
            if(!Objects.isNull(inTibetanDO.getGmtCreate())){
                inTibetanInfoPageDTO.setGmtCreate(DateUtils.formatDateYYYMMDDHHmmss(inTibetanDO.getGmtCreate()));
            }
            if (bizCollectionRelMap != null && !bizCollectionRelMap.isEmpty()) {
                List<BizCollectionRelDTO> collectionRelDTOList = bizCollectionRelMap.get(inTibetanDO.getId());
                if (CollectionUtils.isNotEmpty(collectionRelDTOList)) {
                    inTibetanInfoPageDTO.setCollectionCount(collectionRelDTOList.size());
                } else {
                    inTibetanInfoPageDTO.setCollectionCount(0);
                }
            }
            if (StringUtils.isNotBlank(checkStatus)) {
                if (checkStatus.equals(inTibetanInfoPageDTO.getCheckStatus())) {
                    return inTibetanInfoPageDTO;
                } else {
                    return null;
                }
            }
            if (isUserChecked == 1) {
                if (inTibetanInfoPageDTO.getHasChecked() == YesOrNoEnum.否.getCode().byteValue()) {
                    return null;
                }
            }
            return inTibetanInfoPageDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
    private InTibetanCertificateEntity convertInTibetanCertificateEntity(AddInTibetanAction action) {
        InTibetanCertificateEntity entity = new InTibetanCertificateEntity();
        entity.setInTibetanCertificateNo(action.getPrefixName()
                + CollectionConstant.IN_TIBETAN_CERTIFICATE_MIDDLE);
        entity.setPrefixName(action.getPrefixName());
        entity.setSuffixNumber(action.getSuffixNumber());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        entity.setDocumentId(action.getDocumentId());
        entity.setCompanyId(action.getCompanyId());
        entity.setAutoAudit(action.getAutoAudit());
        List<CollectionVO> collectionVOList = this.convertInTibetanCollectionVO(action.getInTibetanCollectionList());
        entity.setInTibetanCollectionList(collectionVOList);
        return entity;
    }
    private InTibetanCertificateEntity convertInTibetanCertificateEntity(EditInTibetanAction action) {
        InTibetanCertificateEntity entity = new InTibetanCertificateEntity();
        entity.setInTibetanId(action.getInTibetanId());
        entity.setInTibetanCertificateNo(action.getInTibetanCertificateNo());
        entity.setPrefixName(action.getPrefixName());
        entity.setSuffixNumber(action.getSuffixNumber());
        entity.setModifier(action.getModifier());
        entity.setDocumentId(action.getDocumentId());
        entity.setAutoAudit(action.getAutoAudit());
        List<CollectionVO> collectionVOList = this.convertInTibetanCollectionVO(action.getInTibetanCollectionList());
        entity.setInTibetanCollectionList(collectionVOList);
        return entity;
    }
    private List<CollectionVO> convertInTibetanCollectionVO(List<AddInTibetanCollectionAction> inTibetanCollectionList) {
        if (CollectionUtils.isEmpty(inTibetanCollectionList)) {
            return Lists.newArrayList();
        }
        return inTibetanCollectionList.stream().map(collection -> {
            CollectionVO collectionVO = new CollectionVO();
            collectionVO.setRegisterNo(collection.getRegisterNo());
            collectionVO.setCollectionId(collection.getCollectionId());
            collectionVO.setName(collection.getName());
            collectionVO.setActualCount(collection.getActualCount());
            collectionVO.setCountUnit(collection.getCountUnit());
            collectionVO.setCollectionCount(collection.getCollectionCount());
            collectionVO.setEra(collection.getEra());
            collectionVO.setAge(collection.getAge());
            collectionVO.setTextureCategory(collection.getTextureCategory());
            collectionVO.setTexture(collection.getTexture());
            collectionVO.setSize(collection.getSize());
            collectionVO.setSizeUnit(collection.getSizeUnit());
            collectionVO.setCompleteDegree(collection.getCompleteDegree());
            collectionVO.setCompleteInfo(collection.getCompleteInfo());
            collectionVO.setInitialLevel(collection.getInitialLevel());
            collectionVO.setAmount(collection.getAmount());
            collectionVO.setCollectType(collection.getCollectType());
            collectionVO.setCollectDate(collection.getCollectDate());
            collectionVO.setInMuseumCertificateNo(collection.getInMuseumCertificateNo());
            collectionVO.setInMuseumDate(collection.getInMuseumDate());
            collectionVO.setInTibetanDate(collection.getInTibetanDate());
            collectionVO.setAppraiserName(collection.getAppraiserName());
            collectionVO.setProcessStatus(ProcessStatusEnum.NTBT.getCode());
            collectionVO.setHolder(collection.getHolder());
            collectionVO.setPayVoucherNo(collection.getPayVoucherNo());
            collectionVO.setRemark(collection.getRemark());
            collectionVO.setAppendageDesc(collection.getAppendageDesc());
            collectionVO.setSourceInfo(collection.getSourceInfo());
            collectionVO.setIntroduce(collection.getIntroduce());
            collectionVO.setInputMode(collection.getInputMode().getCode());
            collectionVO.setInputType(collection.getInputType().getCode());
            collectionVO.setDocumentId(collection.getDocumentId());
            collectionVO.setCreator(collection.getCreator());
            collectionVO.setModifier(collection.getModifier());
            collectionVO.setCategory(collection.getCategory());
            collectionVO.setQualityRange(collection.getQualityRange());
            collectionVO.setWarehouseId(collection.getWarehouseId());
            return collectionVO;
        }).collect(Collectors.toList());
    }
}
