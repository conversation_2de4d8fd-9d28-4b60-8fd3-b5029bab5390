package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionAppendageAction;
import com.das.museum.service.biz.collection.action.AppendageQueryPageByConditionAction;
import com.das.museum.service.biz.collection.action.EditCollectionAppendageAction;
import com.das.museum.service.biz.collection.dto.CollectionAppendageDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionAppendagePageDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectionAppendageEntity;
import com.das.museum.domain.collection.repository.CollectionAppendageRepository;
import com.das.museum.infr.dataobject.CommClassificationDO;
import com.das.museum.infr.dataobject.ScsCollectionAppendageDO;
import com.das.museum.infr.mapper.CommClassificationMapper;
import com.das.museum.infr.mapper.ScsCollectionAppendageMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CollectionAppendageServiceImpl implements CollectionAppendageService {
    @Resource
    private CollectionAppendageRepository collectionAppendageRepository;
    @Resource
    private ScsCollectionAppendageMapper scsCollectionAppendageMapper;
    @Resource
    private DocumentService documentService;
    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCollectionAppendage(AddCollectionAppendageAction action) {
        CollectionAppendageEntity entity = this.convertCollectionAppendageEntity(action);
        this.collectionAppendageRepository.saveCollectionAppendage(entity);
        return entity.getCollectionAppendageId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionAppendage(EditCollectionAppendageAction action) {
        Long companyId = action.getCompanyId();
        Long collectionAppendageId = action.getCollectionAppendageId();
        CollectionAppendageDetailDTO collectionAppendageDTO = this.queryByCollectionAppendageId(companyId, collectionAppendageId);
        if (Objects.isNull(collectionAppendageDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "附属物件不存在");
        }
        CollectionAppendageEntity entity = this.convertCollectionAppendageEntity(action);
        this.collectionAppendageRepository.editCollectionAppendage(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByCollectionAppendageId(Long companyId, Long collectionAppendageId) {
        CollectionAppendageDetailDTO collectionAppendageDTO = this.queryByCollectionAppendageId(companyId, collectionAppendageId);
        if (Objects.isNull(collectionAppendageDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "附属物件不存在");
        }
        this.collectionAppendageRepository.delByCollectionAppendageId(companyId, collectionAppendageId);
        if (StringUtils.isBlank(collectionAppendageDTO.getDocumentId())) {
            return;
        }
        this.documentService.delByDocIds(companyId, collectionAppendageDTO.getDocumentId());
    }

    @Override
    public SimplePageInfo<CollectionAppendagePageDTO> queryPageByCondition(AppendageQueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionAppendageDO> collectionAppendageDOList = this.scsCollectionAppendageMapper.listPageByCondition(companyId,
                action.getCollectionId(), action.getSortBy());
        if (CollectionUtils.isEmpty(collectionAppendageDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<CollectionAppendagePageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectionAppendageDOList, CollectionAppendagePageDTO.class);
        Map<Long, String> classificationMap = Maps.newHashMap();
        List<Long> completeDegreeIds = collectionAppendageDOList.stream().map(ScsCollectionAppendageDO::getCompleteDegree).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(completeDegreeIds)) {
            List<CommClassificationDO> classificationDOList = this.commClassificationMapper.listByIds(companyId, completeDegreeIds);
            if (CollectionUtils.isNotEmpty(classificationDOList)) {
                classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                        CommClassificationDO::getName, (key1, key2) -> key2));
            }
        }

        Map<Long, String> finalClassificationMap = classificationMap;
        List<CollectionAppendagePageDTO> collectionMountPageDTOList = collectionAppendageDOList.stream().map(collectionAppendageDO -> {
            CollectionAppendagePageDTO collectionAppendagePageDTO = BeanCopyUtils.copyByJSON(collectionAppendageDO, CollectionAppendagePageDTO.class);
            collectionAppendagePageDTO.setCollectionAppendageId(collectionAppendageDO.getId());
            Long completeDegree = collectionAppendagePageDTO.getCompleteDegree();
            if (Objects.nonNull(completeDegree) && finalClassificationMap.containsKey(completeDegree)) {
                collectionAppendagePageDTO.setCompleteDegreeName(finalClassificationMap.get(completeDegree));
            }
            return collectionAppendagePageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectionMountPageDTOList);
        return pageInfo;
    }

    @Override
    public CollectionAppendageDetailDTO queryByCollectionAppendageId(Long companyId, Long collectionAppendageId) {
        ScsCollectionAppendageDO collectionAppendageDO = this.scsCollectionAppendageMapper.selectById(companyId, collectionAppendageId);
        if (Objects.isNull(collectionAppendageDO)) {
            return null;
        }
        CollectionAppendageDetailDTO collectionAppendageDTO = BeanCopyUtils.copyByJSON(collectionAppendageDO, CollectionAppendageDetailDTO.class);
        collectionAppendageDTO.setCollectionAppendageId(collectionAppendageDO.getId());
        String documentId = collectionAppendageDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return collectionAppendageDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        collectionAppendageDTO.setCommDocumentList(commDocumentList);
        return collectionAppendageDTO;
    }

    private CollectionAppendageEntity convertCollectionAppendageEntity(AddCollectionAppendageAction action) {
        CollectionAppendageEntity entity = new CollectionAppendageEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setCollectionId(action.getCollectionId());
        entity.setName(action.getName());
        entity.setActualCount(action.getActualCount());
        entity.setMeteringUnit(action.getMeteringUnit());
        entity.setMeteringUnitName(action.getMeteringUnitName());
        entity.setShape(action.getShape());
        entity.setCompleteDegree(action.getCompleteDegree());
        entity.setSize(action.getSize());
        entity.setTexture(action.getTexture());
        entity.setWeight(action.getWeight());
        entity.setCurrentSituation(action.getCurrentSituation());
        entity.setRemark(action.getRemark());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        entity.setDocumentId(action.getDocumentId());
        return entity;
    }

    private CollectionAppendageEntity convertCollectionAppendageEntity(EditCollectionAppendageAction action) {
        CollectionAppendageEntity entity = new CollectionAppendageEntity();
        entity.setCollectionAppendageId(action.getCollectionAppendageId());
        entity.setCompanyId(action.getCompanyId());
        entity.setName(action.getName());
        entity.setActualCount(action.getActualCount());
        entity.setMeteringUnit(action.getMeteringUnit());
        entity.setMeteringUnitName(action.getMeteringUnitName());
        entity.setShape(action.getShape());
        entity.setCompleteDegree(action.getCompleteDegree());
        entity.setSize(action.getSize());
        entity.setTexture(action.getTexture());
        entity.setWeight(action.getWeight());
        entity.setCurrentSituation(action.getCurrentSituation());
        entity.setRemark(action.getRemark());
        entity.setModifier(action.getModifier());
        entity.setDocumentId(action.getDocumentId());
        return entity;
    }
}
