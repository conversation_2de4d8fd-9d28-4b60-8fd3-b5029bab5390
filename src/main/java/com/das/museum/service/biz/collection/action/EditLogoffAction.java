package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class EditLogoffAction implements Serializable {
    private static final long serialVersionUID = -5231216539169754088L;
    private Long logoffId;
    private Long companyId;
    private String logoffCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private Date logoffDate;
    private String approveNo;
    private String documentId;
    private Long modifier;
    private String modifierName;
    private List<AddLogoffCollectionAction> logoffCollectionList;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}
