package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PageInTibetanAction extends PageInfoBaseReq {
    private static final long serialVersionUID = 7835255106519728332L;
    private Long companyId;
    private String inMuseumCertificateNo;
    private String sortBy;
}
