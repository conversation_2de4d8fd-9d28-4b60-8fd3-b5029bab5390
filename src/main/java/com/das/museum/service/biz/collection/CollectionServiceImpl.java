package com.das.museum.service.biz.collection;

import com.alibaba.fastjson.JSON;
import com.das.museum.domain.enums.CheckMainStatusEnum;
import com.das.museum.service.biz.collection.action.QueryCheckedCollectionIdAction;
import com.das.museum.domain.enums.CheckStatusChildEnum;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.mapper.*;
import com.das.museum.service.biz.collection.dto.BizCollectionRelDTO;
import com.das.museum.service.biz.collection.dto.CollectCheckedCollectionInfoDTO;
import com.das.museum.service.biz.collection.dto.CollectionInfoDetailDTO;
import com.das.museum.service.biz.collection.dto.IdentifyCollectionInfoPageDTO;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.service.enums.FileTypeEnum;
import com.das.museum.common.bo.PageInfoBaseReq;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.infr.dataobjectexpand.IdentifyCollectionInfoDO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
@Slf4j
@Service
public class CollectionServiceImpl implements CollectionService {

    @Resource
    private ScsCollectionInfoMapper scsCollectionInfoMapper;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private CommDocumentMapper commDocumentMapper;

    @Resource
    private ScsBizCollectionRelMapper scsBizCollectionRelMapper;

    @Resource
    private CheckService checkService;

    @Resource
    private CommCheckMainMapper commCheckMainMapper;

    @Resource
    private CommCheckChildMapper commCheckChildMapper;

    @Override
    public SimplePageInfo<CollectCheckedCollectionInfoDTO> queryPageByBizIdAndBizType(Long companyId, Long bizId, CollectionBizTypeEnum bizType, String collectionName, String era, String payVoucherNo,
                                                                                      Integer pageNum, Integer pageSize) {
        PageInfoBaseReq reqPage = new PageInfoBaseReq();
        reqPage.startPage(pageNum, pageSize, null);
        List<ScsCollectionInfoDO> collectionInfoDOList = scsCollectionInfoMapper
                .listByBizIdAndBizType(companyId, bizId, bizType.getCode(), collectionName, era, payVoucherNo);
        SimplePageInfo<CollectCheckedCollectionInfoDTO> pageInfo = SimplePageInfoUtils.convertSimplePageInfoResult(collectionInfoDOList,
                CollectCheckedCollectionInfoDTO.class);
        List<CollectCheckedCollectionInfoDTO> collectionInfoDTOList = null;
        if (CollectionUtils.isNotEmpty(collectionInfoDOList)) {
            List<Long> classificationIds = this.getClassificationIds(collectionInfoDOList);
            Map<Long, String> classificationMap = null;
            if (CollectionUtils.isNotEmpty(classificationIds)) {
                List<CommClassificationDO> classificationDOList = commClassificationMapper.listByIds(companyId, classificationIds);
                classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                        CommClassificationDO::getName, (key1, key2) -> key2));
            }
            List<Long> documentIds = Lists.newArrayList();
            List<Long> collectionIds = Lists.newArrayList();
            collectionInfoDOList.forEach(scsCollectionInfoDO -> {
                collectionIds.add(scsCollectionInfoDO.getId());
                if (StringUtils.isBlank(scsCollectionInfoDO.getDocumentId())) {
                    return;
                }
                List<Long> documentTempIds = Stream.of(scsCollectionInfoDO.getDocumentId().split(","))
                        .map(Long::parseLong).distinct().collect(Collectors.toList());
                documentIds.addAll(documentTempIds);
            });
            Map<Long, CommDocumentDO> documentMap = null;
            if (CollectionUtils.isNotEmpty(documentIds)) {
                List<CommDocumentDO> commDocumentDOList = commDocumentMapper.listByIds(companyId, documentIds);
                documentMap = commDocumentDOList.stream().collect(Collectors.toMap(CommDocumentDO::getId,
                        commDocumentDO -> commDocumentDO));
            }
            Map<Long, String> finalClassificationMap = classificationMap;
            Map<Long, CommDocumentDO> finalDocumentMap = documentMap;
            CommCheckMainDO checkMainDO = this.commCheckMainMapper.selectByModelAndId(companyId, bizType.getCode(), bizId);
            boolean checkMainFlag = Objects.nonNull(checkMainDO) && StringUtils.isNotBlank(checkMainDO.getCheckStatus())
                    && (CheckMainStatusEnum.REVE.getCode().equals(checkMainDO.getCheckStatus())
                    || CheckMainStatusEnum.NATP.getCode().equals(checkMainDO.getCheckStatus())
                    || CheckMainStatusEnum.BACK.getCode().equals(checkMainDO.getCheckStatus())
                    || CheckMainStatusEnum.USB.getCode().equals(checkMainDO.getCheckStatus())
                    || CheckMainStatusEnum.ATP.getCode().equals(checkMainDO.getCheckStatus()));
            boolean isCheckMainEmpty = Objects.isNull(checkMainDO);
            Map<Long,Object> collectionMap = Maps.newHashMap();
            //if (!checkMainFlag) {
                QueryCheckedCollectionIdAction action  = new QueryCheckedCollectionIdAction();
                action.setCompanyId(companyId);
                action.setBelongModel(bizType);
                action.setModelId(bizId);
                action.setCollectionIds(collectionIds);
                List<Long> checkCollectionIdList = checkService.queryCheckedCollectionId(action);
                for(Long id:checkCollectionIdList){
                    collectionMap.put(id,"");
                }
           // };
            boolean isAllAtdFlag = false;
            if (Objects.nonNull(checkMainDO)) {
                List<CommCheckChildDO> checkChildList = this.commCheckChildMapper.selectCheckListByMainId(checkMainDO.getId());
//                isAllAtdFlag = true;
                if (CollectionUtils.isNotEmpty(checkChildList)) {
                    isAllAtdFlag = CheckStatusChildEnum.ATD.getCode().equals(checkChildList.get(0).getCheckStatus());
                }
            }
            boolean finalIsAllAtdFlag = isAllAtdFlag;
            collectionInfoDTOList = collectionInfoDOList.stream().map(collectionInfoDO -> {
                CollectCheckedCollectionInfoDTO collectionInfoDetailDTO = BeanCopyUtils.copyByJSON(collectionInfoDO, CollectCheckedCollectionInfoDTO.class);
                collectionInfoDetailDTO.setCollectionId(collectionInfoDO.getId());
                if (Objects.nonNull(collectionInfoDO.getAmount())) {
                    collectionInfoDetailDTO.setAmount(collectionInfoDO.getAmount() / 100.0);
                }
                if (Objects.nonNull(collectionInfoDO.getCollectDate())) {
                    collectionInfoDetailDTO.setCollectDate(DateUtils.formatDateYYYMMDD(collectionInfoDO.getCollectDate()));
                }
                if (Objects.nonNull(collectionInfoDO.getInMuseumDate())) {
                    collectionInfoDetailDTO.setInMuseumDate(DateUtils.formatDateYYYMMDD(collectionInfoDO.getInMuseumDate()));
                }
                if (Objects.nonNull(collectionInfoDO.getInTibetanDate())) {
                    collectionInfoDetailDTO.setInTibetanDate(DateUtils.formatDateYYYMMDD(collectionInfoDO.getInTibetanDate()));
                }
                if (finalClassificationMap != null) {
                    collectionInfoDetailDTO.setClassifyName(finalClassificationMap.get(collectionInfoDetailDTO.getClassify()));
                    collectionInfoDetailDTO.setCountUnitName(finalClassificationMap.get(collectionInfoDetailDTO.getCountUnit()));
                    collectionInfoDetailDTO.setAgeName(finalClassificationMap.get(collectionInfoDetailDTO.getAge()));
                    collectionInfoDetailDTO.setTextureCategoryName(finalClassificationMap.get(collectionInfoDetailDTO.getTextureCategory()));
                    String texture = collectionInfoDetailDTO.getTexture();
                    if (StringUtils.isNotBlank(texture)) {
                        List<Long> textureIds = Stream.of(texture.split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                        StringBuilder textureBuilder = new StringBuilder();
                        textureIds.forEach(textureId -> {
                            textureBuilder.append(finalClassificationMap.get(textureId));
                            textureBuilder.append(",");
                        });
                        textureBuilder.deleteCharAt(textureBuilder.toString().length() - 1);
                        collectionInfoDetailDTO.setTextureName(textureBuilder.toString());
                    }
                    collectionInfoDetailDTO.setSizeUnitName(finalClassificationMap.get(collectionInfoDetailDTO.getSizeUnit()));
                    collectionInfoDetailDTO.setCompleteDegreeName(finalClassificationMap.get(collectionInfoDetailDTO.getCompleteDegree()));
                    collectionInfoDetailDTO.setInitialLevelName(finalClassificationMap.get(collectionInfoDetailDTO.getInitialLevel()));
                    collectionInfoDetailDTO.setCollectTypeName(finalClassificationMap.get(collectionInfoDetailDTO.getCollectType()));
                    collectionInfoDetailDTO.setWarehouseName(finalClassificationMap.get(collectionInfoDetailDTO.getWarehouseId()));
                }
                if (finalDocumentMap != null) {
                    String documentId = collectionInfoDetailDTO.getDocumentId();
                    if (StringUtils.isNotBlank(documentId)) {
                        List<Long> documentTempIds = Stream.of(documentId.split(",")).map(Long::parseLong)
                                .distinct().collect(Collectors.toList());
                        List<CommonDocumentDTO> commonDocumentList = Lists.newArrayList();
                        List<CommonDocumentDTO> attachmentDocumentList = Lists.newArrayList();
                        List<CommonDocumentDTO> imageDocumentList = Lists.newArrayList();
                        documentTempIds.forEach(docId -> {
                            CommDocumentDO commDocumentDO = finalDocumentMap.get(docId);
                            if (Objects.isNull(commDocumentDO)) {
                                return;
                            }
                            CommonDocumentDTO commonDocumentDTO = BeanCopyUtils.copyByJSON(commDocumentDO, CommonDocumentDTO.class);
                            commonDocumentDTO.setDocumentId(commDocumentDO.getId());
                            commonDocumentList.add(commonDocumentDTO);
                            if (FileTypeEnum.ATCHMT.getCode().equals(commonDocumentDTO.getFileType())) {
                                attachmentDocumentList.add(commonDocumentDTO);
                            }
                            if (FileTypeEnum.IMAGE.getCode().equals(commonDocumentDTO.getFileType())) {
                                imageDocumentList.add(commonDocumentDTO);
                            }
                        });
                        collectionInfoDetailDTO.setCommonDocumentList(commonDocumentList);
                        collectionInfoDetailDTO.setAttachmentDocumentList(attachmentDocumentList);
                        collectionInfoDetailDTO.setImageDocumentList(imageDocumentList);
                    }
                }
                if(collectionMap.containsKey(collectionInfoDetailDTO.getCollectionId())){
                    collectionInfoDetailDTO.setChecked(true);
                }else{
                    collectionInfoDetailDTO.setChecked(false);
                }
                if (isCheckMainEmpty) {
                    collectionInfoDetailDTO.setCheckStatus(CheckMainStatusEnum.USB.getCode());
                    collectionInfoDetailDTO.setCheckStatusDesc(CheckMainStatusEnum.USB.getDesc());
                    return collectionInfoDetailDTO;
                }
                if (finalIsAllAtdFlag) {
                    collectionInfoDetailDTO.setCheckStatus(CheckMainStatusEnum.ATD.getCode());
                    collectionInfoDetailDTO.setCheckStatusDesc(CheckMainStatusEnum.ATD.getDesc());
                    return collectionInfoDetailDTO;
                }
                if (checkMainFlag) {
                    collectionInfoDetailDTO.setCheckStatus(checkMainDO.getCheckStatus());
                    collectionInfoDetailDTO.setCheckStatusDesc(CheckMainStatusEnum.fromCode(checkMainDO.getCheckStatus()).getDesc());
                    if (CheckMainStatusEnum.ATP.getCode().equals(checkMainDO.getCheckStatus())) {
                        if(collectionMap.containsKey(collectionInfoDetailDTO.getCollectionId())){
                            collectionInfoDetailDTO.setCheckStatus(CheckMainStatusEnum.ATP.getCode());
                            collectionInfoDetailDTO.setCheckStatusDesc(CheckMainStatusEnum.ATP.getDesc());
                        }else{
                            collectionInfoDetailDTO.setCheckStatus(CheckMainStatusEnum.NATP.getCode());
                            collectionInfoDetailDTO.setCheckStatusDesc(CheckMainStatusEnum.NATP.getDesc());
                        }
                    }
                    return collectionInfoDetailDTO;
                } else {
                    collectionInfoDetailDTO.setCheckStatus(CheckMainStatusEnum.ATD.getCode());
                    collectionInfoDetailDTO.setCheckStatusDesc(CheckMainStatusEnum.ATD.getDesc());
                    /*if(collectionMap.containsKey(collectionInfoDetailDTO.getCollectionId())){
                        collectionInfoDetailDTO.setCheckStatus(CheckMainStatusEnum.ATP.getCode());
                        collectionInfoDetailDTO.setCheckStatusDesc(CheckMainStatusEnum.ATP.getDesc());
                    }else{
                        collectionInfoDetailDTO.setCheckStatus(CheckMainStatusEnum.NATP.getCode());
                        collectionInfoDetailDTO.setCheckStatusDesc(CheckMainStatusEnum.NATP.getDesc());
                    }*/
                }
                return collectionInfoDetailDTO;
            }).collect(Collectors.toList());
        }
        pageInfo.setList(collectionInfoDTOList);
        return pageInfo;
    }

    @Override
    public List<CollectionInfoDetailDTO> queryByBizIdAndBizType(Long companyId, Long bizId, CollectionBizTypeEnum bizType){
        List<ScsCollectionInfoDO> collectionInfoDOList = scsCollectionInfoMapper
                .listByBizIdAndBizType(companyId, bizId, bizType.getCode(), null, null, null);
        List<CollectionInfoDetailDTO> collectionInfoDTOList = null;
        if (CollectionUtils.isNotEmpty(collectionInfoDOList)) {
            List<Long> classificationIds = this.getClassificationIds(collectionInfoDOList);
            Map<Long, String> classificationMap = null;
            if (CollectionUtils.isNotEmpty(classificationIds)) {
                List<CommClassificationDO> classificationDOList = commClassificationMapper.listByIds(companyId, classificationIds);
                classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                        CommClassificationDO::getName, (key1, key2) -> key2));
            }
            List<Long> documentIds = Lists.newArrayList();
            collectionInfoDOList.forEach(scsCollectionInfoDO -> {
                if (StringUtils.isBlank(scsCollectionInfoDO.getDocumentId())) {
                    return;
                }
                List<Long> documentTempIds = Stream.of(scsCollectionInfoDO.getDocumentId().split(","))
                        .map(Long::parseLong).distinct().collect(Collectors.toList());
                documentIds.addAll(documentTempIds);
            });
            Map<Long, CommDocumentDO> documentMap = null;
            if (CollectionUtils.isNotEmpty(documentIds)) {
                List<CommDocumentDO> commDocumentDOList = commDocumentMapper.listByIds(companyId, documentIds);
                documentMap = commDocumentDOList.stream().collect(Collectors.toMap(CommDocumentDO::getId,
                        commDocumentDO -> commDocumentDO));
            }
            Map<Long, String> finalClassificationMap = classificationMap;
            Map<Long, CommDocumentDO> finalDocumentMap = documentMap;
            collectionInfoDTOList = collectionInfoDOList.stream().map(collectionInfoDO -> {
                CollectionInfoDetailDTO collectionInfoDetailDTO = BeanCopyUtils.copyByJSON(collectionInfoDO, CollectionInfoDetailDTO.class);
                collectionInfoDetailDTO.setCollectionId(collectionInfoDO.getId());
                if (Objects.nonNull(collectionInfoDO.getAmount())) {
                    collectionInfoDetailDTO.setAmount(collectionInfoDO.getAmount() / 100.0);
                }
                if(Objects.nonNull(collectionInfoDO.getCollectDate())){
                    collectionInfoDetailDTO.setCollectDate(DateUtils.formatDateYYYMMDD(collectionInfoDO.getCollectDate()));
                }
                if(Objects.nonNull(collectionInfoDO.getInMuseumDate())){
                    collectionInfoDetailDTO.setInMuseumDate(DateUtils.formatDateYYYMMDD(collectionInfoDO.getInMuseumDate()));
                }
                if (finalClassificationMap != null) {
                    collectionInfoDetailDTO.setClassifyName(finalClassificationMap.get(collectionInfoDetailDTO.getClassify()));
                    collectionInfoDetailDTO.setCountUnitName(finalClassificationMap.get(collectionInfoDetailDTO.getCountUnit()));
                    collectionInfoDetailDTO.setAgeName(finalClassificationMap.get(collectionInfoDetailDTO.getAge()));
                    collectionInfoDetailDTO.setTextureCategoryName(finalClassificationMap.get(collectionInfoDetailDTO.getTextureCategory()));
                    String texture = collectionInfoDetailDTO.getTexture();
                    if (StringUtils.isNotBlank(texture)) {
                        List<Long> textureIds = Stream.of(texture.split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                        StringBuilder textureBuilder = new StringBuilder();
                        textureIds.forEach(textureId -> {
                            textureBuilder.append(finalClassificationMap.get(textureId));
                            textureBuilder.append(",");
                        });
                        textureBuilder.deleteCharAt(textureBuilder.toString().length() - 1);
                        collectionInfoDetailDTO.setTextureName(textureBuilder.toString());
                    }
                    collectionInfoDetailDTO.setSizeUnitName(finalClassificationMap.get(collectionInfoDetailDTO.getSizeUnit()));
                    collectionInfoDetailDTO.setCompleteDegreeName(finalClassificationMap.get(collectionInfoDetailDTO.getCompleteDegree()));
                    collectionInfoDetailDTO.setInitialLevelName(finalClassificationMap.get(collectionInfoDetailDTO.getInitialLevel()));
                    collectionInfoDetailDTO.setCollectTypeName(finalClassificationMap.get(collectionInfoDetailDTO.getCollectType()));
                    collectionInfoDetailDTO.setWarehouseName(finalClassificationMap.get(collectionInfoDetailDTO.getWarehouseId()));
                }
                if (finalDocumentMap != null) {
                    String documentId = collectionInfoDetailDTO.getDocumentId();
                    if (StringUtils.isNotBlank(documentId)) {
                        List<Long> documentTempIds = Stream.of(documentId.split(",")).map(Long::parseLong)
                                .distinct().collect(Collectors.toList());
                        List<CommonDocumentDTO> commonDocumentList = Lists.newArrayList();
                        List<CommonDocumentDTO> attachmentDocumentList = Lists.newArrayList();
                        List<CommonDocumentDTO> imageDocumentList = Lists.newArrayList();
                        documentTempIds.forEach(docId -> {
                            CommDocumentDO commDocumentDO = finalDocumentMap.get(docId);
                            if (Objects.isNull(commDocumentDO)) {
                                return;
                            }
                            CommonDocumentDTO commonDocumentDTO = BeanCopyUtils.copyByJSON(commDocumentDO, CommonDocumentDTO.class);
                            commonDocumentDTO.setDocumentId(commDocumentDO.getId());
                            commonDocumentList.add(commonDocumentDTO);
                            if (FileTypeEnum.ATCHMT.getCode().equals(commonDocumentDTO.getFileType())) {
                                attachmentDocumentList.add(commonDocumentDTO);
                            }
                            if (FileTypeEnum.IMAGE.getCode().equals(commonDocumentDTO.getFileType())) {
                                imageDocumentList.add(commonDocumentDTO);
                            }
                        });
                        collectionInfoDetailDTO.setCommonDocumentList(commonDocumentList);
                        collectionInfoDetailDTO.setAttachmentDocumentList(attachmentDocumentList);
                        collectionInfoDetailDTO.setImageDocumentList(imageDocumentList);
                    }
                }
                return collectionInfoDetailDTO;
            }).collect(Collectors.toList());
        }
        return collectionInfoDTOList;
    }

    @Override
    public SimplePageInfo<IdentifyCollectionInfoPageDTO> queryCollectionInfoByIdentifyId(Long companyId, Long identifyId, Integer pageNum, Integer pageSize){

        PageInfoBaseReq reqPage = new PageInfoBaseReq();
        reqPage.startPage(pageNum, pageSize, null);
        List<IdentifyCollectionInfoDO> collectionInfoList = scsCollectionInfoMapper.queryCollectionInfoByIdentifyId(CollectionBizTypeEnum.IDY.getCode(),identifyId);
        if (CollectionUtils.isEmpty(collectionInfoList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<IdentifyCollectionInfoPageDTO> pageInfo = SimplePageInfoUtils.convertSimplePageInfoResult(collectionInfoList,
                IdentifyCollectionInfoPageDTO.class);
        List<IdentifyCollectionInfoPageDTO> collectionInfoDTOList = null;
        if (CollectionUtils.isNotEmpty(collectionInfoList)) {
            List<Long> classificationIds = this.getClassificationIdsOnIdentify(collectionInfoList);
            Map<Long, String> classificationMap = null;
            if (CollectionUtils.isNotEmpty(classificationIds)) {
                List<CommClassificationDO> classificationDOList = commClassificationMapper.listByIds(companyId, classificationIds);
                classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                        CommClassificationDO::getName, (key1, key2) -> key2));
            }
            List<Long> documentIds = Lists.newArrayList();
            collectionInfoList.forEach(identifyCollectionInfoDO -> {
                if (StringUtils.isBlank(identifyCollectionInfoDO.getDocumentId())) {
                    return;
                }
                List<Long> documentTempIds = Stream.of(identifyCollectionInfoDO.getDocumentId().split(","))
                        .map(Long::parseLong).distinct().collect(Collectors.toList());
                documentIds.addAll(documentTempIds);
            });
            Map<Long, CommDocumentDO> documentMap = null;
            if (CollectionUtils.isNotEmpty(documentIds)) {
                List<CommDocumentDO> commDocumentDOList = commDocumentMapper.listByIds(companyId, documentIds);
                documentMap = commDocumentDOList.stream().collect(Collectors.toMap(CommDocumentDO::getId,
                        commDocumentDO -> commDocumentDO));
            }
            List<Long> identifyDocumentIds = Lists.newArrayList();
            List<Long> collectionIds = Lists.newArrayList();
            collectionInfoList.forEach(identifyCollectionInfoEntity -> {
                collectionIds.add(identifyCollectionInfoEntity.getCollectionId());
                if (StringUtils.isBlank(identifyCollectionInfoEntity.getIdentifyDocumentId())) {
                    return;
                }
                List<Long> documentTempIds = Stream.of(identifyCollectionInfoEntity.getIdentifyDocumentId().split(","))
                        .map(Long::parseLong).distinct().collect(Collectors.toList());
                identifyDocumentIds.addAll(documentTempIds);
            });
            Map<Long, CommDocumentDO> identifyDocumentMap = null;
            if (CollectionUtils.isNotEmpty(identifyDocumentIds)) {
                List<CommDocumentDO> commDocumentDOList = commDocumentMapper.listByIds(companyId, identifyDocumentIds);
                identifyDocumentMap = commDocumentDOList.stream().collect(Collectors.toMap(CommDocumentDO::getId,
                        commDocumentDO -> commDocumentDO));
            }
            CommCheckMainDO checkMainDO = this.commCheckMainMapper.selectByModelAndId(companyId, CollectionBizTypeEnum.IDY.getCode(), identifyId);
            boolean checkMainFlag = Objects.nonNull(checkMainDO) && StringUtils.isNotBlank(checkMainDO.getCheckStatus())
                    && (CheckMainStatusEnum.REVE.getCode().equals(checkMainDO.getCheckStatus())
                    || CheckMainStatusEnum.NATP.getCode().equals(checkMainDO.getCheckStatus())
                    || CheckMainStatusEnum.BACK.getCode().equals(checkMainDO.getCheckStatus())
                    || CheckMainStatusEnum.USB.getCode().equals(checkMainDO.getCheckStatus())
                    || CheckMainStatusEnum.ATP.getCode().equals(checkMainDO.getCheckStatus()));
            boolean isCheckMainEmpty = Objects.isNull(checkMainDO);
            Map<Long,Object> collectionMap = Maps.newHashMap();

            //if (!checkMainFlag) {
                QueryCheckedCollectionIdAction action  = new QueryCheckedCollectionIdAction();
                action.setCompanyId(companyId);
                action.setBelongModel(CollectionBizTypeEnum.IDY);
                action.setModelId(identifyId);
                action.setCollectionIds(collectionIds);
                List<Long> checkCollectionIdList = checkService.queryCheckedCollectionId(action);
                for(Long id:checkCollectionIdList){
                    collectionMap.put(id,"");
                }
            //};
            boolean isAllAtdFlag = false;
            if (Objects.nonNull(checkMainDO)) {
                List<CommCheckChildDO> checkChildList = this.commCheckChildMapper.selectCheckListByMainId(checkMainDO.getId());
                isAllAtdFlag = true;
                if (CollectionUtils.isNotEmpty(checkChildList)) {
                    isAllAtdFlag = CheckStatusChildEnum.ATD.getCode().equals(checkChildList.get(0).getCheckStatus());
                }
            }

            Map<Long, String> finalClassificationMap = classificationMap;
            Map<Long, CommDocumentDO> finalDocumentMap = documentMap;
            Map<Long, CommDocumentDO> finalIdentifyDocumentMap = identifyDocumentMap;
            boolean finalIsAllAtdFlag = isAllAtdFlag;
            collectionInfoDTOList = collectionInfoList.stream().map(identifyCollectionInfoEntity -> {
                IdentifyCollectionInfoPageDTO identifyCollectionInfoPageDTO = BeanCopyUtils.copyByJSON(identifyCollectionInfoEntity, IdentifyCollectionInfoPageDTO.class);
                identifyCollectionInfoPageDTO.setCollectionId(identifyCollectionInfoEntity.getCollectionId());
                if (Objects.nonNull(identifyCollectionInfoEntity.getAmount())) {
                    identifyCollectionInfoPageDTO.setAmount(identifyCollectionInfoEntity.getAmount() / 100.0);
                }
                if(Objects.nonNull(identifyCollectionInfoEntity.getCollectDate())) {
                    identifyCollectionInfoPageDTO.setCollectDate(DateUtils.formatDateYYYMMDD(identifyCollectionInfoEntity.getCollectDate()));
                }
                if(Objects.nonNull(identifyCollectionInfoEntity.getIdentifyDate())) {
                    identifyCollectionInfoPageDTO.setIdentifyDate(DateUtils.formatDateYYYMMDD(identifyCollectionInfoEntity.getIdentifyDate()));
                }
                if (finalClassificationMap != null) {
                    identifyCollectionInfoPageDTO.setCountUnitName(finalClassificationMap.get(identifyCollectionInfoEntity.getCountUnit()));
                    identifyCollectionInfoPageDTO.setTextureCategoryName(finalClassificationMap.get(identifyCollectionInfoEntity.getTextureCategory()));
                    String texture = identifyCollectionInfoEntity.getTexture();
                    if (StringUtils.isNotBlank(texture)) {
                        List<Long> textureIds = Stream.of(texture.split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                        StringBuilder textureBuilder = new StringBuilder();
                        textureIds.forEach(textureId -> {
                            textureBuilder.append(finalClassificationMap.get(textureId));
                            textureBuilder.append(",");
                        });
                        textureBuilder.deleteCharAt(textureBuilder.toString().length() - 1);
                        identifyCollectionInfoPageDTO.setTextureName(textureBuilder.toString());
                    }
                    identifyCollectionInfoPageDTO.setCompleteDegreeName(finalClassificationMap.get(identifyCollectionInfoEntity.getCompleteDegree()));
                    identifyCollectionInfoPageDTO.setInitialLevelName(finalClassificationMap.get(identifyCollectionInfoEntity.getInitialLevel()));
                    identifyCollectionInfoPageDTO.setCollectTypeName(finalClassificationMap.get(identifyCollectionInfoEntity.getCollectType()));
                    identifyCollectionInfoPageDTO.setSizeUnitName(finalClassificationMap.get(identifyCollectionInfoEntity.getSizeUnit()));
                }
                if (finalDocumentMap != null) {
                    String documentId = identifyCollectionInfoEntity.getDocumentId();
                    if (StringUtils.isNotBlank(documentId)) {
                        List<Long> documentTempIds = Stream.of(documentId.split(",")).map(Long::parseLong)
                                .distinct().collect(Collectors.toList());
                        List<CommonDocumentDTO> commonDocumentList = Lists.newArrayList();
                        documentTempIds.forEach(docId -> {
                            CommDocumentDO commDocumentDO = finalDocumentMap.get(docId);
                            if (Objects.isNull(commDocumentDO)) {
                                return;
                            }
                            CommonDocumentDTO commonDocumentDTO = BeanCopyUtils.copyByJSON(commDocumentDO, CommonDocumentDTO.class);
                            commonDocumentDTO.setDocumentId(commDocumentDO.getId());
                            commonDocumentList.add(commonDocumentDTO);
                        });
                        identifyCollectionInfoPageDTO.setCommonDocumentList(commonDocumentList);
                    }
                }
                if (finalIdentifyDocumentMap != null) {
                    String identifyDocumentId = identifyCollectionInfoEntity.getIdentifyDocumentId();
                    if (StringUtils.isNotBlank(identifyDocumentId)) {
                        List<Long> documentTempIds = Stream.of(identifyDocumentId.split(",")).map(Long::parseLong)
                                .distinct().collect(Collectors.toList());
                        List<CommonDocumentDTO> identifyDocumentList = Lists.newArrayList();
                        documentTempIds.forEach(docId -> {
                            CommDocumentDO commDocumentDO = finalIdentifyDocumentMap.get(docId);
                            if (Objects.isNull(commDocumentDO)) {
                                return;
                            }
                            CommonDocumentDTO commonDocumentDTO = BeanCopyUtils.copyByJSON(commDocumentDO, CommonDocumentDTO.class);
                            commonDocumentDTO.setDocumentId(commDocumentDO.getId());
                            identifyDocumentList.add(commonDocumentDTO);
                        });
                        identifyCollectionInfoPageDTO.setIdentifyDocumentId(identifyDocumentId);
                        identifyCollectionInfoPageDTO.setIdentifyDocumentList(identifyDocumentList);
                    }
                }
                if(collectionMap.containsKey(identifyCollectionInfoEntity.getCollectionId())){
                    identifyCollectionInfoPageDTO.setChecked(true);
                }else{
                    identifyCollectionInfoPageDTO.setChecked(false);
                }
                if (isCheckMainEmpty) {
                    identifyCollectionInfoPageDTO.setCheckStatus(CheckMainStatusEnum.USB.getCode());
                    identifyCollectionInfoPageDTO.setCheckStatusDesc(CheckMainStatusEnum.USB.getDesc());
                    return identifyCollectionInfoPageDTO;
                }
                if (finalIsAllAtdFlag) {
                    identifyCollectionInfoPageDTO.setCheckStatus(CheckMainStatusEnum.ATD.getCode());
                    identifyCollectionInfoPageDTO.setCheckStatusDesc(CheckMainStatusEnum.ATD.getDesc());
                    return identifyCollectionInfoPageDTO;
                }
                if (checkMainFlag) {
                    identifyCollectionInfoPageDTO.setCheckStatus(checkMainDO.getCheckStatus());
                    identifyCollectionInfoPageDTO.setCheckStatusDesc(CheckMainStatusEnum.fromCode(checkMainDO.getCheckStatus()).getDesc());
                    if (CheckMainStatusEnum.ATP.getCode().equals(checkMainDO.getCheckStatus())) {
                        if(collectionMap.containsKey(identifyCollectionInfoEntity.getCollectionId())){
                            identifyCollectionInfoPageDTO.setCheckStatus(CheckMainStatusEnum.ATP.getCode());
                            identifyCollectionInfoPageDTO.setCheckStatusDesc(CheckMainStatusEnum.ATP.getDesc());
                        }else{
                            identifyCollectionInfoPageDTO.setCheckStatus(CheckMainStatusEnum.NATP.getCode());
                            identifyCollectionInfoPageDTO.setCheckStatusDesc(CheckMainStatusEnum.NATP.getDesc());
                        }
                    }
                    return identifyCollectionInfoPageDTO;
                } else {
                    identifyCollectionInfoPageDTO.setCheckStatus(CheckMainStatusEnum.ATD.getCode());
                    identifyCollectionInfoPageDTO.setCheckStatusDesc(CheckMainStatusEnum.ATD.getDesc());
                    /*if(collectionMap.containsKey(identifyCollectionInfoEntity.getCollectionId())){
                        identifyCollectionInfoPageDTO.setCheckStatus(CheckMainStatusEnum.ATP.getCode());
                        identifyCollectionInfoPageDTO.setCheckStatusDesc(CheckMainStatusEnum.ATP.getDesc());
                    }else{
                        identifyCollectionInfoPageDTO.setCheckStatus(CheckMainStatusEnum.NATP.getCode());
                        identifyCollectionInfoPageDTO.setCheckStatusDesc(CheckMainStatusEnum.NATP.getDesc());
                    }*/
                }
                return identifyCollectionInfoPageDTO;
            }).collect(Collectors.toList());
        }
        pageInfo.setList(collectionInfoDTOList);
        return pageInfo;
    }

    @Override
    public List<IdentifyCollectionInfoPageDTO> queryCollectionInfoByIdentifyIdNoPage(Long companyId, Long identifyId){
        List<IdentifyCollectionInfoDO> collectionInfoList = scsCollectionInfoMapper.queryCollectionInfoByIdentifyId(CollectionBizTypeEnum.IDY.getCode(),identifyId);
        List<IdentifyCollectionInfoPageDTO> collectionInfoDTOList = null;
        if (CollectionUtils.isNotEmpty(collectionInfoList)) {
            List<Long> classificationIds = this.getClassificationIdsOnIdentify(collectionInfoList);
            Map<Long, String> classificationMap = null;
            if (CollectionUtils.isNotEmpty(classificationIds)) {
                List<CommClassificationDO> classificationDOList = commClassificationMapper.listByIds(companyId, classificationIds);
                classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                        CommClassificationDO::getName, (key1, key2) -> key2));
            }
            List<Long> documentIds = Lists.newArrayList();
            collectionInfoList.forEach(identifyCollectionInfoDO -> {
                if (StringUtils.isBlank(identifyCollectionInfoDO.getDocumentId())) {
                    return;
                }
                List<Long> documentTempIds = Stream.of(identifyCollectionInfoDO.getDocumentId().split(","))
                        .map(Long::parseLong).distinct().collect(Collectors.toList());
                documentIds.addAll(documentTempIds);
            });
            Map<Long, CommDocumentDO> documentMap = null;
            if (CollectionUtils.isNotEmpty(documentIds)) {
                List<CommDocumentDO> commDocumentDOList = commDocumentMapper.listByIds(companyId, documentIds);
                documentMap = commDocumentDOList.stream().collect(Collectors.toMap(CommDocumentDO::getId,
                        commDocumentDO -> commDocumentDO));
            }
            List<Long> identifyDocumentIds = Lists.newArrayList();
            collectionInfoList.forEach(identifyCollectionInfoEntity -> {
                if (StringUtils.isBlank(identifyCollectionInfoEntity.getIdentifyDocumentId())) {
                    return;
                }
                List<Long> documentTempIds = Stream.of(identifyCollectionInfoEntity.getIdentifyDocumentId().split(","))
                        .map(Long::parseLong).distinct().collect(Collectors.toList());
                identifyDocumentIds.addAll(documentTempIds);
            });
            Map<Long, CommDocumentDO> identifyDocumentMap = null;
            if (CollectionUtils.isNotEmpty(identifyDocumentIds)) {
                List<CommDocumentDO> commDocumentDOList = commDocumentMapper.listByIds(companyId, identifyDocumentIds);
                identifyDocumentMap = commDocumentDOList.stream().collect(Collectors.toMap(CommDocumentDO::getId,
                        commDocumentDO -> commDocumentDO));
            }
            Map<Long, String> finalClassificationMap = classificationMap;
            Map<Long, CommDocumentDO> finalDocumentMap = documentMap;
            Map<Long, CommDocumentDO> finalIdentifyDocumentMap = identifyDocumentMap;
            collectionInfoDTOList = collectionInfoList.stream().map(identifyCollectionInfoEntity -> {
                IdentifyCollectionInfoPageDTO identifyCollectionInfoPageDTO = BeanCopyUtils.copyByJSON(identifyCollectionInfoEntity, IdentifyCollectionInfoPageDTO.class);
                identifyCollectionInfoPageDTO.setCollectionId(identifyCollectionInfoEntity.getCollectionId());
                if (Objects.nonNull(identifyCollectionInfoEntity.getAmount())) {
                    identifyCollectionInfoPageDTO.setAmount(identifyCollectionInfoEntity.getAmount() / 100.0);
                }
                if (finalClassificationMap != null) {
                    identifyCollectionInfoPageDTO.setCountUnitName(finalClassificationMap.get(identifyCollectionInfoEntity.getCountUnit()));
                    identifyCollectionInfoPageDTO.setTextureCategoryName(finalClassificationMap.get(identifyCollectionInfoEntity.getTextureCategory()));
                    String texture = identifyCollectionInfoEntity.getTexture();
                    if (StringUtils.isNotBlank(texture)) {
                        List<Long> textureIds = Stream.of(texture.split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                        StringBuilder textureBuilder = new StringBuilder();
                        textureIds.forEach(textureId -> {
                            textureBuilder.append(finalClassificationMap.get(textureId));
                            textureBuilder.append(",");
                        });
                        textureBuilder.deleteCharAt(textureBuilder.toString().length() - 1);
                        identifyCollectionInfoPageDTO.setTextureName(textureBuilder.toString());
                    }
                    identifyCollectionInfoPageDTO.setCompleteDegreeName(finalClassificationMap.get(identifyCollectionInfoEntity.getCompleteDegree()));
                    identifyCollectionInfoPageDTO.setInitialLevelName(finalClassificationMap.get(identifyCollectionInfoEntity.getInitialLevel()));
                    identifyCollectionInfoPageDTO.setCollectTypeName(finalClassificationMap.get(identifyCollectionInfoEntity.getCollectType()));
                }
                if (finalDocumentMap != null) {
                    String documentId = identifyCollectionInfoEntity.getDocumentId();
                    if (StringUtils.isNotBlank(documentId)) {
                        List<Long> documentTempIds = Stream.of(documentId.split(",")).map(Long::parseLong)
                                .distinct().collect(Collectors.toList());
                        List<CommonDocumentDTO> commonDocumentList = Lists.newArrayList();
                        documentTempIds.forEach(docId -> {
                            CommDocumentDO commDocumentDO = finalDocumentMap.get(docId);
                            if (Objects.isNull(commDocumentDO)) {
                                return;
                            }
                            CommonDocumentDTO commonDocumentDTO = BeanCopyUtils.copyByJSON(commDocumentDO, CommonDocumentDTO.class);
                            commonDocumentDTO.setDocumentId(commDocumentDO.getId());
                            commonDocumentList.add(commonDocumentDTO);
                        });
                        identifyCollectionInfoPageDTO.setCommonDocumentList(commonDocumentList);
                    }
                }
                if (finalIdentifyDocumentMap != null) {
                    String identifyDocumentId = identifyCollectionInfoEntity.getIdentifyDocumentId();
                    if (StringUtils.isNotBlank(identifyDocumentId)) {
                        List<Long> documentTempIds = Stream.of(identifyDocumentId.split(",")).map(Long::parseLong)
                                .distinct().collect(Collectors.toList());
                        List<CommonDocumentDTO> identifyDocumentList = Lists.newArrayList();
                        documentTempIds.forEach(docId -> {
                            CommDocumentDO commDocumentDO = finalIdentifyDocumentMap.get(docId);
                            if (Objects.isNull(commDocumentDO)) {
                                return;
                            }
                            CommonDocumentDTO commonDocumentDTO = BeanCopyUtils.copyByJSON(commDocumentDO, CommonDocumentDTO.class);
                            commonDocumentDTO.setDocumentId(commDocumentDO.getId());
                            identifyDocumentList.add(commonDocumentDTO);
                        });
                        identifyCollectionInfoPageDTO.setIdentifyDocumentList(identifyDocumentList);
                    }
                }

                // 调整日期显示格式
                identifyCollectionInfoPageDTO.setIdentifyDate(DateUtils.formatDateYYYMMDD(identifyCollectionInfoEntity.getIdentifyDate()));

                if(Objects.nonNull(identifyCollectionInfoEntity.getCollectDate())){
                    identifyCollectionInfoPageDTO.setCollectDate(DateUtils.formatDateYYYMMDD(identifyCollectionInfoEntity.getCollectDate()));
                }

                return identifyCollectionInfoPageDTO;
            }).collect(Collectors.toList());
        }
        return collectionInfoDTOList;
    }

    @Override
    public CollectionInfoDetailDTO queryCollectionInfoById(Long companyId, Long collectionId){
        ScsCollectionInfoDO scsCollectionInfoDO = this.scsCollectionInfoMapper.queryCollectionInfoById(companyId,collectionId);
        if(Objects.isNull(scsCollectionInfoDO)){
           return null;
        }
        CollectionInfoDetailDTO collectionInfoDetailDTO = BeanCopyUtils.copyByJSON(scsCollectionInfoDO, CollectionInfoDetailDTO.class);
        return collectionInfoDetailDTO;
    }

    @Override
    public List<BizCollectionRelDTO> listByBizIdsAndBizType(List<Long> bizIds, CollectionBizTypeEnum bizType) {
        List<ScsBizCollectionRelDO> bizCollectionRelDOList = scsBizCollectionRelMapper
                .listByBizIdsAndBizType(bizIds, bizType.getCode());
        if (CollectionUtils.isEmpty(bizCollectionRelDOList)) {
            return Lists.newArrayList();
        }
        return BeanCopyUtils.copyArrayByJSON(bizCollectionRelDOList, BizCollectionRelDTO.class);
    }

    @Override
    public List<BizCollectionRelDTO> listByBizIdsAndBizTypeExclude(List<Long> bizIds, CollectionBizTypeEnum bizType,
                                                                   List<Long> collectionIds) {
        List<ScsBizCollectionRelDO> bizCollectionRelDOList = scsBizCollectionRelMapper
                .listByBizIdsAndBizTypeExclude(bizIds, bizType.getCode(), collectionIds);
        if (CollectionUtils.isEmpty(bizCollectionRelDOList)) {
            return Lists.newArrayList();
        }
        return BeanCopyUtils.copyArrayByJSON(bizCollectionRelDOList, BizCollectionRelDTO.class);
    }

    @Override
    public List<BizCollectionRelDTO> listByBizIdAndBizType(Long companyId, Long bizId, CollectionBizTypeEnum bizType) {
        List<ScsBizCollectionRelDO> collectionInfoDOList = scsBizCollectionRelMapper
                .listByBizIdAndBizType(bizId, bizType.getCode());
        if (CollectionUtils.isEmpty(collectionInfoDOList)) {
            return Lists.newArrayList();
        }
        return BeanCopyUtils.copyArrayByJSON(collectionInfoDOList, BizCollectionRelDTO.class);
    }

    @Override
    public List<BizCollectionRelDTO> listByBizType(Long companyId, CollectionBizTypeEnum bizType) {
        List<ScsBizCollectionRelDO> collectionInfoDOList = scsBizCollectionRelMapper
                .listByBizType(bizType.getCode());
        if (CollectionUtils.isEmpty(collectionInfoDOList)) {
            return Lists.newArrayList();
        }
        return BeanCopyUtils.copyArrayByJSON(collectionInfoDOList, BizCollectionRelDTO.class);
    }

    @Override
    public Map<Long, List<BizCollectionRelDTO>> queryBizCollectionRelMap(Long companyId, List<Long> bizIds, CollectionBizTypeEnum bizType) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return Maps.newHashMap();
        }
        List<BizCollectionRelDTO> collectionRelDTOList = this.listByBizIdsAndBizType(bizIds, bizType);
        if (CollectionUtils.isEmpty(collectionRelDTOList)) {
            return Maps.newHashMap();
        }
        return collectionRelDTOList.stream().collect(Collectors.groupingBy(BizCollectionRelDTO::getBizId));
    }

    @Override
    public void assignWarehouseByCollectionIds(Long companyId, Long bizId, String bizType, List<Long> collectionIds,
                                               Long warehouseId, Long modifier) {
        List<ScsBizCollectionRelDO> bizCollectionRelDOList =
                this.scsBizCollectionRelMapper.listByBizIdAndBizType(bizId, bizType);
        if (CollectionUtils.isEmpty(bizCollectionRelDOList)
                || bizCollectionRelDOList.size() < collectionIds.size()) {
            log.warn("CollectionServiceImpl assignWarehouseByCollectionIds companyId: [{}] bizId: "
                    + "[{}] collectionIds: [{}] is not exist", companyId, bizId, JSON.toJSONString(collectionIds));
            throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST);
        }
        this.scsCollectionInfoMapper.updateWarehouseByIds(companyId, collectionIds, warehouseId, modifier);
    }
    private List<Long> getClassificationIds(List<ScsCollectionInfoDO> collectionInfoDOList) {
        List<Long> classificationIds = Lists.newArrayList();
        collectionInfoDOList.forEach(scsCollectionInfoDO -> {
            if (Objects.nonNull(scsCollectionInfoDO.getClassify())) {
                classificationIds.add(scsCollectionInfoDO.getClassify());
            }
            if (Objects.nonNull(scsCollectionInfoDO.getCountUnit())) {
                classificationIds.add(scsCollectionInfoDO.getCountUnit());
            }
            if (Objects.nonNull(scsCollectionInfoDO.getAge())) {
                classificationIds.add(scsCollectionInfoDO.getAge());
            }
            if (Objects.nonNull(scsCollectionInfoDO.getTextureCategory())) {
                classificationIds.add(scsCollectionInfoDO.getTextureCategory());
            }
            if (StringUtils.isNotBlank(scsCollectionInfoDO.getTexture())) {
                List<Long> textureIds = Stream.of(scsCollectionInfoDO.getTexture().split(","))
                        .map(Long::parseLong).distinct().collect(Collectors.toList());
                classificationIds.addAll(textureIds);
            }
            if (Objects.nonNull(scsCollectionInfoDO.getSizeUnit())) {
                classificationIds.add(scsCollectionInfoDO.getSizeUnit());
            }
            if (Objects.nonNull(scsCollectionInfoDO.getCompleteDegree())) {
                classificationIds.add(scsCollectionInfoDO.getCompleteDegree());
            }
            if (Objects.nonNull(scsCollectionInfoDO.getInitialLevel())) {
                classificationIds.add(scsCollectionInfoDO.getInitialLevel());
            }
            if (Objects.nonNull(scsCollectionInfoDO.getCollectType())) {
                classificationIds.add(scsCollectionInfoDO.getCollectType());
            }
            if (Objects.nonNull(scsCollectionInfoDO.getWarehouseId())) {
                classificationIds.add(scsCollectionInfoDO.getWarehouseId());
            }
        });
        return classificationIds.stream().distinct().collect(Collectors.toList());
    }
    private List<Long> getClassificationIdsOnIdentify(List<IdentifyCollectionInfoDO> collectionInfoList) {
        List<Long> classificationIds = Lists.newArrayList();
        collectionInfoList.forEach(identifyCollectionInfoEntity -> {

            if (Objects.nonNull(identifyCollectionInfoEntity.getCountUnit())) {
                classificationIds.add(identifyCollectionInfoEntity.getCountUnit());
            }
            if (Objects.nonNull(identifyCollectionInfoEntity.getTextureCategory())) {
                classificationIds.add(identifyCollectionInfoEntity.getTextureCategory());
            }
            if (StringUtils.isNotBlank(identifyCollectionInfoEntity.getTexture())) {
                List<Long> textureIds = Stream.of(identifyCollectionInfoEntity.getTexture().split(","))
                        .map(Long::parseLong).distinct().collect(Collectors.toList());
                classificationIds.addAll(textureIds);
            }
            if (Objects.nonNull(identifyCollectionInfoEntity.getCompleteDegree())) {
                classificationIds.add(identifyCollectionInfoEntity.getCompleteDegree());
            }
            if (Objects.nonNull(identifyCollectionInfoEntity.getInitialLevel())) {
                classificationIds.add(identifyCollectionInfoEntity.getInitialLevel());
            }
            if (Objects.nonNull(identifyCollectionInfoEntity.getCollectType())) {
                classificationIds.add(identifyCollectionInfoEntity.getCollectType());
            }
            if (Objects.nonNull(identifyCollectionInfoEntity.getSizeUnit())) {
                classificationIds.add(identifyCollectionInfoEntity.getSizeUnit());
            }
        });
        return classificationIds.stream().distinct().collect(Collectors.toList());
    }
}
