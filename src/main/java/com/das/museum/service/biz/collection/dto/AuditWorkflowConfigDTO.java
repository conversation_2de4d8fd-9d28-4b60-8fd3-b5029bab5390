package com.das.museum.service.biz.collection.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AuditWorkflowConfigDTO implements Serializable {
    private static final long serialVersionUID = 4881497988706496863L;
    private Long auditWorkflowConfigId;
    private Long companyId;
    private Long bizId;
    private String flowName;
    private String reviewer;
    private String reviewerName;
    private Byte sortIndex;
    private Long prevNodeId;
    private Long nextNodeId;
    private String remark;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}