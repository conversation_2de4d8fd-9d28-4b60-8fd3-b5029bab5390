package com.das.museum.service.biz.collection.dto;

import lombok.Data;

@Data
public class QueryThreeModelPageListDTO {

    /**
     * 三维模型id
     */
    private Long threeModelId;

    /**
     * 文件地址
     */
    private String url;

    /**
     * 三维模型url
     */
    private String modelUrl;

    /**
     * 文件名称
     */
    private String sourceFileName;

    /**
     * 文件批次
     */
    private String fileBatch;

    /**
     * 应用等级 1展示级，2存储级，3复制级
     */
    private Byte applicationLevel;

    /**
     * 文件格式
     */
    private String fileFormat;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 上传人
     */
    private String creatorName;

    /**
     * 更新人
     */
    private String modifierName;

    /**
     * 上传时间
     */
    private String gmtCreate;

    /**
     * 更新时间
     */
    private String gmtModified;


}
