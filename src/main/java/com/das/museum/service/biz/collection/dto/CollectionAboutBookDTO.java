package com.das.museum.service.biz.collection.dto;

import lombok.Data;

/**
 * @Author: Lee
 * @Date: 2023/2/20
 * 填充藏品档案中的藏品相关著录信息的数据
 */
@Data
public class CollectionAboutBookDTO {
    /**
     * 作者
     */
    private String author;
    /**
     * 出版机构及书名
     */
    private String pressName;
    /**
     * 文章章节及页码
     */
    private String article;
    /**
     * 级别
     */
    private String bookLevel;
    /**
     * 著录时间
     */
    private String recordDate;
}
