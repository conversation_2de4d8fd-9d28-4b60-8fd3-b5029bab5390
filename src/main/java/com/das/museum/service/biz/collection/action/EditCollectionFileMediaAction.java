package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EditCollectionFileMediaAction implements Serializable {
    private static final long serialVersionUID = 2554237581216693739L;
    private Long collectionFileMediaId;
    private Long companyId;
    private String mediaType;
    private String fileName;
    private String fileNo;
    private String producerName;
    private String proportion;
    private Date shotDate;
    private Date productionDate;
    private Byte clarity;
    private String remark;
    private String documentId;
    private Long modifier;
}
