package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class MountQueryPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = 9016337118624951110L;
    private Long companyId;
    private Long collectionId;
    private String sortBy;
}
