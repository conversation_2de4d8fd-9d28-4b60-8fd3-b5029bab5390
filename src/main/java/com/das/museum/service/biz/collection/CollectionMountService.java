package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionMountAction;
import com.das.museum.service.biz.collection.action.EditCollectionMountAction;
import com.das.museum.service.biz.collection.action.MountQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionMountDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionMountPageDTO;
import com.das.museum.common.bo.SimplePageInfo;

public interface CollectionMountService {
    Long addCollectionMount(AddCollectionMountAction action);
    void editCollectionMount(EditCollectionMountAction action);
    void delByCollectionMountId(Long companyId, Long collectionMountId);
    SimplePageInfo<CollectionMountPageDTO> queryPageByCondition(MountQueryPageByConditionAction action);
    CollectionMountDetailDTO queryByCollectionMountId(Long companyId, Long collectionMountId);
}
