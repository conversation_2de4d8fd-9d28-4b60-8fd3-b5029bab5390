package com.das.museum.service.biz.collection.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectionNoteDetailDTO implements Serializable {
    private static final long serialVersionUID = 2709395694147322699L;
    private Long collectionNoteId;
    private Long companyId;
    private Long collectionId;
    private String content;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}