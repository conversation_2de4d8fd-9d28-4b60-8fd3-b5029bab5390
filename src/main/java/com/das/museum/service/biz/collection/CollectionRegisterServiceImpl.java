package com.das.museum.service.biz.collection;

import com.das.museum.common.bo.PageInfoBaseReq;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.*;
import com.das.museum.domain.collection.entity.CollectionRegisterEntity;
import com.das.museum.domain.collection.repository.CollectionRegisterRepository;
import com.das.museum.domain.collection.valueobject.*;
import com.das.museum.domain.enums.CheckStatusChildEnum;
import com.das.museum.domain.enums.CollectionBizTypeEnum;
import com.das.museum.domain.enums.InCupboardStatusEnum;
import com.das.museum.domain.enums.RegisterStatusEnum;
import com.das.museum.domain.event.LoginCollectionUpdateRecordEvent;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.dataobjectexpand.GroupByRegisterInfoIdDO;
import com.das.museum.infr.mapper.*;
import com.das.museum.service.biz.collection.action.*;
import com.das.museum.service.biz.collection.dto.*;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.service.biz.user.UserService;
import com.das.museum.service.enums.FileTypeEnum;
import com.das.museum.service.enums.InputModeEnum;
import com.das.museum.service.enums.InputTypeEnum;
import com.das.museum.service.enums.YesOrNoEnum;
import com.das.museum.web.controller.bo.LoginUserInfoBO;
import com.das.museum.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class CollectionRegisterServiceImpl implements CollectionRegisterService {

    @Resource
    private CollectionRegisterRepository collectionRegisterRepository;

    @Resource
    private ScsCollectionInfoMapper scsCollectionInfoMapper;

    @Resource
    private ScsInMuseumCertificateInfoMapper scsInMuseumCertificateInfoMapper;

    @Resource
    private ScsInTibetanCertificateInfoMapper scsInTibetanCertificateInfoMapper;

    @Resource
    private ScsCollectionRegisterInfoMapper scsCollectionRegisterInfoMapper;

    @Resource
    private ScsCollectionRegisterManageInfoMapper scsCollectionRegisterManageInfoMapper;

    @Resource
    private ScsCollectionRegisterAttrInfoMapper scsCollectionRegisterAttrInfoMapper;

    @Resource
    private ScsCollectionRegisterAttrExMapper scsCollectionRegisterAttrExMapper;

    @Resource
    private ScsCollectionRegisterInfoSnapshotMapper scsCollectionRegisterInfoSnapshotMapper;

    @Resource
    private ScsCollectionRegisterAttrInfoSnapshotMapper scsCollectionRegisterAttrInfoSnapshotMapper;

    @Resource
    private ScsCollectionRegisterManageInfoSnapshotMapper scsCollectionRegisterManageInfoSnapshotMapper;

    @Resource
    private ScsCollectionRegisterAttrExSnapshotMapper scsCollectionRegisterAttrExSnapshotMapper;

    @Resource
    private ScsLogoffCollectionInfoMapper scsLogoffCollectionInfoMapper;

    @Resource
    private DocumentService documentService;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private CheckService checkService;

    @Resource
    private CommCheckMainMapper commCheckMainMapper;

    @Resource
    private CommCheckChildMapper commCheckChildMapper;

    @Resource
    private CommCheckLogMapper commCheckLogMapper;

    @Resource
    private UserService userService;

    @Resource
    private ScsCollectionTwoImageMapper scsCollectionTwoImageMapper;

    @Resource
    private ScsCollectionThreeModelMapper scsCollectionThreeModelMapper;

    @Resource
    private SwzsCulturalRelicsMapper swzsCulturalRelicsMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateCollectionRegister(Long companyId, Long userId, Long collectionId) {
        ScsCollectionInfoDO collectionInfoDO = this.scsCollectionInfoMapper.selectById(companyId, collectionId);
        if (Objects.isNull(collectionInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品信息不存在");
        }
        ScsCollectionRegisterInfoDO registerInfoDO = this.scsCollectionRegisterInfoMapper.selectByRegisterNo(companyId, collectionInfoDO.getRegisterNo());
        if (Objects.nonNull(registerInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品登录信息已经存在");
        }
        CollectionRegisterEntity registerEntity = new CollectionRegisterEntity();
        registerEntity.setCompanyId(companyId);
        registerEntity.setRegisterNo(collectionInfoDO.getRegisterNo());
        CollectionRegisterBaseInfoVO registerBaseInfo = this.buildRegisterBaseInfo(collectionInfoDO);
        registerBaseInfo.setCompanyId(companyId);
        registerBaseInfo.setCollectionId(collectionId);
        registerBaseInfo.setRegisterStatus(RegisterStatusEnum.NRTS.getCode());
        registerBaseInfo.setInCupboardStatus(InCupboardStatusEnum.NCBD.getCode());
        registerBaseInfo.setCreator(userId);
        registerBaseInfo.setModifier(userId);
        registerEntity.setCollectionRegisterBaseInfo(registerBaseInfo);
        CollectionRegisterManageInfoVO registerManageInfo = this.buildRegisterManageInfo(collectionInfoDO);
        ScsInMuseumCertificateInfoDO inMuseumCertificateInfoDO = this.scsInMuseumCertificateInfoMapper.selectByCollectionId(companyId, collectionId);
        if (Objects.nonNull(inMuseumCertificateInfoDO)) {
            registerManageInfo.setInMuseumCertificateNo(inMuseumCertificateInfoDO.getInMuseumCertificateNo());
        }
        ScsInTibetanCertificateInfoDO inTibetanCertificateInfoDO = this.scsInTibetanCertificateInfoMapper.selectByCollectionId(companyId, collectionId);
        if (Objects.nonNull(inTibetanCertificateInfoDO)) {
            registerManageInfo.setInTibetanCertificateNo(inTibetanCertificateInfoDO.getInTibetanCertificateNo());
        }
        registerManageInfo.setCompanyId(companyId);
        registerManageInfo.setCreator(userId);
        registerManageInfo.setModifier(userId);
        registerEntity.setCollectionRegisterManageInfo(registerManageInfo);
        CollectionRegisterAttrInfoVO registerAttrInfoVO = this.buildRegisterAttrInfo(collectionInfoDO);
        registerAttrInfoVO.setCompanyId(companyId);
        registerAttrInfoVO.setCreator(userId);
        registerAttrInfoVO.setModifier(userId);
        registerEntity.setCollectionRegisterAttrInfo(registerAttrInfoVO);
        this.collectionRegisterRepository.saveCollectionRegister(registerEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AddCollectionRegisterDTO addCollectionRegister(AddCollectionRegisterAction action) {
        Long companyId = action.getCompanyId();
        Long userId = action.getCreator();
        ScsCollectionInfoDO collectionInfoDO = this.scsCollectionInfoMapper.selectByRegisterNo(companyId, action.getRegisterNo());
        if (Objects.nonNull(collectionInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品总登记号已存在");
        }
        AddCollectionRegisterDTO addCollectionRegisterDTO = new AddCollectionRegisterDTO();
        ScsCollectionInfoDO insertCollectionInfoDO = this.buildCollectionInfoDO(action);
        insertCollectionInfoDO.setInputMode(InputModeEnum.REG.getCode());
        insertCollectionInfoDO.setInputType(InputTypeEnum.MLE.getCode());
        insertCollectionInfoDO.setCompanyId(companyId);
        insertCollectionInfoDO.setCreator(userId);
        insertCollectionInfoDO.setModifier(userId);
        this.scsCollectionInfoMapper.insertSelective(insertCollectionInfoDO);
        addCollectionRegisterDTO.setCollectionId(insertCollectionInfoDO.getId());
        CollectionRegisterEntity registerEntity = new CollectionRegisterEntity();
        CollectionRegisterBaseInfoVO registerBaseInfo = BeanCopyUtils.copyByJSON(action, CollectionRegisterBaseInfoVO.class);
        registerBaseInfo.setCollectionId(insertCollectionInfoDO.getId());
        if(StringUtils.isNotBlank(action.getRegisterStatus())){
            registerBaseInfo.setRegisterStatus(action.getRegisterStatus());
        }else{
            registerBaseInfo.setRegisterStatus(RegisterStatusEnum.NRTS.getCode());
        }
        registerBaseInfo.setInCupboardStatus(InCupboardStatusEnum.NCBD.getCode());
        registerBaseInfo.setCompanyId(companyId);
        registerBaseInfo.setCreator(userId);
        registerBaseInfo.setModifier(userId);
        registerEntity.setCollectionRegisterBaseInfo(registerBaseInfo);
        CollectionRegisterManageInfoVO registerManageInfo = BeanCopyUtils.copyByJSON(action, CollectionRegisterManageInfoVO.class);
        registerManageInfo.setCompanyId(companyId);
        registerManageInfo.setCreator(userId);
        registerManageInfo.setModifier(userId);
        registerEntity.setCollectionRegisterManageInfo(registerManageInfo);
        CollectionRegisterAttrInfoVO registerAttrInfoVO = BeanCopyUtils.copyByJSON(action, CollectionRegisterAttrInfoVO.class);
        registerAttrInfoVO.setCompanyId(companyId);
        registerAttrInfoVO.setCreator(userId);
        registerAttrInfoVO.setModifier(userId);
        if (Objects.nonNull(action.getAmount())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getAmount());
            registerAttrInfoVO.setAmount((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue()));
        }
        if (Objects.nonNull(action.getLength())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getLength());
            registerAttrInfoVO.setLength((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue()));
        }
        if (Objects.nonNull(action.getWidth())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getWidth());
            registerAttrInfoVO.setWidth((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue()));
        }
        if (Objects.nonNull(action.getHeight())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getHeight());
            registerAttrInfoVO.setHeight((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue()));
        }
        if (Objects.nonNull(action.getPresence())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getPresence());
            registerAttrInfoVO.setPresence((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue()));
        }
        if (Objects.nonNull(action.getCaliber())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getCaliber());
            registerAttrInfoVO.setCaliber((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue()));
        }

        if (Objects.nonNull(action.getBottomDiameter())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getBottomDiameter());
            registerAttrInfoVO.setBottomDiameter((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue()));
        }
        registerEntity.setCollectionRegisterAttrInfo(registerAttrInfoVO);
        CollectionRegisterAttrExVO registerAttrExVO = BeanCopyUtils.copyByJSON(action, CollectionRegisterAttrExVO.class);
        registerAttrExVO.setCompanyId(companyId);
        registerAttrExVO.setCreator(userId);
        registerAttrExVO.setModifier(userId);
        registerAttrExVO.setCollectsDate(action.getCollectsDate());
        registerEntity.setCollectionRegisterAttrExVO(registerAttrExVO);
        registerEntity.setAutoAudit(action.getAutoAudit());
        this.collectionRegisterRepository.saveCollectionRegister(registerEntity);

        // 自动提审（excel表格导入的没有autoAudit字段，不做处理）
        if(Objects.nonNull(action.getAutoAudit()) && action.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(userId);
            submitCheckAction.setCompanyId(companyId);
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.LOGIN);
            ScsCollectionRegisterInfoDO registerInfoDO = this.scsCollectionRegisterInfoMapper.selectByRegisterNo(companyId, action.getRegisterNo());
            Long registerInfoId = registerInfoDO.getId();
            submitCheckAction.setModelId(registerInfoId);
            submitCheckAction.setCreatorName(action.getCreatorName());
            this.checkService.submitCheck(submitCheckAction);
        }
        addCollectionRegisterDTO.setCollectionRegisterId(registerEntity.getCollectionRegisterId());
        return addCollectionRegisterDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionRegister(EditCollectionRegisterAction action) {
        Long companyId = action.getCompanyId();
        Long userId = action.getModifier();
        ScsCollectionRegisterInfoDO registerInfoDO = this.scsCollectionRegisterInfoMapper.selectById(companyId, action.getCollectionRegisterId());
        if (Objects.isNull(registerInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品登录信息不存在");
        }
        String registerNo = registerInfoDO.getRegisterNo();
        String registerNoNew = action.getRegisterNo();
        Long collectionId = registerInfoDO.getCollectionId();
        ScsCollectionInfoDO collectionInfoDO = this.scsCollectionInfoMapper.selectByRegisterNo(companyId, action.getRegisterNo());
        if (Objects.nonNull(collectionInfoDO) && !collectionId.equals(collectionInfoDO.getId())) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品总登记号已存在");
        }

        CollectionRegisterDetailDTO dto = queryByRegisterNo(companyId,registerNo);
        CollectionRegisterDetailVO collectionRegisterDetailVO = BeanCopyUtils.copyByJSON(dto, CollectionRegisterDetailVO.class);

        if (!registerNo.equals(registerNoNew)) {
            ScsCollectionInfoDO updateCollectionDO = new ScsCollectionInfoDO();
            updateCollectionDO.setId(collectionId);
            updateCollectionDO.setRegisterNo(registerNoNew);
            updateCollectionDO.setModifier(userId);
            this.scsCollectionInfoMapper.updateByPrimaryKeySelective(updateCollectionDO);
        }

        CollectionRegisterEntity registerEntity = new CollectionRegisterEntity();
        CollectionRegisterBaseInfoVO registerBaseInfo = BeanCopyUtils.copyByJSON(action, CollectionRegisterBaseInfoVO.class);
        registerBaseInfo.setModifier(userId);
        registerBaseInfo.setRegisterBaseId(action.getCollectionRegisterId());
        registerEntity.setCollectionRegisterBaseInfo(registerBaseInfo);
        CollectionRegisterManageInfoVO registerManageInfo = BeanCopyUtils.copyByJSON(action, CollectionRegisterManageInfoVO.class);
        registerManageInfo.setModifier(userId);
        registerManageInfo.setRegisterNo(registerNoNew);
        registerEntity.setCollectionRegisterManageInfo(registerManageInfo);
        CollectionRegisterAttrInfoVO registerAttrInfoVO = BeanCopyUtils.copyByJSON(action, CollectionRegisterAttrInfoVO.class);
        registerAttrInfoVO.setRegisterNo(registerNoNew);
        registerAttrInfoVO.setModifier(userId);

        registerEntity.setCollectionRegisterAttrInfo(registerAttrInfoVO);
        CollectionRegisterAttrExVO registerAttrExVO = BeanCopyUtils.copyByJSON(action, CollectionRegisterAttrExVO.class);
        registerAttrExVO.setRegisterNo(registerNoNew);
        registerAttrExVO.setModifier(userId);
        registerAttrExVO.setCollectsDate(action.getCollectsDate());
        registerEntity.setCollectionRegisterAttrExVO(registerAttrExVO);
        registerEntity.setAutoAudit(action.getAutoAudit());
        this.collectionRegisterRepository.generateSnapshotByRegisterId(companyId, userId, action.getCollectionRegisterId());
        this.collectionRegisterRepository.editCollectionRegister(registerEntity);
        LoginCollectionUpdateRecordEvent event = new LoginCollectionUpdateRecordEvent();
        event.setCompanyId(companyId);
        event.setRegisterInfoId(action.getCollectionRegisterId());
        event.setCollectionId(collectionId);
        event.setRegisterNo(registerNo);
        event.setModifier(action.getModifier());
        event.setModifierName(action.getModifierName());
        event.setRegisterEntity(registerEntity);
        event.setCollectionRegisterDetailVO(collectionRegisterDetailVO);
        DomainEventHolder.syncPublish(event);

        // 自动提审
        if(action.getAutoAudit() == (byte)1){
            SubmitCheckAction submitCheckAction = new SubmitCheckAction();
            submitCheckAction.setCreator(action.getModifier());
            submitCheckAction.setCompanyId(action.getCompanyId());
            submitCheckAction.setBelongModel(CollectionBizTypeEnum.LOGIN);
            submitCheckAction.setModelId(action.getCollectionRegisterId());
            submitCheckAction.setCreatorName(action.getModifierName());
            this.checkService.submitCheck(submitCheckAction);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delCollectionRegisterByRegisterNo(Long companyId, String registerNo) {
        ScsCollectionRegisterInfoDO registerInfoDO = this.scsCollectionRegisterInfoMapper.selectByRegisterNo(companyId, registerNo);
        if (Objects.isNull(registerInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品登录信息不存在");
        }

        ScsCollectionInfoDO collectionInfoDO = this.scsCollectionInfoMapper.selectByRegisterNo(companyId, registerNo);
        if (Objects.isNull(collectionInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品信息不存在");
        }
        String inputMode = collectionInfoDO.getInputMode();
        String inputType = collectionInfoDO.getInputType();
        if (StringUtils.isNotBlank(inputMode) && StringUtils.isNotBlank(inputType)
                && InputModeEnum.REG.getCode().equals(inputMode) && InputTypeEnum.MLE.getCode().equals(inputType)) {
            this.scsCollectionInfoMapper.deleteById(companyId, collectionInfoDO.getId());
        }
        this.collectionRegisterRepository.delByRegisterNo(companyId, registerNo);
        String documentId = collectionInfoDO.getDocumentId();
        if (StringUtils.isNotBlank(documentId)) {
            this.documentService.listByDocIds(companyId, documentId);
        }
    }

    @Override
    public SimplePageInfo<CollectionRegisterPageDTO> queryPageByCondition(RegisterQueryPageByConditionAction action) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        Long companyId = action.getCompanyId();
        List<RegisterStatusEnum> registerStatus = action.getRegisterStatus();
        List<String> registerStatusQuery = null;
        if (CollectionUtils.isNotEmpty(registerStatus)) {
            registerStatusQuery = registerStatus.stream().map(RegisterStatusEnum::getCode).collect(Collectors.toList());
        }

        // 是否需要筛选藏品登录属性信息
        String attrInfo = null;
        if(action.getCompleteDegree() != null || action.getSource() != null){
            attrInfo = "是";
        }

        // 是否需要筛选质地
        String selectTexture = null;
        List<Long> registerInfoIds = Lists.newArrayList();
        if(StringUtils.isNotBlank(action.getTexture())){
            selectTexture = "是";
            List<ScsCollectionRegisterAttrInfoDO> attrInfoDOList = scsCollectionRegisterAttrInfoMapper.listByCompanyId(companyId);
            registerInfoIds = selectRegisterInfoIdsByTexture(action.getTexture(),attrInfoDOList);
            if(CollectionUtils.isEmpty(registerInfoIds)){
                return new SimplePageInfo<>();
            }
        }

        action.startPage();
        List<ScsCollectionRegisterInfoDO> registerInfoDOList = this.scsCollectionRegisterInfoMapper
                .listPageByCondition(companyId, action.getRegisterNo(), action.getName(), action.getClassifyDesc(), registerStatusQuery, action.getCollectionIds(), action.getSortBy(),
                        action.getStartModified(), action.getEndModified(), action.getIdentifyLevel(), action.getCategory(), action.getAge(),attrInfo,
                        action.getCompleteDegree(), selectTexture,registerInfoIds, action.getSource(),
                        action.getCheckStatus(), action.getIsUserChecked(), userBO.getUserId(), action.getWarehouseId());
        if (CollectionUtils.isEmpty(registerInfoDOList)) {
            return new SimplePageInfo<>();
        }

        SimplePageInfo<CollectionRegisterPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(registerInfoDOList, CollectionRegisterPageDTO.class);



        List<String> registerList = registerInfoDOList.stream().map(ScsCollectionRegisterInfoDO::getRegisterNo).collect(Collectors.toList());
        List<Long> registerIdList = registerInfoDOList.stream().map(ScsCollectionRegisterInfoDO::getId).collect(Collectors.toList());

        // 二维、三维数量
        List<GroupByRegisterInfoIdDO>  twoImageDOList = scsCollectionTwoImageMapper.groupByRegisterInfoId(companyId,registerIdList);
        List<GroupByRegisterInfoIdDO>  threeModelDOList = scsCollectionThreeModelMapper.groupByRegisterInfoId(companyId,registerIdList);
        Map<Long,Integer> twoImageMap = Maps.newHashMap();
        Map<Long,Integer> threeModelMap = Maps.newHashMap();
        twoImageDOList.forEach(s -> twoImageMap.put(s.getRegisterInfoId(),s.getNum()));
        threeModelDOList.forEach(s -> threeModelMap.put(s.getRegisterInfoId(),s.getNum()));

        List<ScsCollectionRegisterAttrInfoDO> attrInfoDOList = this.scsCollectionRegisterAttrInfoMapper.listByRegisterNos(companyId,registerList);
        Map<String, ScsCollectionRegisterAttrInfoDO> attrInfoDOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(attrInfoDOList)){
            attrInfoDOList.forEach(attrInfoDO-> attrInfoDOMap.put(attrInfoDO.getRegisterNo(), attrInfoDO));
        }
        List<ScsCollectionRegisterManageInfoDO> manageInfoDOList = this.scsCollectionRegisterManageInfoMapper.listByRegisterNos(companyId,registerList);
        Map<String, ScsCollectionRegisterManageInfoDO> manageInfoDOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(manageInfoDOList)){
            manageInfoDOList.forEach(manageInfoDO-> manageInfoDOMap.put(manageInfoDO.getRegisterNo(), manageInfoDO));
        }
        List<Long> collectionIds = registerInfoDOList.stream().map(ScsCollectionRegisterInfoDO::getCollectionId).collect(Collectors.toList());
        List<ScsLogoffCollectionInfoDO> logoffInfoList = this.scsLogoffCollectionInfoMapper.listByCollectionIds(companyId, collectionIds);
        Map<Long, String> logoffInfoMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(logoffInfoList)){
            logoffInfoMap = logoffInfoList.stream().collect(Collectors.toMap(ScsLogoffCollectionInfoDO::getCollectionId, ScsLogoffCollectionInfoDO::getLogoffCertificateNo,
                    (key1, key2) -> key2));
        }

        Map<Long, String> finalLogoffInfoMap = logoffInfoMap;

        List<Long> classificationIds = Lists.newArrayList();
        StringBuilder documentIdBuilder = new StringBuilder();
        registerInfoDOList.forEach(registerInfoDO -> {
            if (Objects.nonNull(registerInfoDO.getClassify())) {
                classificationIds.add(registerInfoDO.getClassify());
            }
            if (Objects.nonNull(registerInfoDO.getIdentifyLevel())) {
                classificationIds.add(registerInfoDO.getIdentifyLevel());
            }
            if (Objects.nonNull(registerInfoDO.getAge())) {
                classificationIds.add(registerInfoDO.getAge());
            }
            if (Objects.nonNull(registerInfoDO.getCategory())) {
                classificationIds.add(registerInfoDO.getCategory());
            }
            if (StringUtils.isNotBlank(registerInfoDO.getDocumentId())) {
                documentIdBuilder.append(registerInfoDO.getDocumentId()).append(",");
            }
            // 如果是PPOWH的藏品，直接视作POWH
            if(StringUtils.isNotBlank(registerInfoDO.getRegisterStatus()) && RegisterStatusEnum.PPOWH.getCode().equals(registerInfoDO.getRegisterStatus())){
                registerInfoDO.setRegisterStatus(RegisterStatusEnum.POWH.getCode());
            }
        });

        List<String> registerNoList = registerInfoDOList.stream().map(ScsCollectionRegisterInfoDO::getRegisterNo).collect(Collectors.toList());
        List<Long> registerIds = registerInfoDOList.stream().map(ScsCollectionRegisterInfoDO::getId).collect(Collectors.toList());
        List<Long> userIds = registerInfoDOList.stream().map(ScsCollectionRegisterInfoDO::getCreator).collect(Collectors.toList());
        List<ScsCollectionRegisterManageInfoDO> registerManageInfoDOList = this.scsCollectionRegisterManageInfoMapper.listByRegisterNos(companyId, registerNoList);
        Map<String, ScsCollectionRegisterManageInfoDO> registerManageInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(registerManageInfoDOList)) {
            registerManageInfoDOList.forEach(registerManageInfoDO -> {
                registerManageInfoMap.put(registerManageInfoDO.getRegisterNo(), registerManageInfoDO);
                if (Objects.nonNull(registerManageInfoDO.getWarehouseId())) {
                    classificationIds.add(registerManageInfoDO.getWarehouseId());
                }
                if (Objects.nonNull(registerManageInfoDO.getCupboardId())) {
                    classificationIds.add(registerManageInfoDO.getCupboardId());
                }
                if (Objects.nonNull(registerManageInfoDO.getFloorId())) {
                    classificationIds.add(registerManageInfoDO.getFloorId());
                }
                if (Objects.nonNull(registerManageInfoDO.getDrawerId())) {
                    classificationIds.add(registerManageInfoDO.getDrawerId());
                }
                if (Objects.nonNull(registerManageInfoDO.getColumnId())) {
                    classificationIds.add(registerManageInfoDO.getColumnId());
                }
                if (Objects.nonNull(registerManageInfoDO.getNumberId())) {
                    classificationIds.add(registerManageInfoDO.getNumberId());
                }
            });
        }
        QueryBatchCheckStatusAction batchCheckStatusAction = new QueryBatchCheckStatusAction();
        batchCheckStatusAction.setBelongModel(CollectionBizTypeEnum.LOGIN);
        batchCheckStatusAction.setCompanyId(companyId);
        batchCheckStatusAction.setModelIds(registerIds);
        Map<Long, CheckCurrentInfoDTO> checkInfoMap = this.checkService.queryBatchCheckStatus(batchCheckStatusAction);
        List<ScsCollectionRegisterAttrInfoDO> registerAttrInfoDOList = this.scsCollectionRegisterAttrInfoMapper.listByRegisterNos(companyId, registerNoList);
        Map<String, ScsCollectionRegisterAttrInfoDO> registerAttrInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(registerAttrInfoDOList)) {
            registerAttrInfoDOList.forEach(registerAttrInfoDO -> {
                registerAttrInfoMap.put(registerAttrInfoDO.getRegisterNo(), registerAttrInfoDO);
                if (Objects.nonNull(registerAttrInfoDO.getCompleteDegree())) {
                    classificationIds.add(registerAttrInfoDO.getCompleteDegree());
                }
                if (Objects.nonNull(registerAttrInfoDO.getCountUnit())) {
                    classificationIds.add(registerAttrInfoDO.getCountUnit());
                }
                if (Objects.nonNull(registerAttrInfoDO.getCountUnit())) {
                    classificationIds.add(registerAttrInfoDO.getCountUnit());
                }
                if (StringUtils.isNotBlank(registerAttrInfoDO.getTexture())) {
                    List<Long> textureIds = Stream.of(registerAttrInfoDO.getTexture().split(","))
                            .map(Long::parseLong).distinct().collect(Collectors.toList());
                    classificationIds.addAll(textureIds);
                }
                if (Objects.nonNull(registerAttrInfoDO.getSource())) {
                    classificationIds.add(registerAttrInfoDO.getSource());
                }
                if (Objects.nonNull(registerAttrInfoDO.getQualityRange())) {
                    classificationIds.add(registerAttrInfoDO.getQualityRange());
                }
                if (Objects.nonNull(registerAttrInfoDO.getQualityUnit())) {
                    classificationIds.add(registerAttrInfoDO.getQualityUnit());
                }
            });
        }

        Map<Long, String> classificationMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(classificationIds)) {
            List<CommClassificationDO> classificationDOList = this.commClassificationMapper.listByIds(companyId, classificationIds);
            if (CollectionUtils.isNotEmpty(classificationDOList)) {
                classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                        CommClassificationDO::getName, (key1, key2) -> key2));
            }
        }
        Map<Long, String> userMap = this.userService.queryUserNameMap(companyId, userIds);
        Map<Long, CommonDocumentDTO> documentDTOMap =Maps.newHashMap();
        if (documentIdBuilder.length() != 0) {
            String documentStr = documentIdBuilder.deleteCharAt(documentIdBuilder.length()-1).toString();
            List<CommonDocumentDTO> documentDTOList = this.documentService.listByDocIds(companyId, documentStr);
            if (CollectionUtils.isNotEmpty(documentDTOList)) {
                documentDTOList.forEach(document -> {
                    documentDTOMap.put(document.getDocumentId(), document);
                });
            }
        }

        Map<Long, String> finalClassificationMap = classificationMap;
        AtomicLong index = new AtomicLong(1L);
        List<CollectionRegisterPageDTO> pageDTOList = registerInfoDOList.stream().map(collectionRegisterInfoDO -> {
            CollectionRegisterPageDTO collectionRegisterPageDTO = BeanCopyUtils.copyByJSON(collectionRegisterInfoDO, CollectionRegisterPageDTO.class);
            collectionRegisterPageDTO.setRegisterInfoId(collectionRegisterInfoDO.getId());
            collectionRegisterPageDTO.setRegisterName("藏品总登记号");
            collectionRegisterPageDTO.setIndex(index.get());
            index.getAndIncrement();
            if (registerManageInfoMap.containsKey(collectionRegisterInfoDO.getRegisterNo())) {
                ScsCollectionRegisterManageInfoDO registerManageInfoDO = registerManageInfoMap.get(collectionRegisterInfoDO.getRegisterNo());
                collectionRegisterPageDTO.setWarehouseId(registerManageInfoDO.getWarehouseId());
                if (finalClassificationMap.containsKey(registerManageInfoDO.getWarehouseId())) {
                    collectionRegisterPageDTO.setWarehouseName(finalClassificationMap.get(registerManageInfoDO.getWarehouseId()));
                }
                collectionRegisterPageDTO.setCupboardId(registerManageInfoDO.getCupboardId());
                if (finalClassificationMap.containsKey(registerManageInfoDO.getCupboardId())) {
                    collectionRegisterPageDTO.setCupboardName(finalClassificationMap.get(registerManageInfoDO.getCupboardId()));
                }
                collectionRegisterPageDTO.setFloorId(registerManageInfoDO.getFloorId());
                if (finalClassificationMap.containsKey(registerManageInfoDO.getFloorId())) {
                    collectionRegisterPageDTO.setFloorName(finalClassificationMap.get(registerManageInfoDO.getFloorId()));
                }
                collectionRegisterPageDTO.setDrawerId(registerManageInfoDO.getDrawerId());
                if (finalClassificationMap.containsKey(registerManageInfoDO.getDrawerId())) {
                    collectionRegisterPageDTO.setDrawerName(finalClassificationMap.get(registerManageInfoDO.getDrawerId()));
                }
                collectionRegisterPageDTO.setColumnId(registerManageInfoDO.getColumnId());
                if (finalClassificationMap.containsKey(registerManageInfoDO.getColumnId())) {
                    collectionRegisterPageDTO.setColumnName(finalClassificationMap.get(registerManageInfoDO.getColumnId()));
                }
                collectionRegisterPageDTO.setNumberId(registerManageInfoDO.getNumberId());
                if (finalClassificationMap.containsKey(registerManageInfoDO.getNumberId())) {
                    collectionRegisterPageDTO.setNumberName(finalClassificationMap.get(registerManageInfoDO.getNumberId()));
                }
            }
            if (registerAttrInfoMap.containsKey(collectionRegisterInfoDO.getRegisterNo())) {
                ScsCollectionRegisterAttrInfoDO registerAttrInfoDO = registerAttrInfoMap.get(collectionRegisterInfoDO.getRegisterNo());
                collectionRegisterPageDTO.setCompleteDegree(registerAttrInfoDO.getCompleteDegree());
                if (finalClassificationMap.containsKey(registerAttrInfoDO.getCompleteDegree())) {
                    collectionRegisterPageDTO.setCompleteDegreeName(finalClassificationMap.get(registerAttrInfoDO.getCompleteDegree()));
                }
                String texture = registerAttrInfoDO.getTexture();
                if (StringUtils.isNotBlank(texture)) {
                    collectionRegisterPageDTO.setTexture(texture);
                    List<Long> textureIds = Stream.of(texture.split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                    StringBuilder textureBuilder = new StringBuilder();
                    textureIds.forEach(textureId -> {
                        textureBuilder.append(finalClassificationMap.get(textureId));
                        textureBuilder.append(",");
                    });
                    textureBuilder.deleteCharAt(textureBuilder.toString().length() - 1);
                    collectionRegisterPageDTO.setTextureName(textureBuilder.toString());
                }
                collectionRegisterPageDTO.setSource(registerAttrInfoDO.getSource());
                if (finalClassificationMap.containsKey(registerAttrInfoDO.getSource())) {
                    collectionRegisterPageDTO.setSourceName(finalClassificationMap.get(registerAttrInfoDO.getSource()));
                }

                collectionRegisterPageDTO.setCountUnit(registerAttrInfoDO.getCountUnit());
                if (finalClassificationMap.containsKey(registerAttrInfoDO.getCountUnit())) {
                    collectionRegisterPageDTO.setCountUnitName(finalClassificationMap.get(registerAttrInfoDO.getCountUnit()));
                }
                collectionRegisterPageDTO.setQualityRange(registerAttrInfoDO.getQualityRange());
                if (finalClassificationMap.containsKey(registerAttrInfoDO.getQualityRange())) {
                    collectionRegisterPageDTO.setQualityRangeName(finalClassificationMap.get(registerAttrInfoDO.getQualityRange()));
                }
                collectionRegisterPageDTO.setQualityRange(registerAttrInfoDO.getQualityUnit());
                if (finalClassificationMap.containsKey(registerAttrInfoDO.getQualityUnit())) {
                    collectionRegisterPageDTO.setQualityUnitName(finalClassificationMap.get(registerAttrInfoDO.getQualityUnit()));
                }
                collectionRegisterPageDTO.setActualCount(registerAttrInfoDO.getActualCount());
                collectionRegisterPageDTO.setCollectionCount(registerAttrInfoDO.getCollectionCount());
                collectionRegisterPageDTO.setSize(registerAttrInfoDO.getSize());
                collectionRegisterPageDTO.setLength(Objects.nonNull(registerAttrInfoDO.getLength()) ? registerAttrInfoDO.getLength() / 100.0 : null);
                collectionRegisterPageDTO.setWidth(Objects.nonNull(registerAttrInfoDO.getWidth()) ? registerAttrInfoDO.getWidth() / 100.0 : null);
                collectionRegisterPageDTO.setHeight(Objects.nonNull(registerAttrInfoDO.getHeight()) ? registerAttrInfoDO.getHeight() / 100.0 : null);
                collectionRegisterPageDTO.setSpecificQuality(Objects.nonNull(registerAttrInfoDO.getSpecificQuality()) ? registerAttrInfoDO.getSpecificQuality() / 100.0 : null);
            }

            if(twoImageMap.containsKey(collectionRegisterInfoDO.getId())){
                collectionRegisterPageDTO.setTwoModelCount(twoImageMap.get(collectionRegisterInfoDO.getId()));
            }

            if(threeModelMap.containsKey(collectionRegisterInfoDO.getId())){
                collectionRegisterPageDTO.setThreeModelCount(threeModelMap.get(collectionRegisterInfoDO.getId()));
            }

            if (finalClassificationMap.containsKey(collectionRegisterInfoDO.getClassify())) {
                collectionRegisterPageDTO.setClassifyName(finalClassificationMap.get(collectionRegisterInfoDO.getClassify()));
            }
            if (finalClassificationMap.containsKey(collectionRegisterInfoDO.getIdentifyLevel())) {
                collectionRegisterPageDTO.setIdentifyLevelName(finalClassificationMap.get(collectionRegisterInfoDO.getIdentifyLevel()));
            }
            if (finalClassificationMap.containsKey(collectionRegisterInfoDO.getAge())) {
                collectionRegisterPageDTO.setAgeName(finalClassificationMap.get(collectionRegisterInfoDO.getAge()));
            }
            if (userMap.containsKey(collectionRegisterInfoDO.getCreator())) {
                collectionRegisterPageDTO.setCreatorName(userMap.get(collectionRegisterInfoDO.getCreator()));
            }
            collectionRegisterPageDTO.setCategory(collectionRegisterInfoDO.getCategory());
            if (finalClassificationMap.containsKey(collectionRegisterInfoDO.getCategory())) {
                collectionRegisterPageDTO.setCategoryName(finalClassificationMap.get(collectionRegisterInfoDO.getCategory()));
            }

            if(Objects.nonNull(attrInfoDOMap.get(collectionRegisterInfoDO.getRegisterNo()))){
                collectionRegisterPageDTO.setRemarks(attrInfoDOMap.get(collectionRegisterInfoDO.getRegisterNo()).getCollectionRemark());
            }
            if(Objects.nonNull(manageInfoDOMap.get(collectionRegisterInfoDO.getRegisterNo()))){
                collectionRegisterPageDTO.setInMuseumCertificateNo(manageInfoDOMap.get(collectionRegisterInfoDO.getRegisterNo()).getInMuseumCertificateNo());
            }
            if(Objects.nonNull(finalLogoffInfoMap.get(collectionRegisterInfoDO.getCollectionId()))){
                collectionRegisterPageDTO.setLogoffCerNo(finalLogoffInfoMap.get(collectionRegisterInfoDO.getCollectionId()));
            }

            List<CommonDocumentDTO> documentDTOList = Lists.newArrayList();
            if (StringUtils.isNotBlank(collectionRegisterInfoDO.getDocumentId())) {
                List<Long> documentIds = Stream.of(collectionRegisterInfoDO.getDocumentId().split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                documentIds.forEach(documentId -> documentDTOList.add(documentDTOMap.get(documentId)));
            }
            collectionRegisterPageDTO.setCommonDocumentList(documentDTOList);
            if (checkInfoMap != null && checkInfoMap.containsKey(collectionRegisterInfoDO.getId())) {
                CheckCurrentInfoDTO checkCurrentInfoDTO = checkInfoMap.get(collectionRegisterInfoDO.getId());
                collectionRegisterPageDTO.setCheckStatus(checkCurrentInfoDTO.getCheckStatus().getCode());
                collectionRegisterPageDTO.setCheckStatusDesc(checkCurrentInfoDTO.getCheckStatus().getDesc());
                collectionRegisterPageDTO.setNeedManName(checkCurrentInfoDTO.getNeedManName());
                collectionRegisterPageDTO.setHasChecked(YesOrNoEnum.否.getCode().byteValue());
                if(Objects.nonNull(checkCurrentInfoDTO.getNeedManId()) && checkCurrentInfoDTO.getNeedManId().contains(userBO.getUserId())){
                    collectionRegisterPageDTO.setHasChecked(YesOrNoEnum.是.getCode().byteValue());
                }
            }
//            boolean returnFlag = true;
//            if (StringUtils.isNotBlank(action.getCheckStatus())) {
//                if (!action.getCheckStatus().equals(collectionRegisterPageDTO.getCheckStatus())) {
//                    returnFlag = false;
//                }
//            }
//            if (Objects.nonNull(action.getSource())) {
//                if (!action.getSource().equals(collectionRegisterPageDTO.getSource())) {
//                    returnFlag = false;
//                }
//            }
//            if (Objects.nonNull(action.getWarehouseId())) {
//                if (!action.getWarehouseId().equals(collectionRegisterPageDTO.getWarehouseId())) {
//                    returnFlag = false;
//                }
//            }
//            if (Objects.nonNull(action.getCompleteDegree())) {
//                if (!action.getCompleteDegree().equals(collectionRegisterPageDTO.getCompleteDegree())) {
//                    returnFlag = false;
//                }
//            }
//            if (StringUtils.isNotBlank(action.getTexture())) {
//                if (!action.getTexture().equals(collectionRegisterPageDTO.getTexture())) {
//                    returnFlag = false;
//                }
//            }
//            if (action.getIsUserChecked() != null && action.getIsUserChecked() == 1) {
//                if (collectionRegisterPageDTO.getHasChecked() == YesOrNoEnum.否.getCode().byteValue()) {
//                    returnFlag = false;
//                }
//            }
//            if (returnFlag) {
//                return collectionRegisterPageDTO;
//            } else {
//                return null;
//            }
            return collectionRegisterPageDTO;
        }).collect(Collectors.toList());
//        return PaginationUtils.pagination(pageDTOList, action.getPageNum(), action.getPageSize());
        pageInfo.setList(pageDTOList);
        return pageInfo;
    }

    private List<Long> selectRegisterInfoIdsByTexture(String texture,List<ScsCollectionRegisterAttrInfoDO> attrInfoDOList){
        List<Long> registerInfoIds = Lists.newArrayList();
        if(CollectionUtils.isEmpty(attrInfoDOList)){
            return registerInfoIds;
        }
        List<String> selectTextures = Arrays.asList(texture.split(","));
        attrInfoDOList.forEach(s -> {
            if(StringUtils.isNotBlank(s.getTexture())){
                List<String> textures = Arrays.asList(s.getTexture().split(","));
                if(textures.containsAll(selectTextures)){
                    registerInfoIds.add(s.getRegisterInfoId());
                }
            }
        });
        return registerInfoIds;
    }

    @Override
    public CollectionRegisterDetailDTO queryByRegisterNo(Long companyId, String registerNo) {
        ScsCollectionRegisterInfoDO registerInfoDO = this.scsCollectionRegisterInfoMapper.selectByRegisterNo(companyId, registerNo);
        if (Objects.isNull(registerInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品登录信息不存在");
        }


        CollectionRegisterDetailDTO registerDetailDTO = BeanCopyUtils.copyByJSON(registerInfoDO, CollectionRegisterDetailDTO.class);
        registerDetailDTO.setRegisterInfoId(registerInfoDO.getId());
        ScsCollectionRegisterManageInfoDO registerManageInfoDO = this.scsCollectionRegisterManageInfoMapper.selectByRegisterNo(companyId, registerNo);
        this.setRegisterManageInfo(registerDetailDTO, registerManageInfoDO);
        ScsCollectionRegisterAttrInfoDO registerAttrInfoDO = this.scsCollectionRegisterAttrInfoMapper.selectByRegisterNo(companyId, registerNo);
        this.setRegisterAttrInfo(registerDetailDTO, registerAttrInfoDO);
        ScsCollectionRegisterAttrExDO registerAttrExDO = this.scsCollectionRegisterAttrExMapper.selectByRegisterNo(companyId, registerNo);
        this.setRegisterAttrEx(registerDetailDTO, registerAttrExDO);
        String documentId = registerDetailDTO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return registerDetailDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        registerDetailDTO.setCommDocumentList(commDocumentList);
        return registerDetailDTO;
    }


    @Override
    public CollectionRegisterDetailDTO queryById(Long companyId,Long registerInfoId) {
        ScsCollectionRegisterInfoDO registerInfoDO = this.scsCollectionRegisterInfoMapper.selectById(companyId, registerInfoId);
        if (Objects.isNull(registerInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品登录信息不存在");
        }

        CollectionRegisterDetailDTO registerDetailDTO = BeanCopyUtils.copyByJSON(registerInfoDO, CollectionRegisterDetailDTO.class);
        registerDetailDTO.setRegisterInfoId(registerInfoDO.getId());
        ScsCollectionRegisterManageInfoDO registerManageInfoDO = this.scsCollectionRegisterManageInfoMapper.selectByRegisterId(companyId, registerInfoId);
        this.setRegisterManageInfo(registerDetailDTO, registerManageInfoDO);
        ScsCollectionRegisterAttrInfoDO registerAttrInfoDO = this.scsCollectionRegisterAttrInfoMapper.selectByRegisterId(companyId, registerInfoId);
        this.setRegisterAttrInfo(registerDetailDTO, registerAttrInfoDO);
        ScsCollectionRegisterAttrExDO registerAttrExDO = this.scsCollectionRegisterAttrExMapper.selectByRegisterId(companyId, registerInfoId);
        this.setRegisterAttrEx(registerDetailDTO, registerAttrExDO);
        ScsLogoffCollectionInfoDO logoffCollectionInfoDO = this.scsLogoffCollectionInfoMapper.selectByCollectionId(companyId, registerInfoDO.getCollectionId());
        if (Objects.nonNull(logoffCollectionInfoDO)) {
            registerDetailDTO.setLogoffCerNo(logoffCollectionInfoDO.getLogoffCertificateNo());
        }
        String documentId = registerDetailDTO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return registerDetailDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        registerDetailDTO.setCommDocumentList(commDocumentList);
        return registerDetailDTO;
    }

    @Override
    public SimplePageInfo<CollectionRegisterDetailSnapshotDTO> queryByRegisterInfoId(Long companyId, Long registerInfoId, Integer pageNum, Integer pageSize) {
        PageInfoBaseReq pageInfoBaseReq = new PageInfoBaseReq();
        pageInfoBaseReq.startPage(pageNum, pageSize, null);
        List<ScsCollectionRegisterInfoSnapshotDO> registerInfoSnapshotDOList = this.scsCollectionRegisterInfoSnapshotMapper
                .listByRegisterInfoId(companyId, registerInfoId, "gmt_modified desc");
        if (CollectionUtils.isEmpty(registerInfoSnapshotDOList)) {
            return new SimplePageInfo<>();
        }
        List<Long> classificationIds = Lists.newArrayList();
        List<Long> registerInfoIds = Lists.newArrayList();
        registerInfoSnapshotDOList.forEach(registerInfoDO -> {
            registerInfoIds.add(registerInfoDO.getId());
            if (Objects.nonNull(registerInfoDO.getIdentifyLevel())) {
                classificationIds.add(registerInfoDO.getIdentifyLevel());
            }
            if (Objects.nonNull(registerInfoDO.getAge())) {
                classificationIds.add(registerInfoDO.getAge());
            }
        });
        Map<Long, String> classificationMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(classificationIds)) {
            List<CommClassificationDO> classificationDOList = this.commClassificationMapper.listByIds(companyId, classificationIds);
            if (CollectionUtils.isNotEmpty(classificationDOList)) {
                classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                        CommClassificationDO::getName, (key1, key2) -> key2));
            }
        }
        List<ScsCollectionRegisterAttrInfoSnapshotDO> registerAttrInfoSnapshotDOS = this.scsCollectionRegisterAttrInfoSnapshotMapper
                .listByRegisterInfoIds(companyId, registerInfoIds);
        Map<Long, ScsCollectionRegisterAttrInfoSnapshotDO> registerAttrInfoSnapshotMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(registerAttrInfoSnapshotDOS)) {
            registerAttrInfoSnapshotDOS.forEach(registerAttrInfoSnapshotDO -> {
                registerAttrInfoSnapshotMap.put(registerAttrInfoSnapshotDO.getRegisterInfoSnapshotId(), registerAttrInfoSnapshotDO);
            });
        }
        SimplePageInfo<CollectionRegisterDetailSnapshotDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(registerInfoSnapshotDOList, CollectionRegisterDetailSnapshotDTO.class);
        Map<Long, String> finalClassificationMap = classificationMap;
        List<CollectionRegisterDetailSnapshotDTO> registerDetailSnapshotDTOList = registerInfoSnapshotDOList.stream().map(registerInfoSnapshotDO -> {
            CollectionRegisterDetailSnapshotDTO registerDetailSnapshotDTO =
                    BeanCopyUtils.copyByJSON(registerInfoSnapshotDO, CollectionRegisterDetailSnapshotDTO.class);
            registerDetailSnapshotDTO.setRegisterInfoSnapshotId(registerInfoSnapshotDO.getId());
            registerDetailSnapshotDTO.setRegisterInfoId(registerInfoSnapshotDO.getRegisterInfoId());
            if (finalClassificationMap.containsKey(registerInfoSnapshotDO.getIdentifyLevel())) {
                registerDetailSnapshotDTO.setIdentifyLevelName(finalClassificationMap.get(registerInfoSnapshotDO.getIdentifyLevel()));
            }
            if (finalClassificationMap.containsKey(registerInfoSnapshotDO.getAge())) {
                registerDetailSnapshotDTO.setAgeName(finalClassificationMap.get(registerInfoSnapshotDO.getAge()));
            }
            if (registerAttrInfoSnapshotMap.containsKey(registerInfoSnapshotDO.getId())) {
                ScsCollectionRegisterAttrInfoSnapshotDO registerAttrInfoSnapshotDO = registerAttrInfoSnapshotMap.get(registerInfoSnapshotDO.getId());
                registerDetailSnapshotDTO.setSize(registerAttrInfoSnapshotDO.getSize());
                registerDetailSnapshotDTO.setActualCount(registerAttrInfoSnapshotDO.getActualCount());
                registerDetailSnapshotDTO.setCollectionCount(registerAttrInfoSnapshotDO.getCollectionCount());
            }
            return registerDetailSnapshotDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(registerDetailSnapshotDTOList);
        return pageInfo;
    }

    @Override
    public CollectionRegisterDetailDTO queryByRegisterInfoSnapshotId(Long companyId, Long registerInfoSnapshotId) {
        ScsCollectionRegisterInfoSnapshotDO registerInfoSnapshotDO = this.scsCollectionRegisterInfoSnapshotMapper.selectById(companyId, registerInfoSnapshotId);
        if (Objects.isNull(registerInfoSnapshotDO)) {
            return null;
        }
        CollectionRegisterDetailDTO registerDetailDTO = BeanCopyUtils.copyByJSON(registerInfoSnapshotDO, CollectionRegisterDetailDTO.class);
        registerDetailDTO.setRegisterInfoSnapshotId(registerInfoSnapshotDO.getId());
        registerDetailDTO.setRegisterInfoId(registerInfoSnapshotDO.getRegisterInfoId());
        ScsCollectionRegisterManageInfoSnapshotDO registerManageInfoSnapshotDO = this.scsCollectionRegisterManageInfoSnapshotMapper
                .selectByRegisterInfoId(companyId, registerInfoSnapshotId);
        if (Objects.nonNull(registerManageInfoSnapshotDO)) {
            ScsCollectionRegisterManageInfoDO registerManageInfoDO = BeanCopyUtils
                    .copyByJSON(registerManageInfoSnapshotDO, ScsCollectionRegisterManageInfoDO.class);
            this.setRegisterManageInfo(registerDetailDTO, registerManageInfoDO);
        }
        ScsCollectionRegisterAttrInfoSnapshotDO registerAttrInfoSnapshotDO = this.scsCollectionRegisterAttrInfoSnapshotMapper
                .selectByRegisterInfoId(companyId, registerInfoSnapshotId);
        if (Objects.nonNull(registerAttrInfoSnapshotDO)) {
            ScsCollectionRegisterAttrInfoDO registerAttrInfoDO = BeanCopyUtils.copyByJSON(registerAttrInfoSnapshotDO, ScsCollectionRegisterAttrInfoDO.class);
            this.setRegisterAttrInfo(registerDetailDTO, registerAttrInfoDO);
        }
        ScsCollectionRegisterAttrExSnapshotDO registerAttrExSnapshotDO = this.scsCollectionRegisterAttrExSnapshotMapper
                .selectByRegisterInfoId(companyId, registerInfoSnapshotId);
        if (Objects.nonNull(registerAttrExSnapshotDO)) {
            ScsCollectionRegisterAttrExDO registerAttrExDO = BeanCopyUtils.copyByJSON(registerAttrExSnapshotDO, ScsCollectionRegisterAttrExDO.class);
            this.setRegisterAttrEx(registerDetailDTO, registerAttrExDO);
        }
        String documentId = registerDetailDTO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return registerDetailDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        registerDetailDTO.setCommDocumentList(commDocumentList);
        return registerDetailDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByRegisterInfoSnapshotId(Long companyId, Long registerInfoSnapshotId) {
        ScsCollectionRegisterInfoSnapshotDO registerInfoSnapshotDO = this.scsCollectionRegisterInfoSnapshotMapper.selectById(companyId, registerInfoSnapshotId);
        if (Objects.isNull(registerInfoSnapshotDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品登录底账信息不存在");
        }
        this.collectionRegisterRepository.delByRegisterInfoSnapshotId(companyId, registerInfoSnapshotId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inCupboardByRegisterInfoId(InCupboardByRegisterNoAction action) {
        Long companyId = action.getCompanyId();
        Long registerInfoId = action.getRegisterInfoId();
        Long modifier = action.getModifier();
        ScsCollectionRegisterInfoDO registerInfoDO = this.scsCollectionRegisterInfoMapper.selectById(companyId, registerInfoId);
        if (Objects.isNull(registerInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品登录信息不存在");
        }
        this.collectionRegisterRepository.generateSnapshotByRegisterId(companyId, action.getModifier(), action.getRegisterInfoId());
        ScsCollectionRegisterManageInfoDO registerManageInfoDO = this.scsCollectionRegisterManageInfoMapper.selectByRegisterNo(companyId, registerInfoDO.getRegisterNo());
        registerManageInfoDO.setRegisterInfoId(registerInfoId);
        registerManageInfoDO.setWarehouseId(action.getWarehouseId());
        registerManageInfoDO.setCupboardId(action.getCupboardId());
        registerManageInfoDO.setFloorId(action.getFloorId());
        registerManageInfoDO.setDrawerId(action.getDrawerId());
        registerManageInfoDO.setColumnId(action.getColumnId());
        registerManageInfoDO.setNumberId(action.getNumberId());
        registerManageInfoDO.setModifier(modifier);
        this.scsCollectionRegisterManageInfoMapper.updateByRegisterInfoIdAll(registerManageInfoDO);
        ScsCollectionRegisterInfoDO registerInfoUpdate = new ScsCollectionRegisterInfoDO();
        registerInfoUpdate.setId(registerInfoId);
        registerInfoUpdate.setInCupboardStatus(InCupboardStatusEnum.ACBD.getCode());
        registerInfoUpdate.setModifier(modifier);
        this.scsCollectionRegisterInfoMapper.updateByPrimaryKeySelective(registerInfoUpdate);
    }

    @Override
    public void updateRegisterStatuesByCollectionIds(Long companyId, RegisterStatusEnum registerStatusEnum, List<Long> collectionIds,Long modifier) {
        if (CollectionUtils.isEmpty(collectionIds)) {
            return;
        }
        this.scsCollectionRegisterInfoMapper.updateRegisterStatusByCollectionIds(companyId, registerStatusEnum.getCode(), collectionIds,modifier);
    }

    @Override
    public List<CollectionLedgerDTO> queryLedgerByRegisterNo(Long companyId,List<String> registerNos,String registerNo,
                                                             String classifyDesc,String name,Long age,Long identifyLevel,String texture,
                                                             Long source,Long category,Long completeDegree) {
        // 判断是否包含 质地、藏品来源、完残程度等查询条件
        int query = 0;
        if(StringUtils.isNotBlank(texture) || source != null || completeDegree != null){
            query = 1;
        }
        List<ScsCollectionRegisterInfoDO> registerInfoList = this.scsCollectionRegisterInfoMapper.queryByRegisterNos(companyId,registerNos,registerNo,
                classifyDesc,name,age,identifyLevel,texture,source,category,completeDegree,query);
        if(CollectionUtils.isEmpty(registerInfoList)){
            return null;
        }

        List<String> registerNoList = registerInfoList.stream().map(ScsCollectionRegisterInfoDO::getRegisterNo).collect(Collectors.toList());

        List<ScsCollectionRegisterAttrInfoDO> attrInfoDOList = this.scsCollectionRegisterAttrInfoMapper.listByRegisterNos(companyId,registerNoList);
        Map<String, ScsCollectionRegisterAttrInfoDO> attrInfoDOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(attrInfoDOList)){
            attrInfoDOList.forEach(attrInfoDO-> attrInfoDOMap.put(attrInfoDO.getRegisterNo(), attrInfoDO));
        }
        List<ScsCollectionRegisterManageInfoDO> manageInfoDOList = this.scsCollectionRegisterManageInfoMapper.listByRegisterNos(companyId,registerNoList);
        Map<String, ScsCollectionRegisterManageInfoDO> manageInfoDOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(manageInfoDOList)){
            manageInfoDOList.forEach(manageInfoDO-> manageInfoDOMap.put(manageInfoDO.getRegisterNo(), manageInfoDO));
        }
        List<Long> collectionIds = registerInfoList.stream().map(ScsCollectionRegisterInfoDO::getCollectionId).collect(Collectors.toList());
        List<ScsLogoffCollectionInfoDO> logoffInfoList = this.scsLogoffCollectionInfoMapper.listByCollectionIds(companyId, collectionIds);
        Map<Long, String> logoffInfoMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(logoffInfoList)){
            logoffInfoMap = logoffInfoList.stream().collect(Collectors.toMap(ScsLogoffCollectionInfoDO::getCollectionId, ScsLogoffCollectionInfoDO::getLogoffCertificateNo,
                    (key1, key2) -> key2));
        }

        Map<Long, String> finalLogoffInfoMap = logoffInfoMap;
        return registerInfoList.stream().map(registerInfoDO->{
            CollectionLedgerDTO ledgerDTO = new CollectionLedgerDTO();
            ledgerDTO.setRegDate(DateUtils.formatDateYYYMMDD(registerInfoDO.getGmtCreate()));
            ledgerDTO.setRegNo(registerInfoDO.getRegisterNo());
            ledgerDTO.setClassifyDesc(registerInfoDO.getClassifyDesc());
            ledgerDTO.setCollName(registerInfoDO.getName());
            ledgerDTO.setYear(registerInfoDO.getAge());
            ledgerDTO.setLevel(registerInfoDO.getIdentifyLevel());
            if(Objects.nonNull(attrInfoDOMap.get(registerInfoDO.getRegisterNo()))){
                ledgerDTO.setCount(attrInfoDOMap.get(registerInfoDO.getRegisterNo()).getCollectionCount());
                ledgerDTO.setCountUnit(attrInfoDOMap.get(registerInfoDO.getRegisterNo()).getCountUnit());
                ledgerDTO.setActCount(attrInfoDOMap.get(registerInfoDO.getRegisterNo()).getActualCount());
                ledgerDTO.setSize(attrInfoDOMap.get(registerInfoDO.getRegisterNo()).getSize());
                ledgerDTO.setWeight(attrInfoDOMap.get(registerInfoDO.getRegisterNo()).getSpecificQuality());
                ledgerDTO.setTexture(attrInfoDOMap.get(registerInfoDO.getRegisterNo()).getTexture());
                ledgerDTO.setDegree(attrInfoDOMap.get(registerInfoDO.getRegisterNo()).getCompleteDegree());
                ledgerDTO.setCollectType(attrInfoDOMap.get(registerInfoDO.getRegisterNo()).getSource());
                ledgerDTO.setQualityUnit(attrInfoDOMap.get(registerInfoDO.getRegisterNo()).getQualityUnit());
                ledgerDTO.setRemarks(attrInfoDOMap.get(registerInfoDO.getRegisterNo()).getCollectionRemark());
            }
            if(Objects.nonNull(manageInfoDOMap.get(registerInfoDO.getRegisterNo()))){
                ledgerDTO.setInMuCerNo(manageInfoDOMap.get(registerInfoDO.getRegisterNo()).getInMuseumCertificateNo());
            }
            if(Objects.nonNull(finalLogoffInfoMap.get(registerInfoDO.getCollectionId()))){
                ledgerDTO.setLogoffCerNo(finalLogoffInfoMap.get(registerInfoDO.getCollectionId()));
            }
            return ledgerDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateRegisterStatusById(Long companyId, List<Long> ids, RegisterStatusEnum registerStatus){
        this.scsCollectionRegisterInfoMapper.updateBatchRegisterStatusById(companyId,ids,registerStatus.getCode());
    }

    @Override
    public void updateRegisterStatusByCollectionId(Long companyId, List<Long> collectionIds, RegisterStatusEnum registerStatus){
        this.scsCollectionRegisterInfoMapper.updateBatchRegisterStatusByCollectionId(companyId,collectionIds,registerStatus.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCheck(Integer isAllFlag, List<Long> checkIds) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        if (CollectionUtils.isEmpty(checkIds)) {
            return;
        }
        checkIds.forEach(k -> {
            CheckAction checkAction = new CheckAction();
            checkAction.setBelongModel(CollectionBizTypeEnum.LOGIN);
            checkAction.setModelId(k);
            checkAction.setCompanyId(userBO.getCompanyId());
            checkAction.setCreator(userBO.getUserId());
            checkAction.setCreatorName(userBO.getUserName());
            checkAction.setCheckStatus(CheckStatusChildEnum.ATP.getCode());
            checkAction.setCheckOpinion("");
            this.checkService.check(checkAction);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRegisterStatus(Long collectionRegisterId, String registerStatus) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        ScsCollectionRegisterInfoDO collectionRegisterInfoDO = this.scsCollectionRegisterInfoMapper.selectById(userBO.getCompanyId(), collectionRegisterId);
        if (Objects.isNull(collectionRegisterInfoDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "藏品登录信息不存在");
        }
        String registerStatusTemp = collectionRegisterInfoDO.getRegisterStatus();
        if (!(RegisterStatusEnum.AIWH.getCode().equals(registerStatusTemp)
                || RegisterStatusEnum.ARTS.getCode().equals(registerStatusTemp))) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "当前藏品登录状态不支持编辑");
        }
        ScsCollectionRegisterInfoDO registerInfoDO = new ScsCollectionRegisterInfoDO();
        registerInfoDO.setId(collectionRegisterId);
        registerInfoDO.setRegisterStatus(registerStatus);
        registerInfoDO.setModifier(userBO.getModifier());
        this.scsCollectionRegisterInfoMapper.updateByPrimaryKeySelective(registerInfoDO);
        if (!RegisterStatusEnum.NRTS.getCode().equals(registerStatus)) {
            return;
        }
        CommCheckMainDO commCheckMainDO = this.commCheckMainMapper.selectByModelAndId(userBO.getCompanyId(), "LOGIN", collectionRegisterId);
        if (Objects.isNull(commCheckMainDO)) {
            return;
        }
        this.commCheckMainMapper.deleteByModelId(collectionRegisterId, "LOGIN");
        this.commCheckChildMapper.deleteByMainId(commCheckMainDO.getId());
        CommCheckLogDO commCheckLogDO = new CommCheckLogDO();
        commCheckLogDO.setMainId(commCheckMainDO.getId());
        commCheckLogDO.setBelongModel("LOGIN");
        commCheckLogDO.setModelId(collectionRegisterId);
        commCheckLogDO.setCheckStatus("RESBMT");
        commCheckLogDO.setCheckManId(userBO.getUserId());
        commCheckLogDO.setCheckManName(userBO.getUserName());
        commCheckLogDO.setCheckOpinion("重新编辑");
        commCheckLogDO.setCreator(userBO.getUserId());
        commCheckLogDO.setModifier(userBO.getUserId());
        commCheckLogDO.setCheckTime(new Date());
        this.commCheckLogMapper.insertSelective(commCheckLogDO);
    }

    private CollectionRegisterBaseInfoVO buildRegisterBaseInfo(ScsCollectionInfoDO collectionInfoDO) {
        CollectionRegisterBaseInfoVO registerBaseInfo = new CollectionRegisterBaseInfoVO();
        registerBaseInfo.setRegisterNo(collectionInfoDO.getRegisterNo());
        registerBaseInfo.setClassify(collectionInfoDO.getClassify());
        registerBaseInfo.setName(collectionInfoDO.getName());
        registerBaseInfo.setIdentifyLevel(collectionInfoDO.getInitialLevel());
        registerBaseInfo.setInTibetanDate(collectionInfoDO.getInTibetanDate());
        registerBaseInfo.setAge(collectionInfoDO.getAge());
        registerBaseInfo.setEra(collectionInfoDO.getEra());
        registerBaseInfo.setAttachmentDesc(collectionInfoDO.getAppendageDesc());
        registerBaseInfo.setCategory(collectionInfoDO.getCategory());
        if (StringUtils.isNotBlank(collectionInfoDO.getDocumentId())) {
            List<CommonDocumentDTO> docs = documentService.listByDocIds(collectionInfoDO.getCompanyId(), collectionInfoDO.getDocumentId());
            if (CollectionUtils.isEmpty(docs)) {
                return registerBaseInfo;
            }
            docs.forEach(d -> {
                if (FileTypeEnum.IMAGE.getCode().equals(d.getFileType())) {
                    registerBaseInfo.setDocumentId(d.getDocumentId() + "");
                }
            });
        }
        return registerBaseInfo;
    }

    @Override
    public SimplePageInfo<ExhibitRegisterPageDTO> queryExhibitRegisterPage(QueryExhibitRegisterPageAction action) {
        Long companyId = action.getCompanyId();
        //List<Integer> digitalType = action.getDigitalType();
        Integer digitalType = action.getDigitalType();
        Long texture = action.getTexture();
        action.startPage();
        List<ScsCollectionRegisterInfoDO> registerInfoDOList = this.scsCollectionRegisterInfoMapper
                .listExhibitRegister(companyId, action.getKeyword(),
                        Lists.newArrayList(RegisterStatusEnum.ALGF.getCode()),
                        action.getIdentifyLevel(), action.getAge(), action.getSource(), action.getCompleteDegree(),texture,digitalType,action.getDataFlag());
        if (CollectionUtils.isEmpty(registerInfoDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<ExhibitRegisterPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(registerInfoDOList, ExhibitRegisterPageDTO.class);

        List<Long> registerIds = Lists.newArrayList();
        List<Long> classificationIds = Lists.newArrayList();
        StringBuilder documentIdBuilder = new StringBuilder();
        registerInfoDOList.forEach(r -> {
            registerIds.add(r.getId());
            if (Objects.nonNull(r.getIdentifyLevel())) {
                classificationIds.add(r.getIdentifyLevel());
            }
            if (Objects.nonNull(r.getAge())) {
                classificationIds.add(r.getAge());
            }
            if (Objects.nonNull(r.getSource())) {
                classificationIds.add(r.getSource());
            }
            if (Objects.nonNull(r.getCompleteDegree())) {
                classificationIds.add(r.getCompleteDegree());
            }
            if (StringUtils.isNotBlank(r.getTexture())) {
                String[] textureIds = r.getTexture().split(",");
                for (String t : textureIds ) {
                    classificationIds.add(Long.valueOf(t));
                }
            }
            if (StringUtils.isNotBlank(r.getDocumentId())) {
                documentIdBuilder.append(r.getDocumentId()).append(",");
            }
        });
        List<CommClassificationDO> classificationDOList = this.commClassificationMapper.listByIds(companyId, classificationIds);
        Map<Long, String> classificationMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(classificationDOList)) {
            classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                    CommClassificationDO::getName, (key1, key2) -> key2));
        }
        List<ScsCollectionTwoImageDO> twoImageDOList = this.scsCollectionTwoImageMapper.selectListByRegisterInfoIds(registerIds);
        Map<Long, List<ScsCollectionTwoImageDO>> twoImageMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(twoImageDOList)) {
            twoImageMap = twoImageDOList.stream().collect(Collectors.groupingBy(ScsCollectionTwoImageDO::getRegisterInfoId));
        }
        List<ScsCollectionThreeModelDO> threeModelDOList = this.scsCollectionThreeModelMapper.selectListByRegisterInfoIds(registerIds);
        Map<Long, List<ScsCollectionThreeModelDO>> threeModelMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(threeModelDOList)) {
            threeModelMap = threeModelDOList.stream().collect(Collectors.groupingBy(ScsCollectionThreeModelDO::getRegisterInfoId));
        }
        Map<Long, CommonDocumentDTO> documentDTOMap =Maps.newHashMap();
        if (documentIdBuilder.length() != 0) {
            String documentStr = documentIdBuilder.deleteCharAt(documentIdBuilder.length()-1).toString();
            List<CommonDocumentDTO> documentDTOList = this.documentService.listByDocIds(companyId, documentStr);
            if (CollectionUtils.isNotEmpty(documentDTOList)) {
                documentDTOList.forEach(document -> {
                    documentDTOMap.put(document.getDocumentId(), document);
                });
            }
        }
        List<SwzsCulturalRelicsDO> culturalRelicsList = this.swzsCulturalRelicsMapper.selectAll(companyId);
        Set<String> culturalRelicsSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(culturalRelicsList)) {
            culturalRelicsSet = culturalRelicsList.stream().map(SwzsCulturalRelicsDO::getNumber).collect(Collectors.toSet());
        }
        Map<Long, List<ScsCollectionTwoImageDO>> finalTwoImageMap = twoImageMap;
        Map<Long, List<ScsCollectionThreeModelDO>> finalThreeModelMap = threeModelMap;
        Map<Long, String> finalClassificationMap = classificationMap;
        Set<String> finalCulturalRelicsSet = culturalRelicsSet;
        List<ExhibitRegisterPageDTO> resultList = registerInfoDOList.stream().map(s -> {
            Long registerId = s.getId();
//            if (Objects.nonNull(digitalType)) {
//                AtomicBoolean digitalFlag = new AtomicBoolean(false);
//                digitalType.forEach(d -> {
//                    if (d == 1 && finalTwoImageMap.containsKey(registerId) && finalThreeModelMap.containsKey(registerId)) {
//                        digitalFlag.set(true);
//                    } else if (d == 2 && finalTwoImageMap.containsKey(registerId) && !finalThreeModelMap.containsKey(registerId)) {
//                        digitalFlag.set(true);
//                    } else if (d == 3 && finalThreeModelMap.containsKey(registerId) && !finalTwoImageMap.containsKey(registerId)) {
//                        digitalFlag.set(true);
//                    }
//                });
//                if (!digitalFlag.get()) {
//                    return null;
//                }
//            }
//            if (Objects.nonNull(texture)) {
//                if (StringUtils.isBlank(s.getTexture())) {
//                    return null;
//                }
//                String[] textureIds = s.getTexture().split(",");
//                boolean textureFlag = false;
//                for (String t : textureIds ) {
//                    if (texture == Long.parseLong(t)) {
//                        textureFlag = true;
//                    }
//                }
//                if (!textureFlag) {
//                    return null;
//                }
//            }
            ExhibitRegisterPageDTO exhibitRegisterPageDTO = BeanCopyUtils.copyByJSON(s, ExhibitRegisterPageDTO.class);
            exhibitRegisterPageDTO.setRegisterInfoId(s.getId());
            exhibitRegisterPageDTO.setIsExistSame(finalCulturalRelicsSet.contains(s.getRegisterNo()) ? 1 : 0);
            exhibitRegisterPageDTO.setTwoImageCount(finalTwoImageMap.containsKey(registerId) ? finalTwoImageMap.get(registerId).size() : 0);
            exhibitRegisterPageDTO.setThreeCount(finalThreeModelMap.containsKey(registerId) ? finalThreeModelMap.get(registerId).size() : 0);
            List<CommonDocumentDTO> documentDTOList = Lists.newArrayList();
            if (StringUtils.isNotBlank(s.getDocumentId())) {
                List<Long> documentIds = Stream.of(s.getDocumentId().split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                documentIds.forEach(documentId -> documentDTOList.add(documentDTOMap.get(documentId)));
            }
            exhibitRegisterPageDTO.setCommonDocumentList(documentDTOList);
            if (StringUtils.isNotBlank(s.getTexture())) {
                exhibitRegisterPageDTO.setTexture(s.getTexture());
                List<Long> textureIds = Stream.of(s.getTexture().split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                StringBuilder textureBuilder = new StringBuilder();
                textureIds.forEach(textureId -> {
                    textureBuilder.append(finalClassificationMap.get(textureId));
                    textureBuilder.append(",");
                });
                textureBuilder.deleteCharAt(textureBuilder.toString().length() - 1);
                exhibitRegisterPageDTO.setTextureName(textureBuilder.toString());
            }
            exhibitRegisterPageDTO.setSource(s.getSource());
            if (finalClassificationMap.containsKey(s.getSource())) {
                exhibitRegisterPageDTO.setSourceName(finalClassificationMap.get(s.getSource()));
            }
            if (Objects.nonNull(s.getCompleteDegree())) {
                exhibitRegisterPageDTO.setCompleteDegreeName(finalClassificationMap.get(s.getCompleteDegree()));
            }
            if (Objects.nonNull(s.getIdentifyLevel())) {
                exhibitRegisterPageDTO.setIdentifyLevelName(finalClassificationMap.get(s.getIdentifyLevel()));
            }
            if (Objects.nonNull(s.getAge())) {
                exhibitRegisterPageDTO.setAgeName(finalClassificationMap.get(s.getAge()));
            }
            return exhibitRegisterPageDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        pageInfo.setList(resultList);
        return pageInfo;
    }

    private CollectionRegisterManageInfoVO buildRegisterManageInfo(ScsCollectionInfoDO collectionInfoDO) {
        CollectionRegisterManageInfoVO registerManageInfo = new CollectionRegisterManageInfoVO();
        registerManageInfo.setCollectDate(collectionInfoDO.getCollectDate());
        registerManageInfo.setInMuseumDate(collectionInfoDO.getInMuseumDate());
        registerManageInfo.setInTibetanDate(collectionInfoDO.getInTibetanDate());
        registerManageInfo.setWarehouseId(collectionInfoDO.getWarehouseId());
        registerManageInfo.setAppraiserName(collectionInfoDO.getAppraiserName());
        return registerManageInfo;
    }
    private CollectionRegisterAttrInfoVO buildRegisterAttrInfo(ScsCollectionInfoDO collectionInfoDO) {
        CollectionRegisterAttrInfoVO registerAttrInfo = new CollectionRegisterAttrInfoVO();
        registerAttrInfo.setCollectionCount(collectionInfoDO.getCollectionCount());
        registerAttrInfo.setCountUnit(collectionInfoDO.getCountUnit());
        registerAttrInfo.setActualCount(collectionInfoDO.getActualCount());
        registerAttrInfo.setCompleteDegree(collectionInfoDO.getCompleteDegree());
        registerAttrInfo.setCompleteInfo(collectionInfoDO.getCompleteInfo());
        registerAttrInfo.setTexture(collectionInfoDO.getTexture());
        registerAttrInfo.setTextureCategory(collectionInfoDO.getTextureCategory());
        registerAttrInfo.setSource(collectionInfoDO.getCollectType());
        registerAttrInfo.setHolder(collectionInfoDO.getHolder());
        registerAttrInfo.setPayVoucherNo(collectionInfoDO.getPayVoucherNo());
        registerAttrInfo.setAmount(collectionInfoDO.getAmount());
        registerAttrInfo.setCollectionRemark(collectionInfoDO.getRemark());
        registerAttrInfo.setQualityRange(collectionInfoDO.getQualityRange());
        registerAttrInfo.setSize(collectionInfoDO.getSize());
        return registerAttrInfo;
    }
    private ScsCollectionInfoDO buildCollectionInfoDO(AddCollectionRegisterAction action) {
        ScsCollectionInfoDO collectionInfoDO = new ScsCollectionInfoDO();
        collectionInfoDO.setRegisterNo(action.getRegisterNo());
        collectionInfoDO.setClassify(action.getClassify());
        collectionInfoDO.setName(action.getName());
        collectionInfoDO.setInitialLevel(action.getIdentifyLevel());
        collectionInfoDO.setInTibetanDate(action.getInTibetanDate());
        collectionInfoDO.setAge(action.getAge());
        collectionInfoDO.setEra(action.getEra());
        collectionInfoDO.setAppendageDesc(action.getAttachmentDesc());
        collectionInfoDO.setCollectDate(action.getCollectDate());
        collectionInfoDO.setInMuseumDate(action.getInMuseumDate());
        collectionInfoDO.setInTibetanDate(action.getInTibetanDate());
        collectionInfoDO.setWarehouseId(action.getWarehouseId());
        collectionInfoDO.setCollectionCount(action.getCollectionCount());
        collectionInfoDO.setCountUnit(action.getCountUnit());
        collectionInfoDO.setActualCount(action.getActualCount());
        collectionInfoDO.setCompleteDegree(action.getCompleteDegree());
        collectionInfoDO.setCompleteInfo(action.getCompleteInfo());
        collectionInfoDO.setTexture(action.getTexture());
        collectionInfoDO.setTextureCategory(action.getTextureCategory());
        collectionInfoDO.setCollectType(action.getSource());
        collectionInfoDO.setHolder(action.getHolder());
        collectionInfoDO.setPayVoucherNo(action.getPayVoucherNo());
        if (Objects.nonNull(action.getAmount())) {
            BigDecimal bigDecimal = BigDecimal.valueOf(action.getAmount());
            collectionInfoDO.setAmount((long) (bigDecimal.setScale(2, RoundingMode.HALF_EVEN).doubleValue() * 100));
        }
        collectionInfoDO.setRemark(action.getCollectionRemark());
        return collectionInfoDO;
    }
    private void setRegisterManageInfo(CollectionRegisterDetailDTO registerDetailDTO, ScsCollectionRegisterManageInfoDO registerManageInfoDO) {
        if (Objects.isNull(registerManageInfoDO)) {
            return;
        }
        registerDetailDTO.setCollectCertificateNo(registerManageInfoDO.getCollectCertificateNo());
        registerDetailDTO.setCollectDate(registerManageInfoDO.getCollectDate());
        registerDetailDTO.setIdentifyCertificateNo(registerManageInfoDO.getIdentifyCertificateNo());
        registerDetailDTO.setIdentifyDate(registerManageInfoDO.getIdentifyDate());
        registerDetailDTO.setInMuseumCertificateNo(registerManageInfoDO.getInMuseumCertificateNo());
        registerDetailDTO.setInMuseumDate(registerManageInfoDO.getInMuseumDate());
        registerDetailDTO.setInTibetanCertificateNo(registerManageInfoDO.getInTibetanCertificateNo());
        registerDetailDTO.setInTibetanDate(registerManageInfoDO.getInTibetanDate());
        registerDetailDTO.setAppraiserName(registerManageInfoDO.getAppraiserName());
        registerDetailDTO.setOrdinal(registerManageInfoDO.getOrdinal());
        registerDetailDTO.setWarehouseId(registerManageInfoDO.getWarehouseId());
        registerDetailDTO.setCupboardId(registerManageInfoDO.getCupboardId());
        registerDetailDTO.setFloorId(registerManageInfoDO.getFloorId());
        registerDetailDTO.setDrawerId(registerManageInfoDO.getDrawerId());
        registerDetailDTO.setColumnId(registerManageInfoDO.getColumnId());
        registerDetailDTO.setNumberId(registerManageInfoDO.getNumberId());
        registerDetailDTO.setPackageId(registerManageInfoDO.getPackageId());
        registerDetailDTO.setPackageSize(registerManageInfoDO.getPackageSize());
        registerDetailDTO.setIsBox(registerManageInfoDO.getIsBox());
        registerDetailDTO.setIsDoubtful(registerManageInfoDO.getIsDoubtful());
        registerDetailDTO.setIsForeign(registerManageInfoDO.getIsForeign());
        registerDetailDTO.setIsBooking(registerManageInfoDO.getIsBooking());
        registerDetailDTO.setBookingInfo(registerManageInfoDO.getBookingInfo());
    }
    private void setRegisterAttrInfo(CollectionRegisterDetailDTO registerDetailDTO, ScsCollectionRegisterAttrInfoDO registerAttrInfoDO) {
        if (Objects.isNull(registerAttrInfoDO)) {
            return;
        }
        List<Long> classificationIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(registerAttrInfoDO.getTexture())) {
            List<Long> textureIds = Stream.of(registerAttrInfoDO.getTexture().split(","))
                    .map(Long::parseLong).distinct().collect(Collectors.toList());
            classificationIds.addAll(textureIds);
        }
        List<CommClassificationDO> classificationDOList = Lists.newArrayList();
        Map<Long, String> classificationMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(classificationIds)){
            classificationDOList = this.commClassificationMapper.listByIds(registerAttrInfoDO.getCompanyId(), classificationIds);

        }
        if (CollectionUtils.isNotEmpty(classificationDOList)) {
            classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                    CommClassificationDO::getName, (key1, key2) -> key2));
        }
        registerDetailDTO.setLength(Objects.nonNull(registerAttrInfoDO.getLength()) ? registerAttrInfoDO.getLength() : null);
        registerDetailDTO.setWidth(Objects.nonNull(registerAttrInfoDO.getWidth()) ? registerAttrInfoDO.getWidth() : null);
        registerDetailDTO.setHeight(Objects.nonNull(registerAttrInfoDO.getHeight()) ? registerAttrInfoDO.getHeight() : null);
        registerDetailDTO.setPresence(Objects.nonNull(registerAttrInfoDO.getPresence()) ? registerAttrInfoDO.getPresence() : null);
        registerDetailDTO.setCaliber(Objects.nonNull(registerAttrInfoDO.getCaliber()) ? registerAttrInfoDO.getCaliber() : null);
        registerDetailDTO.setBottomDiameter(Objects.nonNull(registerAttrInfoDO.getBottomDiameter()) ? registerAttrInfoDO.getBottomDiameter() : null);
        registerDetailDTO.setSize(registerAttrInfoDO.getSize());
        registerDetailDTO.setQualityRange(registerAttrInfoDO.getQualityRange());
        registerDetailDTO.setQualityUnit(registerAttrInfoDO.getQualityUnit());
        registerDetailDTO.setSpecificQuality(registerAttrInfoDO.getSpecificQuality());
        registerDetailDTO.setActualCount(registerAttrInfoDO.getActualCount());
        registerDetailDTO.setCountUnit(registerAttrInfoDO.getCountUnit());
        registerDetailDTO.setCollectionCount(registerAttrInfoDO.getCollectionCount());
        registerDetailDTO.setCountRemark(registerAttrInfoDO.getCountRemark());
        registerDetailDTO.setSaveStatus(registerAttrInfoDO.getSaveStatus());
        registerDetailDTO.setCompleteDegree(registerAttrInfoDO.getCompleteDegree());
        registerDetailDTO.setCompleteInfo(registerAttrInfoDO.getCompleteInfo());
        registerDetailDTO.setTexture(registerAttrInfoDO.getTexture());
        Map<Long, String> finalClassificationMap = classificationMap;
        String texture = registerAttrInfoDO.getTexture();
        if (StringUtils.isNotBlank(texture)) {
            List<Long> textureIds = Stream.of(texture.split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
            StringBuilder textureBuilder = new StringBuilder();
            textureIds.forEach(textureId -> {
                textureBuilder.append(finalClassificationMap.get(textureId));
                textureBuilder.append(",");
            });
            textureBuilder.deleteCharAt(textureBuilder.toString().length() - 1);
            registerDetailDTO.setTextureName(textureBuilder.toString());
        }
        registerDetailDTO.setTextureCategory(registerAttrInfoDO.getTextureCategory());
        registerDetailDTO.setTextureRemark(registerAttrInfoDO.getTextureRemark());
        registerDetailDTO.setSource(registerAttrInfoDO.getSource());
        registerDetailDTO.setSpecificSource(registerAttrInfoDO.getSpecificSource());
        registerDetailDTO.setExcavatedLocation(registerAttrInfoDO.getExcavatedLocation());
        registerDetailDTO.setExcavatedDate(registerAttrInfoDO.getExcavatedDate());
        registerDetailDTO.setHolder(registerAttrInfoDO.getHolder());
        registerDetailDTO.setPayVoucherNo(registerAttrInfoDO.getPayVoucherNo());
        registerDetailDTO.setAmount(Objects.nonNull(registerAttrInfoDO.getAmount()) ? registerAttrInfoDO.getAmount() : null);
        registerDetailDTO.setCollectionRemark(registerAttrInfoDO.getCollectionRemark());
    }
    private void setRegisterAttrEx(CollectionRegisterDetailDTO registerDetailDTO, ScsCollectionRegisterAttrExDO registerAttrExDO) {
        if (Objects.isNull(registerAttrExDO)) {
            return;
        }
        registerDetailDTO.setSeller(registerAttrExDO.getSeller());
        registerDetailDTO.setBuyDate(registerAttrExDO.getBuyDate());
        registerDetailDTO.setBuyHandler(registerAttrExDO.getBuyHandler());
        registerDetailDTO.setBuyAddress(registerAttrExDO.getBuyAddress());
        registerDetailDTO.setDonors(registerAttrExDO.getDonors());
        registerDetailDTO.setDonateDate(registerAttrExDO.getDonateDate());
        registerDetailDTO.setDonateHandler(registerAttrExDO.getDonateHandler());
        registerDetailDTO.setDonateAddress(registerAttrExDO.getDonateAddress());
        registerDetailDTO.setCollector(registerAttrExDO.getCollector());
        registerDetailDTO.setCollectHandler(registerAttrExDO.getCollectHandler());
        registerDetailDTO.setCollectAddress(registerAttrExDO.getCollectAddress());
        registerDetailDTO.setExcavator(registerAttrExDO.getExcavator());
        registerDetailDTO.setExcavationDate(registerAttrExDO.getExcavationDate());
        registerDetailDTO.setExcavatorAddress(registerAttrExDO.getExcavatorAddress());
        registerDetailDTO.setTransferDate(registerAttrExDO.getTransferDate());
        registerDetailDTO.setTransferHandler(registerAttrExDO.getTransferHandler());
        registerDetailDTO.setTransferUnit(registerAttrExDO.getTransferUnit());
        registerDetailDTO.setAppendix(registerAttrExDO.getAppendix());
        registerDetailDTO.setAppropriationDate(registerAttrExDO.getAppropriationDate());
        registerDetailDTO.setAppropriationHandler(registerAttrExDO.getAppropriationHandler());
        registerDetailDTO.setAppropriationUnit(registerAttrExDO.getAppropriationUnit());
        registerDetailDTO.setDescription(registerAttrExDO.getDescription());
        registerDetailDTO.setProcess(registerAttrExDO.getProcess());
        registerDetailDTO.setHandDown(registerAttrExDO.getHandDown());
        registerDetailDTO.setStatusRecord(registerAttrExDO.getStatusRecord());
        registerDetailDTO.setSynopsis(registerAttrExDO.getSynopsis());
        registerDetailDTO.setCollectsDate(registerAttrExDO.getCollectDate());
        registerDetailDTO.setSelectHandler(registerAttrExDO.getSelectHandler());
        registerDetailDTO.setSelectDate(registerAttrExDO.getSelectDate());
        registerDetailDTO.setExchangeHandler(registerAttrExDO.getExchangeHandler());
        registerDetailDTO.setExchangeUnit(registerAttrExDO.getExchangeUnit());
        registerDetailDTO.setExchangeDate(registerAttrExDO.getExchangeDate());
    }

}
