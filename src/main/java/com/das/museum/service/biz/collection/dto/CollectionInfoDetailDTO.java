package com.das.museum.service.biz.collection.dto;

import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CollectionInfoDetailDTO implements Serializable {
    private static final long serialVersionUID = 4260375773183539088L;
    private Long collectionId;
    private String registerNo;
    private Long classify;
    private String classifyName;
    private String name;
    private Integer actualCount;
    private Long countUnit;
    private String countUnitName;
    private Integer collectionCount;
    private String era;
    private Long age;
    private String ageName;
    private Long textureCategory;
    private String textureCategoryName;
    private String texture;
    private String textureName;
    private String size;
    private Long sizeUnit;
    private String sizeUnitName;
    private Long completeDegree;
    private String completeDegreeName;
    private String completeInfo;
    private Long initialLevel;
    private String initialLevelName;
    private Double amount;
    private Long collectType;
    private String collectTypeName;
    private String collectDate;
    private String holder;
    private String payVoucherNo;
    private String remark;
    private String appendageDesc;
    private String sourceInfo;
    private String introduce;
    private String whereabouts;
    private String inputMode;
    private String inputType;
    private String collectionStatus;
    private String processStatus;
    private String inMuseumCertificateNo;
    private String inMuseumDate;
    private String inMuseumStatus;
    private String inTibetanDate;
    private String appraiserName;
    private Long warehouseId;
    private String warehouseName;
    private String warehouseStatus;
    private Date inWarehouseDate;
    private Date outWarehouseDate;
    private String documentId;
    private List<CommonDocumentDTO> commonDocumentList;
    private List<CommonDocumentDTO> attachmentDocumentList;
    private List<CommonDocumentDTO> imageDocumentList;

    /**
     * 文物类别：classification_id
     */
    private Long category;

    /**
     * 质量范围:classification_id
     */
    private Long qualityRange;

    /**
     * 审核状态
     */
    private String auditStatus;
}
