package com.das.museum.service.biz.collection.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectionNotePageDTO implements Serializable {
    private static final long serialVersionUID = 2140694930849057230L;
    private Long collectionNoteId;
    private Long companyId;
    private Long collectionId;
    private String content;
    private String documentId;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}