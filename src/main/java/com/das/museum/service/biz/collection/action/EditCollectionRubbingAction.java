package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;

@Data
public class EditCollectionRubbingAction implements Serializable {
    private static final long serialVersionUID = 8899253377061073027L;
    private Long collectionRubbingId;
    private Long companyId;
    private Long collectionId;
    private String bookStyle;
    private String mountSituation;
    private String rubbingNo;
    private String recognition;
    private String seal;
    private String postscript;
    private String interpretation;
    private String documentId;
    private Long modifier;
}
