package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BookQueryPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = -7693839908104924000L;
    private Long companyId;
    private Long collectionId;
    private String sortBy;
}
