package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;

import java.util.Date;

@Data
public class IdentifyQueryAction extends PageInfoBaseReq {
    private Long companyId;
    private String identifyCertificateNo;
    private Long collectType;
    private String collectionName;
    private Date collectionStartDate;
    private Date collectionEndDate;
    private Long completeDegree;
    private Long initialLevel;
    private String era;
    private String texture;
    private Date collectStartDate;
    private Date collectEndDate;
    private String checkStatus;
    private String sortBy;
    private Integer isUserChecked;
}
