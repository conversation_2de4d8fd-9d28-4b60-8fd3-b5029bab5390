package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.util.List;

@Data
public class AddIdentifyAction {
    private String identifyCertificateNo;
    private String prefixName;
    private Long suffixNumber;
    private Long companyId;
    private Long creator;
    private String creatorName;
    private List<AddIdentifyCollectionAction> identifyCollectionList;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;
}
