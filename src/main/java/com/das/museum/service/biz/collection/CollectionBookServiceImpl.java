package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionBookAction;
import com.das.museum.service.biz.collection.action.BookQueryPageByConditionAction;
import com.das.museum.service.biz.collection.action.EditCollectionBookAction;
import com.das.museum.service.biz.collection.dto.CollectionBookDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionBookPageDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectionBookEntity;
import com.das.museum.domain.collection.repository.CollectionBookRepository;
import com.das.museum.infr.dataobject.CommClassificationDO;
import com.das.museum.infr.dataobject.ScsCollectionBookDO;
import com.das.museum.infr.mapper.CommClassificationMapper;
import com.das.museum.infr.mapper.ScsCollectionBookMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CollectionBookServiceImpl implements CollectionBookService {

    @Resource
    private CollectionBookRepository collectionBookRepository;

    @Resource
    private ScsCollectionBookMapper scsCollectionBookMapper;

    @Resource
    private DocumentService documentService;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCollectionBook(AddCollectionBookAction action) {
        CollectionBookEntity entity = this.convertCollectionBookEntity(action);
        this.collectionBookRepository.saveCollectionBook(entity);
        return entity.getCollectionBookId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionBook(EditCollectionBookAction action) {
        Long companyId = action.getCompanyId();
        Long collectionBookId = action.getCollectionBookId();
        CollectionBookDetailDTO collectionBookDTO = this.queryByCollectionBookId(companyId, collectionBookId);
        if (Objects.isNull(collectionBookDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "研究著录不存在");
        }
        CollectionBookEntity entity = this.convertCollectionBookEntity(action);
        this.collectionBookRepository.editCollectionBook(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByCollectionBookId(Long companyId, Long collectionBookId) {
        CollectionBookDetailDTO collectionBookDTO = this.queryByCollectionBookId(companyId, collectionBookId);
        if (Objects.isNull(collectionBookDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "研究著录不存在");
        }
        this.collectionBookRepository.delByCollectionBookId(companyId, collectionBookId);
        if (StringUtils.isBlank(collectionBookDTO.getDocumentId())) {
            return;
        }
        this.documentService.delByDocIds(companyId, collectionBookDTO.getDocumentId());
    }

    @Override
    public SimplePageInfo<CollectionBookPageDTO> queryPageByCondition(BookQueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionBookDO> collectionBookDOList = this.scsCollectionBookMapper.listPageByCondition(companyId,
                action.getCollectionId(), action.getSortBy());
        if (CollectionUtils.isEmpty(collectionBookDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<CollectionBookPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectionBookDOList, CollectionBookPageDTO.class);
        List<CollectionBookPageDTO> collectionBookPageDTOList = collectionBookDOList.stream().map(collectionBookDO -> {
            CollectionBookPageDTO collectionBookPageDTO = BeanCopyUtils.copyByJSON(collectionBookDO, CollectionBookPageDTO.class);
            collectionBookPageDTO.setCollectionBookId(collectionBookDO.getId());
            return collectionBookPageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectionBookPageDTOList);
        List<Long> bookLevelIds = collectionBookDOList.stream().map(ScsCollectionBookDO::getBookLevel).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bookLevelIds)) {
            return pageInfo;
        }
        List<CommClassificationDO> classificationDOList = commClassificationMapper.listByIds(companyId, bookLevelIds);
        if (CollectionUtils.isEmpty(classificationDOList)) {
            return pageInfo;
        }
        Map<Long, String> classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                CommClassificationDO::getName, (key1, key2) -> key2));
        pageInfo.getList().forEach(collectionBookPageDTO -> {
            Long bookLevel = collectionBookPageDTO.getBookLevel();
            if (Objects.nonNull(bookLevel) && classificationMap.containsKey(bookLevel)) {
                collectionBookPageDTO.setBookLevelName(classificationMap.get(bookLevel));
            }
        });
        return pageInfo;
    }

    @Override
    public CollectionBookDetailDTO queryByCollectionBookId(Long companyId, Long collectionBookId) {
        ScsCollectionBookDO collectionBookDO = this.scsCollectionBookMapper.selectById(companyId, collectionBookId);
        if (Objects.isNull(collectionBookDO)) {
            return null;
        }
        CollectionBookDetailDTO collectionBookDTO = BeanCopyUtils.copyByJSON(collectionBookDO, CollectionBookDetailDTO.class);
        collectionBookDTO.setCollectionBookId(collectionBookDO.getId());
        String documentId = collectionBookDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return collectionBookDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        collectionBookDTO.setCommDocumentList(commDocumentList);
        return collectionBookDTO;
    }

    private CollectionBookEntity convertCollectionBookEntity(AddCollectionBookAction action) {
        CollectionBookEntity entity = new CollectionBookEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setCollectionId(action.getCollectionId());
        entity.setAuthor(action.getAuthor());
        entity.setRecordDate(action.getRecordDate());
        entity.setBookLevel(action.getBookLevel());
        entity.setPressName(action.getPressName());
        entity.setArticle(action.getArticle());
        entity.setContent(action.getContent());
        entity.setRemark(action.getRemark());
        entity.setDocumentId(action.getDocumentId());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        return entity;
    }

    private CollectionBookEntity convertCollectionBookEntity(EditCollectionBookAction action) {
        CollectionBookEntity entity = new CollectionBookEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setCollectionBookId(action.getCollectionBookId());
        entity.setAuthor(action.getAuthor());
        entity.setRecordDate(action.getRecordDate());
        entity.setBookLevel(action.getBookLevel());
        entity.setPressName(action.getPressName());
        entity.setArticle(action.getArticle());
        entity.setContent(action.getContent());
        entity.setRemark(action.getRemark());
        entity.setDocumentId(action.getDocumentId());
        entity.setModifier(action.getModifier());
        return entity;
    }
}
