package com.das.museum.service.biz.collection.action;

import com.das.museum.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RubbingQueryPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = 1936121827018322753L;
    private Long companyId;
    private Long collectionId;
    private String bookStyle;
    private String mountSituation;
    private String sortBy;
}
