package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.dto.GetImportResultDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Author: shaochengwei
 * @Date: 2023-01-07
 */
public interface CollectionLoginExcelImportService {

    /**
     * 导入数据
     * formType 1 藏品登录导入 2 藏品档案导入
     * @param file
     */
    void importData(MultipartFile file,Long companyId,Long userId,Byte formType);

    /**
     * 获取导入结果
     * @return
     */
    GetImportResultDTO getImportResult();

    /**
     * 导入结果确认
     */
    void confirmKnow(Long id);

}
