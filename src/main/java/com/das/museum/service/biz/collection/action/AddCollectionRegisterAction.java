package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AddCollectionRegisterAction implements Serializable {
    private static final long serialVersionUID = -228315306888331619L;
    private Long companyId;
    private String registerNo;
    private String rfid;
    private String name;
    private String originalNo;
    private String originalName;
    private Long classify;
    private String classifyDesc;
    private Long identifyLevel;
    private Long category;
    private Long inTibetanAgeRange;
    private Date inTibetanDate;
    private String address;
    private String specificInfo;
    private String era;
    private Long age;
    private String use;
    private String color;
    private String author;
    private String authorBiography;
    private String attachmentDesc;
    private String documentId;
    private String collectCertificateNo;
    private Date collectDate;
    private String identifyCertificateNo;
    private Date identifyDate;
    private String inMuseumCertificateNo;
    private Date inMuseumDate;
    private String inTibetanCertificateNo;
    private String appraiserName;
    private String ordinal;
    private Long warehouseId;
    private Long cupboardId;
    private Long floorId;
    private Long drawerId;
    private Long columnId;
    private Long numberId;
    private Long packageId;
    private String packageSize;
    private Byte isBox;
    private Byte isDoubtful;
    private Byte isForeign;
    private Byte isBooking;
    private String bookingInfo;
    private Long length;
    private Long width;
    private Long height;
    private Long presence;
    private Long caliber;
    private Long bottomDiameter;
    private String size;
    private Long qualityRange;
    private Long qualityUnit;
    private Long specificQuality;
    private Integer actualCount;
    private Long countUnit;
    private Integer collectionCount;
    private String countRemark;
    private Long saveStatus;
    private Long completeDegree;
    private String completeInfo;
    private Long textureCategory;
    private String texture;
    private String textureRemark;
    private Long source;
    private String specificSource;
    private String excavatedLocation;
    private Date excavatedDate;
    private String holder;
    private String payVoucherNo;
    private Long amount;
    private String collectionRemark;
    private String seller;
    private Date buyDate;
    private String buyHandler;
    private String buyAddress;
    private String donors;
    private Date donateDate;
    private String donateHandler;
    private String donateAddress;
    private String collector;
    private String collectHandler;
    private String collectAddress;
    private String excavator;
    private Date excavationDate;
    private String excavatorAddress;
    private String transferUnit;
    private Date transferDate;
    private String transferHandler;
    private String appropriationUnit;
    private Date appropriationDate;
    private String appropriationHandler;
    private String description;
    private String process;
    private String handDown;
    private String statusRecord;
    private String appendix;
    private String synopsis;
    private Long creator;
    private String creatorName;
    /**
     * 采集日期
     */
    private Date collectsDate;

    /**
     * 拣选经手人
     */
    private String selectHandler;

    /**
     * 拣选日期
     */
    private Date selectDate;

    /**
     * 依法交换经手人
     */
    private String exchangeHandler;

    /**
     * 依法交换单位
     */
    private String exchangeUnit;

    /**
     * 依法交换日期
     */
    private Date exchangeDate;

    /**
     * 是否自动提交审核 0: 否 1: 是
     */
    private Byte autoAudit;

    /**
     * 藏品登录状态
     */
    private String registerStatus;
}
