package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionRubbingAction;
import com.das.museum.service.biz.collection.action.EditCollectionRubbingAction;
import com.das.museum.service.biz.collection.action.RubbingQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionRubbingDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionRubbingPageDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectionRubbingEntity;
import com.das.museum.domain.collection.repository.CollectionRubbingRepository;
import com.das.museum.infr.dataobject.ScsCollectionRubbingDO;
import com.das.museum.infr.mapper.ScsCollectionRubbingMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CollectionRubbingServiceImpl implements CollectionRubbingService {

    @Resource
    private CollectionRubbingRepository collectionRubbingRepository;

    @Resource
    private ScsCollectionRubbingMapper scsCollectionRubbingMapper;

    @Resource
    private DocumentService documentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCollectionRubbing(AddCollectionRubbingAction action) {
        CollectionRubbingEntity entity = this.convertAddCollectionRubbingAction(action);
        this.collectionRubbingRepository.saveCollectionRubbing(entity);
        return entity.getCollectionRubbingId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionRubbing(EditCollectionRubbingAction action) {
        Long companyId = action.getCompanyId();
        Long collectionRubbingId = action.getCollectionRubbingId();
        CollectionRubbingDetailDTO collectionRubbingDTO = this.queryByCollectionRubbingId(companyId, collectionRubbingId);
        if (Objects.isNull(collectionRubbingDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "拓片信息不存在");
        }
        CollectionRubbingEntity entity = this.convertEditCollectionRubbingAction(action);
        this.collectionRubbingRepository.editCollectionRubbing(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByCollectionRubbingId(Long companyId, Long collectionRubbingId) {
        CollectionRubbingDetailDTO collectionRubbingDTO = this.queryByCollectionRubbingId(companyId, collectionRubbingId);
        if (Objects.isNull(collectionRubbingDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "拓片不存在");
        }
        this.collectionRubbingRepository.delByCollectionRubbingId(companyId, collectionRubbingId);
        if (StringUtils.isBlank(collectionRubbingDTO.getDocumentId())) {
            return;
        }
        this.documentService.delByDocIds(companyId, collectionRubbingDTO.getDocumentId());
    }

    @Override
    public SimplePageInfo<CollectionRubbingPageDTO> queryPageByCondition(RubbingQueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionRubbingDO> collectionRubbingDOList = this.scsCollectionRubbingMapper.listPageByCondition(companyId,
                action.getCollectionId(), action.getBookStyle(), action.getMountSituation(), action.getSortBy());
        if (CollectionUtils.isEmpty(collectionRubbingDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<CollectionRubbingPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectionRubbingDOList, CollectionRubbingPageDTO.class);
        List<CollectionRubbingPageDTO> collectionNotePageDTOList = collectionRubbingDOList.stream().map(collectionRubbingDO -> {
            CollectionRubbingPageDTO collectionRubbingPageDTO = BeanCopyUtils.copyByJSON(collectionRubbingDO, CollectionRubbingPageDTO.class);
            collectionRubbingPageDTO.setCollectionRubbingId(collectionRubbingDO.getId());
            return collectionRubbingPageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectionNotePageDTOList);
        return pageInfo;
    }

    @Override
    public CollectionRubbingDetailDTO queryByCollectionRubbingId(Long companyId, Long collectionRubbingId) {
        ScsCollectionRubbingDO collectionRubbingDO = this.scsCollectionRubbingMapper.selectById(companyId, collectionRubbingId);
        if (Objects.isNull(collectionRubbingDO)) {
            return null;
        }
        CollectionRubbingDetailDTO collectionRubbingDTO = BeanCopyUtils.copyByJSON(collectionRubbingDO, CollectionRubbingDetailDTO.class);
        collectionRubbingDTO.setCollectionRubbingId(collectionRubbingDO.getId());
        String documentId = collectionRubbingDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return collectionRubbingDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        collectionRubbingDTO.setCommDocumentList(commDocumentList);
        return collectionRubbingDTO;
    }
    private CollectionRubbingEntity convertAddCollectionRubbingAction(AddCollectionRubbingAction action) {
        CollectionRubbingEntity entity = new CollectionRubbingEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setCollectionId(action.getCollectionId());
        entity.setBookStyle(action.getBookStyle());
        entity.setMountSituation(action.getMountSituation());
        entity.setRubbingNo(action.getRubbingNo());
        entity.setRecognition(action.getRecognition());
        entity.setSeal(action.getSeal());
        entity.setPostscript(action.getPostscript());
        entity.setInterpretation(action.getInterpretation());
        entity.setDocumentId(action.getDocumentId());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        return entity;
    }
    private CollectionRubbingEntity convertEditCollectionRubbingAction(EditCollectionRubbingAction action) {
        CollectionRubbingEntity entity = new CollectionRubbingEntity();
        entity.setCollectionRubbingId(action.getCollectionRubbingId());
        entity.setCompanyId(action.getCompanyId());
        entity.setBookStyle(action.getBookStyle());
        entity.setMountSituation(action.getMountSituation());
        entity.setRubbingNo(action.getRubbingNo());
        entity.setRecognition(action.getRecognition());
        entity.setSeal(action.getSeal());
        entity.setPostscript(action.getPostscript());
        entity.setInterpretation(action.getInterpretation());
        entity.setDocumentId(action.getDocumentId());
        entity.setModifier(action.getModifier());
        return entity;
    }
}
