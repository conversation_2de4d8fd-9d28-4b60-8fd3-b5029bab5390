package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddInMuseumAction;
import com.das.museum.service.biz.collection.action.EditInMuseumAction;
import com.das.museum.service.biz.collection.action.InMuseumConditionQueryAction;
import com.das.museum.service.biz.collection.action.PageInTibetanAction;
import com.das.museum.service.biz.collection.dto.CollectCheckedCollectionInfoDTO;
import com.das.museum.service.biz.collection.dto.InMuseumInfoDetailDTO;
import com.das.museum.service.biz.collection.dto.InMuseumInfoPageDTO;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;

import java.util.List;

public interface InMuseumService {
    Long addInMuseumCertificateInfo(AddInMuseumAction addInMuseumAction);
    void editInMuseumCertificateInfo(Long companyId, Long inMuseumId, EditInMuseumAction editInMuseumAction);
    void delBynMuseumCertificateById(Long companyId, Long inMuseumId);
    InMuseumInfoDetailDTO queryDetailInfoById(Long companyId, Long inMuseumId);
    SimplePageInfo<InMuseumInfoPageDTO> queryPageByConditionQuery(InMuseumConditionQueryAction action);
    List<CommonDocumentDTO> queryAttachmentByInMuseumId(Long companyId, Long inMuseumId);
    SimplePageInfo<InMuseumInfoPageDTO> queryPageInTibetanList(PageInTibetanAction action);

    List<CollectCheckedCollectionInfoDTO> queryCheckedCollectionInfoList(Long companyId, Long inMuseumId);

    SimplePageInfo<CollectCheckedCollectionInfoDTO> queryAtpCollectionInfoList(Long companyId, Long inMuseumId, String collectionName, String payVoucherNo, Integer pageNum, Integer pageSize);
}
