package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EditCollectionMountAction implements Serializable {
    private static final long serialVersionUID = 5349209419765724678L;
    private Long companyId;
    private Long collectionMountId;
    private String contractUnit;
    private Long producerId;
    private String producerName;
    private String remark;
    private Date mountDate;
    private String documentId;
    private Long modifier;
}
