package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionAncientBookAction;
import com.das.museum.service.biz.collection.action.AncientBookQueryPageByConditionAction;
import com.das.museum.service.biz.collection.action.EditCollectionAncientBookAction;
import com.das.museum.service.biz.collection.dto.CollectionAncientBookDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionAncientBookPageDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectionAncientBookEntity;
import com.das.museum.domain.collection.repository.CollectionAncientBookRepository;
import com.das.museum.infr.dataobject.ScsCollectionAncientBookDO;
import com.das.museum.infr.mapper.ScsCollectionAncientBookMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CollectionAncientBookServiceImpl implements CollectionAncientBookService {

    @Resource
    private CollectionAncientBookRepository collectionAncientBookRepository;

    @Resource
    private ScsCollectionAncientBookMapper scsCollectionAncientBookMapper;

    @Resource
    private DocumentService documentService;

    @Override
    public Long addCollectionAncientBook(AddCollectionAncientBookAction action) {
        CollectionAncientBookEntity entity = this.convertCollectionAncientBookEntity(action);
        this.collectionAncientBookRepository.saveCollectionAncientBook(entity);
        return entity.getCollectionAncientBookId();
    }

    @Override
    public void editCollectionAncientBook(EditCollectionAncientBookAction action) {
        Long companyId = action.getCompanyId();
        Long collectionAncientBookId = action.getCollectionAncientBookId();
        CollectionAncientBookDetailDTO collectionAncientBookDTO = this.queryByCollectionAncientBookId(companyId, collectionAncientBookId);
        if (Objects.isNull(collectionAncientBookDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "古籍文献信息不存在");
        }
        CollectionAncientBookEntity entity = this.convertCollectionAncientBookEntity(action);
        this.collectionAncientBookRepository.editCollectionAncientBook(entity);
    }

    @Override
    public void delByCollectionAncientBookId(Long companyId, Long collectionAncientBookId) {
        CollectionAncientBookDetailDTO collectionAncientBookDTO = this.queryByCollectionAncientBookId(companyId, collectionAncientBookId);
        if (Objects.isNull(collectionAncientBookDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "古籍文献信息不存在");
        }
        this.collectionAncientBookRepository.delByCollectionAncientBookId(companyId, collectionAncientBookId);
        if (StringUtils.isBlank(collectionAncientBookDTO.getDocumentId())) {
            return;
        }
        this.documentService.delByDocIds(companyId, collectionAncientBookDTO.getDocumentId());
    }

    @Override
    public SimplePageInfo<CollectionAncientBookPageDTO> queryPageByCondition(AncientBookQueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionAncientBookDO> collectionAncientBookDOList = this.scsCollectionAncientBookMapper.listPageByCondition(companyId,
                action.getCollectionId(), action.getSortBy());
        if (CollectionUtils.isEmpty(collectionAncientBookDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<CollectionAncientBookPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectionAncientBookDOList, CollectionAncientBookPageDTO.class);
        List<CollectionAncientBookPageDTO> collectionAncientBookPageDTOList = collectionAncientBookDOList.stream().map(collectionAncientBookDO -> {
            CollectionAncientBookPageDTO collectionAncientBookPageDTO = BeanCopyUtils.copyByJSON(collectionAncientBookDO, CollectionAncientBookPageDTO.class);
            collectionAncientBookPageDTO.setCollectionAncientBookId(collectionAncientBookDO.getId());
            return collectionAncientBookPageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectionAncientBookPageDTOList);
        return pageInfo;
    }

    @Override
    public CollectionAncientBookDetailDTO queryByCollectionAncientBookId(Long companyId, Long collectionAncientBookId) {
        ScsCollectionAncientBookDO collectionAncientBookDO = this.scsCollectionAncientBookMapper.selectById(companyId, collectionAncientBookId);
        if (Objects.isNull(collectionAncientBookDO)) {
            return null;
        }
        CollectionAncientBookDetailDTO collectionAncientBookDTO = BeanCopyUtils.copyByJSON(collectionAncientBookDO, CollectionAncientBookDetailDTO.class);
        collectionAncientBookDTO.setCollectionAncientBookId(collectionAncientBookDTO.getCollectionAncientBookId());
        String documentId = collectionAncientBookDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return collectionAncientBookDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        collectionAncientBookDTO.setCommDocumentList(commDocumentList);
        return collectionAncientBookDTO;
    }

    private CollectionAncientBookEntity convertCollectionAncientBookEntity(AddCollectionAncientBookAction action) {
        CollectionAncientBookEntity entity = new CollectionAncientBookEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setCollectionId(action.getCollectionId());
        entity.setClassification(action.getClassification());
        entity.setVersion(action.getVersion());
        entity.setArchive(action.getArchive());
        entity.setFrame(action.getFrame());
        entity.setFormat(action.getFormat());
        entity.setDocumentId(action.getDocumentId());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        return entity;
    }

    private CollectionAncientBookEntity convertCollectionAncientBookEntity(EditCollectionAncientBookAction action) {
        CollectionAncientBookEntity entity = new CollectionAncientBookEntity();
        entity.setCollectionAncientBookId(action.getCollectionAncientBookId());
        entity.setCompanyId(action.getCompanyId());
        entity.setClassification(action.getClassification());
        entity.setVersion(action.getVersion());
        entity.setArchive(action.getArchive());
        entity.setFrame(action.getFrame());
        entity.setFormat(action.getFormat());
        entity.setDocumentId(action.getDocumentId());
        entity.setModifier(action.getModifier());
        return entity;
    }
}
