package com.das.museum.service.biz.collection;

import com.das.museum.infr.dataobject.ScsLogoffCollectionInfoDO;
import com.das.museum.infr.mapper.ScsLogoffCollectionInfoMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LogoffCollectionServiceImpl implements LogoffCollectionService{

    @Resource
    private ScsLogoffCollectionInfoMapper scsLogoffCollectionInfoMapper;

    @Override
    public List<Long> queryLogoffCollectionIds(Long companyId, Long logoffId){
        List<ScsLogoffCollectionInfoDO> logoffCollectionInfoDOList = this.scsLogoffCollectionInfoMapper
                .listByLogoffId(companyId, logoffId);
        if(CollectionUtils.isEmpty(logoffCollectionInfoDOList)){
            return Lists.newArrayList();
        }
        return logoffCollectionInfoDOList.stream().map(ScsLogoffCollectionInfoDO::getCollectionId).collect(Collectors.toList());
    }
}
