package com.das.museum.service.biz.collection;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.common.utils.MinIoUtils;
import com.das.museum.common.utils.UUIDUtils;
import com.das.museum.infr.dataobject.ScsCollectionThreeModelDO;
import com.das.museum.infr.mapper.ScsCollectionThreeModelMapper;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.action.AddDocumentAction;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.service.task.ZipToGet3dTask;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.MDC;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class UploadCollectionThreeModelTask implements Runnable{

    private static final String TRACE_ID = "TraceId";

    private static final String THREE_MODEL = "threeModel";

    private MultipartFile[] files;

    private DocumentService documentService;

    private RedisTemplate<String, JSONObject> redisTemplate;

    private ScsCollectionThreeModelMapper scsCollectionThreeModelMapper;

    private ZipToGet3dTask zipToGet3dTask;

    private Long registerInfoId;

    private Long companyId;

    private Long userId;

    private String bucketName;

    private String ftpPath;

    UploadCollectionThreeModelTask(){

    }

    public UploadCollectionThreeModelTask(MultipartFile[] files,
                                          DocumentService documentService,
                                          RedisTemplate<String, JSONObject> redisTemplate,
                                          Long registerInfoId,
                                          Long companyId,
                                          Long userId,
                                          String bucketName,
                                          String ftpPath,
                                          ScsCollectionThreeModelMapper scsCollectionThreeModelMapper,
                                          ZipToGet3dTask zipToGet3dTask){
        this.files = files;
        this.documentService = documentService;
        this.redisTemplate = redisTemplate;
        this.registerInfoId = registerInfoId;
        this.companyId = companyId;
        this.userId = userId;
        this.bucketName = bucketName;
        this.ftpPath = ftpPath;
        this.scsCollectionThreeModelMapper = scsCollectionThreeModelMapper;
        this.zipToGet3dTask = zipToGet3dTask;
    }

    @Override
    public void run() {
        log.info("UploadCollectionThreeModelTask task started");
        try {
            String requestId = UUIDUtils.generateUUID(16);
            MDC.put(TRACE_ID, requestId);
            handle();
        } catch (Throwable e) {
            log.error("UploadCollectionThreeModelTask Error task", e);
        } finally {
            MDC.remove(TRACE_ID);
        }
    }

    private void handle() {
        UploadCollectionThreeModelTask.UploadResult uploadResult = new UploadCollectionThreeModelTask.UploadResult();
        List<ModelInfo> modelList = Lists.newArrayList();
        List<String> successList = Lists.newArrayList();
        List<String> failedList = Lists.newArrayList();
        for(MultipartFile file : files){
            ModelInfo modelInfo = new ModelInfo();
            String fileName = file.getOriginalFilename();
            Long documentId = uploadFile(file);
            String modelPath = uploadFileToTransModel(file);
            if(documentId > 0 && StringUtils.isNotBlank(modelPath)){
                modelInfo.setModelUrl(modelPath);
                modelInfo.setDocumentId(documentId);
                modelList.add(modelInfo);
                successList.add(fileName);
            }else{
                failedList.add(fileName);
            }
        }
        uploadResult.setSuccessNum(successList.size());
        uploadResult.setFailedNum(failedList.size());
        uploadResult.setSuccessAll(Strings.join(successList, ','));
        uploadResult.setFailedAll(Strings.join(failedList,','));
        redisTemplate.opsForValue().set(THREE_MODEL +"_"+ companyId+"_"+registerInfoId+"_"+userId, (JSONObject) JSONObject.toJSON(uploadResult), 7, TimeUnit.DAYS);
        modelList.forEach(s -> {
            ScsCollectionThreeModelDO scsCollectionThreeModelDO = new ScsCollectionThreeModelDO();
            scsCollectionThreeModelDO.setCompanyId(companyId);
            scsCollectionThreeModelDO.setDocumentId(s.getDocumentId());
            scsCollectionThreeModelDO.setThreeModelStatus((byte)2);
            scsCollectionThreeModelDO.setThreeModelUrl(s.getModelUrl());
            scsCollectionThreeModelDO.setRegisterInfoId(registerInfoId);
            scsCollectionThreeModelDO.setCreator(userId);
            scsCollectionThreeModelDO.setModifier(userId);
            scsCollectionThreeModelMapper.insertSelective(scsCollectionThreeModelDO);
        });
    }

    private Long uploadFile(MultipartFile file){
        CommonDocumentDTO commonDocumentDTO;
        try {
            String fileName = file.getOriginalFilename();
            if (StringUtils.isBlank(fileName)) {
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST);
            }
            String fileFormat = fileName.substring(fileName.lastIndexOf('.') + 1);
            String saveFileName = RandomStringUtils.randomAlphanumeric(32) + "_" + fileName;
            String baseUrlStr = DateUtils.formatDateYYYMMDD() + MinIoUtils.SEPARATOR +
                    companyId + MinIoUtils.SEPARATOR + saveFileName;
            Long fileSize = file.getSize();
            String url = MinIoUtils.getVisitUrl() + baseUrlStr;
            MinIoUtils.putObject(bucketName, file, baseUrlStr, file.getContentType());
            AddDocumentAction action = new AddDocumentAction();
            action.setCompanyId(companyId);
            action.setCreator(userId);
            action.setSourceFileName(fileName);
            action.setSaveFileName(saveFileName);
            action.setFileFormat(fileFormat);
            action.setFileType("");
            action.setFileSize(fileSize);
            action.setBucket(bucketName);
            action.setPath(baseUrlStr);
            action.setUrl(url);
            commonDocumentDTO = this.documentService.addDocument(action);
        } catch (Throwable e) {
            log.error("UploadRecordTask task", e);
            return -1L;
        }
        return commonDocumentDTO.getDocumentId();
    }

    /**
     * 模型上传的服务器本地用于解压
     * @param multipartFile
     * @return
     */
    private String uploadFileToTransModel(MultipartFile multipartFile) {
        String fileName = getRealFileName(multipartFile);

        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        String uuid =  IdUtil.simpleUUID();
        String saveName = uuid + "." + suffix;
        String path = ftpPath + saveName;
        File dest = new File(path);
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }

        try {
            InputStream input = multipartFile.getInputStream();
            File realFile = new File(path);
            OutputStream bos = new FileOutputStream(realFile);
            int len;
            byte[] buf = new byte[1024];
            while ((len = input.read(buf)) != -1) {
                bos.write(buf, 0, len);
            }
            // 关流顺序，先打开的后关闭
            input.close();
            bos.close();
        }catch (Exception e){
            log.error("三维模型上传失败！");
            e.printStackTrace();
        }
        return zipToGet3dTask.zipToModel(path,companyId);
    }

    public static String getRealFileName(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (StringUtils.isBlank(filename)) {
            return null;
        }
        int unixSep = filename.lastIndexOf('/');
        int winSep = filename.lastIndexOf('\\');
        int pos = (winSep > unixSep ? winSep : unixSep);
        if (pos != -1) {
            filename = filename.substring(pos + 1);
        }
        return filename;
    }


    @Data
    public static class ModelInfo{

        /**
         * 模型zip文件
         */
        private Long documentId;

        /**
         * 模型解析后的地址
         */
        private String modelUrl;
    }

    @Data
    public static class UploadResult{
        /**
         * 上传成功个数
         */
        private int successNum;

        /**
         * 上传成功文件名称，多个用英文逗号分隔
         */
        private String successAll;

        /**
         * 上传失败个数
         */
        private int failedNum;

        /**
         * 上传失败文件名称，多个用英文逗号分隔
         */
        private String failedAll;
    }

}
