package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionNoteAction;
import com.das.museum.service.biz.collection.action.EditCollectionNoteAction;
import com.das.museum.service.biz.collection.action.NoteQueryPageByConditionAction;
import com.das.museum.service.biz.collection.dto.CollectionNoteDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionNotePageDTO;
import com.das.museum.common.bo.SimplePageInfo;

public interface CollectionNoteService {
    Long addCollectionNote(AddCollectionNoteAction action);
    void editCollectionNote(EditCollectionNoteAction action);
    void delByCollectionNoteId(Long companyId, Long collectionNoteId);
    SimplePageInfo<CollectionNotePageDTO> queryPageByCondition(NoteQueryPageByConditionAction action);
    CollectionNoteDetailDTO queryByCollectionNoteId(Long companyId, Long collectionNoteId);
}
