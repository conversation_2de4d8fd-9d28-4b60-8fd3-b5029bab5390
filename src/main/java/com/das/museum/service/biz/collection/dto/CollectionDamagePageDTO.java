package com.das.museum.service.biz.collection.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CollectionDamagePageDTO implements Serializable {
    private static final long serialVersionUID = -7780936448195201104L;
    private Long collectionDamageId;
    private Long collectionId;
    private String address;
    private Long responsibleId;
    private String responsibleName;
    private String damageInfo;
    private String reason;
    private String handleInfo;
    private String processInfo;
    private String remark;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date damageDate;
    private String documentId;
    private Long creator;
    private Long modifier;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModified;
}