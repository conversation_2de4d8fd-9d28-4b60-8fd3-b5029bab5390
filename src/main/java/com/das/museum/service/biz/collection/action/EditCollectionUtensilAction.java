package com.das.museum.service.biz.collection.action;

import lombok.Data;

import java.io.Serializable;

@Data
public class EditCollectionUtensilAction implements Serializable {
    private static final long serialVersionUID = 1288529583995553056L;
    private Long collectionUtensilId;
    private Long companyId;
    private Long collectionId;
    private String inscription;
    private String ornament;
    private String documentId;
    private Long modifier;
}
