package com.das.museum.service.biz.collection;

import com.das.museum.service.biz.collection.action.AddCollectionDrawAction;
import com.das.museum.service.biz.collection.action.DrawQueryPageByConditionAction;
import com.das.museum.service.biz.collection.action.EditCollectionDrawAction;
import com.das.museum.service.biz.collection.dto.CollectionDrawDetailDTO;
import com.das.museum.service.biz.collection.dto.CollectionDrawPageDTO;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.common.bo.SimplePageInfo;
import com.das.museum.common.exception.CommonErrorCodeEnum;
import com.das.museum.common.exception.CommonException;
import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.SimplePageInfoUtils;
import com.das.museum.domain.collection.entity.CollectionDrawEntity;
import com.das.museum.domain.collection.repository.CollectionDrawRepository;
import com.das.museum.infr.dataobject.ScsCollectionDrawDO;
import com.das.museum.infr.mapper.ScsCollectionDrawMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CollectionDrawServiceImpl implements CollectionDrawService {

    @Resource
    private CollectionDrawRepository collectionDrawRepository;

    @Resource
    private ScsCollectionDrawMapper scsCollectionDrawMapper;

    @Resource
    private DocumentService documentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCollectionDraw(AddCollectionDrawAction action) {
        CollectionDrawEntity entity = this.convertCollectionDrawEntity(action);
        this.collectionDrawRepository.saveCollectionDraw(entity);
        return entity.getCollectionDrawId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCollectionDraw(EditCollectionDrawAction action) {
        Long companyId = action.getCompanyId();
        Long collectionDrawId = action.getCollectionDrawId();
        CollectionDrawDetailDTO collectionDrawDTO = this.queryByCollectionDrawId(companyId, collectionDrawId);
        if (Objects.isNull(collectionDrawDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "绘图信息不存在");
        }
        CollectionDrawEntity entity = this.convertCollectionDrawEntity(action);
        this.collectionDrawRepository.editCollectionDraw(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByCollectionDrawId(Long companyId, Long collectionDrawIdId) {
        CollectionDrawDetailDTO collectionDrawDTO = this.queryByCollectionDrawId(companyId, collectionDrawIdId);
        if (Objects.isNull(collectionDrawDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "绘图信息不存在");
        }
        this.collectionDrawRepository.delByCollectionDrawId(companyId, collectionDrawIdId);
        if (StringUtils.isBlank(collectionDrawDTO.getDocumentId())) {
            return;
        }
        this.documentService.delByDocIds(companyId, collectionDrawDTO.getDocumentId());
    }

    @Override
    public SimplePageInfo<CollectionDrawPageDTO> queryPageByCondition(DrawQueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        action.startPage();
        List<ScsCollectionDrawDO> collectionDrawDOList = this.scsCollectionDrawMapper.listPageByCondition(companyId,
                action.getCollectionId(), action.getTitle(), action.getDrawNo(), action.getSortBy());
        if (CollectionUtils.isEmpty(collectionDrawDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<CollectionDrawPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(collectionDrawDOList, CollectionDrawPageDTO.class);
        List<CollectionDrawPageDTO> collectionDamagePageDTOList = collectionDrawDOList.stream().map(collectionDrawDO -> {
            CollectionDrawPageDTO collectionDrawPageDTO = BeanCopyUtils.copyByJSON(collectionDrawDO, CollectionDrawPageDTO.class);
            collectionDrawPageDTO.setCollectionDrawId(collectionDrawDO.getId());
            return collectionDrawPageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectionDamagePageDTOList);
        return pageInfo;
    }

    @Override
    public CollectionDrawDetailDTO queryByCollectionDrawId(Long companyId, Long collectionDrawId) {
        ScsCollectionDrawDO collectionDrawDO = this.scsCollectionDrawMapper.selectById(companyId, collectionDrawId);
        if (Objects.isNull(collectionDrawDO)) {
            return null;
        }
        CollectionDrawDetailDTO collectionDrawDTO = BeanCopyUtils.copyByJSON(collectionDrawDO, CollectionDrawDetailDTO.class);
        collectionDrawDTO.setCollectionDrawId(collectionDrawDO.getId());
        String documentId = collectionDrawDO.getDocumentId();
        if (StringUtils.isBlank(documentId)) {
            return collectionDrawDTO;
        }
        List<CommonDocumentDTO> commDocumentList = this.documentService.listByDocIds(companyId, documentId);
        collectionDrawDTO.setCommDocumentList(commDocumentList);
        return collectionDrawDTO;
    }
    private CollectionDrawEntity convertCollectionDrawEntity(AddCollectionDrawAction action) {
        CollectionDrawEntity entity = new CollectionDrawEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setCollectionId(action.getCollectionId());
        entity.setTitle(action.getTitle());
        entity.setDrawNo(action.getDrawNo());
        entity.setDraughtsmanId(action.getDraughtsmanId());
        entity.setDraughtsmanName(action.getDraughtsmanName());
        entity.setProportion(action.getProportion());
        entity.setDrawDate(action.getDrawDate());
        entity.setDocumentId(action.getDocumentId());
        entity.setCreator(action.getCreator());
        entity.setModifier(action.getCreator());
        return entity;
    }
    private CollectionDrawEntity convertCollectionDrawEntity(EditCollectionDrawAction action) {
        CollectionDrawEntity entity = new CollectionDrawEntity();
        entity.setCompanyId(action.getCompanyId());
        entity.setCollectionDrawId(action.getCollectionDrawId());
        entity.setTitle(action.getTitle());
        entity.setDrawNo(action.getDrawNo());
        entity.setDraughtsmanId(action.getDraughtsmanId());
        entity.setDraughtsmanName(action.getDraughtsmanName());
        entity.setProportion(action.getProportion());
        entity.setDrawDate(action.getDrawDate());
        entity.setDocumentId(action.getDocumentId());
        entity.setModifier(action.getModifier());
        return entity;
    }
}
