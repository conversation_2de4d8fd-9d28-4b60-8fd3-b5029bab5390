package com.das.museum.service.province;

import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.dependency.province.constant.SendDataBO;
import com.das.museum.dependency.province.cultural.ProvinceCulturalFacade;
import com.das.museum.dependency.province.cultural.bo.SyncCulturalRelicsInfoBO;
import com.das.museum.infr.dataobject.SwzsCulturalRelicsConfigDO;
import com.das.museum.infr.dataobject.SwzsCulturalRelicsDO;
import com.das.museum.infr.mapper.SwzsCulturalRelicsConfigMapper;
import com.das.museum.infr.mapper.SwzsCulturalRelicsMapper;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.service.province.bo.SyncProvinceMuseumAuthBO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/29
 */
@Slf4j
@Service
public class ProvinceCulturalInfoServiceImpl implements ProvinceCulturalInfoService {

    @Resource
    private SwzsCulturalRelicsMapper swzsCulturalRelicsMapper;

    @Resource
    private SwzsCulturalRelicsConfigMapper swzsCulturalRelicsConfigMapper;

    @Resource
    private ProvinceAuthService provinceAuthService;

    @Resource
    private DocumentService documentService;

    @Resource
    private ProvinceCulturalFacade provinceCulturalFacade;

    @Override
    public void syncCollectionCulturalInfo(Date start, Date end, Long companyId) {
        SyncProvinceMuseumAuthBO authServiceMuseumAuth = provinceAuthService.getMuseumAuth(companyId);
        if (Objects.isNull(authServiceMuseumAuth)) {
            return;
        }
        SwzsCulturalRelicsConfigDO culturalRelicsConfigDO = this.swzsCulturalRelicsConfigMapper.selectInfoOnlyOne(companyId);
        if (Objects.isNull(culturalRelicsConfigDO)
                || (StringUtils.isBlank(culturalRelicsConfigDO.getDomainName())
                && StringUtils.isBlank(culturalRelicsConfigDO.getPanoramicUrl()))) {
            return;
        }
        List<SwzsCulturalRelicsDO> culturalRelicsDOList = this.swzsCulturalRelicsMapper.listAll(companyId, start, end);
        if (CollectionUtils.isEmpty(culturalRelicsDOList)) {
            SyncCulturalRelicsInfoBO culturalRelicsInfoBO = new SyncCulturalRelicsInfoBO();
            culturalRelicsInfoBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
            culturalRelicsInfoBO.setNumber("-1000L");
            this.provinceCulturalFacade.syncCulturalInfo(new SendDataBO("CL_INFO", Lists.newArrayList(culturalRelicsInfoBO)),
                    authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
            return;
        }
        StringBuilder coverIdBuilder = new StringBuilder();
        culturalRelicsDOList.forEach(c -> {
            if (StringUtils.isBlank(c.getCoverId())) {
                return;
            }
            coverIdBuilder.append(c.getCoverId());
            coverIdBuilder.append(",");
        });
        coverIdBuilder.deleteCharAt(coverIdBuilder.length()-1);
        List<CommonDocumentDTO> documentList = this.documentService
                .listByDocIds(culturalRelicsConfigDO.getCompanyId(), coverIdBuilder.toString());
        Map<Long, String> docUrlMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(documentList)) {
            documentList.forEach(d -> docUrlMap.put(d.getDocumentId(), d.getUrl()));
        }
        List<SyncCulturalRelicsInfoBO> syncCulturalRelicsInfoBOList = culturalRelicsDOList.stream().map(c -> {
            SyncCulturalRelicsInfoBO syncCulturalRelicsInfoBO = BeanCopyUtils.copyByJSON(c, SyncCulturalRelicsInfoBO.class);
            syncCulturalRelicsInfoBO.setSourceCulturalId(c.getId());
            syncCulturalRelicsInfoBO.setUniqueCode(authServiceMuseumAuth.getUniqueCode());
            syncCulturalRelicsInfoBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
            syncCulturalRelicsInfoBO.setDomainName(culturalRelicsConfigDO.getDomainName());
            syncCulturalRelicsInfoBO.setPanoramicUrl(culturalRelicsConfigDO.getPanoramicUrl());
            if (StringUtils.isBlank(c.getCoverId())) {
                return syncCulturalRelicsInfoBO;
            }
            String[] coverIds = c.getCoverId().split(",");
            StringBuilder coverUrlBuilder = new StringBuilder();
            for (String coverId : coverIds) {
                if (StringUtils.isNotBlank(coverId)
                        && docUrlMap.containsKey(Long.valueOf(coverId))) {
                    coverUrlBuilder.append(docUrlMap.get(Long.valueOf(coverId)));
                }
            }
            syncCulturalRelicsInfoBO.setCoverUrl(coverUrlBuilder.toString());
            return syncCulturalRelicsInfoBO;
        }).collect(Collectors.toList());
        this.provinceCulturalFacade.syncCulturalInfo(new SendDataBO("CL_INFO", syncCulturalRelicsInfoBOList),
                authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
        syncCulturalRelicsInfoBOList.clear();
        syncCulturalRelicsInfoBOList = null;
    }
}
