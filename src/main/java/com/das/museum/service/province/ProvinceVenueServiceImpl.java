package com.das.museum.service.province;

import com.das.museum.dependency.province.constant.SendDataBO;
import com.das.museum.dependency.province.venue.ProvinceVenueFacade;
import com.das.museum.dependency.province.venue.bo.SyncVenueBO;
import com.das.museum.infr.dataobject.TktWechatVenueDO;
import com.das.museum.infr.mapper.TktWechatVenueMapper;
import com.das.museum.service.enums.ChannelTypeEnum;
import com.das.museum.service.province.bo.SyncProvinceMuseumAuthBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class ProvinceVenueServiceImpl implements ProvinceVenueService{

    @Resource
    private ProvinceAuthService provinceAuthService;

    @Resource
    private ProvinceVenueFacade provinceVenueFacade;

    @Resource
    private TktWechatVenueMapper tktWechatVenueMapper;

    @Override
    public void syncVenueInfo(Long companyId) {
        try {
            SyncProvinceMuseumAuthBO authServiceMuseumAuth = provinceAuthService.getMuseumAuth(companyId);
            if (Objects.isNull(authServiceMuseumAuth)) {
                return;
            }
            SyncVenueBO syncVenueBO = new SyncVenueBO();
            syncVenueBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
            syncVenueBO.setVenueClose((byte) 1);

            // 查询场馆信息
            TktWechatVenueDO tktVenueDO = tktWechatVenueMapper.selectByCompanyId(companyId, ChannelTypeEnum.微信小程序.getCode().byteValue());
            if (Objects.nonNull(tktVenueDO)) {
                Date currentTime = new Date();
                // 是否为临时闭馆
                Byte isTemporaryClose = tktVenueDO.getIsTemporaryClose();
                Date temporaryCloseStartDate = tktVenueDO.getTemporaryCloseStartDate();
                Date temporaryCloseEndDate = tktVenueDO.getTemporaryCloseEndDate();
                // 校验临时闭馆
                if (1 == isTemporaryClose && currentTime.after(temporaryCloseStartDate)) {
                    syncVenueBO.setVenueClose((byte) 0);
                    // 如果闭馆已经超过设置的结束时间，则为开馆
                    if(temporaryCloseEndDate != null && currentTime.after(temporaryCloseEndDate)){
                        syncVenueBO.setVenueClose((byte) 1);
                    }
                }
                // 是否为闭馆日
                String closeDay = tktVenueDO.getCloseDay();
                if (StringUtils.isNotBlank(closeDay)) {
                    String[] closeDayArray = closeDay.split(",");
                    Boolean[] closeDayArr = new Boolean[closeDayArray.length];
                    for (int i = 0; i < closeDayArray.length; i++) {
                        closeDayArr[i] = (1 == Integer.parseInt(closeDayArray[i]));
                    }
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(currentTime);
                    int week = cal.get(Calendar.DAY_OF_WEEK) - 1;
                    if (week < 0) {
                        week = 0;
                    }
                    if (closeDayArr[week]) {
                        syncVenueBO.setVenueClose((byte) 0);
                    }
                }
            }
            this.provinceVenueFacade.syncVenueInfo(new SendDataBO("VE", syncVenueBO),
                    authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
        }catch (Exception e) {
            log.error("同步场馆信息失败", e);
        }
    }

}
