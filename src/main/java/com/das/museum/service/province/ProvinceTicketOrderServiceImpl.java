package com.das.museum.service.province;

import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.dependency.province.constant.SendDataBO;
import com.das.museum.dependency.province.ticket.ProvinceOrderFacade;
import com.das.museum.dependency.province.ticket.bo.SyncOrderBaseBO;
import com.das.museum.infr.dataobject.TktOrderBaseDO;
import com.das.museum.infr.dataobject.TktPersonalOrderDetailDO;
import com.das.museum.infr.dataobject.TktTeamOrderDetailDO;
import com.das.museum.infr.mapper.TktOrderBaseMapper;
import com.das.museum.infr.mapper.TktPersonalOrderDetailMapper;
import com.das.museum.infr.mapper.TktTeamOrderDetailMapper;
import com.das.museum.service.enums.OrderTypeEnum;
import com.das.museum.service.province.bo.SyncProvinceMuseumAuthBO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
public class ProvinceTicketOrderServiceImpl implements ProvinceTicketOrderService {

    @Resource
    private TktPersonalOrderDetailMapper personalOrderDetailMapper;

    @Resource
    private TktTeamOrderDetailMapper teamOrderDetailMapper;

    @Resource
    private TktOrderBaseMapper tktOrderBaseMapper;

    @Resource
    private ProvinceAuthService provinceAuthService;

    @Resource
    private ProvinceOrderFacade provinceOrderFacade;

    @Override
    public void syncTicketOrderInfo(Date start, Date end, Long companyId) {
        SyncProvinceMuseumAuthBO authServiceMuseumAuth = this.provinceAuthService.getMuseumAuth(companyId);
        if (Objects.isNull(authServiceMuseumAuth)) {
            return;
        }
        List<SyncOrderBaseBO> orderList = Lists.newArrayList();
        List<TktPersonalOrderDetailDO> personalOrderList = this.personalOrderDetailMapper.listByAll(companyId, start, end);
        if (CollectionUtils.isNotEmpty(personalOrderList)) {
            personalOrderList.forEach(k -> {
                SyncOrderBaseBO syncOrderBaseBO = BeanCopyUtils.copyByJSON(k, SyncOrderBaseBO.class);
                syncOrderBaseBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
                syncOrderBaseBO.setOrderNo(k.getPersonalOrderNo());
                syncOrderBaseBO.setOrderType(OrderTypeEnum.个人预约.getCode().byteValue());
                syncOrderBaseBO.setTotalTicketCount(1L);
                syncOrderBaseBO.setTotalPeopleCount(1L);
                syncOrderBaseBO.setWriteOffDate(k.getWriteOffDate());
                syncOrderBaseBO.setStatisticsDate(DateUtils.parseDateYYYMMDDHHmmss(DateUtils.formatDateYYYMMDD(k.getGmtCreate()) + " 00:00:00"));
                orderList.add(syncOrderBaseBO);
            });
        }
        List<TktTeamOrderDetailDO> teamOrderList = this.teamOrderDetailMapper.listAll(companyId, start, end);
        if (CollectionUtils.isNotEmpty(teamOrderList)) {
            List<Long> ids = teamOrderList.stream().map(TktTeamOrderDetailDO::getOrderId).collect(Collectors.toList());
            List<TktOrderBaseDO> orderBaseDOList = this.tktOrderBaseMapper.selectByOrderIds(ids);
            Map<Long, TktOrderBaseDO> orderMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(orderBaseDOList)) {
                orderBaseDOList.forEach(k -> orderMap.put(k.getId(), k));
            }
            teamOrderList.forEach(k -> {
                if (!orderMap.containsKey(k.getOrderId())) {
                    return;
                }
                TktOrderBaseDO orderBaseDO = orderMap.get(k.getOrderId());
                SyncOrderBaseBO syncOrderBaseBO = BeanCopyUtils.copyByJSON(k, SyncOrderBaseBO.class);
                syncOrderBaseBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
                syncOrderBaseBO.setOrderNo(k.getTeamOrderNo());
                syncOrderBaseBO.setOrderType(OrderTypeEnum.团队预约.getCode().byteValue());
                syncOrderBaseBO.setTotalTicketCount(orderBaseDO.getTotalTicketCount());
                syncOrderBaseBO.setTotalPeopleCount(orderBaseDO.getTotalPeopleCount());
                syncOrderBaseBO.setVenueName(orderBaseDO.getVenueName());
                syncOrderBaseBO.setPhone(k.getLeaderPhone());
                syncOrderBaseBO.setTouristName(k.getLeaderName());
                syncOrderBaseBO.setTouristCertificateType(k.getLeaderCertificateType());
                syncOrderBaseBO.setTouristCertificateNo(k.getLeaderCertificateNo());
                syncOrderBaseBO.setWriteOffDate(k.getWriteOffDate());
                syncOrderBaseBO.setStatisticsDate(DateUtils.parseDateYYYMMDDHHmmss(DateUtils.formatDateYYYMMDD(k.getGmtCreate()) + " 00:00:00"));
                orderList.add(syncOrderBaseBO);
            });
        }
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        this.provinceOrderFacade.syncOrderInfo(new SendDataBO("OD", orderList),
                authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
        orderList.clear();
    }

    @Override
    public void syncTicketHistoryOrderInfo(Date start, Date end, Long companyId) {
        SyncProvinceMuseumAuthBO authServiceMuseumAuth = this.provinceAuthService.getMuseumAuth(companyId);
        if (Objects.isNull(authServiceMuseumAuth)) {
            return;
        }
        List<SyncOrderBaseBO> orderList = Lists.newArrayList();
        List<TktPersonalOrderDetailDO> personalOrderList = this.personalOrderDetailMapper.listByAll(companyId, start, end);
        if (CollectionUtils.isNotEmpty(personalOrderList)) {
            personalOrderList.forEach(k -> {
                SyncOrderBaseBO syncOrderBaseBO = BeanCopyUtils.copyByJSON(k, SyncOrderBaseBO.class);
                syncOrderBaseBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
                syncOrderBaseBO.setOrderNo(k.getPersonalOrderNo());
                syncOrderBaseBO.setOrderType(OrderTypeEnum.个人预约.getCode().byteValue());
                syncOrderBaseBO.setTotalTicketCount(1L);
                syncOrderBaseBO.setTotalPeopleCount(1L);
                syncOrderBaseBO.setWriteOffDate(k.getWriteOffDate());
                syncOrderBaseBO.setStatisticsDate(DateUtils.parseDateYYYMMDDHHmmss(DateUtils.formatDateYYYMMDD(k.getGmtCreate()) + " 00:00:00"));
                orderList.add(syncOrderBaseBO);
            });
        }
        List<TktTeamOrderDetailDO> teamOrderList = this.teamOrderDetailMapper.listAll(companyId, start, end);
        if (CollectionUtils.isNotEmpty(teamOrderList)) {
            List<Long> ids = teamOrderList.stream().map(TktTeamOrderDetailDO::getOrderId).collect(Collectors.toList());
            List<TktOrderBaseDO> orderBaseDOList = this.tktOrderBaseMapper.selectByOrderIds(ids);
            Map<Long, TktOrderBaseDO> orderMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(orderBaseDOList)) {
                orderBaseDOList.forEach(k -> orderMap.put(k.getId(), k));
            }
            teamOrderList.forEach(k -> {
                if (!orderMap.containsKey(k.getOrderId())) {
                    return;
                }
                TktOrderBaseDO orderBaseDO = orderMap.get(k.getOrderId());
                SyncOrderBaseBO syncOrderBaseBO = BeanCopyUtils.copyByJSON(k, SyncOrderBaseBO.class);
                syncOrderBaseBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
                syncOrderBaseBO.setOrderNo(k.getTeamOrderNo());
                syncOrderBaseBO.setOrderType(OrderTypeEnum.团队预约.getCode().byteValue());
                syncOrderBaseBO.setTotalTicketCount(orderBaseDO.getTotalTicketCount());
                syncOrderBaseBO.setTotalPeopleCount(orderBaseDO.getTotalPeopleCount());
                syncOrderBaseBO.setVenueName(orderBaseDO.getVenueName());
                syncOrderBaseBO.setPhone(k.getLeaderPhone());
                syncOrderBaseBO.setTouristName(k.getLeaderName());
                syncOrderBaseBO.setTouristCertificateType(k.getLeaderCertificateType());
                syncOrderBaseBO.setTouristCertificateNo(k.getLeaderCertificateNo());
                syncOrderBaseBO.setWriteOffDate(k.getWriteOffDate());
                syncOrderBaseBO.setStatisticsDate(DateUtils.parseDateYYYMMDDHHmmss(DateUtils.formatDateYYYMMDD(k.getGmtCreate()) + " 00:00:00"));
                orderList.add(syncOrderBaseBO);
            });
        }
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        this.provinceOrderFacade.syncOrderInfo(new SendDataBO("HISTORY_OD", orderList),
                authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
        orderList.clear();
        personalOrderList.clear();
        teamOrderList.clear();
        personalOrderList = null;
        teamOrderList = null;
    }
}
