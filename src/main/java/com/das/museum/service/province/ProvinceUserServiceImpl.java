package com.das.museum.service.province;

import com.das.museum.common.utils.DateUtils;
import com.das.museum.dependency.province.constant.SendDataBO;
import com.das.museum.dependency.province.user.ProvinceUserFacade;
import com.das.museum.dependency.province.user.bo.SyncUserInfoBO;
import com.das.museum.infr.dataobject.AccUserDO;
import com.das.museum.infr.mapper.AccUserMapper;
import com.das.museum.service.enums.PostCodeEnum;
import com.das.museum.service.province.bo.SyncProvinceMuseumAuthBO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
public class ProvinceUserServiceImpl implements ProvinceUserService {

    @Resource
    private AccUserMapper accUserMapper;

    @Resource
    private ProvinceAuthService provinceAuthService;

    @Resource
    private ProvinceUserFacade provinceUserFacade;

    @Override
    public void syncUserInfo(Long companyId) {
        SyncProvinceMuseumAuthBO authServiceMuseumAuth = provinceAuthService.getMuseumAuth(companyId);
        if (Objects.isNull(authServiceMuseumAuth)) {
            return;
        }
        List<AccUserDO> userDOList = this.accUserMapper.listAll(companyId);
        if (CollectionUtils.isEmpty(userDOList)) {
            SyncUserInfoBO syncUserInfoBO = new SyncUserInfoBO();
            syncUserInfoBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
            syncUserInfoBO.setPostCode("-1000L");
            this.provinceUserFacade.syncUserInfo(new SendDataBO("UZ", Lists.newArrayList(syncUserInfoBO)),
                    authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
            return;
        }
        AtomicLong count1 = new AtomicLong(0L);
        AtomicLong count2 = new AtomicLong(0L);
        userDOList.forEach(k -> {
            if (PostCodeEnum.CONT.getCode().equals(k.getPostCode())) {
                count1.getAndIncrement();
            } else if (PostCodeEnum.PREN.getCode().equals(k.getPostCode())) {
                count2.getAndIncrement();
            }
        });
        SyncUserInfoBO syncUserInfo1 = new SyncUserInfoBO();
        syncUserInfo1.setPostCode(PostCodeEnum.CONT.getCode());
        syncUserInfo1.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
        syncUserInfo1.setCount(count1.get());
        syncUserInfo1.setStatisticsDate(DateUtils.getTimesDayMorning());
        SyncUserInfoBO syncUserInfo2 = new SyncUserInfoBO();
        syncUserInfo2.setPostCode(PostCodeEnum.PREN.getCode());
        syncUserInfo2.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
        syncUserInfo2.setCount(count2.get());
        syncUserInfo2.setStatisticsDate(DateUtils.getTimesDayMorning());
        this.provinceUserFacade.syncUserInfo(new SendDataBO("UZ", Lists.newArrayList(syncUserInfo1, syncUserInfo2)),
                authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
    }
}
