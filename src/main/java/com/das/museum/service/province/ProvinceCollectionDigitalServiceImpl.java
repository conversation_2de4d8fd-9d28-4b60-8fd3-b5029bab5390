package com.das.museum.service.province;

import com.das.museum.dependency.province.constant.SendDataBO;
import com.das.museum.dependency.province.data.ProvinceDataFacade;
import com.das.museum.dependency.province.data.bo.SyncCollectionDigitalBO;
import com.das.museum.infr.dataobject.SwzsCulturalRelicsDO;
import com.das.museum.infr.mapper.SwzsCulturalRelicsMapper;
import com.das.museum.service.enums.CulturalDataStatusEnum;
import com.das.museum.service.province.bo.SyncProvinceMuseumAuthBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2023/4/11
 */
@Slf4j
@Service
public class ProvinceCollectionDigitalServiceImpl implements ProvinceCollectionDigitalService {

    @Resource
    private SwzsCulturalRelicsMapper swzsCulturalRelicsMapper;

    @Resource
    private ProvinceAuthService provinceAuthService;

    @Resource
    private ProvinceDataFacade provinceDataFacade;

    @Override
    public void syncCollectionDigitalInfo(Long companyId) {
        SyncProvinceMuseumAuthBO authServiceMuseumAuth = provinceAuthService.getMuseumAuth(companyId);
        if (Objects.isNull(authServiceMuseumAuth)) {
            return;
        }
        List<SwzsCulturalRelicsDO> culturalRelicsDOList = this.swzsCulturalRelicsMapper.listAll(companyId, null, null);
        if (CollectionUtils.isEmpty(culturalRelicsDOList)) {
            SyncCollectionDigitalBO syncCollectionDigitalBO = new SyncCollectionDigitalBO();
            syncCollectionDigitalBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
            syncCollectionDigitalBO.setTwoModelNum(-1000);
            this.provinceDataFacade.syncDataInfo(new SendDataBO("CP_DIGITAL", syncCollectionDigitalBO),
                    authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
            return;
        }
        AtomicInteger twoModelNum = new AtomicInteger(0);
        AtomicInteger threeModelNum = new AtomicInteger(0);
        culturalRelicsDOList.forEach(cul -> {
            if (CulturalDataStatusEnum.现场窗口.getCode().byteValue() == cul.getDataStatus()) {
                return;
            }
            if (CulturalDataStatusEnum.二维.getCode().byteValue() == cul.getDataStatus()) {
                twoModelNum.getAndIncrement();
            } else if (CulturalDataStatusEnum.三维.getCode().byteValue() == cul.getDataStatus()) {
                threeModelNum.getAndIncrement();
            } else if (CulturalDataStatusEnum.二维和三维.getCode().byteValue() == cul.getDataStatus()) {
                threeModelNum.getAndIncrement();
                twoModelNum.getAndIncrement();
            }
        });
        SyncCollectionDigitalBO syncCollectionDigitalBO = new SyncCollectionDigitalBO();
        syncCollectionDigitalBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
        syncCollectionDigitalBO.setTwoModelNum(twoModelNum.get());
        syncCollectionDigitalBO.setThreeModelNum(threeModelNum.get());
        this.provinceDataFacade.syncDataInfo(new SendDataBO("CP_DIGITAL", syncCollectionDigitalBO),
                authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
    }
}
