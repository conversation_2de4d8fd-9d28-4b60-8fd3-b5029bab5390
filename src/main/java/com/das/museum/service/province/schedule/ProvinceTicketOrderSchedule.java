package com.das.museum.service.province.schedule;

import com.das.museum.common.utils.DateUtils;
import com.das.museum.infr.dataobject.SyncProvinceMuseumAuthDO;
import com.das.museum.infr.mapper.SyncProvinceMuseumAuthMapper;
import com.das.museum.service.province.ProvinceTicketOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
@Async
public class ProvinceTicketOrderSchedule {

    @Resource
    private ProvinceTicketOrderService provinceTicketOrderService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SyncProvinceMuseumAuthMapper syncProvinceMuseumAuthMapper;

    /**
     * 历史数据同步（同步历史所有）,每天22点11分执行
     */
    @Scheduled(cron = "0 01 11 * * ?")
    public void syncAllTicketOrderInfo() {
        log.info("ProvinceTicketOrderSchedule syncAllTicketOrderInfo start.......");
        RLock rLock = redissonClient.getLock("syncAllTicketOrderInfo");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                List<SyncProvinceMuseumAuthDO> provinceMuseumAuthList = this.syncProvinceMuseumAuthMapper.selectByAll();
                if (CollectionUtils.isEmpty(provinceMuseumAuthList)) {
                    return;
                }
                Date start = DateUtils.getTimesYearMorning();
                Date end = new Date();
                provinceMuseumAuthList.forEach(p -> {
                    if (StringUtils.isBlank(p.getKey())) {
                        return;
                    }
                    // 伪满皇宫、东北沦陷陈列馆、长影旧址三个馆观众数据不从单馆同步
                    if("1678320746423783424".equals(p.getUniqueCode())
                            || "1684468846498549760".equals(p.getUniqueCode())
                            || "1702249354724773888".equals(p.getUniqueCode())){
                        return;
                    }
                    this.provinceTicketOrderService.syncTicketHistoryOrderInfo(start, end, p.getCompanyId());
                    try {
                        // 每个场馆数据同步间隔40s
                        Thread.sleep(5000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.info("ProvinceTicketOrderSchedule syncAllTicketOrderInfo Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("ProvinceTicketOrderSchedule syncAllTicketOrderInfo end.......");
    }

    /**
     * 每10分钟同步一次今日核销的观众数据
     */
    //@Scheduled(cron = "0 0/10 * * * ?")
    public void syncTicketOrderInfo() {
        // 22点之后 - 凌晨6点之前不执行此定时任务
        if(DateUtils.getCurrentHour() >= 22 || DateUtils.getCurrentHour() < 6){
            return;
        }
        log.info("ProvinceTicketOrderSchedule syncTicketOrderInfo start.......");
        RLock rLock = redissonClient.getLock("syncTicketOrderInfo");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                List<SyncProvinceMuseumAuthDO> provinceMuseumAuthList = this.syncProvinceMuseumAuthMapper.selectByAll();
                if (CollectionUtils.isEmpty(provinceMuseumAuthList)) {
                    return;
                }
                provinceMuseumAuthList.forEach(p -> {
                    if (StringUtils.isBlank(p.getKey())) {
                        return;
                    }
                    // 伪满皇宫、东北沦陷陈列馆、长影旧址三个馆观众数据不从单馆同步
                    if("1678320746423783424".equals(p.getUniqueCode())
                            || "1684468846498549760".equals(p.getUniqueCode())
                            || "1702249354724773888".equals(p.getUniqueCode())){
                        return;
                    }
                    this.provinceTicketOrderService.syncTicketOrderInfo(DateUtils.getTimesDayMorning(), new Date(), p.getCompanyId());
                });
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.info("ProvinceTicketOrderSchedule syncTicketOrderInfo Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("ProvinceTicketOrderSchedule syncTicketOrderInfo end.......");
    }
}
