package com.das.museum.service.province.schedule;

import com.das.museum.infr.dataobject.SyncProvinceMuseumAuthDO;
import com.das.museum.infr.mapper.SyncProvinceMuseumAuthMapper;
import com.das.museum.service.province.ProvinceCollectionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
public class ProvinceCollectionSchedule {

    @Resource
    private ProvinceCollectionService provinceCollectionService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SyncProvinceMuseumAuthMapper syncProvinceMuseumAuthMapper;

    /**
     * 同步博物馆藏品登录信息，每晚22点01分同步
     */
    //@Scheduled(cron = "0 01 22 * * ?")
    public void syncCollectionLoginInfo() {
        log.info("ProvinceCollectionSchedule syncCollectionLoginInfo start.......");
        RLock rLock = redissonClient.getLock("syncCollectionLoginInfo");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                List<SyncProvinceMuseumAuthDO> provinceMuseumAuthList = this.syncProvinceMuseumAuthMapper.selectByAll();
                if (CollectionUtils.isEmpty(provinceMuseumAuthList)) {
                    return;
                }
                provinceMuseumAuthList.forEach(p -> {
                    if (StringUtils.isBlank(p.getKey())) {
                        return;
                    }
                    this.provinceCollectionService.syncCollectionLoginInfo(p.getCompanyId());
                });
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.info("ProvinceCollectionSchedule syncCollectionLoginInfo Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("ProvinceCollectionSchedule syncCollectionLoginInfo end.......");
    }
}
