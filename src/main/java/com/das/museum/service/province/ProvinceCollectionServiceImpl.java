package com.das.museum.service.province;

import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.dependency.province.collection.ProvinceCollectionFacade;
import com.das.museum.dependency.province.collection.bo.SyncCommCollectionRegisterInfoBO;
import com.das.museum.dependency.province.constant.SendDataBO;
import com.das.museum.domain.enums.RegisterStatusEnum;
import com.das.museum.infr.dataobject.*;
import com.das.museum.infr.mapper.*;
import com.das.museum.service.biz.common.DocumentService;
import com.das.museum.service.biz.common.dto.CommonDocumentDTO;
import com.das.museum.service.enums.DeleteStatusEnum;
import com.das.museum.service.enums.ResourceTypeEnum;
import com.das.museum.service.province.bo.SyncProvinceMuseumAuthBO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
public class ProvinceCollectionServiceImpl implements ProvinceCollectionService {

    @Resource
    private ScsCollectionRegisterInfoMapper scsCollectionRegisterInfoMapper;

    @Resource
    private ScsCollectionRegisterAttrInfoMapper scsCollectionRegisterAttrInfoMapper;

    @Resource
    private ScsCollectionTwoImageMapper scsCollectionTwoImageMapper;

    @Resource
    private ScsCollectionThreeModelMapper scsCollectionThreeModelMapper;

    @Resource
    private DocumentService documentService;

    @Resource
    private CommDocumentMapper commDocumentMapper;

    @Resource
    private SzzyResourceMapper szzyResourceMapper;

    @Resource
    private SzzyResourceSearchMapper szzyResourceSearchMapper;

    @Resource
    private ProvinceCollectionFacade provinceCollectionFacade;

    @Resource
    private ProvinceAuthService provinceAuthService;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private SwzsCulturalRelicsMapper swzsCulturalRelicsMapper;

    @Override
    public void syncCollectionLoginInfo(Long companyId) {
        SyncProvinceMuseumAuthBO authServiceMuseumAuth = provinceAuthService.getMuseumAuth(companyId);
        if (Objects.isNull(authServiceMuseumAuth)) {
            return;
        }
        // 一次性同步单个场馆所有数据
        List<SyncCommCollectionRegisterInfoBO> syncCollectionList = Lists.newArrayList();

        List<ScsCollectionRegisterInfoDO> collectionLoginListAll = this.scsCollectionRegisterInfoMapper.listByRegisterStatus(companyId,
                Lists.newArrayList(RegisterStatusEnum.ARTS.getCode(), RegisterStatusEnum.AOWH.getCode(), RegisterStatusEnum.POWH.getCode(),
                        RegisterStatusEnum.AIWH.getCode(), RegisterStatusEnum.WLGF.getCode()));
        // 数据量过大，拆分10000条 整合一次数据
        int num = collectionLoginListAll.size()/10000;
        for(int i = 0; i < num+1;i++){
            int endIndex = (i+1)*10000;
            if(endIndex > collectionLoginListAll.size()){
                endIndex = collectionLoginListAll.size();
            }
            List<ScsCollectionRegisterInfoDO>  collectionLoginList = collectionLoginListAll.subList(i*10000,endIndex);
            if (CollectionUtils.isEmpty(collectionLoginList)) {
                SyncCommCollectionRegisterInfoBO collection = new SyncCommCollectionRegisterInfoBO();
                collection.setCollectionId(-1000L);
                collection.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
                List<SyncCommCollectionRegisterInfoBO> collectionList = Lists.newArrayList(collection);
                this.provinceCollectionFacade.syncCollectionInfo(new SendDataBO("CP", collectionList),
                        authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
                return;
            }

            List<Long> documentIds = collectionLoginList.stream().filter(s -> StringUtils.isNotBlank(s.getDocumentId()))
                    .map(ScsCollectionRegisterInfoDO::getDocumentId).map(Long::parseLong).collect(Collectors.toList());
            Map<String, String> coverMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(documentIds)){
                List<CommDocumentDO> commDocumentDOList = this.commDocumentMapper.listAllByIds(documentIds);
                commDocumentDOList.forEach(k->coverMap.put(k.getId().toString(),k.getUrl()));
            }

            List<Long> registerInfoIdList = collectionLoginList.stream().map(ScsCollectionRegisterInfoDO::getId).collect(Collectors.toList());
            Map<Long,String> twoModelUrlMap = getCollectionTwoModelUrl(registerInfoIdList);
            Map<Long,String> threeModelUrlMap = getCollectionThreeModelUrl(companyId,registerInfoIdList);

            List<ScsCollectionRegisterAttrInfoDO> attrInfoDOList = this.scsCollectionRegisterAttrInfoMapper.listByRegisterInfoIds(registerInfoIdList);
            Map<Long,ScsCollectionRegisterAttrInfoDO> attrInfoMap = new HashMap<>();

            List<Long> classificationIds = Lists.newArrayList();
            List<String> registerNos = Lists.newArrayList();
            collectionLoginList.forEach(registerInfoDO -> {
                registerNos.add(registerInfoDO.getRegisterNo());
                if (Objects.nonNull(registerInfoDO.getClassify())) {
                    classificationIds.add(registerInfoDO.getClassify());
                }
                if (Objects.nonNull(registerInfoDO.getIdentifyLevel())) {
                    classificationIds.add(registerInfoDO.getIdentifyLevel());
                }
                if (Objects.nonNull(registerInfoDO.getAge())) {
                    classificationIds.add(registerInfoDO.getAge());
                }
                if (Objects.nonNull(registerInfoDO.getCategory())) {
                    classificationIds.add(registerInfoDO.getCategory());
                }
            });

            if(CollectionUtils.isNotEmpty(attrInfoDOList)){
                attrInfoDOList.forEach(attrInfoDO -> {
                    attrInfoMap.put(attrInfoDO.getRegisterInfoId(),attrInfoDO);
                    if (StringUtils.isNotBlank(attrInfoDO.getTexture())) {
                        List<Long> textureIds = Stream.of(attrInfoDO.getTexture().split(","))
                                .map(Long::parseLong).distinct().collect(Collectors.toList());
                        classificationIds.addAll(textureIds);
                    }
                    if (Objects.nonNull(attrInfoDO.getCompleteDegree())) {
                        classificationIds.add(attrInfoDO.getCompleteDegree());
                    }
                    if (Objects.nonNull(attrInfoDO.getSource())) {
                        classificationIds.add(attrInfoDO.getSource());
                    }
                });
            }

            Map<Long, String> classificationMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(classificationIds)) {
                List<CommClassificationDO> classificationDOList = this.commClassificationMapper.selectByIds(classificationIds);
                if (CollectionUtils.isNotEmpty(classificationDOList)) {
                    classificationMap = classificationDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                            CommClassificationDO::getName, (key1, key2) -> key2));
                }
            }
            Map<String, Byte> registerNoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(registerNos)) {
                List<SwzsCulturalRelicsDO> culturalRelicsDOList = this.swzsCulturalRelicsMapper.listByNumbers(registerNos);
                if (CollectionUtils.isNotEmpty(culturalRelicsDOList)) {
                    registerNoMap = culturalRelicsDOList.stream().collect(Collectors.toMap(SwzsCulturalRelicsDO::getNumber,
                            SwzsCulturalRelicsDO::getStatus, (key1, key2) -> key2));
                }
            }
            Map<Long, String> finalClassificationMap = classificationMap;
            Map<String, Byte> finalRegisterNoMap = registerNoMap;
            List<SyncCommCollectionRegisterInfoBO> collectionList = collectionLoginList.stream().map(k -> {
                SyncCommCollectionRegisterInfoBO collectionRegisterInfoBO = BeanCopyUtils.copyByJSON(k, SyncCommCollectionRegisterInfoBO.class);
                collectionRegisterInfoBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
                collectionRegisterInfoBO.setLoginDate(DateUtils.parseDateYYYMMDDHHmmss(DateUtils.formatDateYYYMMDD(k.getGmtModified()) + " 00:00:00"));
                if (finalClassificationMap.containsKey(k.getClassify())) {
                    collectionRegisterInfoBO.setClassifyName(finalClassificationMap.get(k.getClassify()));
                }
                if (finalClassificationMap.containsKey(k.getIdentifyLevel())) {
                    collectionRegisterInfoBO.setIdentifyLevelName(finalClassificationMap.get(k.getIdentifyLevel()));
                }
                if (finalClassificationMap.containsKey(k.getAge())) {
                    collectionRegisterInfoBO.setAge(finalClassificationMap.get(k.getAge()));
                }
                if (finalClassificationMap.containsKey(k.getCategory())) {
                    collectionRegisterInfoBO.setCategoryName(finalClassificationMap.get(k.getCategory()));
                }
                if (!RegisterStatusEnum.ALGF.getCode().equals(k.getRegisterStatus())) {
                    collectionRegisterInfoBO.setRegisterStatus(RegisterStatusEnum.ARTS.getCode());
                } else {
                    collectionRegisterInfoBO.setRegisterStatus(RegisterStatusEnum.ALGF.getCode());
                }
                if (finalRegisterNoMap.containsKey(k.getRegisterNo())) {
                    collectionRegisterInfoBO.setCulturalStatus(finalRegisterNoMap.get(k.getRegisterNo()));
                } else {
                    collectionRegisterInfoBO.setCulturalStatus((byte)0);
                }
                if(coverMap.containsKey(k.getDocumentId())){
                    collectionRegisterInfoBO.setCoverUrl(coverMap.get(k.getDocumentId()));
                }
                // 藏品登录扩展表数据
                if(attrInfoMap.containsKey(k.getId())){
                    ScsCollectionRegisterAttrInfoDO attrInfoDO = attrInfoMap.get(k.getId());
                    collectionRegisterInfoBO.setSize(attrInfoDO.getSize());
                    if (finalClassificationMap.containsKey(attrInfoDO.getSource())) {
                        collectionRegisterInfoBO.setSourceName(finalClassificationMap.get(attrInfoDO.getSource()));
                    }
                    if (finalClassificationMap.containsKey(attrInfoDO.getCompleteDegree())) {
                        collectionRegisterInfoBO.setCompleteDegreeName(finalClassificationMap.get(attrInfoDO.getCompleteDegree()));
                    }

                    String texture = attrInfoDO.getTexture();
                    if (StringUtils.isNotBlank(texture)) {
                        List<Long> textureIds = Stream.of(texture.split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                        StringBuilder textureBuilder = new StringBuilder();
                        textureIds.forEach(textureId -> {
                            textureBuilder.append(finalClassificationMap.get(textureId));
                            textureBuilder.append(",");
                        });
                        textureBuilder.deleteCharAt(textureBuilder.toString().length() - 1);
                        collectionRegisterInfoBO.setTextureName(textureBuilder.toString());
                    }
                }

                if(twoModelUrlMap.containsKey(k.getId())){
                    collectionRegisterInfoBO.setTwoModelUrl(twoModelUrlMap.get(k.getId()));
                }
                if(threeModelUrlMap.containsKey(k.getId())){
                    collectionRegisterInfoBO.setThreeModelUrl(threeModelUrlMap.get(k.getId()));
                }
                return collectionRegisterInfoBO;
            }).collect(Collectors.toList());
            syncCollectionList.addAll(collectionList);
        }
        this.provinceCollectionFacade.syncCollectionInfo(new SendDataBO("CP", syncCollectionList),
                authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
        syncCollectionList.clear();
        collectionLoginListAll.clear();
        syncCollectionList = null;
        collectionLoginListAll = null;
    }


     /**
     * 获取藏品二维影像
     * @param registerInfoIdList 藏品登录id集合
     * @return
     */
    private Map<Long,String> getCollectionTwoModelUrl(List<Long> registerInfoIdList){
        Map<Long,String> resultMap = new HashMap<>();
        if(CollectionUtils.isEmpty(registerInfoIdList)){
            return resultMap;
        }
        List<ScsCollectionTwoImageDO> twoImageDOList = scsCollectionTwoImageMapper.selectListByRegisterInfoIds(registerInfoIdList);
        if(CollectionUtils.isEmpty(twoImageDOList)){
            return resultMap;
        }

        List<Long> sourceDocIdList = twoImageDOList.stream().map(ScsCollectionTwoImageDO::getDocumentId).collect(Collectors.toList());
        List<CommDocumentDO> commDocumentDOList = this.commDocumentMapper.listAllByIds(sourceDocIdList);
        Map<Long,String> documentUrlMap = new HashMap<>();
        commDocumentDOList.forEach(k -> documentUrlMap.put(k.getId(),k.getUrl()));

        twoImageDOList.forEach(k -> {
            if(documentUrlMap.containsKey(k.getDocumentId())){
                if(resultMap.containsKey(k.getRegisterInfoId())){
                        resultMap.put(k.getRegisterInfoId(),resultMap.get(k.getRegisterInfoId())+","+documentUrlMap.get(k.getDocumentId()));
                }else{
                    resultMap.put(k.getRegisterInfoId(),documentUrlMap.get(k.getDocumentId()));
                }
            }
        });

        return resultMap;
    }

    /**
     * 获取藏品三维影像
     * @param registerInfoIdList 藏品登录id集合
     * @return
     */
    private Map<Long,String> getCollectionThreeModelUrl(Long companyId,List<Long> registerInfoIdList){
        Map<Long,String> resultMap = new HashMap<>();
        if(CollectionUtils.isEmpty(registerInfoIdList)){
            return resultMap;
        }
        List<ScsCollectionThreeModelDO> threeModelDOList = scsCollectionThreeModelMapper.selectListByRegisterInfoIds(registerInfoIdList);
        if(CollectionUtils.isEmpty(threeModelDOList)){
            return resultMap;
        }

        Map<Long, ScsCollectionThreeModelDO> threeModelMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(threeModelDOList)) {
            List<Long> threeModelId = threeModelDOList.stream().map(ScsCollectionThreeModelDO::getDocumentId).collect(Collectors.toList());
            List<CommonDocumentDTO> commonDocumentDTOList = documentService.listByDocIds(companyId,threeModelId);
            Map<Long,Long> fileSizeMap = new HashMap<>();
            commonDocumentDTOList.forEach(s->fileSizeMap.put(s.getDocumentId(),s.getFileSize()));
            threeModelDOList.forEach(s -> {
                if(threeModelMap.containsKey(s.getRegisterInfoId())){
                    if(fileSizeMap.containsKey(s.getDocumentId()) && fileSizeMap.get(s.getDocumentId()) < fileSizeMap.get(threeModelMap.get(s.getRegisterInfoId()).getDocumentId())){
                        threeModelMap.put(s.getRegisterInfoId(),s);
                    }
                }else{
                    threeModelMap.put(s.getRegisterInfoId(),s);
                }
            });
        }

//        threeModelDOList.forEach(k -> {
//            if(k.getThreeModelStatus() == 2){
//                if(resultMap.containsKey(k.getRegisterInfoId())){
//                    resultMap.put(k.getRegisterInfoId(),resultMap.get(k.getRegisterInfoId())+","+k.getThreeModelUrl());
//                }else{
//                    resultMap.put(k.getRegisterInfoId(),k.getThreeModelUrl());
//                }
//            }
//        });

        threeModelDOList.stream().filter(s->s.getThreeModelStatus() == 2).forEach(k ->{
            if(threeModelMap.containsKey(k.getRegisterInfoId())){
                resultMap.put(k.getRegisterInfoId(),threeModelMap.get(k.getRegisterInfoId()).getThreeModelUrl());
            }
        });
        return resultMap;
    }
}
