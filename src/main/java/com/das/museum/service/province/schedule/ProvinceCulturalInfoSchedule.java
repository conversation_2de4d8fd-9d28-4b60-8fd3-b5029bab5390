package com.das.museum.service.province.schedule;

import com.das.museum.infr.dataobject.SyncProvinceMuseumAuthDO;
import com.das.museum.infr.mapper.SyncProvinceMuseumAuthMapper;
import com.das.museum.service.province.ProvinceCulturalInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
public class ProvinceCulturalInfoSchedule {

    @Resource
    private ProvinceCulturalInfoService provinceCulturalInfoService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SyncProvinceMuseumAuthMapper syncProvinceMuseumAuthMapper;

    //@Scheduled(cron = "0 18 0 * * ?")
    public void syncAllCollectionCulturalBaseInfo() {
        log.info("ProvinceCulturalInfoSchedule syncAllCollectionCulturalBaseInfo start.......");
        RLock rLock = redissonClient.getLock("syncAllCollectionCulturalBaseInfo");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                List<SyncProvinceMuseumAuthDO> provinceMuseumAuthList = this.syncProvinceMuseumAuthMapper.selectByAll();
                if (CollectionUtils.isEmpty(provinceMuseumAuthList)) {
                    return;
                }
                provinceMuseumAuthList.forEach(p -> {
                    if (StringUtils.isBlank(p.getKey())) {
                        return;
                    }
                    this.provinceCulturalInfoService.syncCollectionCulturalInfo(null, null, p.getCompanyId());
                });
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.info("ProvinceCulturalInfoSchedule syncAllCollectionCulturalBaseInfo Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("ProvinceCulturalInfoSchedule syncAllCollectionCulturalBaseInfo end.......");
    }
}
