package com.das.museum.service.province.schedule;

import com.das.museum.service.province.ProvinceAuthService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
@Async
public class ProvinceAuthSchedule {

    @Resource
    private ProvinceAuthService provinceAuthService;

    @Resource
    private RedissonClient redissonClient;

    //@Scheduled(cron = "0/30 * * * * ?")
    public void getSessionKey() {
        log.info("ProvinceAuthSchedule getSessionKey start.......");
        RLock rLock = redissonClient.getLock("getSessionKey");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                this.provinceAuthService.getSessionKey();
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("ProvinceAuthSchedule getSessionKey Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("ProvinceAuthSchedule getSessionKey end.......");
    }

}
