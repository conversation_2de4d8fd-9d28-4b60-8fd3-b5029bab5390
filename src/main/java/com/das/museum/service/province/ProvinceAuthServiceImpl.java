package com.das.museum.service.province;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.das.museum.common.utils.Md5DigesterUtil;
import com.das.museum.dependency.province.auth.ProvinceAuthFacade;
import com.das.museum.dependency.province.auth.bo.GetSessionKeyBO;
import com.das.museum.infr.dataobject.SyncProvinceMuseumAuthDO;
import com.das.museum.infr.mapper.SyncProvinceMuseumAuthMapper;
import com.das.museum.service.province.bo.SyncProvinceMuseumAuthBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/17
 */
@Slf4j
@Service
public class ProvinceAuthServiceImpl implements ProvinceAuthService {

    @Value("${aes.session.key}")
    private String sessionKey;

    @Resource
    private SyncProvinceMuseumAuthMapper syncProvinceMuseumAuthMapper;

    @Resource
    private ProvinceAuthFacade provinceAuthFacade;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ProvinceCollectionService provinceCollectionService;

    @Resource
    private ProvinceCulturalService provinceCulturalService;

    @Resource
    private ProvinceTicketOrderService provinceTicketOrderService;

    @Resource
    private ProvinceUserService provinceUserService;

    @Resource
    private ProvinceCulturalInfoService provinceCulturalInfoService;

    @Resource
    private ProvinceSafeService provinceSafeService;

    private static final String PROVINCE_AUTH_KEY = "PROVINCE_AUTH_KEY_";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getSessionKey() {
        List<SyncProvinceMuseumAuthDO> provinceMuseumAuthList = this.syncProvinceMuseumAuthMapper.selectByAll();
        if (CollectionUtils.isEmpty(provinceMuseumAuthList)) {
            return;
        }
        provinceMuseumAuthList.forEach(provinceMuseumAuth -> {
            if (StringUtils.isNotBlank(provinceMuseumAuth.getKey())) {
                return;
            }
            log.info("ProvinceAuthServiceImpl getSessionKey start......");
            Long clientPreMaster = RandomUtil.randomLong(10000, 100000);
            GetSessionKeyBO getSessionKeyBO = new GetSessionKeyBO();
            getSessionKeyBO.setMuseumBaseId(provinceMuseumAuth.getMuseumBaseId());
            getSessionKeyBO.setPreMaster(clientPreMaster);
            String serverPreMasterStr = this.provinceAuthFacade.getSessionKey(getSessionKeyBO, null, provinceMuseumAuth.getProvinceUrl());
            if (StringUtils.isBlank(serverPreMasterStr)) {
                return;
            }
            long serverPreMaster = Long.parseLong(serverPreMasterStr);
            if (serverPreMaster == -1L) {
                log.info("ProvinceAuthServiceImpl getSessionKey province not exist museum info MuseumId: [{}]", provinceMuseumAuth.getMuseumBaseId());
                log.info("ProvinceAuthServiceImpl getSessionKey end......");
                return;
            }
            String content = clientPreMaster + String.valueOf(serverPreMaster) + provinceMuseumAuth.getUniqueCode();
            // SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, sessionKey.getBytes());
            String encryptHexKey = Md5DigesterUtil.digestHex(content, sessionKey);
            // aes.encryptHex(content);
            SyncProvinceMuseumAuthDO updateProvinceMuseumAuth = new SyncProvinceMuseumAuthDO();
            updateProvinceMuseumAuth.setMuseumBaseId(provinceMuseumAuth.getMuseumBaseId());
            updateProvinceMuseumAuth.setKey(encryptHexKey);
            this.syncProvinceMuseumAuthMapper.updateByMuseumBaseId(updateProvinceMuseumAuth);
            Long companyId = provinceMuseumAuth.getCompanyId();
            try {
                SyncProvinceMuseumAuthBO syncProvinceMuseumAuthBO = new SyncProvinceMuseumAuthBO();
                syncProvinceMuseumAuthBO.setMuseumBaseId(provinceMuseumAuth.getMuseumBaseId());
                syncProvinceMuseumAuthBO.setUniqueCode(provinceMuseumAuth.getUniqueCode());
                syncProvinceMuseumAuthBO.setProvinceUrl(provinceMuseumAuth.getProvinceUrl());
                syncProvinceMuseumAuthBO.setKey(encryptHexKey);

                this.stringRedisTemplate.opsForValue().set(PROVINCE_AUTH_KEY + companyId, JSON.toJSONString(syncProvinceMuseumAuthBO));
            } catch (Exception e) {
                log.error("ProvinceAuthServiceImpl getSessionKey error: ", e);
            }
            log.info("ProvinceAuthServiceImpl getSessionKey end......");
//            try {
//                log.info("ProvinceAuthServiceImpl syncAllCollectionLoginInfo start......");
//                this.provinceCollectionService.syncCollectionLoginInfo(companyId);
//                log.info("ProvinceAuthServiceImpl syncAllCollectionLoginInfo end......");
//            } catch (Exception e) {
//                log.error("ProvinceAuthServiceImpl syncAllCollectionLoginInfo Exception:", e);
//            }
            // 省平台已不再统计文物在线访问人数，2024-6-17 不再推送数据到省平台，如果打开需要解决省平台缓存数据量大导致的堆内存溢出问题
//            try {
//                log.info("ProvinceAuthServiceImpl syncAllCollectionCulturalInfo start......");
//                this.provinceCulturalService.syncCollectionCulturalInfo(null, null, companyId);
//                log.info("ProvinceAuthServiceImpl syncAllCollectionCulturalInfo end......");
//            } catch (Exception e) {
//                log.error("ProvinceAuthServiceImpl syncAllCollectionCulturalInfo Exception:", e);
//            }
//            try {
//                log.info("ProvinceAuthServiceImpl syncAllTicketOrderInfo start......");
//                this.provinceTicketOrderService.syncTicketOrderInfo(null, null, companyId);
//                log.info("ProvinceAuthServiceImpl syncAllTicketOrderInfo end......");
//            } catch (Exception e) {
//                log.error("ProvinceAuthServiceImpl syncAllTicketOrderInfo Exception:", e);
//            }
//            try {
//                log.info("ProvinceAuthServiceImpl syncAllUserInfo start......");
//                this.provinceUserService.syncUserInfo(companyId);
//                log.info("ProvinceAuthServiceImpl syncAllUserInfo end......");
//            } catch (Exception e) {
//                log.error("ProvinceAuthServiceImpl syncAllUserInfo Exception: ", e);
//            }
//            try {
//                log.info("ProvinceAuthServiceImpl syncAllCollectionCulturalBaseInfo start......");
//                this.provinceCulturalInfoService.syncCollectionCulturalInfo(null, null, companyId);
//                log.info("ProvinceAuthServiceImpl syncAllCollectionCulturalBaseInfo end......");
//            } catch (Exception e) {
//                log.error("ProvinceAuthServiceImpl syncAllCollectionCulturalBaseInfo Exception: ", e);
//            }

//            try {
//                log.info("ProvinceAuthServiceImpl syncDeviceInfo start......");
//                this.provinceSafeService.syncDeviceInfo(companyId);
//                log.info("ProvinceAuthServiceImpl syncDeviceInfo end......");
//            } catch (Exception e) {
//                log.error("ProvinceAuthServiceImpl syncDeviceInfo Exception: ", e);
//            }
//            try {
//                log.info("ProvinceAuthServiceImpl syncDeviceRecord start......");
//                this.provinceSafeService.syncDeviceRecord(companyId);
//                log.info("ProvinceAuthServiceImpl syncDeviceRecord end......");
//            } catch (Exception e) {
//                log.error("ProvinceAuthServiceImpl syncDeviceRecord Exception: ", e);
//            }
        });
    }

    @Override
    public SyncProvinceMuseumAuthBO getMuseumAuth(Long companyId) {
//        try {
//            String authValue = this.stringRedisTemplate.opsForValue().get(PROVINCE_AUTH_KEY + companyId);
//            if (StringUtils.isNotBlank(authValue)) {
//                return JSON.parseObject(authValue, SyncProvinceMuseumAuthBO.class);
//            }
//        } catch (Exception e) {
//            log.error("ProvinceAuthServiceImpl getMuseumAuth redis error: ", e);
//        }
        SyncProvinceMuseumAuthDO provinceMuseumAuth = this.syncProvinceMuseumAuthMapper.selectByCompanyId(companyId);
        if (Objects.isNull(provinceMuseumAuth) || StringUtils.isBlank(provinceMuseumAuth.getKey())) {
            return null;
        }
        SyncProvinceMuseumAuthBO syncProvinceMuseumAuthBO = new SyncProvinceMuseumAuthBO();
        syncProvinceMuseumAuthBO.setMuseumBaseId(provinceMuseumAuth.getMuseumBaseId());
        syncProvinceMuseumAuthBO.setUniqueCode(provinceMuseumAuth.getUniqueCode());
        syncProvinceMuseumAuthBO.setProvinceUrl(provinceMuseumAuth.getProvinceUrl());
        syncProvinceMuseumAuthBO.setKey(provinceMuseumAuth.getKey());
        try {
            this.stringRedisTemplate.opsForValue().set(PROVINCE_AUTH_KEY  + companyId, JSON.toJSONString(syncProvinceMuseumAuthBO));
        } catch (Exception e) {
            log.error("ProvinceAuthServiceImpl getMuseumAuth redis error: ", e);
        }
        return syncProvinceMuseumAuthBO;
    }
}
