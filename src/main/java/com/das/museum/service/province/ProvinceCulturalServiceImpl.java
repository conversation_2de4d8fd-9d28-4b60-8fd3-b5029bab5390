package com.das.museum.service.province;

import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.dependency.province.constant.SendDataBO;
import com.das.museum.dependency.province.cultural.ProvinceCulturalFacade;
import com.das.museum.dependency.province.cultural.bo.SyncCulturalRelicsBO;
import com.das.museum.infr.dataobject.SwzsCulturalRelicsAccessDO;
import com.das.museum.infr.dataobject.SwzsCulturalRelicsDO;
import com.das.museum.infr.mapper.SwzsCulturalRelicsAccessMapper;
import com.das.museum.infr.mapper.SwzsCulturalRelicsLikeMapper;
import com.das.museum.infr.mapper.SwzsCulturalRelicsMapper;
import com.das.museum.service.province.bo.SyncProvinceMuseumAuthBO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
public class ProvinceCulturalServiceImpl implements ProvinceCulturalService{

    @Resource
    private SwzsCulturalRelicsMapper swzsCulturalRelicsMapper;

    @Resource
    private SwzsCulturalRelicsLikeMapper swzsCulturalRelicsLikeMapper;

    @Resource
    private SwzsCulturalRelicsAccessMapper swzsCulturalRelicsAccessMapper;

    @Resource
    private ProvinceCulturalFacade provinceCulturalFacade;

    @Resource
    private ProvinceAuthService provinceAuthService;

    @Override
    public void syncCollectionCulturalInfo(Date start, Date end, Long companyId) {
        SyncProvinceMuseumAuthBO authServiceMuseumAuth = provinceAuthService.getMuseumAuth(companyId);
        if (Objects.isNull(authServiceMuseumAuth)) {
            return;
        }
        List<SwzsCulturalRelicsDO> culturalRelicsDOList = this.swzsCulturalRelicsMapper.listAll(companyId, null, null);
        if (CollectionUtils.isEmpty(culturalRelicsDOList)) {
            SyncCulturalRelicsBO syncCulturalRelicsBO = new SyncCulturalRelicsBO();
            syncCulturalRelicsBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
            syncCulturalRelicsBO.setNumber("-1000L");
            this.provinceCulturalFacade.syncCulturalInfo(new SendDataBO("CL", Lists.newArrayList(syncCulturalRelicsBO)),
                    authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
            return;
        }
        List<Long> ids = culturalRelicsDOList.stream().map(SwzsCulturalRelicsDO::getId).collect(Collectors.toList());
        List<SwzsCulturalRelicsAccessDO> relicsLikeDOList = this.swzsCulturalRelicsAccessMapper.listByIds(ids, start, end);
        Map<String, DataBO> dataMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(relicsLikeDOList)) {
            relicsLikeDOList.forEach(k -> {
                String dateKey = DateUtils.formatDateYYYMMDD(k.getGmtCreate()) + " 00:00:00" + "_" + k.getCulturalRelicsId();
                DataBO dataBO;
                if (dataMap.containsKey(dateKey)) {
                    dataBO = dataMap.get(dateKey);
                } else {
                    dataBO = new DataBO();
                    dataBO.setCulturalRelicsId(k.getCulturalRelicsId());
                    dataBO.setHolographicAccessCount(0L);
                    dataBO.setOverallAccessCount(0L);
                    dataBO.setDataDate(DateUtils.parseDateYYYMMDDHHmmss(DateUtils.formatDateYYYMMDD(k.getGmtCreate()) + " 00:00:00"));
                    dataMap.put(dateKey, dataBO);
                }
                if (k.getType() == 1) {
                    dataBO.setHolographicAccessCount(dataBO.getHolographicAccessCount() + k.getAccessCount());
                } else {
                    dataBO.setOverallAccessCount(dataBO.getOverallAccessCount() + k.getAccessCount());
                }
            });
            Map<String, SwzsCulturalRelicsDO> relicsMap = Maps.newHashMap();
            culturalRelicsDOList.forEach(k -> relicsMap.put(String.valueOf(k.getId()), k));
            List<SyncCulturalRelicsBO> relicsBOList = Lists.newArrayList();
            dataMap.forEach((k, v) -> {
                if (relicsMap.containsKey(v.getCulturalRelicsId())) {
                    SwzsCulturalRelicsDO culturalRelicsDO = relicsMap.get(v.getCulturalRelicsId());
                    SyncCulturalRelicsBO relicsBO = BeanCopyUtils.copyByJSON(culturalRelicsDO, SyncCulturalRelicsBO.class);
                    relicsBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
                    relicsBO.setHolographicAccessCount(v.getHolographicAccessCount());
                    relicsBO.setOverallAccessCount(v.getOverallAccessCount());
                    relicsBO.setStatisticsDate(v.getDataDate());
                    relicsBOList.add(relicsBO);
                }
            });
            this.provinceCulturalFacade.syncCulturalInfo(new SendDataBO("CL", relicsBOList),
                    authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
        } else {
            List<SyncCulturalRelicsBO> relicsBOList = culturalRelicsDOList.stream().map(k -> {
                SyncCulturalRelicsBO relicsBO = BeanCopyUtils.copyByJSON(k, SyncCulturalRelicsBO.class);
                relicsBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
                relicsBO.setHolographicAccessCount(0L);
                relicsBO.setOverallAccessCount(0L);
                relicsBO.setStatisticsDate(DateUtils.parseDateYYYMMDDHHmmss(DateUtils.formatDateYYYMMDD(new Date()) + " 00:00:00"));
                return relicsBO;
            }).collect(Collectors.toList());
            this.provinceCulturalFacade.syncCulturalInfo(new SendDataBO("CL", relicsBOList),
                    authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
        }

    }

    @Data
    private static class DataBO {

        private String culturalRelicsId;

        private Date dataDate;

        /**
         * 全息访问数
         */
        private Long holographicAccessCount;

        /**
         * 全景访问数
         */
        private Long overallAccessCount;
    }
}
