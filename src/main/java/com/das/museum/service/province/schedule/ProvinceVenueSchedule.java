package com.das.museum.service.province.schedule;

import com.das.museum.infr.dataobject.SyncProvinceMuseumAuthDO;
import com.das.museum.infr.mapper.SyncProvinceMuseumAuthMapper;
import com.das.museum.service.province.ProvinceVenueService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ProvinceVenueSchedule {

    @Resource
    private ProvinceVenueService provinceVenueService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SyncProvinceMuseumAuthMapper syncProvinceMuseumAuthMapper;


    /**
     * 每天凌晨0点07分同步场馆信息
     */
    //@Scheduled(cron = "0 07 0 * * ?")
    public void syncVenueInfo() {
        log.info("ProvinceVenueSchedule syncVenueInfo start.......");
        RLock rLock = redissonClient.getLock("syncVenueInfo");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                List<SyncProvinceMuseumAuthDO> provinceMuseumAuthList = this.syncProvinceMuseumAuthMapper.selectByAll();
                if (CollectionUtils.isEmpty(provinceMuseumAuthList)) {
                    return;
                }
                provinceMuseumAuthList.forEach(p -> {
                    if (StringUtils.isBlank(p.getKey())) {
                        return;
                    }
                    this.provinceVenueService.syncVenueInfo(p.getCompanyId());
                });
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("ProvinceVenueSchedule syncVenueInfo Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("ProvinceVenueSchedule syncVenueInfo end.......");
    }

}
