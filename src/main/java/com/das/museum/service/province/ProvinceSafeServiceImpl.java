package com.das.museum.service.province;

import com.das.museum.common.utils.BeanCopyUtils;
import com.das.museum.common.utils.DateUtils;
import com.das.museum.dependency.province.constant.SendDataBO;
import com.das.museum.dependency.province.safe.bo.SyncSafeTemperatureHumidityDeviceBO;
import com.das.museum.dependency.province.safe.bo.SyncSafeTemperatureHumidityRecordBO;
import com.das.museum.dependency.province.user.ProvinceUserFacade;
import com.das.museum.infr.dataobject.SafeTemperatureHumidityDeviceDO;
import com.das.museum.infr.dataobject.SafeTemperatureHumidityRecordDO;
import com.das.museum.infr.mapper.SafeTemperatureHumidityDeviceMapper;
import com.das.museum.infr.mapper.SafeTemperatureHumidityRecordMapper;
import com.das.museum.service.province.bo.SyncProvinceMuseumAuthBO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/7/14
 */
@Slf4j
@Component
public class ProvinceSafeServiceImpl implements ProvinceSafeService {

    @Resource
    private SafeTemperatureHumidityDeviceMapper safeTemperatureHumidityDeviceMapper;

    @Resource
    private SafeTemperatureHumidityRecordMapper safeTemperatureHumidityRecordMapper;

    @Resource
    private ProvinceAuthService provinceAuthService;

    @Resource
    private ProvinceUserFacade provinceUserFacade;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final Long MAX_VALUE = 3 * 60 * 60 * 1000L + 60 * 1000L;

    @Override
    public void syncDeviceInfo(Long companyId) {
        SyncProvinceMuseumAuthBO authServiceMuseumAuth = this.provinceAuthService.getMuseumAuth(companyId);
        if (Objects.isNull(authServiceMuseumAuth)) {
            return;
        }
        List<SafeTemperatureHumidityDeviceDO> deviceDOList = this.safeTemperatureHumidityDeviceMapper.listByCompanyId(companyId);
        if (CollectionUtils.isEmpty(deviceDOList)) {
            SyncSafeTemperatureHumidityDeviceBO deviceBO = new SyncSafeTemperatureHumidityDeviceBO();
            deviceBO.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
            deviceBO.setDeviceCode("-1000L");
            this.provinceUserFacade.syncUserInfo(new SendDataBO("DE", Lists.newArrayList(deviceBO)),
                    authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
            return;
        }
        List<SyncSafeTemperatureHumidityDeviceBO> result = BeanCopyUtils.copyArrayByJSON(deviceDOList, SyncSafeTemperatureHumidityDeviceBO.class);
        result.forEach(k -> k.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId()));
        this.provinceUserFacade.syncUserInfo(new SendDataBO("DE", result),
                authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
    }

    @Override
    public void syncDeviceRecord(Long companyId) {
        SyncProvinceMuseumAuthBO authServiceMuseumAuth = this.provinceAuthService.getMuseumAuth(companyId);
        if (Objects.isNull(authServiceMuseumAuth)) {
            return;
        }
        List<SafeTemperatureHumidityDeviceDO> deviceDOList = this.safeTemperatureHumidityDeviceMapper.listByCompanyId(companyId);
        if (CollectionUtils.isEmpty(deviceDOList)) {
            return;
        }
        List<SafeTemperatureHumidityRecordDO> dataList = this.safeTemperatureHumidityRecordMapper.listSyncData(companyId, (byte)0);
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        List<SyncSafeTemperatureHumidityRecordBO> result = BeanCopyUtils.copyArrayByJSON(dataList, SyncSafeTemperatureHumidityRecordBO.class);
        List<Long> ids = Lists.newArrayList();
        result.forEach(k -> {
            k.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
            ids.add(k.getId());
        });
        Integer httpCode = this.provinceUserFacade.syncUserInfo(new SendDataBO("DER", result),
                authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
        if (HttpStatus.SC_OK != httpCode) {
            return;
        }
        this.safeTemperatureHumidityRecordMapper.updateByIds(companyId, ids, (byte)1);
    }

    @Override
    public void syncOfflineDeviceRecord(Long companyId) {
        SyncProvinceMuseumAuthBO authServiceMuseumAuth = this.provinceAuthService.getMuseumAuth(companyId);
        if (Objects.isNull(authServiceMuseumAuth)) {
            return;
        }
        List<SafeTemperatureHumidityDeviceDO> deviceDOList = this.safeTemperatureHumidityDeviceMapper.listByCompanyId(companyId);
        if (CollectionUtils.isEmpty(deviceDOList)) {
            return;
        }
        Date nowDate = new Date();
        List<SyncSafeTemperatureHumidityRecordBO> result = Lists.newArrayList();
        deviceDOList.forEach(k -> {
            SafeTemperatureHumidityRecordDO isSyncData = this.safeTemperatureHumidityRecordMapper.selectSyncDataOne(k.getDeviceCode());
            if (Objects.isNull(isSyncData))
                return;
            String value = stringRedisTemplate.opsForValue().get(isSyncData.getDeviceCode());
            if (StringUtils.isNotBlank(value)) {
                Date oldDate = DateUtils.parseDateYYYMMDDHHmmss(value);
                if (nowDate.getTime() - oldDate.getTime() <= MAX_VALUE) {
                    log.info("syncOfflineDeviceRecord is old sync data: [{}]", isSyncData);
                    return;
                }
            }
            if (Objects.nonNull(isSyncData.getReportTime()) && nowDate.getTime() - isSyncData.getReportTime().getTime() > MAX_VALUE) {
                SyncSafeTemperatureHumidityRecordBO offline = new SyncSafeTemperatureHumidityRecordBO();
                offline.setCurrentHumidity("-");
                offline.setCurrentTemperature("-");
                offline.setDeviceCode(k.getDeviceCode());
                offline.setReportTime(nowDate);
                offline.setMuseumBaseId(authServiceMuseumAuth.getMuseumBaseId());
                result.add(offline);
                stringRedisTemplate.opsForValue().set(isSyncData.getDeviceCode(), DateUtils.formatDateYYYMMDDHHmmss(nowDate));
            }
        });
        log.info("syncOfflineDeviceRecord data: [{}]", result);
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        this.provinceUserFacade.syncUserInfo(new SendDataBO("DER", result),
                authServiceMuseumAuth.getKey(), authServiceMuseumAuth.getProvinceUrl(), authServiceMuseumAuth.getMuseumBaseId());
    }
}
