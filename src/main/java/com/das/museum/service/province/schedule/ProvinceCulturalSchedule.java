package com.das.museum.service.province.schedule;

import com.das.museum.common.utils.DateUtils;
import com.das.museum.infr.dataobject.SyncProvinceMuseumAuthDO;
import com.das.museum.infr.mapper.SyncProvinceMuseumAuthMapper;
import com.das.museum.service.province.ProvinceCulturalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
public class ProvinceCulturalSchedule {

    @Resource
    private ProvinceCulturalService provinceCulturalService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SyncProvinceMuseumAuthMapper syncProvinceMuseumAuthMapper;

    // 省平台已不再统计文物在线访问人数，2024-6-17 不再推送数据到省平台，如果打开需要解决省平台缓存数据量大导致的堆内存溢出问题
    //@Scheduled(cron = "0 15 0 * * ?")
    public void syncAllCollectionCulturalInfo() {
        log.info("ProvinceCulturalSchedule syncAllCollectionCulturalInfo start.......");
        RLock rLock = redissonClient.getLock("syncCollectionCulturalInfo");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                List<SyncProvinceMuseumAuthDO> provinceMuseumAuthList = this.syncProvinceMuseumAuthMapper.selectByAll();
                if (CollectionUtils.isEmpty(provinceMuseumAuthList)) {
                    return;
                }
                provinceMuseumAuthList.forEach(p -> {
                    if (StringUtils.isBlank(p.getKey())) {
                        return;
                    }
                    this.provinceCulturalService.syncCollectionCulturalInfo(null, null, p.getCompanyId());
                });
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.info("ProvinceCulturalSchedule syncAllCollectionCulturalInfo Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("ProvinceCulturalSchedule syncAllCollectionCulturalInfo end.......");
    }

    // 省平台已不再统计文物在线访问人数，2024-6-17 不再推送数据到省平台，如果打开需要解决省平台缓存数据量大导致的堆内存溢出问题
    //@Scheduled(cron = "0 0/5 * * * ?")
    public void syncCollectionCulturalInfo() {
        log.info("ProvinceCulturalSchedule syncCollectionCulturalInfo start.......");
        RLock rLock = redissonClient.getLock("syncCollectionCulturalInfo");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                List<SyncProvinceMuseumAuthDO> provinceMuseumAuthList = this.syncProvinceMuseumAuthMapper.selectByAll();
                if (CollectionUtils.isEmpty(provinceMuseumAuthList)) {
                    return;
                }
                provinceMuseumAuthList.forEach(p -> {
                    if (StringUtils.isBlank(p.getKey())) {
                        return;
                    }
                    this.provinceCulturalService.syncCollectionCulturalInfo(DateUtils.getTimesDayMorning(), new Date(), p.getCompanyId());
                });
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.info("ProvinceCulturalSchedule syncCollectionCulturalInfo Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("ProvinceCulturalSchedule syncCollectionCulturalInfo end.......");
    }
}
