# mysql config
spring.datasource.druid.name=digital_museum
spring.datasource.druid.url=***********************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.druid.username=root
spring.datasource.druid.password=Das2023root.com
spring.datasource.druid.initial-size=5
spring.datasource.druid.maximum-pool-size=100
spring.datasource.druid.max-wait=60000
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-active=20
spring.datasource.druid.validation-query=SELECT 1
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-while-return=true
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictableIdle-time-millis=300000
spring.datasource.druid.connection-init-sqls=set names utf8mb4;
spring.datasource.druid.filters=stat,slf4j
# reids config
spring.redis.database=0
spring.redis.host=**********
spring.redis.port=6379
spring.redis.password=Das2023redis.com
spring.redis.timeout=1000
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=5000
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
logging.file.path=/opt/saas/logs/${spring.application.name}
logging.level.root=error
logging.level.sql=error
#file url
file.base.url=/opt/saas/file/${spring.application.name}
#minio
#外网访问的minio基础路径
minio.visitUrl=https://www.diaolongyun.com
minio.endpoint=http://**********:9000
minio.bucketName=museum
minio.accessKey=IPg75qNb7gwJCozS
minio.secretKey=708o8aO5j3CPlkyZoSeuX4tlRg5ztjBd
minio.delDirectory=delete
# multipart
spring.servlet.multipart.max-file-size=-1
spring.servlet.multipart.max-request-size=-1

# 配置 Sa-Token 单独使用的 Redis 连接
sa-token.alone-redis.database=1
sa-token.alone-redis.host=**********
sa-token.alone-redis.port=6379
sa-token.alone-redis.password=Das2023redis.com
sa-token.alone-redis.timeout=10s
sa-token.alone-redis.lettuce.pool.max-active=8
sa-token.alone-redis.lettuce.pool.max-wait=5000
sa-token.alone-redis.lettuce.pool.max-idle=8
sa-token.alone-redis.lettuce.pool.min-idle=0

md5.salt=das123456
aes.passkey=das123456
md5.digester.salt=123456
data.aes.key=1661540033456377856

# ticket http
ticket.http.protocol=http
ticket.http.ipAddress=localhost:7107
ticket.http.timeout=10000

# 三维模型虚拟地址
ftp.uploadConf.ftpUrl=/wbResource/model/
# 三维模型真实地址
ftp.uploadConf.ftpPath=/opt/saas/threeModel/

# 系统所属博物馆名称配置(配置unicode编码格式)
system.belong=\u957f\u767d\u5c71\u6ee1\u65cf\u6587\u5316\u535a\u7269\u9986
# 本省的身份证开头配置
system.location.adCode=220
# 静态资源访问路径
system.static.resource.url=https://www.diaolongyun.com

# 导出凭证word模板存放路径
exportWord.inFilePath=/opt/saas/template/
# 导出凭证word生成后临时存放路径
exportWord.outFilePath=/opt/saas/outFile/

# 温湿度上报间隔(默认3小时，单位分钟，上报允许2分钟延迟)
temperature.time.interval=182


# 伪满皇宫闸机数据获取地址
weiman.ticket.url=https://index.wmhg.com.cn/api/get_id_card_list

# 全景地址配置
quanxi.url=https://quanxi.diaolongyun.com?uniqueCode=

# 精创温湿度设备参数
jc.url=https://www.i-elitech.net
jc.keyId=06838413
jc.keySecret=BNSFgRRITiTBpGGiXmcm
jc.userName=diaolongyun
jc.password=diaolong6688*