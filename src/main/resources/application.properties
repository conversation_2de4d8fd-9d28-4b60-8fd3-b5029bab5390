spring.profiles.active=prod
spring.application.name=digital-museum
server.port=7106
spring.main.allow-circular-references:true
server.jetty.acceptors=2
##########mybatis##########
mybatis.config-location=classpath:mybatis/config/mybatis-config.xml
mybatis.mapper-locations=classpath:mybatis/mapper/*.xml
mybatis.type-aliases-package=com.das.museum.infr.dataobject
mybatis.configuration.log-impl=org.apache.ibatis.logging.slf4j.Slf4jImpl
mybatis.configuration.cache-enabled=false
spring.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver
# page config
pagehelper.helper-dialect=mysql
pagehelper.reasonable=false
pagehelper.support-methods-arguments=true
pagehelper.params=count=countSql
output.ansi.enabled=detect
# spring session
spring.session.store-type=redis

# Sa-Token 配置
sa-token.token-name=satoken
sa-token.timeout=2592000
sa-token.activity-timeout=-1
sa-token.is-concurrent=false
sa-token.is-share=true
sa-token.token-style=uuid
sa-token.is-log=true

#雪花算法参数1终端ID
snowflake.workerId=1
#雪花算法参数2数据中心ID
snowflake.datacenterId=1

# 云端地球自动建模访问地址
autobuild.url=https://earth.daspatial.com/desktop/uploadFor3rd?param={}&appId={}
autobuild.appid=APPIDiqlr46erw1h
autobuild.accessKey=f1934gei8734gnyyw6p3x8ro5u2sfb9j

# holidays
# 2022
2022.holiday.newYearStart=2022-01-01 00:00:00
2022.holiday.newYearEnd=2022-01-03 23:59:59
2022.holiday.chineseNewYearStart=2022-01-31 00:00:00
2022.holiday.chineseNewYearEnd=2022-02-06 23:59:59
2022.holiday.chingMingStart=2022-04-03 00:00:00
2022.holiday.chingMingEnd=2022-04-05 23:59:59
2022.holiday.laborStart=2022-04-30 00:00:00
2022.holiday.laborEnd=2022-05-04 23:59:59
2022.holiday.dragonBoatStart=2022-06-03 00:00:00
2022.holiday.dragonBoatEnd=2022-06-05 23:59:59
2022.holiday.midAutumnStart=2022-09-10 00:00:00
2022.holiday.midAutumnEnd=2022-09-12 23:59:59
2022.holiday.nationalStart=2022-10-01 00:00:00
2022.holiday.nationalEnd=2022-10-07 23:59:59
# 2023
2023.holiday.newYearStart=2022-12-31 00:00:00
2023.holiday.newYearEnd=2023-01-02 23:59:59
2023.holiday.chineseNewYearStart=2023-01-21 00:00:00
2023.holiday.chineseNewYearEnd=2023-01-27 23:59:59
2023.holiday.chingMingStart=2023-04-05 00:00:00
2023.holiday.chingMingEnd=2023-04-05 23:59:59
2023.holiday.laborStart=2023-04-29 00:00:00
2023.holiday.laborEnd=2023-05-03 23:59:59
2023.holiday.dragonBoatStart=2023-06-22 00:00:00
2023.holiday.dragonBoatEnd=2023-06-24 23:59:59
2023.holiday.nationalPlusStart=2023-09-29 00:00:00
2023.holiday.nationalPlusEnd=2023-10-06 23:59:59

aes.session.key=1234567890123456