<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionRegisterManageInfoMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionRegisterManageInfoDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="register_info_id" jdbcType="BIGINT" property="registerInfoId"/>
        <result column="register_no" jdbcType="VARCHAR" property="registerNo"/>
        <result column="collect_certificate_no" jdbcType="VARCHAR" property="collectCertificateNo"/>
        <result column="collect_date" jdbcType="TIMESTAMP" property="collectDate"/>
        <result column="identify_certificate_no" jdbcType="VARCHAR" property="identifyCertificateNo"/>
        <result column="identify_date" jdbcType="TIMESTAMP" property="identifyDate"/>
        <result column="in_museum_certificate_no" jdbcType="VARCHAR" property="inMuseumCertificateNo"/>
        <result column="in_museum_date" jdbcType="TIMESTAMP" property="inMuseumDate"/>
        <result column="in_tibetan_certificate_no" jdbcType="VARCHAR" property="inTibetanCertificateNo"/>
        <result column="in_tibetan_date" jdbcType="TIMESTAMP" property="inTibetanDate"/>
        <result column="appraiser_name" jdbcType="VARCHAR" property="appraiserName"/>
        <result column="ordinal" jdbcType="VARCHAR" property="ordinal"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="cupboard_id" jdbcType="BIGINT" property="cupboardId"/>
        <result column="floor_id" jdbcType="BIGINT" property="floorId"/>
        <result column="drawer_id" jdbcType="BIGINT" property="drawerId"/>
        <result column="column_id" jdbcType="BIGINT" property="columnId"/>
        <result column="number_id" jdbcType="BIGINT" property="numberId"/>
        <result column="package_id" jdbcType="BIGINT" property="packageId"/>
        <result column="package_size" jdbcType="VARCHAR" property="packageSize"/>
        <result column="is_box" jdbcType="TINYINT" property="isBox"/>
        <result column="is_doubtful" jdbcType="TINYINT" property="isDoubtful"/>
        <result column="is_foreign" jdbcType="TINYINT" property="isForeign"/>
        <result column="is_booking" jdbcType="TINYINT" property="isBooking"/>
        <result column="booking_info" jdbcType="VARCHAR" property="bookingInfo"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, register_info_id, register_no, collect_certificate_no, collect_date, identify_certificate_no,
    identify_date, in_museum_certificate_no, in_museum_date, in_tibetan_certificate_no, 
    in_tibetan_date, appraiser_name, ordinal, warehouse_id, cupboard_id, floor_id, drawer_id, 
    column_id, number_id, package_id, package_size, is_box, is_doubtful, is_foreign, 
    is_booking, booking_info, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_manage_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_register_manage_info
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterManageInfoDO" useGeneratedKeys="true">
    insert into scs_collection_register_manage_info (company_id, register_info_id, register_no, collect_certificate_no,
      collect_date, identify_certificate_no, identify_date, 
      in_museum_certificate_no, in_museum_date, 
      in_tibetan_certificate_no, in_tibetan_date, 
      appraiser_name, ordinal, warehouse_id, 
      cupboard_id, floor_id, drawer_id, 
      column_id, number_id, package_id, 
      package_size, is_box, is_doubtful, 
      is_foreign, is_booking, booking_info, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{registerInfoId,jdbcType=BIGINT}, #{registerNo,jdbcType=VARCHAR}, #{collectCertificateNo,jdbcType=VARCHAR},
      #{collectDate,jdbcType=TIMESTAMP}, #{identifyCertificateNo,jdbcType=VARCHAR}, #{identifyDate,jdbcType=TIMESTAMP}, 
      #{inMuseumCertificateNo,jdbcType=VARCHAR}, #{inMuseumDate,jdbcType=TIMESTAMP}, 
      #{inTibetanCertificateNo,jdbcType=VARCHAR}, #{inTibetanDate,jdbcType=TIMESTAMP}, 
      #{appraiserName,jdbcType=VARCHAR}, #{ordinal,jdbcType=VARCHAR}, #{warehouseId,jdbcType=BIGINT}, 
      #{cupboardId,jdbcType=BIGINT}, #{floorId,jdbcType=BIGINT}, #{drawerId,jdbcType=BIGINT}, 
      #{columnId,jdbcType=BIGINT}, #{numberId,jdbcType=BIGINT}, #{packageId,jdbcType=BIGINT}, 
      #{packageSize,jdbcType=VARCHAR}, #{isBox,jdbcType=TINYINT}, #{isDoubtful,jdbcType=TINYINT}, 
      #{isForeign,jdbcType=TINYINT}, #{isBooking,jdbcType=TINYINT}, #{bookingInfo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterManageInfoDO" useGeneratedKeys="true">
        insert into scs_collection_register_manage_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="registerInfoId != null">
                register_info_id,
            </if>
            <if test="registerNo != null">
                register_no,
            </if>
            <if test="collectCertificateNo != null">
                collect_certificate_no,
            </if>
            <if test="collectDate != null">
                collect_date,
            </if>
            <if test="identifyCertificateNo != null">
                identify_certificate_no,
            </if>
            <if test="identifyDate != null">
                identify_date,
            </if>
            <if test="inMuseumCertificateNo != null">
                in_museum_certificate_no,
            </if>
            <if test="inMuseumDate != null">
                in_museum_date,
            </if>
            <if test="inTibetanCertificateNo != null">
                in_tibetan_certificate_no,
            </if>
            <if test="inTibetanDate != null">
                in_tibetan_date,
            </if>
            <if test="appraiserName != null">
                appraiser_name,
            </if>
            <if test="ordinal != null">
                ordinal,
            </if>
            <if test="warehouseId != null">
                warehouse_id,
            </if>
            <if test="cupboardId != null">
                cupboard_id,
            </if>
            <if test="floorId != null">
                floor_id,
            </if>
            <if test="drawerId != null">
                drawer_id,
            </if>
            <if test="columnId != null">
                column_id,
            </if>
            <if test="numberId != null">
                number_id,
            </if>
            <if test="packageId != null">
                package_id,
            </if>
            <if test="packageSize != null">
                package_size,
            </if>
            <if test="isBox != null">
                is_box,
            </if>
            <if test="isDoubtful != null">
                is_doubtful,
            </if>
            <if test="isForeign != null">
                is_foreign,
            </if>
            <if test="isBooking != null">
                is_booking,
            </if>
            <if test="bookingInfo != null">
                booking_info,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="registerInfoId != null">
                #{registerInfoId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="collectCertificateNo != null">
                #{collectCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="collectDate != null">
                #{collectDate,jdbcType=TIMESTAMP},
            </if>
            <if test="identifyCertificateNo != null">
                #{identifyCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="identifyDate != null">
                #{identifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="inMuseumCertificateNo != null">
                #{inMuseumCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="inMuseumDate != null">
                #{inMuseumDate,jdbcType=TIMESTAMP},
            </if>
            <if test="inTibetanCertificateNo != null">
                #{inTibetanCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="inTibetanDate != null">
                #{inTibetanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="appraiserName != null">
                #{appraiserName,jdbcType=VARCHAR},
            </if>
            <if test="ordinal != null">
                #{ordinal,jdbcType=VARCHAR},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=BIGINT},
            </if>
            <if test="cupboardId != null">
                #{cupboardId,jdbcType=BIGINT},
            </if>
            <if test="floorId != null">
                #{floorId,jdbcType=BIGINT},
            </if>
            <if test="drawerId != null">
                #{drawerId,jdbcType=BIGINT},
            </if>
            <if test="columnId != null">
                #{columnId,jdbcType=BIGINT},
            </if>
            <if test="numberId != null">
                #{numberId,jdbcType=BIGINT},
            </if>
            <if test="packageId != null">
                #{packageId,jdbcType=BIGINT},
            </if>
            <if test="packageSize != null">
                #{packageSize,jdbcType=VARCHAR},
            </if>
            <if test="isBox != null">
                #{isBox,jdbcType=TINYINT},
            </if>
            <if test="isDoubtful != null">
                #{isDoubtful,jdbcType=TINYINT},
            </if>
            <if test="isForeign != null">
                #{isForeign,jdbcType=TINYINT},
            </if>
            <if test="isBooking != null">
                #{isBooking,jdbcType=TINYINT},
            </if>
            <if test="bookingInfo != null">
                #{bookingInfo,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterManageInfoDO">
        update scs_collection_register_manage_info
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="registerInfoId != null">
                register_info_id = #{registerInfoId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                register_no = #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="collectCertificateNo != null">
                collect_certificate_no = #{collectCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="collectDate != null">
                collect_date = #{collectDate,jdbcType=TIMESTAMP},
            </if>
            <if test="identifyCertificateNo != null">
                identify_certificate_no = #{identifyCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="identifyDate != null">
                identify_date = #{identifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="inMuseumCertificateNo != null">
                in_museum_certificate_no = #{inMuseumCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="inMuseumDate != null">
                in_museum_date = #{inMuseumDate,jdbcType=TIMESTAMP},
            </if>
            <if test="inTibetanCertificateNo != null">
                in_tibetan_certificate_no = #{inTibetanCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="inTibetanDate != null">
                in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="appraiserName != null">
                appraiser_name = #{appraiserName,jdbcType=VARCHAR},
            </if>
            <if test="ordinal != null">
                ordinal = #{ordinal,jdbcType=VARCHAR},
            </if>
            <if test="warehouseId != null">
                warehouse_id = #{warehouseId,jdbcType=BIGINT},
            </if>
            <if test="cupboardId != null">
                cupboard_id = #{cupboardId,jdbcType=BIGINT},
            </if>
            <if test="floorId != null">
                floor_id = #{floorId,jdbcType=BIGINT},
            </if>
            <if test="drawerId != null">
                drawer_id = #{drawerId,jdbcType=BIGINT},
            </if>
            <if test="columnId != null">
                column_id = #{columnId,jdbcType=BIGINT},
            </if>
            <if test="numberId != null">
                number_id = #{numberId,jdbcType=BIGINT},
            </if>
            <if test="packageId != null">
                package_id = #{packageId,jdbcType=BIGINT},
            </if>
            <if test="packageSize != null">
                package_size = #{packageSize,jdbcType=VARCHAR},
            </if>
            <if test="isBox != null">
                is_box = #{isBox,jdbcType=TINYINT},
            </if>
            <if test="isDoubtful != null">
                is_doubtful = #{isDoubtful,jdbcType=TINYINT},
            </if>
            <if test="isForeign != null">
                is_foreign = #{isForeign,jdbcType=TINYINT},
            </if>
            <if test="isBooking != null">
                is_booking = #{isBooking,jdbcType=TINYINT},
            </if>
            <if test="bookingInfo != null">
                booking_info = #{bookingInfo,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterManageInfoDO">
    update scs_collection_register_manage_info
    set company_id = #{companyId,jdbcType=BIGINT},
      register_info_id = #{registerInfoId,jdbcType=BIGINT},
      register_no = #{registerNo,jdbcType=VARCHAR},
      collect_certificate_no = #{collectCertificateNo,jdbcType=VARCHAR},
      collect_date = #{collectDate,jdbcType=TIMESTAMP},
      identify_certificate_no = #{identifyCertificateNo,jdbcType=VARCHAR},
      identify_date = #{identifyDate,jdbcType=TIMESTAMP},
      in_museum_certificate_no = #{inMuseumCertificateNo,jdbcType=VARCHAR},
      in_museum_date = #{inMuseumDate,jdbcType=TIMESTAMP},
      in_tibetan_certificate_no = #{inTibetanCertificateNo,jdbcType=VARCHAR},
      in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
      appraiser_name = #{appraiserName,jdbcType=VARCHAR},
      ordinal = #{ordinal,jdbcType=VARCHAR},
      warehouse_id = #{warehouseId,jdbcType=BIGINT},
      cupboard_id = #{cupboardId,jdbcType=BIGINT},
      floor_id = #{floorId,jdbcType=BIGINT},
      drawer_id = #{drawerId,jdbcType=BIGINT},
      column_id = #{columnId,jdbcType=BIGINT},
      number_id = #{numberId,jdbcType=BIGINT},
      package_id = #{packageId,jdbcType=BIGINT},
      package_size = #{packageSize,jdbcType=VARCHAR},
      is_box = #{isBox,jdbcType=TINYINT},
      is_doubtful = #{isDoubtful,jdbcType=TINYINT},
      is_foreign = #{isForeign,jdbcType=TINYINT},
      is_booking = #{isBooking,jdbcType=TINYINT},
      booking_info = #{bookingInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <update id="updateByRegisterInfoId"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterManageInfoDO">
        update scs_collection_register_manage_info
        <set>
            <if test="registerNo != null">
                register_no = #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="collectCertificateNo != null">
                collect_certificate_no = #{collectCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="collectDate != null">
                collect_date = #{collectDate,jdbcType=TIMESTAMP},
            </if>
            <if test="identifyCertificateNo != null">
                identify_certificate_no = #{identifyCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="identifyDate != null">
                identify_date = #{identifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="inMuseumCertificateNo != null">
                in_museum_certificate_no = #{inMuseumCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="inMuseumDate != null">
                in_museum_date = #{inMuseumDate,jdbcType=TIMESTAMP},
            </if>
            <if test="inTibetanCertificateNo != null">
                in_tibetan_certificate_no = #{inTibetanCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="inTibetanDate != null">
                in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="appraiserName != null">
                appraiser_name = #{appraiserName,jdbcType=VARCHAR},
            </if>
            <if test="ordinal != null">
                ordinal = #{ordinal,jdbcType=VARCHAR},
            </if>
            <if test="warehouseId != null">
                warehouse_id = #{warehouseId,jdbcType=BIGINT},
            </if>
            <if test="cupboardId != null">
                cupboard_id = #{cupboardId,jdbcType=BIGINT},
            </if>
            <if test="floorId != null">
                floor_id = #{floorId,jdbcType=BIGINT},
            </if>
            <if test="drawerId != null">
                drawer_id = #{drawerId,jdbcType=BIGINT},
            </if>
            <if test="columnId != null">
                column_id = #{columnId,jdbcType=BIGINT},
            </if>
            <if test="numberId != null">
                number_id = #{numberId,jdbcType=BIGINT},
            </if>
            <if test="packageId != null">
                package_id = #{packageId,jdbcType=BIGINT},
            </if>
            <if test="packageSize != null">
                package_size = #{packageSize,jdbcType=VARCHAR},
            </if>
            <if test="isBox != null">
                is_box = #{isBox,jdbcType=TINYINT},
            </if>
            <if test="isDoubtful != null">
                is_doubtful = #{isDoubtful,jdbcType=TINYINT},
            </if>
            <if test="isForeign != null">
                is_foreign = #{isForeign,jdbcType=TINYINT},
            </if>
            <if test="isBooking != null">
                is_booking = #{isBooking,jdbcType=TINYINT},
            </if>
            <if test="bookingInfo != null">
                booking_info = #{bookingInfo,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where register_info_id = #{registerInfoId,jdbcType=BIGINT}
    </update>

    <update id="updateByRegisterInfoIdAll" parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterManageInfoDO">
        update scs_collection_register_manage_info
        <set>
                register_no = #{registerNo,jdbcType=VARCHAR},
                collect_certificate_no = #{collectCertificateNo,jdbcType=VARCHAR},
                collect_date = #{collectDate,jdbcType=TIMESTAMP},
                identify_certificate_no = #{identifyCertificateNo,jdbcType=VARCHAR},
                identify_date = #{identifyDate,jdbcType=TIMESTAMP},
                in_museum_certificate_no = #{inMuseumCertificateNo,jdbcType=VARCHAR},
                in_museum_date = #{inMuseumDate,jdbcType=TIMESTAMP},
                in_tibetan_certificate_no = #{inTibetanCertificateNo,jdbcType=VARCHAR},
                in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
                appraiser_name = #{appraiserName,jdbcType=VARCHAR},
                ordinal = #{ordinal,jdbcType=VARCHAR},
                warehouse_id = #{warehouseId,jdbcType=BIGINT},
                cupboard_id = #{cupboardId,jdbcType=BIGINT},
                floor_id = #{floorId,jdbcType=BIGINT},
                drawer_id = #{drawerId,jdbcType=BIGINT},
                column_id = #{columnId,jdbcType=BIGINT},
                number_id = #{numberId,jdbcType=BIGINT},
                package_id = #{packageId,jdbcType=BIGINT},
                package_size = #{packageSize,jdbcType=VARCHAR},
                is_box = #{isBox,jdbcType=TINYINT},
                is_doubtful = #{isDoubtful,jdbcType=TINYINT},
                is_foreign = #{isForeign,jdbcType=TINYINT},
                is_booking = #{isBooking,jdbcType=TINYINT},
                booking_info = #{bookingInfo,jdbcType=VARCHAR},
                modifier = #{modifier,jdbcType=BIGINT},
        </set>
        where register_info_id = #{registerInfoId,jdbcType=BIGINT}
    </update>

    <delete id="deleteByRegisterNo">
    delete from scs_collection_register_manage_info
    where register_no = #{registerNo,jdbcType=VARCHAR} and company_id = #{companyId,jdbcType=BIGINT}
    </delete>

    <select id="listByRegisterNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_manage_info
        where company_id = #{companyId,jdbcType=BIGINT}
        <if test="registerNos != null and registerNos.size() > 0 ">
            and register_no in
            <foreach collection="registerNos" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectByRegisterNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_manage_info
        where company_id = #{companyId,jdbcType=BIGINT} and register_no = #{registerNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByRegisterId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_manage_info
        where company_id = #{companyId,jdbcType=BIGINT} and register_info_id = #{registerId,jdbcType=BIGINT}
    </select>

    <select id="listByCompanyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_manage_info
        where company_id = #{companyId,jdbcType=BIGINT}
    </select>
</mapper>