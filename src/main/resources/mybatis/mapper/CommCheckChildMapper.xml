<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.CommCheckChildMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.CommCheckChildDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="main_id" jdbcType="BIGINT" property="mainId"/>
        <result column="flow_name" jdbcType="VARCHAR" property="flowName"/>
        <result column="node_index" jdbcType="INTEGER" property="nodeIndex"/>
        <result column="end_node" jdbcType="INTEGER" property="endNode"/>
        <result column="check_status" jdbcType="VARCHAR" property="checkStatus"/>
        <result column="collection_id" jdbcType="VARCHAR" property="collectionId"/>
        <result column="arrive_time" jdbcType="TIMESTAMP" property="arriveTime"/>
        <result column="need_man_id" jdbcType="VARCHAR" property="needManId"/>
        <result column="need_man_name" jdbcType="VARCHAR" property="needManName"/>
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="check_man_id" jdbcType="BIGINT" property="checkManId"/>
        <result column="check_man_name" jdbcType="VARCHAR" property="checkManName"/>
        <result column="check_opinion" jdbcType="VARCHAR" property="checkOpinion"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, main_id, flow_name, node_index, end_node, check_status, collection_id, arrive_time, need_man_id,
    need_man_name, check_time, check_man_id, check_man_name, check_opinion, creator, 
    modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_child
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_check_child
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommCheckChildDO"
            useGeneratedKeys="true">
    insert into comm_check_child (main_id, flow_name, node_index,
      end_node, check_status, collection_id, arrive_time,
      need_man_id, need_man_name, check_time, 
      check_man_id, check_man_name, check_opinion, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{mainId,jdbcType=BIGINT}, #{flowName,jdbcType=VARCHAR}, #{nodeIndex,jdbcType=INTEGER},
      #{endNode,jdbcType=INTEGER}, #{checkStatus,jdbcType=VARCHAR}, #{collectionId,jdbcType=VARCHAR}, #{arriveTime,jdbcType=TIMESTAMP},
      #{needManId,jdbcType=VARCHAR}, #{needManName,jdbcType=VARCHAR}, #{checkTime,jdbcType=TIMESTAMP},
      #{checkManId,jdbcType=BIGINT}, #{checkManName,jdbcType=VARCHAR}, #{checkOpinion,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.CommCheckChildDO" useGeneratedKeys="true">
        insert into comm_check_child
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mainId != null">
                main_id,
            </if>
            <if test="flowName != null">
                flow_name,
            </if>
            <if test="nodeIndex != null">
                node_index,
            </if>
            <if test="endNode != null">
                end_node,
            </if>
            <if test="checkStatus != null">
                check_status,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="arriveTime != null">
                arrive_time,
            </if>
            <if test="needManId != null">
                need_man_id,
            </if>
            <if test="needManName != null">
                need_man_name,
            </if>
            <if test="checkTime != null">
                check_time,
            </if>
            <if test="checkManId != null">
                check_man_id,
            </if>
            <if test="checkManName != null">
                check_man_name,
            </if>
            <if test="checkOpinion != null">
                check_opinion,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mainId != null">
                #{mainId,jdbcType=BIGINT},
            </if>
            <if test="flowName != null">
                #{flowName,jdbcType=VARCHAR},
            </if>
            <if test="nodeIndex != null">
                #{nodeIndex,jdbcType=INTEGER},
            </if>
            <if test="endNode != null">
                #{endNode,jdbcType=INTEGER},
            </if>
            <if test="checkStatus != null">
                #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=VARCHAR},
            </if>
            <if test="arriveTime != null">
                #{arriveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="needManId != null">
                #{needManId,jdbcType=VARCHAR},
            </if>
            <if test="needManName != null">
                #{needManName,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null">
                #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkManId != null">
                #{checkManId,jdbcType=BIGINT},
            </if>
            <if test="checkManName != null">
                #{checkManName,jdbcType=VARCHAR},
            </if>
            <if test="checkOpinion != null">
                #{checkOpinion,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.CommCheckChildDO">
        update comm_check_child
        <set>
            <if test="mainId != null">
                main_id = #{mainId,jdbcType=BIGINT},
            </if>
            <if test="flowName != null">
                flow_name = #{flowName,jdbcType=VARCHAR},
            </if>
            <if test="nodeIndex != null">
                node_index = #{nodeIndex,jdbcType=INTEGER},
            </if>
            <if test="endNode != null">
                end_node = #{endNode,jdbcType=INTEGER},
            </if>
            <if test="checkStatus != null">
                check_status = #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=VARCHAR},
            </if>
            <if test="arriveTime != null">
                arrive_time = #{arriveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="needManId != null">
                need_man_id = #{needManId,jdbcType=VARCHAR},
            </if>
            <if test="needManName != null">
                need_man_name = #{needManName,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null">
                check_time = #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkManId != null">
                check_man_id = #{checkManId,jdbcType=BIGINT},
            </if>
            <if test="checkManName != null">
                check_man_name = #{checkManName,jdbcType=VARCHAR},
            </if>
            <if test="checkOpinion != null">
                check_opinion = #{checkOpinion,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.CommCheckChildDO">
    update comm_check_child
    set main_id = #{mainId,jdbcType=BIGINT},
      flow_name = #{flowName,jdbcType=VARCHAR},
      node_index = #{nodeIndex,jdbcType=INTEGER},
      end_node = #{endNode,jdbcType=INTEGER},
      check_status = #{checkStatus,jdbcType=VARCHAR},
      collection_id = #{collectionId,jdbcType=VARCHAR},
      arrive_time = #{arriveTime,jdbcType=TIMESTAMP},
      need_man_id = #{needManId,jdbcType=VARCHAR},
      need_man_name = #{needManName,jdbcType=VARCHAR},
      check_time = #{checkTime,jdbcType=TIMESTAMP},
      check_man_id = #{checkManId,jdbcType=BIGINT},
      check_man_name = #{checkManName,jdbcType=VARCHAR},
      check_opinion = #{checkOpinion,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->
    <select id="selectByMainIdAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_child
        where main_id = #{mainId,jdbcType=BIGINT}
        and check_status = #{checkStatus,jdbcType=VARCHAR}
    </select>

    <select id="selectListByMainIdAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_child
        where main_id = #{mainId,jdbcType=BIGINT}
        and check_status = #{checkStatus,jdbcType=VARCHAR}
    </select>


    <select id="selectEndNodeByMainId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_child
        where main_id = #{mainId,jdbcType=BIGINT}
        and end_node = 1
    </select>

    <select id="selectBatchByMainIdAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_child
        where
        check_status = #{checkStatus,jdbcType=VARCHAR}
        <if test="mainIds != null and mainIds.size() > 0">
            and main_id IN
            <foreach collection="mainIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectByMainIdAndNodeIndex" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_child
        where main_id = #{mainId,jdbcType=BIGINT}
        and node_index = #{nodeIndex,jdbcType=INTEGER}
    </select>

    <delete id="deleteByMainId">
        delete from comm_check_child
        where main_id = #{mainId,jdbcType=BIGINT}
    </delete>

    <select id="selectCheckListByMainId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_child
        where main_id = #{mainId,jdbcType=BIGINT}
        order by node_index
    </select>

    <select id="selectListByUserIdAndStatus" resultType="Integer">
        select
        count(*)
        from comm_check_child A
        inner join comm_check_main B on A.main_id = B.id
        where CONCAT (',' ,A.need_man_id , ',') REGEXP concat(',(',#{userId,jdbcType=VARCHAR},'),')
        and A.check_status = #{checkStatus,jdbcType=VARCHAR}
        and B.belong_model = #{belongModel,jdbcType=VARCHAR}
    </select>

    <select id="queryMyCheckList" resultType="com.das.museum.infr.dataobjectexpand.MyCheckListDO">
        select
            B.belong_model belongModel,
            B.model_id modelId,
            A.main_id mainId,
            A.id childId,
            B.start_man_name startManName,
            A.check_status checkStatus,
            B.start_time startTime
        from comm_check_child A
        inner join comm_check_main B on A.main_id = B.id
        where
        CONCAT (',' ,A.need_man_id , ',') REGEXP concat(',(',#{userId,jdbcType=VARCHAR},'),')
        and A.check_status = #{checkStatus,jdbcType=VARCHAR}
        <if test="startManName != null and startManName != '' ">
            and B.start_man_name like CONCAT('%',#{startManName,jdbcType=VARCHAR},'%')
        </if>
        order by B.start_time desc
    </select>

    <select id="queryMyCheckListByModel" resultType="com.das.museum.infr.dataobjectexpand.MyCheckListDO">
        select
        B.belong_model belongModel,
        B.model_id modelId,
        A.main_id mainId,
        A.id childId,
        B.start_man_name startManName,
        A.check_status checkStatus,
        B.start_time startTime
        from comm_check_child A
        inner join comm_check_main B on A.main_id = B.id
        where
        CONCAT (',' ,A.need_man_id , ',') REGEXP concat(',(',#{userId,jdbcType=VARCHAR},'),')
        and A.check_status = #{checkStatus,jdbcType=VARCHAR}
        <if test="belongModel != null and belongModel != '' ">
            and B.belong_model = #{belongModel,jdbcType=VARCHAR}
        </if>
        <if test="orderBy == 0 ">
            order by B.start_time
        </if>
        <if test="orderBy == 1 ">
            order by B.start_time desc
        </if>
    </select>

    <select id="queryMyHasCheckedListByModel" resultType="com.das.museum.infr.dataobjectexpand.MyHasCheckedListDO">
        select
        B.belong_model belongModel,
        B.model_id modelId,
        A.main_id mainId,
        B.start_man_name startManName,
        B.check_status checkStatus,
        B.start_time startTime
        from (select DISTINCT main_id from comm_check_child where check_man_id = #{userId,jdbcType=VARCHAR} ) A
        inner join comm_check_main B on A.main_id = B.id
        where 1 = 1
        <if test="checkStatus != null and checkStatus != '' ">
            and B.check_status = #{checkStatus,jdbcType=VARCHAR}
        </if>
        <if test="belongModels != null and belongModels.size() > 0">
            and B.belong_model in
            <foreach collection="belongModels" open="(" close=")" separator="," item="belongModel" index="index">
                #{belongModel}
            </foreach>
        </if>
        <if test="orderBy == 0 ">
            order by B.start_time
        </if>
        <if test="orderBy == 1 ">
            order by B.start_time desc
        </if>
    </select>

    <select id="queryMyCheckStatisticsList" resultType="com.das.museum.infr.dataobjectexpand.MyCheckStatisticsDO">
        select
        B.belong_model belongModel,
        count(B.belong_model) num
        from comm_check_child A
        inner join comm_check_main B on A.main_id = B.id
        where B.company_id = #{companyId,jdbcType=BIGINT}
        and CONCAT (',' ,A.need_man_id , ',') REGEXP concat(',(',#{userId,jdbcType=VARCHAR},'),')
        and A.check_status = #{checkStatus,jdbcType=VARCHAR}
        <if test="belongModels != null and belongModels.size() > 0">
            and B.belong_model in
            <foreach collection="belongModels" open="(" close=")" separator="," item="belongModel" index="index">
                #{belongModel}
            </foreach>
        </if>
        group by B.belong_model
    </select>

    <select id="queryMyHasCheckedStatisticsList" resultType="com.das.museum.infr.dataobjectexpand.MyCheckStatisticsDO">
        select
            B.belong_model belongModel,
            count(B.belong_model) num
        from (SELECT DISTINCT main_id FROM comm_check_child where check_man_id = #{userId,jdbcType=VARCHAR}) A
        inner join comm_check_main B on A.main_id = B.id
        where B.company_id = #{companyId,jdbcType=BIGINT}
        group by B.belong_model
    </select>
</mapper>