<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.CommWorkflowConfigMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.CommWorkflowConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="classification_id" jdbcType="BIGINT" property="classificationId" />
    <result column="auto_remove" jdbcType="TINYINT" property="autoRemove" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, classification_id, auto_remove, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from comm_workflow_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_workflow_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommWorkflowConfigDO" useGeneratedKeys="true">
    insert into comm_workflow_config (company_id, classification_id, auto_remove, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{classificationId,jdbcType=BIGINT}, #{autoRemove,jdbcType=TINYINT}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommWorkflowConfigDO" useGeneratedKeys="true">
    insert into comm_workflow_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="classificationId != null">
        classification_id,
      </if>
      <if test="autoRemove != null">
        auto_remove,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="classificationId != null">
        #{classificationId,jdbcType=BIGINT},
      </if>
      <if test="autoRemove != null">
        #{autoRemove,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.CommWorkflowConfigDO">
    update comm_workflow_config
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="classificationId != null">
        classification_id = #{classificationId,jdbcType=BIGINT},
      </if>
      <if test="autoRemove != null">
        auto_remove = #{autoRemove,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.CommWorkflowConfigDO">
    update comm_workflow_config
    set company_id = #{companyId,jdbcType=BIGINT},
      classification_id = #{classificationId,jdbcType=BIGINT},
      auto_remove = #{autoRemove,jdbcType=TINYINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCompanyIdAndBizId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from comm_workflow_config
    where company_id = #{companyId,jdbcType=BIGINT}
    and classification_id = #{classificationId,jdbcType=BIGINT}
  </select>

  <select id="selectByCompanyIdAndId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from comm_workflow_config
    where company_id = #{companyId,jdbcType=BIGINT}
    and id = #{id,jdbcType=BIGINT}
  </select>

  <update id="updateByCompanyIdAndId" parameterType="com.das.museum.infr.dataobject.CommWorkflowConfigDO">
    update comm_workflow_config
    <set>
      <if test="autoRemove != null">
        auto_remove = #{autoRemove,jdbcType=TINYINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where company_id = #{companyId,jdbcType=BIGINT}
    and id = #{id,jdbcType=BIGINT}
  </update>

</mapper>