<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.TktTicketWriteOffUserMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.TktTicketWriteOffUserDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="account_status" jdbcType="TINYINT" property="accountStatus" />
    <result column="wechat_bind_status" jdbcType="TINYINT" property="wechatBindStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, user_id, department_id, user_name, real_name, phone, `type`, account_status, 
    wechat_bind_status, remark, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tkt_ticket_write_off_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tkt_ticket_write_off_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketWriteOffUserDO" useGeneratedKeys="true">
    insert into tkt_ticket_write_off_user (company_id, user_id, department_id, 
      user_name, real_name, phone, 
      `type`, account_status, wechat_bind_status, 
      remark, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, 
      #{userName,jdbcType=VARCHAR}, #{realName,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{accountStatus,jdbcType=TINYINT}, #{wechatBindStatus,jdbcType=TINYINT}, 
      #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketWriteOffUserDO" useGeneratedKeys="true">
    insert into tkt_ticket_write_off_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="accountStatus != null">
        account_status,
      </if>
      <if test="wechatBindStatus != null">
        wechat_bind_status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="accountStatus != null">
        #{accountStatus,jdbcType=TINYINT},
      </if>
      <if test="wechatBindStatus != null">
        #{wechatBindStatus,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.TktTicketWriteOffUserDO">
    update tkt_ticket_write_off_user
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        real_name = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="accountStatus != null">
        account_status = #{accountStatus,jdbcType=TINYINT},
      </if>
      <if test="wechatBindStatus != null">
        wechat_bind_status = #{wechatBindStatus,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.TktTicketWriteOffUserDO">
    update tkt_ticket_write_off_user
    set company_id = #{companyId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      user_name = #{userName,jdbcType=VARCHAR},
      real_name = #{realName,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=TINYINT},
      account_status = #{accountStatus,jdbcType=TINYINT},
      wechat_bind_status = #{wechatBindStatus,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_ticket_write_off_user
    where company_id = #{companyId,jdbcType=BIGINT}
    and id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByPhone" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_ticket_write_off_user
    where phone = #{phone,jdbcType=VARCHAR}
  </select>

  <update id="updateByPhone">
    update tkt_ticket_write_off_user
    set wechat_bind_status = #{wechatBindStatus,jdbcType=TINYINT}
    where phone = #{phone,jdbcType=VARCHAR}
  </update>

  <select id="listByCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_ticket_write_off_user
    where company_id = #{companyId,jdbcType=BIGINT}
  </select>

  <select id="listPageByParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_ticket_write_off_user
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="keyword != null and keyword != '' ">
      and ( real_name like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%') or phone like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%') )
    </if>
    order by ${sortBy}
  </select>

  <update id="updateAccountStatusById">
    update tkt_ticket_write_off_user
    set account_status = #{accountStatus,jdbcType=TINYINT},
        modifier = #{modifier,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </update>

  <delete id="deleteByIdAndCompanyId">
    delete from tkt_ticket_write_off_user
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </delete>
</mapper>