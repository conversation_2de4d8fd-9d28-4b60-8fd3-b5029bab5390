<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.TktPersonalOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.TktPersonalOrderDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="personal_order_no" jdbcType="VARCHAR" property="personalOrderNo" />
    <result column="ticket_name" jdbcType="VARCHAR" property="ticketName" />
    <result column="ticket_code" jdbcType="VARCHAR" property="ticketCode" />
    <result column="ticket_price" jdbcType="BIGINT" property="ticketPrice" />
    <result column="tourist_id" jdbcType="BIGINT" property="touristId" />
    <result column="tourist_name" jdbcType="VARCHAR" property="touristName" />
    <result column="tourist_certificate_type" jdbcType="TINYINT" property="touristCertificateType" />
    <result column="tourist_certificate_no" jdbcType="VARCHAR" property="touristCertificateNo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="qr_code_url" jdbcType="VARCHAR" property="qrCodeUrl" />
    <result column="reserve_date" jdbcType="TIMESTAMP" property="reserveDate" />
    <result column="time_period_id" jdbcType="BIGINT" property="timePeriodId" />
    <result column="time_period_start" jdbcType="TIME" property="timePeriodStart" />
    <result column="time_period_end" jdbcType="TIME" property="timePeriodEnd" />
    <result column="write_off_date" jdbcType="TIMESTAMP" property="writeOffDate" />
    <result column="write_off_count" jdbcType="TINYINT" property="writeOffCount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="writeoff_user_name" jdbcType="VARCHAR" property="writeOffUserName"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, department_id, order_id, order_no, personal_order_no, ticket_name, 
    ticket_code, ticket_price, tourist_id, tourist_name, tourist_certificate_type, tourist_certificate_no, 
    `status`, qr_code_url, reserve_date, time_period_id, time_period_start, time_period_end, 
    write_off_date, write_off_count, remark, creator, modifier, gmt_create, gmt_modified, writeoff_user_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tkt_personal_order_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tkt_personal_order_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktPersonalOrderDetailDO" useGeneratedKeys="true">
    insert into tkt_personal_order_detail (company_id, department_id, order_id, 
      order_no, personal_order_no, ticket_name, 
      ticket_code, ticket_price, tourist_id, 
      tourist_name, tourist_certificate_type, tourist_certificate_no, 
      `status`, qr_code_url, reserve_date, 
      time_period_id, time_period_start, time_period_end, 
      write_off_date, write_off_count, remark, 
      creator, modifier, gmt_create, 
      gmt_modified, writeoff_user_name)
    values (#{companyId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, 
      #{orderNo,jdbcType=VARCHAR}, #{personalOrderNo,jdbcType=VARCHAR}, #{ticketName,jdbcType=VARCHAR}, 
      #{ticketCode,jdbcType=VARCHAR}, #{ticketPrice,jdbcType=BIGINT}, #{touristId,jdbcType=BIGINT}, 
      #{touristName,jdbcType=VARCHAR}, #{touristCertificateType,jdbcType=TINYINT}, #{touristCertificateNo,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{qrCodeUrl,jdbcType=VARCHAR}, #{reserveDate,jdbcType=TIMESTAMP}, 
      #{timePeriodId,jdbcType=BIGINT}, #{timePeriodStart,jdbcType=TIME}, #{timePeriodEnd,jdbcType=TIME}, 
      #{writeOffDate,jdbcType=TIMESTAMP}, #{writeOffCount,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{writeOffUserName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktPersonalOrderDetailDO" useGeneratedKeys="true">
    insert into tkt_personal_order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="personalOrderNo != null">
        personal_order_no,
      </if>
      <if test="ticketName != null">
        ticket_name,
      </if>
      <if test="ticketCode != null">
        ticket_code,
      </if>
      <if test="ticketPrice != null">
        ticket_price,
      </if>
      <if test="touristId != null">
        tourist_id,
      </if>
      <if test="touristName != null">
        tourist_name,
      </if>
      <if test="touristCertificateType != null">
        tourist_certificate_type,
      </if>
      <if test="touristCertificateNo != null">
        tourist_certificate_no,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="qrCodeUrl != null">
        qr_code_url,
      </if>
      <if test="reserveDate != null">
        reserve_date,
      </if>
      <if test="timePeriodId != null">
        time_period_id,
      </if>
      <if test="timePeriodStart != null">
        time_period_start,
      </if>
      <if test="timePeriodEnd != null">
        time_period_end,
      </if>
      <if test="writeOffDate != null">
        write_off_date,
      </if>
      <if test="writeOffCount != null">
        write_off_count,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="writeOffUserName != null">
        writeoff_user_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="personalOrderNo != null">
        #{personalOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="ticketName != null">
        #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketPrice != null">
        #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="touristId != null">
        #{touristId,jdbcType=BIGINT},
      </if>
      <if test="touristName != null">
        #{touristName,jdbcType=VARCHAR},
      </if>
      <if test="touristCertificateType != null">
        #{touristCertificateType,jdbcType=TINYINT},
      </if>
      <if test="touristCertificateNo != null">
        #{touristCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="qrCodeUrl != null">
        #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="reserveDate != null">
        #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodId != null">
        #{timePeriodId,jdbcType=BIGINT},
      </if>
      <if test="timePeriodStart != null">
        #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="writeOffDate != null">
        #{writeOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffCount != null">
        #{writeOffCount,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffUserName != null">
        #{writeOffUserName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.TktPersonalOrderDetailDO">
    update tkt_personal_order_detail
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="personalOrderNo != null">
        personal_order_no = #{personalOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="ticketName != null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        ticket_code = #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketPrice != null">
        ticket_price = #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="touristId != null">
        tourist_id = #{touristId,jdbcType=BIGINT},
      </if>
      <if test="touristName != null">
        tourist_name = #{touristName,jdbcType=VARCHAR},
      </if>
      <if test="touristCertificateType != null">
        tourist_certificate_type = #{touristCertificateType,jdbcType=TINYINT},
      </if>
      <if test="touristCertificateNo != null">
        tourist_certificate_no = #{touristCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="qrCodeUrl != null">
        qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="reserveDate != null">
        reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodId != null">
        time_period_id = #{timePeriodId,jdbcType=BIGINT},
      </if>
      <if test="timePeriodStart != null">
        time_period_start = #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        time_period_end = #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="writeOffDate != null">
        write_off_date = #{writeOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffCount != null">
        write_off_count = #{writeOffCount,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffUserName != null">
        writeoff_user_name = #{writeOffUserName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.TktPersonalOrderDetailDO">
    update tkt_personal_order_detail
    set company_id = #{companyId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      personal_order_no = #{personalOrderNo,jdbcType=VARCHAR},
      ticket_name = #{ticketName,jdbcType=VARCHAR},
      ticket_code = #{ticketCode,jdbcType=VARCHAR},
      ticket_price = #{ticketPrice,jdbcType=BIGINT},
      tourist_id = #{touristId,jdbcType=BIGINT},
      tourist_name = #{touristName,jdbcType=VARCHAR},
      tourist_certificate_type = #{touristCertificateType,jdbcType=TINYINT},
      tourist_certificate_no = #{touristCertificateNo,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      time_period_id = #{timePeriodId,jdbcType=BIGINT},
      time_period_start = #{timePeriodStart,jdbcType=TIME},
      time_period_end = #{timePeriodEnd,jdbcType=TIME},
      write_off_date = #{writeOffDate,jdbcType=TIMESTAMP},
      write_off_count = #{writeOffCount,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      writeoff_user_name = #{writeOffUserName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectListByOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_personal_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    and `status` = 2
    and order_id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="selectListByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_personal_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    and order_id = #{orderId,jdbcType=BIGINT}
  </select>

  <select id="selectListByTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_personal_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="startTime != null ">
      and reserve_date >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null ">
      and reserve_date <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="selectHasWriteOffList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_personal_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    and `status` = 2
    <if test="startTime != null ">
      and write_off_date >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null ">
      and write_off_date <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
    </if>
  </select>

  <update id="updateByPersonalOrderNo" parameterType="com.das.museum.infr.dataobject.TktPersonalOrderDetailDO">
    update tkt_personal_order_detail
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="ticketName != null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        ticket_code = #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketPrice != null">
        ticket_price = #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="touristId != null">
        tourist_id = #{touristId,jdbcType=BIGINT},
      </if>
      <if test="touristName != null">
        tourist_name = #{touristName,jdbcType=VARCHAR},
      </if>
      <if test="touristCertificateType != null">
        tourist_certificate_type = #{touristCertificateType,jdbcType=TINYINT},
      </if>
      <if test="touristCertificateNo != null">
        tourist_certificate_no = #{touristCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="qrCodeUrl != null">
        qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="reserveDate != null">
        reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodId != null">
        time_period_id = #{timePeriodId,jdbcType=BIGINT},
      </if>
      <if test="timePeriodStart != null">
        time_period_start = #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        time_period_end = #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="writeOffDate != null">
        write_off_date = #{writeOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffCount != null">
        write_off_count = #{writeOffCount,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffUserName != null">
        writeoff_user_name = #{writeOffUserName,jdbcType=VARCHAR}
      </if>
    </set>
    where personal_order_no = #{personalOrderNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByOrderNo" parameterType="com.das.museum.infr.dataobject.TktPersonalOrderDetailDO">
    update tkt_personal_order_detail
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="personalOrderNo != null">
        personal_order_no = #{personalOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="ticketName != null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        ticket_code = #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketPrice != null">
        ticket_price = #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="touristId != null">
        tourist_id = #{touristId,jdbcType=BIGINT},
      </if>
      <if test="touristName != null">
        tourist_name = #{touristName,jdbcType=VARCHAR},
      </if>
      <if test="touristCertificateType != null">
        tourist_certificate_type = #{touristCertificateType,jdbcType=TINYINT},
      </if>
      <if test="touristCertificateNo != null">
        tourist_certificate_no = #{touristCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="qrCodeUrl != null">
        qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="reserveDate != null">
        reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodId != null">
        time_period_id = #{timePeriodId,jdbcType=BIGINT},
      </if>
      <if test="timePeriodStart != null">
        time_period_start = #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        time_period_end = #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="writeOffDate != null">
        write_off_date = #{writeOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffCount != null">
        write_off_count = #{writeOffCount,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffUserName != null">
        writeoff_user_name = #{writeOffUserName,jdbcType=VARCHAR},
      </if>
    </set>
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByOrderNoAndPersonNo" parameterType="com.das.museum.infr.dataobject.TktPersonalOrderDetailDO">
    update tkt_personal_order_detail
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="ticketName != null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        ticket_code = #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketPrice != null">
        ticket_price = #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="touristId != null">
        tourist_id = #{touristId,jdbcType=BIGINT},
      </if>
      <if test="touristName != null">
        tourist_name = #{touristName,jdbcType=VARCHAR},
      </if>
      <if test="touristCertificateType != null">
        tourist_certificate_type = #{touristCertificateType,jdbcType=TINYINT},
      </if>
      <if test="touristCertificateNo != null">
        tourist_certificate_no = #{touristCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="qrCodeUrl != null">
        qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="reserveDate != null">
        reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodId != null">
        time_period_id = #{timePeriodId,jdbcType=BIGINT},
      </if>
      <if test="timePeriodStart != null">
        time_period_start = #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        time_period_end = #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="writeOffDate != null">
        write_off_date = #{writeOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffCount != null">
        write_off_count = #{writeOffCount,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffUserName != null">
        writeoff_user_name = #{writeOffUserName,jdbcType=VARCHAR}
      </if>
    </set>
    where order_no = #{orderNo,jdbcType=VARCHAR} and personal_order_no = #{personalOrderNo,jdbcType=VARCHAR}
  </update>
  <select id="selectByPersonalOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_personal_order_detail
    where personal_order_no = #{personalOrderNo,jdbcType=VARCHAR}
  </select>

  <select id="listPersonalByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_personal_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="keyword != null and keyword != ''">
      and (tourist_name like CONCAT('%', #{keyword,jdbcType=VARCHAR},'%') or
           tourist_certificate_no like CONCAT('%', #{keyword,jdbcType=VARCHAR},'%') or
           tourist_name like CONCAT('%', #{keyword,jdbcType=VARCHAR},'%'))
    </if>
    <if test="status != null">
      and `status` = #{status,jdbcType=TINYINT}
    </if>
    <if test="createTimeStart != null and createTimeEnd != null">
      and gmt_create &gt;= #{createTimeStart,jdbcType=TIMESTAMP} and gmt_create &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
    </if>
    order by ${sortBy}
  </select>

  <select id="statisticsOnNumList" resultType="com.das.museum.infr.dataobjectexpand.OrderStatisticsByNumDO">
    select
    left(write_off_date,#{dataType,jdbcType=INTEGER}) dataTime,
    count(personal_order_no) manNum
    from tkt_personal_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    and status = 2
    <if test="startTime != null and startTime !='' ">
      and write_off_date >= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="endTime != null and endTime !='' ">
      and write_off_date <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
    </if>
    group by left(write_off_date,#{dataType,jdbcType=INTEGER})
  </select>

  <select id="listByReserveDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_personal_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="status != null">
      and `status` = #{status,jdbcType=TINYINT}
    </if>
    <if test="reserveDateStart != null and reserveDateEnd != null">
      and reserve_date &gt;= #{reserveDateStart,jdbcType=TIMESTAMP} and reserve_date &lt;= #{reserveDateEnd,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="listGroupByProvince" resultType="com.das.museum.infr.dataobjectexpand.ListGroupByProvinceDO">
    select
        left(tourist_certificate_no,3) provinceCode,
        count(tourist_certificate_no) visitorNum
    from tkt_personal_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="status != null">
      and `status` = #{status,jdbcType=TINYINT}
    </if>
    <if test="reserveDateStart != null ">
      and reserve_date &gt;= #{reserveDateStart,jdbcType=TIMESTAMP}
    </if>
    <if test="reserveDateEnd != null ">
      and reserve_date &lt;= #{reserveDateEnd,jdbcType=TIMESTAMP}
    </if>
    group by left(tourist_certificate_no,3)
  </select>

  <update id="updateByPersonalOrderNos">
    update tkt_personal_order_detail set `status` = #{status,jdbcType=TINYINT}
    where personal_order_no in
    <foreach collection="personalOrderNos" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </update>

  <select id="listByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_personal_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    and status = 2
    <if test="start != null ">
      and write_off_date &gt;= #{start,jdbcType=TIMESTAMP}
    </if>
    <if test="end != null ">
      and write_off_date &lt;= #{end,jdbcType=TIMESTAMP}
    </if>
  </select>
</mapper>