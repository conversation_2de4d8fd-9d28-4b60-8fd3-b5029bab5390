<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.AccCompanyMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.AccCompanyDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="company_level" jdbcType="TINYINT" property="companyLevel"/>
        <result column="try_type" jdbcType="TINYINT" property="tryType"/>
        <result column="try_deadline_date" jdbcType="TIMESTAMP" property="tryDeadlineDate"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , unique_code, company_name, contact_name, contact_phone, company_level, try_type,
    try_deadline_date, address, remark, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_company
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from acc_company
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.AccCompanyDO"
            useGeneratedKeys="true">
        insert into acc_company (unique_code, company_name, contact_name,
                                 contact_phone, company_level, try_type,
                                 try_deadline_date, address, remark,
                                 gmt_create, gmt_modified)
        values (#{uniqueCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR},
                #{contactPhone,jdbcType=VARCHAR}, #{companyLevel,jdbcType=TINYINT}, #{tryType,jdbcType=TINYINT},
                #{tryDeadlineDate,jdbcType=TIMESTAMP}, #{address,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.AccCompanyDO" useGeneratedKeys="true">
        insert into acc_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uniqueCode != null">
                unique_code,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="contactName != null">
                contact_name,
            </if>
            <if test="contactPhone != null">
                contact_phone,
            </if>
            <if test="companyLevel != null">
                company_level,
            </if>
            <if test="tryType != null">
                try_type,
            </if>
            <if test="tryDeadlineDate != null">
                try_deadline_date,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uniqueCode != null">
                #{uniqueCode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null">
                #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null">
                #{contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="companyLevel != null">
                #{companyLevel,jdbcType=TINYINT},
            </if>
            <if test="tryType != null">
                #{tryType,jdbcType=TINYINT},
            </if>
            <if test="tryDeadlineDate != null">
                #{tryDeadlineDate,jdbcType=TIMESTAMP},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.AccCompanyDO">
        update acc_company
        <set>
            <if test="uniqueCode != null">
                unique_code = #{uniqueCode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null">
                contact_name = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null">
                contact_phone = #{contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="companyLevel != null">
                company_level = #{companyLevel,jdbcType=TINYINT},
            </if>
            <if test="tryType != null">
                try_type = #{tryType,jdbcType=TINYINT},
            </if>
            <if test="tryDeadlineDate != null">
                try_deadline_date = #{tryDeadlineDate,jdbcType=TIMESTAMP},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.AccCompanyDO">
        update acc_company
        set unique_code       = #{uniqueCode,jdbcType=VARCHAR},
            company_name      = #{companyName,jdbcType=VARCHAR},
            contact_name      = #{contactName,jdbcType=VARCHAR},
            contact_phone     = #{contactPhone,jdbcType=VARCHAR},
            company_level     = #{companyLevel,jdbcType=TINYINT},
            try_type          = #{tryType,jdbcType=TINYINT},
            try_deadline_date = #{tryDeadlineDate,jdbcType=TIMESTAMP},
            address           = #{address,jdbcType=VARCHAR},
            remark            = #{remark,jdbcType=VARCHAR},
            gmt_create        = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified      = #{gmtModified,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <select id="selectByUniqueCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_company
        where unique_code = #{uniqueCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByContactPhone" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_company
        where contact_phone = #{contactPhone,jdbcType=VARCHAR}
    </select>
</mapper>