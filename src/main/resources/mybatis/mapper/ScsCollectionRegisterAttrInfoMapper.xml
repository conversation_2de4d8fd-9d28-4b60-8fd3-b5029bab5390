<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionRegisterAttrInfoMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionRegisterAttrInfoDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="register_info_id" jdbcType="BIGINT" property="registerInfoId"/>
        <result column="register_no" jdbcType="VARCHAR" property="registerNo"/>
        <result column="length" jdbcType="BIGINT" property="length"/>
        <result column="width" jdbcType="BIGINT" property="width"/>
        <result column="height" jdbcType="BIGINT" property="height"/>
        <result column="presence" jdbcType="BIGINT" property="presence"/>
        <result column="caliber" jdbcType="BIGINT" property="caliber"/>
        <result column="bottom_diameter" jdbcType="BIGINT" property="bottomDiameter"/>
        <result column="size" jdbcType="VARCHAR" property="size"/>
        <result column="quality_range" jdbcType="BIGINT" property="qualityRange"/>
        <result column="quality_unit" jdbcType="BIGINT" property="qualityUnit"/>
        <result column="specific_quality" jdbcType="BIGINT" property="specificQuality"/>
        <result column="actual_count" jdbcType="INTEGER" property="actualCount"/>
        <result column="count_unit" jdbcType="BIGINT" property="countUnit"/>
        <result column="collection_count" jdbcType="INTEGER" property="collectionCount"/>
        <result column="count_remark" jdbcType="VARCHAR" property="countRemark"/>
        <result column="save_status" jdbcType="BIGINT" property="saveStatus"/>
        <result column="complete_degree" jdbcType="BIGINT" property="completeDegree"/>
        <result column="complete_info" jdbcType="VARCHAR" property="completeInfo"/>
        <result column="texture_category" jdbcType="BIGINT" property="textureCategory"/>
        <result column="texture" jdbcType="VARCHAR" property="texture"/>
        <result column="texture_remark" jdbcType="VARCHAR" property="textureRemark"/>
        <result column="source" jdbcType="BIGINT" property="source"/>
        <result column="specific_source" jdbcType="VARCHAR" property="specificSource"/>
        <result column="excavated_location" jdbcType="VARCHAR" property="excavatedLocation"/>
        <result column="excavated_date" jdbcType="TIMESTAMP" property="excavatedDate"/>
        <result column="holder" jdbcType="VARCHAR" property="holder"/>
        <result column="pay_voucher_no" jdbcType="VARCHAR" property="payVoucherNo"/>
        <result column="amount" jdbcType="BIGINT" property="amount"/>
        <result column="collection_remark" jdbcType="VARCHAR" property="collectionRemark"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, register_info_id, register_no, `length`, width, height, presence, caliber, bottom_diameter,
    `size`, quality_range, quality_unit, specific_quality, actual_count, count_unit, 
    collection_count, count_remark, save_status, complete_degree, complete_info, texture_category, 
    texture, texture_remark, `source`, specific_source, excavated_location, excavated_date, 
    holder, pay_voucher_no, amount, collection_remark, creator, modifier, gmt_create, 
    gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_attr_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_register_attr_info
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterAttrInfoDO" useGeneratedKeys="true">
    insert into scs_collection_register_attr_info (company_id, register_info_id, register_no, `length`,
      width, height, presence, 
      caliber, bottom_diameter, `size`, 
      quality_range, quality_unit, specific_quality, 
      actual_count, count_unit, collection_count, 
      count_remark, save_status, complete_degree, 
      complete_info, texture_category, texture, 
      texture_remark, `source`, specific_source, 
      excavated_location, excavated_date, holder, 
      pay_voucher_no, amount, collection_remark, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{registerInfoId,jdbcType=BIGINT}, #{registerNo,jdbcType=VARCHAR}, #{length,jdbcType=BIGINT},
      #{width,jdbcType=BIGINT}, #{height,jdbcType=BIGINT}, #{presence,jdbcType=BIGINT}, 
      #{caliber,jdbcType=BIGINT}, #{bottomDiameter,jdbcType=BIGINT}, #{size,jdbcType=VARCHAR}, 
      #{qualityRange,jdbcType=BIGINT}, #{qualityUnit,jdbcType=BIGINT}, #{specificQuality,jdbcType=BIGINT}, 
      #{actualCount,jdbcType=INTEGER}, #{countUnit,jdbcType=BIGINT}, #{collectionCount,jdbcType=INTEGER}, 
      #{countRemark,jdbcType=VARCHAR}, #{saveStatus,jdbcType=BIGINT}, #{completeDegree,jdbcType=BIGINT}, 
      #{completeInfo,jdbcType=VARCHAR}, #{textureCategory,jdbcType=BIGINT}, #{texture,jdbcType=VARCHAR}, 
      #{textureRemark,jdbcType=VARCHAR}, #{source,jdbcType=BIGINT}, #{specificSource,jdbcType=VARCHAR}, 
      #{excavatedLocation,jdbcType=VARCHAR}, #{excavatedDate,jdbcType=TIMESTAMP}, #{holder,jdbcType=VARCHAR}, 
      #{payVoucherNo,jdbcType=VARCHAR}, #{amount,jdbcType=BIGINT}, #{collectionRemark,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterAttrInfoDO" useGeneratedKeys="true">
        insert into scs_collection_register_attr_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="registerInfoId != null">
                register_info_id,
            </if>
            <if test="registerNo != null">
                register_no,
            </if>
            <if test="length != null">
                `length`,
            </if>
            <if test="width != null">
                width,
            </if>
            <if test="height != null">
                height,
            </if>
            <if test="presence != null">
                presence,
            </if>
            <if test="caliber != null">
                caliber,
            </if>
            <if test="bottomDiameter != null">
                bottom_diameter,
            </if>
            <if test="size != null">
                `size`,
            </if>
            <if test="qualityRange != null">
                quality_range,
            </if>
            <if test="qualityUnit != null">
                quality_unit,
            </if>
            <if test="specificQuality != null">
                specific_quality,
            </if>
            <if test="actualCount != null">
                actual_count,
            </if>
            <if test="countUnit != null">
                count_unit,
            </if>
            <if test="collectionCount != null">
                collection_count,
            </if>
            <if test="countRemark != null">
                count_remark,
            </if>
            <if test="saveStatus != null">
                save_status,
            </if>
            <if test="completeDegree != null">
                complete_degree,
            </if>
            <if test="completeInfo != null">
                complete_info,
            </if>
            <if test="textureCategory != null">
                texture_category,
            </if>
            <if test="texture != null">
                texture,
            </if>
            <if test="textureRemark != null">
                texture_remark,
            </if>
            <if test="source != null">
                `source`,
            </if>
            <if test="specificSource != null">
                specific_source,
            </if>
            <if test="excavatedLocation != null">
                excavated_location,
            </if>
            <if test="excavatedDate != null">
                excavated_date,
            </if>
            <if test="holder != null">
                holder,
            </if>
            <if test="payVoucherNo != null">
                pay_voucher_no,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="collectionRemark != null">
                collection_remark,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="registerInfoId != null">
                #{registerInfoId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="length != null">
                #{length,jdbcType=BIGINT},
            </if>
            <if test="width != null">
                #{width,jdbcType=BIGINT},
            </if>
            <if test="height != null">
                #{height,jdbcType=BIGINT},
            </if>
            <if test="presence != null">
                #{presence,jdbcType=BIGINT},
            </if>
            <if test="caliber != null">
                #{caliber,jdbcType=BIGINT},
            </if>
            <if test="bottomDiameter != null">
                #{bottomDiameter,jdbcType=BIGINT},
            </if>
            <if test="size != null">
                #{size,jdbcType=VARCHAR},
            </if>
            <if test="qualityRange != null">
                #{qualityRange,jdbcType=BIGINT},
            </if>
            <if test="qualityUnit != null">
                #{qualityUnit,jdbcType=BIGINT},
            </if>
            <if test="specificQuality != null">
                #{specificQuality,jdbcType=BIGINT},
            </if>
            <if test="actualCount != null">
                #{actualCount,jdbcType=INTEGER},
            </if>
            <if test="countUnit != null">
                #{countUnit,jdbcType=BIGINT},
            </if>
            <if test="collectionCount != null">
                #{collectionCount,jdbcType=INTEGER},
            </if>
            <if test="countRemark != null">
                #{countRemark,jdbcType=VARCHAR},
            </if>
            <if test="saveStatus != null">
                #{saveStatus,jdbcType=BIGINT},
            </if>
            <if test="completeDegree != null">
                #{completeDegree,jdbcType=BIGINT},
            </if>
            <if test="completeInfo != null">
                #{completeInfo,jdbcType=VARCHAR},
            </if>
            <if test="textureCategory != null">
                #{textureCategory,jdbcType=BIGINT},
            </if>
            <if test="texture != null">
                #{texture,jdbcType=VARCHAR},
            </if>
            <if test="textureRemark != null">
                #{textureRemark,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=BIGINT},
            </if>
            <if test="specificSource != null">
                #{specificSource,jdbcType=VARCHAR},
            </if>
            <if test="excavatedLocation != null">
                #{excavatedLocation,jdbcType=VARCHAR},
            </if>
            <if test="excavatedDate != null">
                #{excavatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="holder != null">
                #{holder,jdbcType=VARCHAR},
            </if>
            <if test="payVoucherNo != null">
                #{payVoucherNo,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=BIGINT},
            </if>
            <if test="collectionRemark != null">
                #{collectionRemark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterAttrInfoDO">
        update scs_collection_register_attr_info
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="registerInfoId != null">
                register_info_id = #{registerInfoId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                register_no = #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="length != null">
                `length` = #{length,jdbcType=BIGINT},
            </if>
            <if test="width != null">
                width = #{width,jdbcType=BIGINT},
            </if>
            <if test="height != null">
                height = #{height,jdbcType=BIGINT},
            </if>
            <if test="presence != null">
                presence = #{presence,jdbcType=BIGINT},
            </if>
            <if test="caliber != null">
                caliber = #{caliber,jdbcType=BIGINT},
            </if>
            <if test="bottomDiameter != null">
                bottom_diameter = #{bottomDiameter,jdbcType=BIGINT},
            </if>
            <if test="size != null">
                `size` = #{size,jdbcType=VARCHAR},
            </if>
            <if test="qualityRange != null">
                quality_range = #{qualityRange,jdbcType=BIGINT},
            </if>
            <if test="qualityUnit != null">
                quality_unit = #{qualityUnit,jdbcType=BIGINT},
            </if>
            <if test="specificQuality != null">
                specific_quality = #{specificQuality,jdbcType=BIGINT},
            </if>
            <if test="actualCount != null">
                actual_count = #{actualCount,jdbcType=INTEGER},
            </if>
            <if test="countUnit != null">
                count_unit = #{countUnit,jdbcType=BIGINT},
            </if>
            <if test="collectionCount != null">
                collection_count = #{collectionCount,jdbcType=INTEGER},
            </if>
            <if test="countRemark != null">
                count_remark = #{countRemark,jdbcType=VARCHAR},
            </if>
            <if test="saveStatus != null">
                save_status = #{saveStatus,jdbcType=BIGINT},
            </if>
            <if test="completeDegree != null">
                complete_degree = #{completeDegree,jdbcType=BIGINT},
            </if>
            <if test="completeInfo != null">
                complete_info = #{completeInfo,jdbcType=VARCHAR},
            </if>
            <if test="textureCategory != null">
                texture_category = #{textureCategory,jdbcType=BIGINT},
            </if>
            <if test="texture != null">
                texture = #{texture,jdbcType=VARCHAR},
            </if>
            <if test="textureRemark != null">
                texture_remark = #{textureRemark,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                `source` = #{source,jdbcType=BIGINT},
            </if>
            <if test="specificSource != null">
                specific_source = #{specificSource,jdbcType=VARCHAR},
            </if>
            <if test="excavatedLocation != null">
                excavated_location = #{excavatedLocation,jdbcType=VARCHAR},
            </if>
            <if test="excavatedDate != null">
                excavated_date = #{excavatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="holder != null">
                holder = #{holder,jdbcType=VARCHAR},
            </if>
            <if test="payVoucherNo != null">
                pay_voucher_no = #{payVoucherNo,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=BIGINT},
            </if>
            <if test="collectionRemark != null">
                collection_remark = #{collectionRemark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterAttrInfoDO">
    update scs_collection_register_attr_info
    set company_id = #{companyId,jdbcType=BIGINT},
      register_info_id = #{registerInfoId,jdbcType=BIGINT},
      register_no = #{registerNo,jdbcType=VARCHAR},
      `length` = #{length,jdbcType=BIGINT},
      width = #{width,jdbcType=BIGINT},
      height = #{height,jdbcType=BIGINT},
      presence = #{presence,jdbcType=BIGINT},
      caliber = #{caliber,jdbcType=BIGINT},
      bottom_diameter = #{bottomDiameter,jdbcType=BIGINT},
      `size` = #{size,jdbcType=VARCHAR},
      quality_range = #{qualityRange,jdbcType=BIGINT},
      quality_unit = #{qualityUnit,jdbcType=BIGINT},
      specific_quality = #{specificQuality,jdbcType=BIGINT},
      actual_count = #{actualCount,jdbcType=INTEGER},
      count_unit = #{countUnit,jdbcType=BIGINT},
      collection_count = #{collectionCount,jdbcType=INTEGER},
      count_remark = #{countRemark,jdbcType=VARCHAR},
      save_status = #{saveStatus,jdbcType=BIGINT},
      complete_degree = #{completeDegree,jdbcType=BIGINT},
      complete_info = #{completeInfo,jdbcType=VARCHAR},
      texture_category = #{textureCategory,jdbcType=BIGINT},
      texture = #{texture,jdbcType=VARCHAR},
      texture_remark = #{textureRemark,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=BIGINT},
      specific_source = #{specificSource,jdbcType=VARCHAR},
      excavated_location = #{excavatedLocation,jdbcType=VARCHAR},
      excavated_date = #{excavatedDate,jdbcType=TIMESTAMP},
      holder = #{holder,jdbcType=VARCHAR},
      pay_voucher_no = #{payVoucherNo,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=BIGINT},
      collection_remark = #{collectionRemark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <update id="updateByRegisterInfoId"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterAttrInfoDO">
        update scs_collection_register_attr_info
        <set>
            <if test="registerNo != null">
                register_no = #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="length != null">
                `length` = #{length,jdbcType=BIGINT},
            </if>
            <if test="width != null">
                width = #{width,jdbcType=BIGINT},
            </if>
            <if test="height != null">
                height = #{height,jdbcType=BIGINT},
            </if>
            <if test="presence != null">
                presence = #{presence,jdbcType=BIGINT},
            </if>
            <if test="caliber != null">
                caliber = #{caliber,jdbcType=BIGINT},
            </if>
            <if test="bottomDiameter != null">
                bottom_diameter = #{bottomDiameter,jdbcType=BIGINT},
            </if>
            <if test="size != null">
                `size` = #{size,jdbcType=VARCHAR},
            </if>
            <if test="qualityRange != null">
                quality_range = #{qualityRange,jdbcType=BIGINT},
            </if>
            <if test="qualityUnit != null">
                quality_unit = #{qualityUnit,jdbcType=BIGINT},
            </if>
            <if test="specificQuality != null">
                specific_quality = #{specificQuality,jdbcType=BIGINT},
            </if>
            <if test="actualCount != null">
                actual_count = #{actualCount,jdbcType=INTEGER},
            </if>
            <if test="countUnit != null">
                count_unit = #{countUnit,jdbcType=BIGINT},
            </if>
            <if test="collectionCount != null">
                collection_count = #{collectionCount,jdbcType=INTEGER},
            </if>
            <if test="countRemark != null">
                count_remark = #{countRemark,jdbcType=VARCHAR},
            </if>
            <if test="saveStatus != null">
                save_status = #{saveStatus,jdbcType=BIGINT},
            </if>
            <if test="completeDegree != null">
                complete_degree = #{completeDegree,jdbcType=BIGINT},
            </if>
            <if test="completeInfo != null">
                complete_info = #{completeInfo,jdbcType=VARCHAR},
            </if>
            <if test="textureCategory != null">
                texture_category = #{textureCategory,jdbcType=BIGINT},
            </if>
            <if test="texture != null">
                texture = #{texture,jdbcType=VARCHAR},
            </if>
            <if test="textureRemark != null">
                texture_remark = #{textureRemark,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                `source` = #{source,jdbcType=BIGINT},
            </if>
            <if test="specificSource != null">
                specific_source = #{specificSource,jdbcType=VARCHAR},
            </if>
            <if test="excavatedLocation != null">
                excavated_location = #{excavatedLocation,jdbcType=VARCHAR},
            </if>
            <if test="excavatedDate != null">
                excavated_date = #{excavatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="holder != null">
                holder = #{holder,jdbcType=VARCHAR},
            </if>
            <if test="payVoucherNo != null">
                pay_voucher_no = #{payVoucherNo,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=BIGINT},
            </if>
            <if test="collectionRemark != null">
                collection_remark = #{collectionRemark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where register_info_id = #{registerInfoId,jdbcType=BIGINT}
    </update>

    <delete id="deleteByRegisterNo">
    delete from scs_collection_register_attr_info
    where register_no = #{registerNo,jdbcType=VARCHAR} and company_id = #{companyId,jdbcType=BIGINT}
    </delete>

    <select id="listByRegisterNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_attr_info
        where company_id = #{companyId,jdbcType=BIGINT}
        <if test="registerNos != null and registerNos.size() > 0 ">
            and register_no in
            <foreach collection="registerNos" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectByRegisterNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_attr_info
        where company_id = #{companyId,jdbcType=BIGINT} and register_no = #{registerNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByRegisterId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_attr_info
        where company_id = #{companyId,jdbcType=BIGINT} and register_info_id = #{registerId,jdbcType=BIGINT}
    </select>

    <select id="listByCompanyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_attr_info
        where company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="listByRegisterInfoIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_attr_info
        where 1 = 1
        <if test="registerInfoIds != null and registerInfoIds.size() > 0 ">
            and register_info_id in
            <foreach collection="registerInfoIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>