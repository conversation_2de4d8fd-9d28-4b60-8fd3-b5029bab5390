<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SwzsCulturalRelicsLikeMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SwzsCulturalRelicsLikeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="cultural_relics_id" jdbcType="BIGINT" property="culturalRelicsId" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="like_count" jdbcType="BIGINT" property="likeCount" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, cultural_relics_id, `number`, `type`, like_count, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_like
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from swzs_cultural_relics_like
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsLikeDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics_like (company_id, cultural_relics_id, `number`, 
      `type`, like_count, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{culturalRelicsId,jdbcType=BIGINT}, #{number,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{likeCount,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsLikeDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics_like
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="culturalRelicsId != null">
        cultural_relics_id,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="likeCount != null">
        like_count,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="culturalRelicsId != null">
        #{culturalRelicsId,jdbcType=BIGINT},
      </if>
      <if test="number != null">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="likeCount != null">
        #{likeCount,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsLikeDO">
    update swzs_cultural_relics_like
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="culturalRelicsId != null">
        cultural_relics_id = #{culturalRelicsId,jdbcType=BIGINT},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="likeCount != null">
        like_count = #{likeCount,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsLikeDO">
    update swzs_cultural_relics_like
    set company_id = #{companyId,jdbcType=BIGINT},
      cultural_relics_id = #{culturalRelicsId,jdbcType=BIGINT},
      `number` = #{number,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=TINYINT},
      like_count = #{likeCount,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listDO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_like
    where company_id = #{companyId,jdbcType=BIGINT} and gmt_create &lt;= #{end,jdbcType=TIMESTAMP}
    <if test="start != null">
      and gmt_create &gt;= #{start,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="selectListByCulturalRelicsId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_like
    where cultural_relics_id = #{culturalRelicsId,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </select>
  <select id="selectByLikeDO" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsLikeDO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_like
    <where>
      <if test="companyId != null">
        and company_id = #{companyId,jdbcType=BIGINT}
      </if>
      <if test="culturalRelicsId != null">
        and cultural_relics_id = #{culturalRelicsId,jdbcType=BIGINT}
      </if>
      <if test="number != null">
        and `number` = #{number,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=TINYINT}
      </if>
      <if test="likeCount != null">
        and like_count = #{likeCount,jdbcType=BIGINT}
      </if>
      <if test="gmtCreate != null">
        and gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
      </if>
      <if test="gmtModified != null">
        and gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>
  <update id="updateLikeCountByCulturalRelicsId" parameterType="java.lang.Long">
    update swzs_cultural_relics_like
    set
        like_count = #{likeCount,jdbcType=BIGINT}
    where cultural_relics_id = #{culturalRelicsId,jdbcType=BIGINT}
  </update>

  <select id="listByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_like
    where cultural_relics_id in
    <foreach collection="culturalRelicsIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>
</mapper>