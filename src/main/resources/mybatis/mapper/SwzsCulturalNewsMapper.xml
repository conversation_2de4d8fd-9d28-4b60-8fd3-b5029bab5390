<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SwzsCulturalNewsMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SwzsCulturalNewsDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="news_title" jdbcType="VARCHAR" property="newsTitle" />
    <result column="news_type" jdbcType="TINYINT" property="newsType" />
    <result column="publish_status" jdbcType="TINYINT" property="publishStatus" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.das.museum.infr.dataobject.SwzsCulturalNewsDO">
    <result column="news_content" jdbcType="LONGVARCHAR" property="newsContent" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, news_title, news_type, publish_status, creator, modifier, gmt_create, gmt_modified,
    publish_time
  </sql>
  <sql id="Blob_Column_List">
    news_content
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from swzs_cultural_news
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByCompanyId"  resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from swzs_cultural_news
    where company_id = #{companyId,jdbcType=BIGINT}  and id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from swzs_cultural_news
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByCompanyId">
    delete from swzs_cultural_news
    where company_id = #{companyId,jdbcType=BIGINT}  and id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalNewsDO" useGeneratedKeys="true">
    insert into swzs_cultural_news (company_id, news_title, news_type, publish_status,
      creator, modifier, gmt_create, 
      gmt_modified, publish_time, news_content
      )
    values (#{companyId,jdbcType=BIGINT}, #{newsTitle,jdbcType=VARCHAR}, #{newsType,jdbcType=TINYINT}, #{publishStatus,jdbcType=TINYINT},
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{publishTime,jdbcType=TIMESTAMP}, #{newsContent,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalNewsDO" useGeneratedKeys="true">
    insert into swzs_cultural_news
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="newsTitle != null">
        news_title,
      </if>
      <if test="newsType != null">
        news_type,
      </if>
      <if test="publishStatus != null">
        publish_status,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="newsContent != null">
        news_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="newsTitle != null">
        #{newsTitle,jdbcType=VARCHAR},
      </if>
      <if test="newsType != null">
        #{newsType,jdbcType=TINYINT},
      </if>
      <if test="publishStatus != null">
        #{publishStatus,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="newsContent != null">
        #{newsContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SwzsCulturalNewsDO">
    update swzs_cultural_news
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="newsTitle != null">
        news_title = #{newsTitle,jdbcType=VARCHAR},
      </if>
      <if test="newsType != null">
        news_type = #{newsType,jdbcType=TINYINT},
      </if>
      <if test="publishStatus != null">
        publish_status = #{publishStatus,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="newsContent != null">
        news_content = #{newsContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByCompanyIdAndId" parameterType="com.das.museum.infr.dataobject.SwzsCulturalNewsDO">
    update swzs_cultural_news
    <set>
      <if test="newsTitle != null">
        news_title = #{newsTitle,jdbcType=VARCHAR},
      </if>
      <if test="newsType != null">
        news_type = #{newsType,jdbcType=TINYINT},
      </if>
      <if test="publishStatus != null">
        publish_status = #{publishStatus,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="newsContent != null">
        news_content = #{newsContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SwzsCulturalNewsDO">
    update swzs_cultural_news
    set company_id = #{companyId,jdbcType=BIGINT},
      news_title = #{newsTitle,jdbcType=VARCHAR},
      news_type = #{newsType,jdbcType=TINYINT},
      news_content = #{newsContent,jdbcType=LONGVARCHAR},
      publish_status = #{publishStatus,jdbcType=TINYINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      publish_time = #{publishTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listCulturalNewsByCondition" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from swzs_cultural_news
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="newsTitle != null and newsTitle != '' ">
      and news_title like CONCAT('%',#{newsTitle,jdbcType=VARCHAR},'%')
    </if>
    <if test="newsType != null ">
      and news_type = #{newsType,jdbcType=TINYINT}
    </if>
    <if test="publishStatus != null ">
      and publish_status = #{publishStatus,jdbcType=TINYINT}
    </if>
    <if test="startTime != null and startTime != '' ">
      and publish_time >= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="endTime != null and endTime != '' ">
      and publish_time &lt;=  #{endTime,jdbcType=VARCHAR}
    </if>
    <if test="creatorName != null and creatorName != '' ">
        and creator in ( select id from acc_user where real_name like CONCAT('%',#{creatorName,jdbcType=VARCHAR},'%') )
    </if>
    order by ${sortBy}
  </select>

  <select id="listCulturalNewsByTime" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from swzs_cultural_news
    where company_id = #{companyId,jdbcType=BIGINT}
    and publish_status = 1
    <if test="startTime != null ">
      and publish_time >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null ">
      and publish_time &lt;=  #{endTime,jdbcType=TIMESTAMP}
    </if>
  </select>
</mapper>