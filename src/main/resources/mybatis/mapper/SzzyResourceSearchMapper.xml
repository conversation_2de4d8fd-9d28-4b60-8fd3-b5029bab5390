<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SzzyResourceSearchMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SzzyResourceSearchDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="resource_id" jdbcType="BIGINT" property="resourceId" />
    <result column="source_doc_id" jdbcType="BIGINT" property="sourceDocId" />
    <result column="resource_pool_code" jdbcType="VARCHAR" property="resourcePoolCode" />
    <result column="resource_type" jdbcType="TINYINT" property="resourceType" />
    <result column="secret_level" jdbcType="TINYINT" property="secretLevel" />
    <result column="resource_name" jdbcType="VARCHAR" property="resourceName" />
    <result column="collection_id" jdbcType="BIGINT" property="collectionId" />
    <result column="collection_name" jdbcType="VARCHAR" property="collectionName" />
    <result column="related_user_name" jdbcType="VARCHAR" property="relatedUserName" />
    <result column="resource_uri_id" jdbcType="VARCHAR" property="resourceUriId" />
    <result column="resource_uri_name" jdbcType="VARCHAR" property="resourceUriName" />
    <result column="resource_url" jdbcType="VARCHAR" property="resourceUrl" />
    <result column="resource_route" jdbcType="VARCHAR" property="resourceRoute" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="related_research_name" jdbcType="VARCHAR" property="relatedResearchName" />
    <result column="three_model_url" jdbcType="VARCHAR" property="threeModelUrl"/>
    <result column="three_model_status" jdbcType="TINYINT" property="threeModelStatus"/>
    <result column="error_info" jdbcType="VARCHAR" property="errorInfo"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, resource_id, source_doc_id, resource_pool_code, resource_type, secret_level, 
    resource_name, collection_id, collection_name, related_user_name, resource_uri_id, resource_uri_name,
    resource_url, resource_route, creator, modifier, gmt_create, gmt_modified, related_research_name,
    three_model_url, three_model_status, error_info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from szzy_resource_search
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szzy_resource_search
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyResourceSearchDO" useGeneratedKeys="true">
    insert into szzy_resource_search (company_id, resource_id, source_doc_id, 
      resource_pool_code, resource_type, secret_level, 
      resource_name, collection_id, collection_name, related_user_name,
      resource_uri_id, resource_uri_name, resource_url, 
      resource_route, creator, modifier, 
      gmt_create, gmt_modified, related_research_name,
      three_model_url, three_model_status, error_info)
    values (#{companyId,jdbcType=BIGINT}, #{resourceId,jdbcType=BIGINT}, #{sourceDocId,jdbcType=BIGINT}, 
      #{resourcePoolCode,jdbcType=VARCHAR}, #{resourceType,jdbcType=TINYINT}, #{secretLevel,jdbcType=TINYINT}, 
      #{resourceName,jdbcType=VARCHAR}, #{collectionId,jdbcType=BIGINT}, #{collectionName,jdbcType=VARCHAR}, #{relatedUserName,jdbcType=VARCHAR},
      #{resourceUriId,jdbcType=VARCHAR}, #{resourceUriName,jdbcType=VARCHAR}, #{resourceUrl,jdbcType=VARCHAR}, 
      #{resourceRoute,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{relatedResearchName,jdbcType=VARCHAR},
      #{threeModelUrl,jdbcType=VARCHAR}, #{threeModelStatus,jdbcType=TINYINT}, #{errorInfo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyResourceSearchDO" useGeneratedKeys="true">
    insert into szzy_resource_search
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="sourceDocId != null">
        source_doc_id,
      </if>
      <if test="resourcePoolCode != null">
        resource_pool_code,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="secretLevel != null">
        secret_level,
      </if>
      <if test="resourceName != null">
        resource_name,
      </if>
      <if test="collectionId != null">
        collection_id,
      </if>
      <if test="collectionName != null">
        collection_name,
      </if>
      <if test="relatedUserName != null">
        related_user_name,
      </if>
      <if test="resourceUriId != null">
        resource_uri_id,
      </if>
      <if test="resourceUriName != null">
        resource_uri_name,
      </if>
      <if test="resourceUrl != null">
        resource_url,
      </if>
      <if test="resourceRoute != null">
        resource_route,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="relatedResearchName != null">
        related_research_name,
      </if>
      <if test="threeModelUrl != null">
        three_model_url,
      </if>
      <if test="threeModelStatus != null">
        three_model_status,
      </if>
      <if test="errorInfo != null">
        error_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="sourceDocId != null">
        #{sourceDocId,jdbcType=BIGINT},
      </if>
      <if test="resourcePoolCode != null">
        #{resourcePoolCode,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=TINYINT},
      </if>
      <if test="secretLevel != null">
        #{secretLevel,jdbcType=TINYINT},
      </if>
      <if test="resourceName != null">
        #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="collectionId != null">
        #{collectionId,jdbcType=BIGINT},
      </if>
      <if test="collectionName != null">
        #{collectionName,jdbcType=VARCHAR},
      </if>
      <if test="relatedUserName != null">
        #{relatedUserName,jdbcType=VARCHAR},
      </if>
      <if test="resourceUriId != null">
        #{resourceUriId,jdbcType=VARCHAR},
      </if>
      <if test="resourceUriName != null">
        #{resourceUriName,jdbcType=VARCHAR},
      </if>
      <if test="resourceUrl != null">
        #{resourceUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceRoute != null">
        #{resourceRoute,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="relatedResearchName != null">
        #{relatedResearchName,jdbcType=VARCHAR},
      </if>
      <if test="threeModelUrl != null">
        #{threeModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="threeModelStatus != null">
        #{threeModelStatus,jdbcType=TINYINT},
      </if>
      <if test="errorInfo != null">
        #{errorInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SzzyResourceSearchDO">
    update szzy_resource_search
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="sourceDocId != null">
        source_doc_id = #{sourceDocId,jdbcType=BIGINT},
      </if>
      <if test="resourcePoolCode != null">
        resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=TINYINT},
      </if>
      <if test="secretLevel != null">
        secret_level = #{secretLevel,jdbcType=TINYINT},
      </if>
      <if test="resourceName != null">
        resource_name = #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="collectionId != null">
        collection_id = #{collectionId,jdbcType=BIGINT},
      </if>
      <if test="collectionName != null">
        collection_name = #{collectionName,jdbcType=VARCHAR},
      </if>
      <if test="relatedUserName != null">
        related_user_name = #{relatedUserName,jdbcType=VARCHAR},
      </if>
      <if test="resourceUriId != null">
        resource_uri_id = #{resourceUriId,jdbcType=VARCHAR},
      </if>
      <if test="resourceUriName != null">
        resource_uri_name = #{resourceUriName,jdbcType=VARCHAR},
      </if>
      <if test="resourceUrl != null">
        resource_url = #{resourceUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceRoute != null">
        resource_route = #{resourceRoute,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="relatedResearchName != null">
        related_research_name = #{relatedResearchName,jdbcType=VARCHAR},
      </if>
      <if test="threeModelUrl != null">
        three_model_url = #{threeModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="threeModelStatus != null">
        three_model_status = #{threeModelStatus,jdbcType=TINYINT},
      </if>
      <if test="errorInfo != null">
        error_info = #{errorInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SzzyResourceSearchDO">
    update szzy_resource_search
    set company_id = #{companyId,jdbcType=BIGINT},
      resource_id = #{resourceId,jdbcType=BIGINT},
      source_doc_id = #{sourceDocId,jdbcType=BIGINT},
      resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=TINYINT},
      secret_level = #{secretLevel,jdbcType=TINYINT},
      resource_name = #{resourceName,jdbcType=VARCHAR},
      collection_id = #{collectionId,jdbcType=BIGINT},
      collection_name = #{collectionName,jdbcType=VARCHAR},
      related_user_name = #{relatedUserName,jdbcType=VARCHAR},
      resource_uri_id = #{resourceUriId,jdbcType=VARCHAR},
      resource_uri_name = #{resourceUriName,jdbcType=VARCHAR},
      resource_url = #{resourceUrl,jdbcType=VARCHAR},
      resource_route = #{resourceRoute,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      related_research_name = #{relatedResearchName,jdbcType=VARCHAR},
      three_model_url = #{threeModelUrl,jdbcType=VARCHAR},
      three_model_status = #{threeModelStatus,jdbcType=TINYINT},
      error_info = #{errorInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listRelatedByKeyword" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
    (
      select
      <include refid="Base_Column_List" />
      from szzy_resource_search
      where company_id = #{companyId,jdbcType=BIGINT} and (collection_name like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                                                           or related_user_name like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                                                           or related_research_name like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                                                          )
      <if test="secretLevels != null and secretLevels.size() > 0">
        and secret_level in
        <foreach collection="secretLevels" item="id" index="index" open="(" close=")" separator=",">
          #{id,jdbcType=TINYINT}
        </foreach>
      </if>
      union
      select
      <include refid="Base_Column_List" />
      from szzy_resource_search
      where company_id = #{companyId,jdbcType=BIGINT}
        and (collection_name like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
             or related_user_name like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
             or related_research_name like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
            )
      and creator = #{creator,jdbcType=BIGINT}
    ) A
    left join (select id rId,resource_pool_code poolCode from szzy_resource where resource_type = 0 and company_id = #{companyId,jdbcType=BIGINT}) B
    on A.resource_pool_code = B.poolCode
    order by B.rId ASC,A.resource_uri_id ASC, A.gmt_create DESC
  </select>

  <select id="listDirectByKeyword" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
    (
      select
      <include refid="Base_Column_List" />
      from szzy_resource_search
      where company_id = #{companyId,jdbcType=BIGINT} and resource_name like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
      <if test="resourcePoolCode != null and resourcePoolCode.size() > 0">
        and resource_pool_code in
        <foreach collection="resourcePoolCode" item="code" index="index" open="(" close=")" separator=",">
          #{code,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="startDate != null ">
        and gmt_modified >= #{startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null ">
        and gmt_modified <![CDATA[<=]]> #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="secretLevels != null and secretLevels.size() > 0">
        and secret_level in
        <foreach collection="secretLevels" item="id" index="index" open="(" close=")" separator=",">
          #{id,jdbcType=TINYINT}
        </foreach>
      </if>
      union
      select
      <include refid="Base_Column_List" />
      from szzy_resource_search
      where company_id = #{companyId,jdbcType=BIGINT} and resource_name like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
      and creator = #{creator,jdbcType=BIGINT}
      <if test="resourcePoolCode != null and resourcePoolCode.size() > 0">
        and resource_pool_code in
        <foreach collection="resourcePoolCode" item="code" index="index" open="(" close=")" separator=",">
          #{code,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="startDate != null ">
        and gmt_modified >= #{startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null ">
        and gmt_modified <![CDATA[<=]]> #{endDate,jdbcType=TIMESTAMP}
      </if>
    ) A
    left join (select id rId,resource_pool_code poolCode from szzy_resource where resource_type = 0 and company_id = #{companyId,jdbcType=BIGINT}) B
    on A.resource_pool_code = B.poolCode
    order by B.rId ASC,A.resource_uri_id ASC, A.gmt_modified DESC
  </select>

  <delete id="deleteByResourceId">
    delete from szzy_resource_search
    where resource_id = #{resourceId,jdbcType=BIGINT}
  </delete>

  <select id="listHotResource" resultType="com.das.museum.infr.dataobject.SzzyResourceSearchByFavoriteDO">
    select
    A.id id,
    A.company_id companyId,
    A.resource_id resourceId,
    A.source_doc_id sourceDocId,
    A.resource_pool_code resourcePoolCode,
    A.resource_type resourceType,
    A.secret_level secretLevel,
    A.resource_name resourceName,
    A.collection_name collectionName,
    A.related_user_name relatedUserName,
    A.resource_uri_id resourceUriId,
    A.resource_uri_name resourceUriName,
    A.resource_url resourceUrl,
    A.resource_route resourceRoute,
    A.creator creator,
    A.modifier modifier,
    A.gmt_create gmtCreate,
    A.gmt_modified gmtModified,
    sr.favorite_count favoriteCount
    from
    (
    select
    <include refid="Base_Column_List" />
    from szzy_resource_search
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="secretLevels != null and secretLevels.size() > 0">
      and secret_level in
      <foreach collection="secretLevels" item="id" index="index" open="(" close=")" separator=",">
        #{id,jdbcType=TINYINT}
      </foreach>
    </if>
    union
    select
    <include refid="Base_Column_List" />
    from szzy_resource_search
    where company_id = #{companyId,jdbcType=BIGINT}
    and creator = #{creator,jdbcType=BIGINT}
    ) as A
    join szzy_resource sr  ON A.resource_name = sr.resource_name
    where A.resource_type in (1, 3)
    ORDER BY sr.favorite_count desc
    LIMIT #{limitCount}
  </select>

  <select id="listNewResource" resultType="com.das.museum.infr.dataobject.SzzyResourceSearchDO">
    select
    A.id id,
    A.company_id companyId,
    A.resource_id resourceId,
    A.source_doc_id sourceDocId,
    A.resource_pool_code resourcePoolCode,
    A.resource_type resourceType,
    A.secret_level secretLevel,
    A.resource_name resourceName,
    A.collection_name collectionName,
    A.related_user_name relatedUserName,
    A.resource_uri_id resourceUriId,
    A.resource_uri_name resourceUriName,
    A.resource_url resourceUrl,
    A.resource_route resourceRoute,
    A.creator creator,
    A.modifier modifier,
    A.gmt_create gmtCreate,
    A.gmt_modified gmtModified
    from
    (
    select
    <include refid="Base_Column_List" />
    from szzy_resource_search
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="secretLevels != null and secretLevels.size() > 0">
      and secret_level in
      <foreach collection="secretLevels" item="id" index="index" open="(" close=")" separator=",">
        #{id,jdbcType=TINYINT}
      </foreach>
    </if>
    union
    select
    <include refid="Base_Column_List" />
    from szzy_resource_search
    where company_id = #{companyId,jdbcType=BIGINT}
    and creator = #{creator,jdbcType=BIGINT}
    ) as A
    join szzy_resource sr  ON A.resource_name = sr.resource_name
    where A.resource_type in (1, 3)
    ORDER BY sr.gmt_create desc
    LIMIT #{limitCount}
  </select>

  <select id="listBySourceDocIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_resource_search
    where company_id = #{companyId,jdbcType=BIGINT}
    and source_doc_id in
    <foreach collection="sourceDocIds" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

  <select id="selectBySourceDocId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_resource_search
    where company_id = #{companyId,jdbcType=BIGINT}
    and resource_id = #{resourceId,jdbcType=BIGINT}
    and source_doc_id = #{sourceDocId,jdbcType=BIGINT}
  </select>

  <select id="selectBySourceIdAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_resource_search
    where company_id = #{companyId,jdbcType=BIGINT}
    and resource_id = #{resourceId,jdbcType=BIGINT}
    and resource_type = #{resourceType,jdbcType=TINYINT}
  </select>


  <select id="listByResourceId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_resource_search
    where company_id = #{companyId,jdbcType=BIGINT}
    and resource_id = #{resourceId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByResourceIdAndSourceDocId">
    delete from szzy_resource_search
    where company_id = #{companyId,jdbcType=BIGINT}
      and resource_id = #{resourceId,jdbcType=BIGINT}
      and source_doc_id in
    <foreach collection="sourceDocIds" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </delete>

  <select id="listByCollectionIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_resource_search
    where resource_type = 3
    and resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR}
    and collection_id in
    <foreach collection="collectionIds" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>
</mapper>