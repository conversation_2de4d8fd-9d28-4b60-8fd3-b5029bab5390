<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionIdentifyMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionIdentifyDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="identify_type" jdbcType="BIGINT" property="identifyType"/>
        <result column="appraiser_id" jdbcType="BIGINT" property="appraiserId"/>
        <result column="appraiser_name" jdbcType="VARCHAR" property="appraiserName"/>
        <result column="org_agency" jdbcType="VARCHAR" property="orgAgency"/>
        <result column="identify_agency" jdbcType="VARCHAR" property="identifyAgency"/>
        <result column="approve_agency" jdbcType="VARCHAR" property="approveAgency"/>
        <result column="identify_opinion" jdbcType="VARCHAR" property="identifyOpinion"/>
        <result column="identify_remark" jdbcType="VARCHAR" property="identifyRemark"/>
        <result column="identify_date" jdbcType="TIMESTAMP" property="identifyDate"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, collection_id, identify_type, appraiser_id, appraiser_name, org_agency, 
    identify_agency, approve_agency, identify_opinion, identify_remark, identify_date, 
    document_id, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_identify
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_identify
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionIdentifyDO" useGeneratedKeys="true">
    insert into scs_collection_identify (company_id, collection_id, identify_type, 
      appraiser_id, appraiser_name, org_agency, 
      identify_agency, approve_agency, identify_opinion, 
      identify_remark, identify_date, document_id, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{collectionId,jdbcType=BIGINT}, #{identifyType,jdbcType=BIGINT}, 
      #{appraiserId,jdbcType=BIGINT}, #{appraiserName,jdbcType=VARCHAR}, #{orgAgency,jdbcType=VARCHAR}, 
      #{identifyAgency,jdbcType=VARCHAR}, #{approveAgency,jdbcType=VARCHAR}, #{identifyOpinion,jdbcType=VARCHAR}, 
      #{identifyRemark,jdbcType=VARCHAR}, #{identifyDate,jdbcType=TIMESTAMP}, #{documentId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionIdentifyDO" useGeneratedKeys="true">
        insert into scs_collection_identify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="identifyType != null">
                identify_type,
            </if>
            <if test="appraiserId != null">
                appraiser_id,
            </if>
            <if test="appraiserName != null">
                appraiser_name,
            </if>
            <if test="orgAgency != null">
                org_agency,
            </if>
            <if test="identifyAgency != null">
                identify_agency,
            </if>
            <if test="approveAgency != null">
                approve_agency,
            </if>
            <if test="identifyOpinion != null">
                identify_opinion,
            </if>
            <if test="identifyRemark != null">
                identify_remark,
            </if>
            <if test="identifyDate != null">
                identify_date,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="identifyType != null">
                #{identifyType,jdbcType=BIGINT},
            </if>
            <if test="appraiserId != null">
                #{appraiserId,jdbcType=BIGINT},
            </if>
            <if test="appraiserName != null">
                #{appraiserName,jdbcType=VARCHAR},
            </if>
            <if test="orgAgency != null">
                #{orgAgency,jdbcType=VARCHAR},
            </if>
            <if test="identifyAgency != null">
                #{identifyAgency,jdbcType=VARCHAR},
            </if>
            <if test="approveAgency != null">
                #{approveAgency,jdbcType=VARCHAR},
            </if>
            <if test="identifyOpinion != null">
                #{identifyOpinion,jdbcType=VARCHAR},
            </if>
            <if test="identifyRemark != null">
                #{identifyRemark,jdbcType=VARCHAR},
            </if>
            <if test="identifyDate != null">
                #{identifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsCollectionIdentifyDO">
        update scs_collection_identify
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="identifyType != null">
                identify_type = #{identifyType,jdbcType=BIGINT},
            </if>
            <if test="appraiserId != null">
                appraiser_id = #{appraiserId,jdbcType=BIGINT},
            </if>
            <if test="appraiserName != null">
                appraiser_name = #{appraiserName,jdbcType=VARCHAR},
            </if>
            <if test="orgAgency != null">
                org_agency = #{orgAgency,jdbcType=VARCHAR},
            </if>
            <if test="identifyAgency != null">
                identify_agency = #{identifyAgency,jdbcType=VARCHAR},
            </if>
            <if test="approveAgency != null">
                approve_agency = #{approveAgency,jdbcType=VARCHAR},
            </if>
            <if test="identifyOpinion != null">
                identify_opinion = #{identifyOpinion,jdbcType=VARCHAR},
            </if>
            <if test="identifyRemark != null">
                identify_remark = #{identifyRemark,jdbcType=VARCHAR},
            </if>
            <if test="identifyDate != null">
                identify_date = #{identifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionIdentifyDO">
    update scs_collection_identify
    set company_id = #{companyId,jdbcType=BIGINT},
      collection_id = #{collectionId,jdbcType=BIGINT},
      identify_type = #{identifyType,jdbcType=BIGINT},
      appraiser_id = #{appraiserId,jdbcType=BIGINT},
      appraiser_name = #{appraiserName,jdbcType=VARCHAR},
      org_agency = #{orgAgency,jdbcType=VARCHAR},
      identify_agency = #{identifyAgency,jdbcType=VARCHAR},
      approve_agency = #{approveAgency,jdbcType=VARCHAR},
      identify_opinion = #{identifyOpinion,jdbcType=VARCHAR},
      identify_remark = #{identifyRemark,jdbcType=VARCHAR},
      identify_date = #{identifyDate,jdbcType=TIMESTAMP},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <delete id="deleteById">
    delete from scs_collection_identify
    where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from scs_collection_identify
    where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="listPageByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from scs_collection_identify
    where company_id = #{companyId,jdbcType=BIGINT} and collection_id = #{collectionId,jdbcType=BIGINT}
    order by ${sortBy}
    </select>
</mapper>