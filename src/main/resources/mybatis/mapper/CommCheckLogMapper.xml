<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.CommCheckLogMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.CommCheckLogDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="main_id" jdbcType="BIGINT" property="mainId"/>
        <result column="belong_model" jdbcType="VARCHAR" property="belongModel"/>
        <result column="model_id" jdbcType="BIGINT" property="modelId"/>
        <result column="check_man_id" jdbcType="BIGINT" property="checkManId"/>
        <result column="check_man_name" jdbcType="VARCHAR" property="checkManName"/>
        <result column="check_status" jdbcType="VARCHAR" property="checkStatus"/>
        <result column="check_opinion" jdbcType="VARCHAR" property="checkOpinion"/>
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, main_id, belong_model, model_id, check_man_id, check_man_name, check_status, check_opinion,
    check_time, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_check_log
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommCheckLogDO"
            useGeneratedKeys="true">
    insert into comm_check_log (main_id, belong_model, model_id, check_man_id,
      check_man_name, check_status, check_opinion, 
      check_time, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{mainId,jdbcType=BIGINT}, #{belongModel,jdbcType=VARCHAR}, #{modelId,jdbcType=BIGINT}, #{checkManId,jdbcType=BIGINT},
      #{checkManName,jdbcType=VARCHAR}, #{checkStatus,jdbcType=VARCHAR}, #{checkOpinion,jdbcType=VARCHAR}, 
      #{checkTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.CommCheckLogDO" useGeneratedKeys="true">
        insert into comm_check_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mainId != null">
                main_id,
            </if>
            <if test="belongModel != null">
                belong_model,
            </if>
            <if test="modelId != null">
                model_id,
            </if>
            <if test="checkManId != null">
                check_man_id,
            </if>
            <if test="checkManName != null">
                check_man_name,
            </if>
            <if test="checkStatus != null">
                check_status,
            </if>
            <if test="checkOpinion != null">
                check_opinion,
            </if>
            <if test="checkTime != null">
                check_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mainId != null">
                #{mainId,jdbcType=BIGINT},
            </if>
            <if test="belongModel != null">
                #{belongModel,jdbcType=VARCHAR},
            </if>
            <if test="modelId != null">
                #{modelId,jdbcType=BIGINT},
            </if>
            <if test="checkManId != null">
                #{checkManId,jdbcType=BIGINT},
            </if>
            <if test="checkManName != null">
                #{checkManName,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null">
                #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="checkOpinion != null">
                #{checkOpinion,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null">
                #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.CommCheckLogDO">
        update comm_check_log
        <set>
            <if test="mainId != null">
                main_id = #{mainId,jdbcType=BIGINT},
            </if>
            <if test="belongModel != null">
                belong_model = #{belongModel,jdbcType=VARCHAR},
            </if>
            <if test="modelId != null">
                model_id = #{modelId,jdbcType=BIGINT},
            </if>
            <if test="checkManId != null">
                check_man_id = #{checkManId,jdbcType=BIGINT},
            </if>
            <if test="checkManName != null">
                check_man_name = #{checkManName,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null">
                check_status = #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="checkOpinion != null">
                check_opinion = #{checkOpinion,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null">
                check_time = #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.CommCheckLogDO">
    update comm_check_log
    set main_id = #{mainId,jdbcType=BIGINT},
      belong_model = #{belongModel,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=BIGINT},
      check_man_id = #{checkManId,jdbcType=BIGINT},
      check_man_name = #{checkManName,jdbcType=VARCHAR},
      check_status = #{checkStatus,jdbcType=VARCHAR},
      check_opinion = #{checkOpinion,jdbcType=VARCHAR},
      check_time = #{checkTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <select id="selectLogListByMainId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_log
        where main_id = #{mainId,jdbcType=BIGINT}
        order by check_time desc
    </select>

    <delete id="deleteByMainId">
        delete from comm_check_log
        where main_id = #{mainId,jdbcType=BIGINT}
    </delete>

    <update id="updateByModelId" parameterType="com.das.museum.infr.dataobject.CommCheckLogDO">
        update comm_check_log
        <set>
            <if test="mainId != null">
                main_id = #{mainId,jdbcType=BIGINT},
            </if>
            <if test="checkManId != null">
                check_man_id = #{checkManId,jdbcType=BIGINT},
            </if>
            <if test="checkManName != null">
                check_man_name = #{checkManName,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null">
                check_status = #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="checkOpinion != null">
                check_opinion = #{checkOpinion,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null">
                check_time = #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where model_id = #{modelId,jdbcType=BIGINT} and belong_model = #{belongModel,jdbcType=VARCHAR}
    </update>
</mapper>