<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsOutInWarehouseCertificateInfoMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsOutInWarehouseCertificateInfoDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="out_in_warehouse_certificate_no" jdbcType="VARCHAR" property="outInWarehouseCertificateNo"/>
        <result column="prefix_name" jdbcType="VARCHAR" property="prefixName"/>
        <result column="suffix_number" jdbcType="BIGINT" property="suffixNumber"/>
        <result column="reason" jdbcType="BIGINT" property="reason"/>
        <result column="reason_info" jdbcType="VARCHAR" property="reasonInfo"/>
        <result column="use_id" jdbcType="VARCHAR" property="useId"/>
        <result column="user_department_id" jdbcType="VARCHAR" property="userDepartmentId"/>
        <result column="use_address" jdbcType="VARCHAR" property="useAddress"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="use_date" jdbcType="TIMESTAMP" property="useDate"/>
        <result column="pre_return_date" jdbcType="TIMESTAMP" property="preReturnDate"/>
        <result column="submitter" jdbcType="BIGINT" property="submitter"/>
        <result column="submitter_name" jdbcType="VARCHAR" property="submitterName"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="auto_audit" jdbcType="TINYINT" property="autoAudit"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, out_in_warehouse_certificate_no, prefix_name, suffix_number, reason, 
    reason_info, use_id, user_department_id, use_address, remark, use_date, pre_return_date, 
    submitter, submitter_name, document_id, creator, modifier, gmt_create, gmt_modified, auto_audit
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_out_in_warehouse_certificate_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_out_in_warehouse_certificate_info
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsOutInWarehouseCertificateInfoDO" useGeneratedKeys="true">
    insert into scs_out_in_warehouse_certificate_info (company_id, out_in_warehouse_certificate_no, 
      prefix_name, suffix_number, reason, 
      reason_info, use_id, user_department_id, 
      use_address, remark, use_date, 
      pre_return_date, submitter, submitter_name, 
      document_id, creator, modifier, 
      gmt_create, gmt_modified, auto_audit)
    values (#{companyId,jdbcType=BIGINT}, #{outInWarehouseCertificateNo,jdbcType=VARCHAR}, 
      #{prefixName,jdbcType=VARCHAR}, #{suffixNumber,jdbcType=BIGINT}, #{reason,jdbcType=BIGINT}, 
      #{reasonInfo,jdbcType=VARCHAR}, #{useId,jdbcType=VARCHAR}, #{userDepartmentId,jdbcType=VARCHAR},
      #{useAddress,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{useDate,jdbcType=TIMESTAMP}, 
      #{preReturnDate,jdbcType=TIMESTAMP}, #{submitter,jdbcType=BIGINT}, #{submitterName,jdbcType=VARCHAR}, 
      #{documentId,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{autoAudit,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsOutInWarehouseCertificateInfoDO" useGeneratedKeys="true">
        insert into scs_out_in_warehouse_certificate_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="outInWarehouseCertificateNo != null">
                out_in_warehouse_certificate_no,
            </if>
            <if test="prefixName != null">
                prefix_name,
            </if>
            <if test="suffixNumber != null">
                suffix_number,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="reasonInfo != null">
                reason_info,
            </if>
            <if test="useId != null">
                use_id,
            </if>
            <if test="userDepartmentId != null">
                user_department_id,
            </if>
            <if test="useAddress != null">
                use_address,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="useDate != null">
                use_date,
            </if>
            <if test="preReturnDate != null">
                pre_return_date,
            </if>
            <if test="submitter != null">
                submitter,
            </if>
            <if test="submitterName != null">
                submitter_name,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="autoAudit != null">
                auto_audit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="outInWarehouseCertificateNo != null">
                #{outInWarehouseCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="prefixName != null">
                #{prefixName,jdbcType=VARCHAR},
            </if>
            <if test="suffixNumber != null">
                #{suffixNumber,jdbcType=BIGINT},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=BIGINT},
            </if>
            <if test="reasonInfo != null">
                #{reasonInfo,jdbcType=VARCHAR},
            </if>
            <if test="useId != null">
                #{useId,jdbcType=VARCHAR},
            </if>
            <if test="userDepartmentId != null">
                #{userDepartmentId,jdbcType=VARCHAR},
            </if>
            <if test="useAddress != null">
                #{useAddress,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="useDate != null">
                #{useDate,jdbcType=TIMESTAMP},
            </if>
            <if test="preReturnDate != null">
                #{preReturnDate,jdbcType=TIMESTAMP},
            </if>
            <if test="submitter != null">
                #{submitter,jdbcType=BIGINT},
            </if>
            <if test="submitterName != null">
                #{submitterName,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="autoAudit != null">
                #{autoAudit,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.das.museum.infr.dataobject.ScsOutInWarehouseCertificateInfoDO">
        update scs_out_in_warehouse_certificate_info
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="outInWarehouseCertificateNo != null">
                out_in_warehouse_certificate_no = #{outInWarehouseCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="prefixName != null">
                prefix_name = #{prefixName,jdbcType=VARCHAR},
            </if>
            <if test="suffixNumber != null">
                suffix_number = #{suffixNumber,jdbcType=BIGINT},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=BIGINT},
            </if>
            <if test="reasonInfo != null">
                reason_info = #{reasonInfo,jdbcType=VARCHAR},
            </if>
            <if test="useId != null">
                use_id = #{useId,jdbcType=VARCHAR},
            </if>
            <if test="userDepartmentId != null">
                user_department_id = #{userDepartmentId,jdbcType=VARCHAR},
            </if>
            <if test="useAddress != null">
                use_address = #{useAddress,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="useDate != null">
                use_date = #{useDate,jdbcType=TIMESTAMP},
            </if>
            <if test="preReturnDate != null">
                pre_return_date = #{preReturnDate,jdbcType=TIMESTAMP},
            </if>
            <if test="submitter != null">
                submitter = #{submitter,jdbcType=BIGINT},
            </if>
            <if test="submitterName != null">
                submitter_name = #{submitterName,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="autoAudit != null">
                auto_audit = #{autoAudit,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsOutInWarehouseCertificateInfoDO">
    update scs_out_in_warehouse_certificate_info
    set company_id = #{companyId,jdbcType=BIGINT},
      out_in_warehouse_certificate_no = #{outInWarehouseCertificateNo,jdbcType=VARCHAR},
      prefix_name = #{prefixName,jdbcType=VARCHAR},
      suffix_number = #{suffixNumber,jdbcType=BIGINT},
      reason = #{reason,jdbcType=BIGINT},
      reason_info = #{reasonInfo,jdbcType=VARCHAR},
      use_id = #{useId,jdbcType=VARCHAR},
      user_department_id = #{userDepartmentId,jdbcType=VARCHAR},
      use_address = #{useAddress,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      use_date = #{useDate,jdbcType=TIMESTAMP},
      pre_return_date = #{preReturnDate,jdbcType=TIMESTAMP},
      submitter = #{submitter,jdbcType=BIGINT},
      submitter_name = #{submitterName,jdbcType=VARCHAR},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      auto_audit = #{autoAudit,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->
    <delete id="deleteById">
    delete from scs_out_in_warehouse_certificate_info
    where company_id = #{companyId,jdbcType=BIGINT} and id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_out_in_warehouse_certificate_info
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="listPageByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_out_in_warehouse_certificate_info
        where company_id = #{companyId,jdbcType=BIGINT}
        <if test="outInWarehouseCertificateNo != null">
            and out_in_warehouse_certificate_no like concat('%', #{outInWarehouseCertificateNo,jdbcType=VARCHAR}, '%')
        </if>
        <if test="useId != null">
            and use_id like concat('%', #{useId,jdbcType=VARCHAR}, '%')
        </if>
        <if test="userDepartmentId != null">
            and user_department_id like concat('%', #{userDepartmentId,jdbcType=VARCHAR}, '%')
        </if>
        <if test="reason != null">
            and reason = #{reason,jdbcType=BIGINT}
        </if>
        <if test="registerNo != null">
            and id in (
            SELECT DISTINCT
            t2.biz_id
            FROM
            scs_collection_info t1
            LEFT JOIN scs_biz_collection_rel t2 ON t1.id = t2.collection_id
            WHERE
            t1.company_id = #{companyId,jdbcType=BIGINT} and t2.biz_type = 'OUTIN'
            <if test="registerNo != null">
                and t1.register_no like concat('%', #{registerNo,jdbcType=VARCHAR}, '%')
            </if>
            )
        </if>
        order by ${sortBy}
    </select>

    <select id="selectReasonByCollectionId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM scs_out_in_warehouse_certificate_info soiwci
        WHERE soiwci.id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
             and company_id = #{companyId,jdbcType=BIGINT};
    </select>
</mapper>