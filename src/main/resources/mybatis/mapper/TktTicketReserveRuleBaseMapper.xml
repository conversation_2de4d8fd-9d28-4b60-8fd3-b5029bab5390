<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.TktTicketReserveRuleBaseMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.TktTicketReserveRuleBaseDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="ticket_id" jdbcType="BIGINT" property="ticketId" />
    <result column="ticket_channel_id" jdbcType="BIGINT" property="ticketChannelId" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="is_today" jdbcType="TINYINT" property="isToday" />
    <result column="today_stop_time" jdbcType="TIME" property="todayStopTime" />
    <result column="advance_future_day" jdbcType="BIGINT" property="advanceFutureDay" />
    <result column="single_limit" jdbcType="TINYINT" property="singleLimit" />
    <result column="certificate_limit" jdbcType="TINYINT" property="certificateLimit" />
    <result column="id_card" jdbcType="TINYINT" property="idCard" />
    <result column="passport" jdbcType="TINYINT" property="passport" />
    <result column="hk_mac_permit" jdbcType="TINYINT" property="hkMacPermit" />
    <result column="time_limit_type" jdbcType="TINYINT" property="timeLimitType" />
    <result column="is_team_reserve" jdbcType="TINYINT" property="isTeamReserve" />
    <result column="reserv_rule_config" jdbcType="VARCHAR" property="reservRuleConfig" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, department_id, ticket_id, ticket_channel_id, channel_type, is_today, 
    today_stop_time, advance_future_day, single_limit, certificate_limit, id_card, passport, 
    hk_mac_permit, time_limit_type, is_team_reserve, reserv_rule_config, remark, creator, 
    modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tkt_ticket_reserve_rule_base
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tkt_ticket_reserve_rule_base
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketReserveRuleBaseDO" useGeneratedKeys="true">
    insert into tkt_ticket_reserve_rule_base (company_id, department_id, ticket_id, 
      ticket_channel_id, channel_type, is_today, 
      today_stop_time, advance_future_day, single_limit, 
      certificate_limit, id_card, passport, 
      hk_mac_permit, time_limit_type, is_team_reserve, 
      reserv_rule_config, remark, creator, 
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, #{ticketId,jdbcType=BIGINT}, 
      #{ticketChannelId,jdbcType=BIGINT}, #{channelType,jdbcType=TINYINT}, #{isToday,jdbcType=TINYINT}, 
      #{todayStopTime,jdbcType=TIME}, #{advanceFutureDay,jdbcType=BIGINT}, #{singleLimit,jdbcType=TINYINT}, 
      #{certificateLimit,jdbcType=TINYINT}, #{idCard,jdbcType=TINYINT}, #{passport,jdbcType=TINYINT}, 
      #{hkMacPermit,jdbcType=TINYINT}, #{timeLimitType,jdbcType=TINYINT}, #{isTeamReserve,jdbcType=TINYINT}, 
      #{reservRuleConfig,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketReserveRuleBaseDO" useGeneratedKeys="true">
    insert into tkt_ticket_reserve_rule_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="ticketId != null">
        ticket_id,
      </if>
      <if test="ticketChannelId != null">
        ticket_channel_id,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="isToday != null">
        is_today,
      </if>
      <if test="todayStopTime != null">
        today_stop_time,
      </if>
      <if test="advanceFutureDay != null">
        advance_future_day,
      </if>
      <if test="singleLimit != null">
        single_limit,
      </if>
      <if test="certificateLimit != null">
        certificate_limit,
      </if>
      <if test="idCard != null">
        id_card,
      </if>
      <if test="passport != null">
        passport,
      </if>
      <if test="hkMacPermit != null">
        hk_mac_permit,
      </if>
      <if test="timeLimitType != null">
        time_limit_type,
      </if>
      <if test="isTeamReserve != null">
        is_team_reserve,
      </if>
      <if test="reservRuleConfig != null">
        reserv_rule_config,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="ticketId != null">
        #{ticketId,jdbcType=BIGINT},
      </if>
      <if test="ticketChannelId != null">
        #{ticketChannelId,jdbcType=BIGINT},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=TINYINT},
      </if>
      <if test="isToday != null">
        #{isToday,jdbcType=TINYINT},
      </if>
      <if test="todayStopTime != null">
        #{todayStopTime,jdbcType=TIME},
      </if>
      <if test="advanceFutureDay != null">
        #{advanceFutureDay,jdbcType=BIGINT},
      </if>
      <if test="singleLimit != null">
        #{singleLimit,jdbcType=TINYINT},
      </if>
      <if test="certificateLimit != null">
        #{certificateLimit,jdbcType=TINYINT},
      </if>
      <if test="idCard != null">
        #{idCard,jdbcType=TINYINT},
      </if>
      <if test="passport != null">
        #{passport,jdbcType=TINYINT},
      </if>
      <if test="hkMacPermit != null">
        #{hkMacPermit,jdbcType=TINYINT},
      </if>
      <if test="timeLimitType != null">
        #{timeLimitType,jdbcType=TINYINT},
      </if>
      <if test="isTeamReserve != null">
        #{isTeamReserve,jdbcType=TINYINT},
      </if>
      <if test="reservRuleConfig != null">
        #{reservRuleConfig,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.TktTicketReserveRuleBaseDO">
    update tkt_ticket_reserve_rule_base
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="ticketId != null">
        ticket_id = #{ticketId,jdbcType=BIGINT},
      </if>
      <if test="ticketChannelId != null">
        ticket_channel_id = #{ticketChannelId,jdbcType=BIGINT},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=TINYINT},
      </if>
      <if test="isToday != null">
        is_today = #{isToday,jdbcType=TINYINT},
      </if>
      <if test="todayStopTime != null">
        today_stop_time = #{todayStopTime,jdbcType=TIME},
      </if>
      <if test="advanceFutureDay != null">
        advance_future_day = #{advanceFutureDay,jdbcType=BIGINT},
      </if>
      <if test="singleLimit != null">
        single_limit = #{singleLimit,jdbcType=TINYINT},
      </if>
      <if test="certificateLimit != null">
        certificate_limit = #{certificateLimit,jdbcType=TINYINT},
      </if>
      <if test="idCard != null">
        id_card = #{idCard,jdbcType=TINYINT},
      </if>
      <if test="passport != null">
        passport = #{passport,jdbcType=TINYINT},
      </if>
      <if test="hkMacPermit != null">
        hk_mac_permit = #{hkMacPermit,jdbcType=TINYINT},
      </if>
      <if test="timeLimitType != null">
        time_limit_type = #{timeLimitType,jdbcType=TINYINT},
      </if>
      <if test="isTeamReserve != null">
        is_team_reserve = #{isTeamReserve,jdbcType=TINYINT},
      </if>
      <if test="reservRuleConfig != null">
        reserv_rule_config = #{reservRuleConfig,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.TktTicketReserveRuleBaseDO">
    update tkt_ticket_reserve_rule_base
    set company_id = #{companyId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      ticket_id = #{ticketId,jdbcType=BIGINT},
      ticket_channel_id = #{ticketChannelId,jdbcType=BIGINT},
      channel_type = #{channelType,jdbcType=TINYINT},
      is_today = #{isToday,jdbcType=TINYINT},
      today_stop_time = #{todayStopTime,jdbcType=TIME},
      advance_future_day = #{advanceFutureDay,jdbcType=BIGINT},
      single_limit = #{singleLimit,jdbcType=TINYINT},
      certificate_limit = #{certificateLimit,jdbcType=TINYINT},
      id_card = #{idCard,jdbcType=TINYINT},
      passport = #{passport,jdbcType=TINYINT},
      hk_mac_permit = #{hkMacPermit,jdbcType=TINYINT},
      time_limit_type = #{timeLimitType,jdbcType=TINYINT},
      is_team_reserve = #{isTeamReserve,jdbcType=TINYINT},
      reserv_rule_config = #{reservRuleConfig,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_ticket_reserve_rule_base
    where  company_id = #{companyId,jdbcType=BIGINT}
    and channel_type = #{channelType,jdbcType=TINYINT}
  </select>


  <update id="updateByIdAndCompanyId" parameterType="com.das.museum.infr.dataobject.TktTicketReserveRuleBaseDO">
    update tkt_ticket_reserve_rule_base
    <set>
      <if test="isToday != null">
        is_today = #{isToday,jdbcType=TINYINT},
      </if>
      <if test="todayStopTime != null">
        today_stop_time = #{todayStopTime,jdbcType=TIME},
      </if>
      <if test="advanceFutureDay != null">
        advance_future_day = #{advanceFutureDay,jdbcType=BIGINT},
      </if>
      <if test="singleLimit != null">
        single_limit = #{singleLimit,jdbcType=TINYINT},
      </if>
      <if test="certificateLimit != null">
        certificate_limit = #{certificateLimit,jdbcType=TINYINT},
      </if>
      <if test="idCard != null">
        id_card = #{idCard,jdbcType=TINYINT},
      </if>
      <if test="passport != null">
        passport = #{passport,jdbcType=TINYINT},
      </if>
      <if test="hkMacPermit != null">
        hk_mac_permit = #{hkMacPermit,jdbcType=TINYINT},
      </if>
      <if test="timeLimitType != null">
        time_limit_type = #{timeLimitType,jdbcType=TINYINT},
      </if>
      <if test="isTeamReserve != null">
        is_team_reserve = #{isTeamReserve,jdbcType=TINYINT},
      </if>
      <if test="reservRuleConfig != null">
        reserv_rule_config = #{reservRuleConfig,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>

      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </update>
</mapper>