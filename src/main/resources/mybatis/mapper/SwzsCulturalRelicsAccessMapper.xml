<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SwzsCulturalRelicsAccessMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SwzsCulturalRelicsAccessDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="cultural_relics_id" jdbcType="VARCHAR" property="culturalRelicsId" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="access_count" jdbcType="BIGINT" property="accessCount" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, cultural_relics_id, `number`, `type`, access_count, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_access
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from swzs_cultural_relics_access
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsAccessDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics_access (company_id, cultural_relics_id, `number`, 
      `type`, access_count, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{culturalRelicsId,jdbcType=VARCHAR}, #{number,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{accessCount,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsAccessDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics_access
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="culturalRelicsId != null">
        cultural_relics_id,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="accessCount != null">
        access_count,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="culturalRelicsId != null">
        #{culturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="accessCount != null">
        #{accessCount,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsAccessDO">
    update swzs_cultural_relics_access
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="culturalRelicsId != null">
        cultural_relics_id = #{culturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="accessCount != null">
        access_count = #{accessCount,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsAccessDO">
    update swzs_cultural_relics_access
    set company_id = #{companyId,jdbcType=BIGINT},
      cultural_relics_id = #{culturalRelicsId,jdbcType=VARCHAR},
      `number` = #{number,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=TINYINT},
      access_count = #{accessCount,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listDO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_access
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="end != null">
      and gmt_create &lt;= #{end,jdbcType=TIMESTAMP}
    </if>
    <if test="start != null">
      and gmt_create &gt;= #{start,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="listByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_access
    where cultural_relics_id in
    <foreach collection="culturalRelicsIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
    <if test="end != null">
      and gmt_create &lt;= #{end,jdbcType=TIMESTAMP}
    </if>
    <if test="start != null">
      and gmt_create &gt;= #{start,jdbcType=TIMESTAMP}
    </if>
  </select>
</mapper>