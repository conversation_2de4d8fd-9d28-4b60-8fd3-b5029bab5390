<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SwzsCulturalRelicsConfigMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SwzsCulturalRelicsConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="panoramic_url" jdbcType="VARCHAR" property="panoramicUrl" />
    <result column="venue_name" jdbcType="VARCHAR" property="venueName" />
    <result column="venue_logo_id" jdbcType="BIGINT" property="venueLogoId" />
    <result column="venue_home_style_url" jdbcType="VARCHAR" property="venueHomeStyleUrl" />
    <result column="venue_home_style_id" jdbcType="VARCHAR" property="venueHomeStyleId" />
    <result column="venue_remark" jdbcType="VARCHAR" property="venueRemark" />
    <result column="venue_picture_id" jdbcType="BIGINT" property="venuePictureId" />
    <result column="venue_phone" jdbcType="VARCHAR" property="venuePhone" />
    <result column="close_day" jdbcType="VARCHAR" property="closeDay" />
    <result column="is_holidays_postpone" jdbcType="TINYINT" property="isHolidaysPostpone" />
    <result column="open_time" jdbcType="TIME" property="openTime" />
    <result column="close_time" jdbcType="TIME" property="closeTime" />
    <result column="stop_time" jdbcType="TIME" property="stopTime" />
    <result column="ad_code" jdbcType="VARCHAR" property="adCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="ad_code_path" jdbcType="VARCHAR" property="adCodePath" />
    <result column="city_name_path" jdbcType="VARCHAR" property="cityNamePath" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="notice" jdbcType="VARCHAR" property="notice" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="left" jdbcType="TINYINT" property="left" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, domain_name, panoramic_url, venue_name, venue_logo_id, venue_home_style_url, 
    venue_home_style_id, venue_remark, venue_picture_id, venue_phone, close_day, is_holidays_postpone, open_time, close_time,
    stop_time, ad_code, city_name, ad_code_path, city_name_path, address, notice, creator,
    modifier, gmt_create, gmt_modified, `left`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from swzs_cultural_relics_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsConfigDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics_config (company_id, domain_name, panoramic_url,
                                             venue_name, venue_logo_id, venue_home_style_url,
                                             venue_home_style_id, venue_remark, venue_picture_id, venue_phone,
                                             close_day, is_holidays_postpone, open_time, close_time,
                                             stop_time, ad_code, city_name,
                                             ad_code_path, city_name_path, address,
                                             notice, creator, modifier,
                                             gmt_create, gmt_modified, `left`
    )
    values (#{companyId,jdbcType=BIGINT}, #{domainName,jdbcType=VARCHAR}, #{panoramicUrl,jdbcType=VARCHAR},
            #{venueName,jdbcType=VARCHAR}, #{venueLogoId,jdbcType=BIGINT}, #{venueHomeStyleUrl,jdbcType=VARCHAR},
            #{venueHomeStyleId,jdbcType=VARCHAR}, #{venueRemark,jdbcType=VARCHAR}, #{venuePictureId,jdbcType=BIGINT},
            #{venuePhone,jdbcType=VARCHAR},
            #{closeDay,jdbcType=VARCHAR}, #{isHolidaysPostpone,jdbcType=TINYINT}, #{openTime,jdbcType=TIME}, #{closeTime,jdbcType=TIME},
            #{stopTime,jdbcType=TIME}, #{adCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR},
            #{adCodePath,jdbcType=VARCHAR}, #{cityNamePath,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
            #{notice,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT},
            #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{left,jdbcType=TINYINT}
           )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsConfigDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="domainName != null">
        domain_name,
      </if>
      <if test="panoramicUrl != null">
        panoramic_url,
      </if>
      <if test="venueName != null">
        venue_name,
      </if>
      <if test="venueLogoId != null">
        venue_logo_id,
      </if>
      <if test="venueHomeStyleUrl != null">
        venue_home_style_url,
      </if>
      <if test="venueHomeStyleId != null">
        venue_home_style_id,
      </if>
      <if test="venueRemark != null">
        venue_remark,
      </if>
      <if test="venuePictureId != null">
        venue_picture_id,
      </if>
      <if test="venuePhone != null">
        venue_phone,
      </if>
      <if test="closeDay != null">
        close_day,
      </if>
      <if test="isHolidaysPostpone != null">
        is_holidays_postpone,
      </if>
      <if test="openTime != null">
        open_time,
      </if>
      <if test="closeTime != null">
        close_time,
      </if>
      <if test="stopTime != null">
        stop_time,
      </if>
      <if test="adCode != null">
        ad_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="adCodePath != null">
        ad_code_path,
      </if>
      <if test="cityNamePath != null">
        city_name_path,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="notice != null">
        notice,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="left != null">
        `left`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="domainName != null">
        #{domainName,jdbcType=VARCHAR},
      </if>
      <if test="panoramicUrl != null">
        #{panoramicUrl,jdbcType=VARCHAR},
      </if>
      <if test="venueName != null">
        #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="venueLogoId != null">
        #{venueLogoId,jdbcType=BIGINT},
      </if>
      <if test="venueHomeStyleUrl != null">
        #{venueHomeStyleUrl,jdbcType=VARCHAR},
      </if>
      <if test="venueHomeStyleId != null">
        #{venueHomeStyleId,jdbcType=VARCHAR},
      </if>
      <if test="venueRemark != null">
        #{venueRemark,jdbcType=VARCHAR},
      </if>
      <if test="venuePictureId != null">
        #{venuePictureId,jdbcType=BIGINT},
      </if>
      <if test="venuePhone != null">
        #{venuePhone,jdbcType=VARCHAR},
      </if>
      <if test="closeDay != null">
        #{closeDay,jdbcType=VARCHAR},
      </if>
      <if test="isHolidaysPostpone != null">
        #{isHolidaysPostpone,jdbcType=TINYINT},
      </if>
      <if test="openTime != null">
        #{openTime,jdbcType=TIME},
      </if>
      <if test="closeTime != null">
        #{closeTime,jdbcType=TIME},
      </if>
      <if test="stopTime != null">
        #{stopTime,jdbcType=TIME},
      </if>
      <if test="adCode != null">
        #{adCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="adCodePath != null">
        #{adCodePath,jdbcType=VARCHAR},
      </if>
      <if test="cityNamePath != null">
        #{cityNamePath,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="notice != null">
        #{notice,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="left != null">
        #{left,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsConfigDO">
    update swzs_cultural_relics_config
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="domainName != null">
        domain_name = #{domainName,jdbcType=VARCHAR},
      </if>
      <if test="panoramicUrl != null">
        panoramic_url = #{panoramicUrl,jdbcType=VARCHAR},
      </if>
      <if test="venueName != null">
        venue_name = #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="venueLogoId != null">
        venue_logo_id = #{venueLogoId,jdbcType=BIGINT},
      </if>
      <if test="venueHomeStyleUrl != null">
        venue_home_style_url = #{venueHomeStyleUrl,jdbcType=VARCHAR},
      </if>
      <if test="venueHomeStyleId != null">
        venue_home_style_id = #{venueHomeStyleId,jdbcType=VARCHAR},
      </if>
      <if test="venueRemark != null">
        venue_remark = #{venueRemark,jdbcType=VARCHAR},
      </if>
      <if test="venuePictureId != null">
        venue_picture_id = #{venuePictureId,jdbcType=BIGINT},
      </if>
      <if test="venuePhone != null">
        venue_phone = #{venuePhone,jdbcType=VARCHAR},
      </if>
      <if test="closeDay != null">
        close_day = #{closeDay,jdbcType=VARCHAR},
      </if>
      <if test="isHolidaysPostpone != null">
        is_holidays_postpone = #{isHolidaysPostpone,jdbcType=TINYINT},
      </if>
      <if test="openTime != null">
        open_time = #{openTime,jdbcType=TIME},
      </if>
      <if test="closeTime != null">
        close_time = #{closeTime,jdbcType=TIME},
      </if>
      <if test="stopTime != null">
        stop_time = #{stopTime,jdbcType=TIME},
      </if>
      <if test="adCode != null">
        ad_code = #{adCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="adCodePath != null">
        ad_code_path = #{adCodePath,jdbcType=VARCHAR},
      </if>
      <if test="cityNamePath != null">
        city_name_path = #{cityNamePath,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="notice != null">
        notice = #{notice,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="left != null">
        `left` = #{left,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsConfigDO">
    update swzs_cultural_relics_config
    set company_id = #{companyId,jdbcType=BIGINT},
        domain_name = #{domainName,jdbcType=VARCHAR},
        panoramic_url = #{panoramicUrl,jdbcType=VARCHAR},
        venue_name = #{venueName,jdbcType=VARCHAR},
        venue_logo_id = #{venueLogoId,jdbcType=BIGINT},
        venue_home_style_url = #{venueHomeStyleUrl,jdbcType=VARCHAR},
        venue_home_style_id = #{venueHomeStyleId,jdbcType=VARCHAR},
        venue_remark = #{venueRemark,jdbcType=VARCHAR},
        venue_picture_id = #{venuePictureId,jdbcType=BIGINT},
        venue_phone = #{venuePhone,jdbcType=VARCHAR},
        close_day = #{closeDay,jdbcType=VARCHAR},
        is_holidays_postpone = #{isHolidaysPostpone,jdbcType=TINYINT},
        open_time = #{openTime,jdbcType=TIME},
        close_time = #{closeTime,jdbcType=TIME},
        stop_time = #{stopTime,jdbcType=TIME},
        ad_code = #{adCode,jdbcType=VARCHAR},
        city_name = #{cityName,jdbcType=VARCHAR},
        ad_code_path = #{adCodePath,jdbcType=VARCHAR},
        city_name_path = #{cityNamePath,jdbcType=VARCHAR},
        address = #{address,jdbcType=VARCHAR},
        notice = #{notice,jdbcType=VARCHAR},
        creator = #{creator,jdbcType=BIGINT},
        modifier = #{modifier,jdbcType=BIGINT},
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
        `left` = #{left,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectInfoOnlyOne" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_config where company_id = #{companyId,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_config
  </select>
  <update id="updateByCompanyIdSelective" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsConfigDO">
    update swzs_cultural_relics_config
    <set>
      <if test="domainName != null">
        domain_name = #{domainName,jdbcType=VARCHAR},
      </if>
      <if test="panoramicUrl != null">
        panoramic_url = #{panoramicUrl,jdbcType=VARCHAR},
      </if>
      <if test="venueName != null">
        venue_name = #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="venueLogoId != null">
        venue_logo_id = #{venueLogoId,jdbcType=BIGINT},
      </if>
      <if test="venueHomeStyleUrl != null">
        venue_home_style_url = #{venueHomeStyleUrl,jdbcType=VARCHAR},
      </if>
      <if test="venueHomeStyleId != null">
        venue_home_style_id = #{venueHomeStyleId,jdbcType=VARCHAR},
      </if>
      <if test="venueRemark != null">
        venue_remark = #{venueRemark,jdbcType=VARCHAR},
      </if>
      <if test="venuePhone != null">
        venue_phone = #{venuePhone,jdbcType=VARCHAR},
      </if>
      <if test="venuePictureId != null">
        venue_picture_id = #{venuePictureId,jdbcType=BIGINT},
      </if>
      <if test="closeDay != null">
        close_day = #{closeDay,jdbcType=VARCHAR},
      </if>
      <if test="isHolidaysPostpone != null">
        is_holidays_postpone = #{isHolidaysPostpone,jdbcType=TINYINT},
      </if>
      <if test="openTime != null">
        open_time = #{openTime,jdbcType=TIME},
      </if>
      <if test="closeTime != null">
        close_time = #{closeTime,jdbcType=TIME},
      </if>
      <if test="stopTime != null">
        stop_time = #{stopTime,jdbcType=TIME},
      </if>
      <if test="adCode != null">
        ad_code = #{adCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="adCodePath != null">
        ad_code_path = #{adCodePath,jdbcType=VARCHAR},
      </if>
      <if test="cityNamePath != null">
        city_name_path = #{cityNamePath,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="notice != null">
        notice = #{notice,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="left != null">
        `left` = #{left,jdbcType=TINYINT},
      </if>
    </set>
    where company_id = #{companyId,jdbcType=BIGINT}
  </update>
  
  <select id="getCodeList" resultType="com.das.museum.web.controller.cultural.response.GetCulturalUrlListDTO">
    SELECT
      A.venue_name name,
      A.city_name cityName,
      B.unique_code uniqueCode,
      '' url
    FROM
      swzs_cultural_relics_config A
        INNER JOIN acc_company B ON A.company_id = B.id
        left join swzs_cultural_relics_config_order C on B.id = C.company_id
    order by IFNULL(C.sort_index,1000)
  </select>
</mapper>