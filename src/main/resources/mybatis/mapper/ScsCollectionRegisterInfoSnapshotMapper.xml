<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionRegisterInfoSnapshotMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoSnapshotDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="register_info_id" jdbcType="BIGINT" property="registerInfoId"/>
        <result column="register_no" jdbcType="VARCHAR" property="registerNo"/>
        <result column="rfid" jdbcType="VARCHAR" property="rfid"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="original_no" jdbcType="VARCHAR" property="originalNo"/>
        <result column="original_name" jdbcType="VARCHAR" property="originalName"/>
        <result column="classify" jdbcType="BIGINT" property="classify"/>
        <result column="classify_desc" jdbcType="VARCHAR" property="classifyDesc"/>
        <result column="identify_level" jdbcType="BIGINT" property="identifyLevel"/>
        <result column="category" jdbcType="BIGINT" property="category"/>
        <result column="in_tibetan_age_range" jdbcType="BIGINT" property="inTibetanAgeRange"/>
        <result column="in_tibetan_date" jdbcType="TIMESTAMP" property="inTibetanDate"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="specific_info" jdbcType="VARCHAR" property="specificInfo"/>
        <result column="era" jdbcType="VARCHAR" property="era"/>
        <result column="age" jdbcType="BIGINT" property="age"/>
        <result column="use" jdbcType="VARCHAR" property="use"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="author" jdbcType="VARCHAR" property="author"/>
        <result column="author_biography" jdbcType="VARCHAR" property="authorBiography"/>
        <result column="attachment_desc" jdbcType="VARCHAR" property="attachmentDesc"/>
        <result column="in_cupboard_status" jdbcType="VARCHAR" property="inCupboardStatus"/>
        <result column="submitter" jdbcType="BIGINT" property="submitter"/>
        <result column="submitter_name" jdbcType="VARCHAR" property="submitterName"/>
        <result column="register_status" jdbcType="VARCHAR" property="registerStatus"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, register_info_id, register_no, rfid, `name`, original_no, original_name, classify,
    classify_desc, identify_level, category, in_tibetan_age_range, in_tibetan_date, address, 
    specific_info, era, age, `use`, color, author, author_biography, attachment_desc, 
    in_cupboard_status, submitter, submitter_name, register_status, document_id, creator, 
    modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_info_snapshot
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_register_info_snapshot
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoSnapshotDO" useGeneratedKeys="true">
    insert into scs_collection_register_info_snapshot (company_id, register_info_id, register_no, rfid,
      `name`, original_no, original_name, 
      classify, classify_desc, identify_level, 
      category, in_tibetan_age_range, in_tibetan_date, 
      address, specific_info, era, 
      age, `use`, color, author, 
      author_biography, attachment_desc, in_cupboard_status, 
      submitter, submitter_name, register_status, 
      document_id, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{registerInfoId,jdbcType=BIGINT}, #{registerNo,jdbcType=VARCHAR}, #{rfid,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR}, #{originalNo,jdbcType=VARCHAR}, #{originalName,jdbcType=VARCHAR}, 
      #{classify,jdbcType=BIGINT}, #{classifyDesc,jdbcType=VARCHAR}, #{identifyLevel,jdbcType=BIGINT}, 
      #{category,jdbcType=BIGINT}, #{inTibetanAgeRange,jdbcType=BIGINT}, #{inTibetanDate,jdbcType=TIMESTAMP}, 
      #{address,jdbcType=VARCHAR}, #{specificInfo,jdbcType=VARCHAR}, #{era,jdbcType=VARCHAR}, 
      #{age,jdbcType=BIGINT}, #{use,jdbcType=VARCHAR}, #{color,jdbcType=VARCHAR}, #{author,jdbcType=VARCHAR}, 
      #{authorBiography,jdbcType=VARCHAR}, #{attachmentDesc,jdbcType=VARCHAR}, #{inCupboardStatus,jdbcType=VARCHAR}, 
      #{submitter,jdbcType=BIGINT}, #{submitterName,jdbcType=VARCHAR}, #{registerStatus,jdbcType=VARCHAR}, 
      #{documentId,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoSnapshotDO" useGeneratedKeys="true">
        insert into scs_collection_register_info_snapshot
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="registerInfoId != null">
                register_info_id,
            </if>
            <if test="registerNo != null">
                register_no,
            </if>
            <if test="rfid != null">
                rfid,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="originalNo != null">
                original_no,
            </if>
            <if test="originalName != null">
                original_name,
            </if>
            <if test="classify != null">
                classify,
            </if>
            <if test="classifyDesc != null">
                classify_desc,
            </if>
            <if test="identifyLevel != null">
                identify_level,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="inTibetanAgeRange != null">
                in_tibetan_age_range,
            </if>
            <if test="inTibetanDate != null">
                in_tibetan_date,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="specificInfo != null">
                specific_info,
            </if>
            <if test="era != null">
                era,
            </if>
            <if test="age != null">
                age,
            </if>
            <if test="use != null">
                `use`,
            </if>
            <if test="color != null">
                color,
            </if>
            <if test="author != null">
                author,
            </if>
            <if test="authorBiography != null">
                author_biography,
            </if>
            <if test="attachmentDesc != null">
                attachment_desc,
            </if>
            <if test="inCupboardStatus != null">
                in_cupboard_status,
            </if>
            <if test="submitter != null">
                submitter,
            </if>
            <if test="submitterName != null">
                submitter_name,
            </if>
            <if test="registerStatus != null">
                register_status,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="registerInfoId != null">
                #{registerInfoId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="rfid != null">
                #{rfid,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="originalNo != null">
                #{originalNo,jdbcType=VARCHAR},
            </if>
            <if test="originalName != null">
                #{originalName,jdbcType=VARCHAR},
            </if>
            <if test="classify != null">
                #{classify,jdbcType=BIGINT},
            </if>
            <if test="classifyDesc != null">
                #{classifyDesc,jdbcType=VARCHAR},
            </if>
            <if test="identifyLevel != null">
                #{identifyLevel,jdbcType=BIGINT},
            </if>
            <if test="category != null">
                #{category,jdbcType=BIGINT},
            </if>
            <if test="inTibetanAgeRange != null">
                #{inTibetanAgeRange,jdbcType=BIGINT},
            </if>
            <if test="inTibetanDate != null">
                #{inTibetanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="specificInfo != null">
                #{specificInfo,jdbcType=VARCHAR},
            </if>
            <if test="era != null">
                #{era,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                #{age,jdbcType=BIGINT},
            </if>
            <if test="use != null">
                #{use,jdbcType=VARCHAR},
            </if>
            <if test="color != null">
                #{color,jdbcType=VARCHAR},
            </if>
            <if test="author != null">
                #{author,jdbcType=VARCHAR},
            </if>
            <if test="authorBiography != null">
                #{authorBiography,jdbcType=VARCHAR},
            </if>
            <if test="attachmentDesc != null">
                #{attachmentDesc,jdbcType=VARCHAR},
            </if>
            <if test="inCupboardStatus != null">
                #{inCupboardStatus,jdbcType=VARCHAR},
            </if>
            <if test="submitter != null">
                #{submitter,jdbcType=BIGINT},
            </if>
            <if test="submitterName != null">
                #{submitterName,jdbcType=VARCHAR},
            </if>
            <if test="registerStatus != null">
                #{registerStatus,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoSnapshotDO">
        update scs_collection_register_info_snapshot
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="registerInfoId != null">
                register_info_id = #{registerInfoId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                register_no = #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="rfid != null">
                rfid = #{rfid,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="originalNo != null">
                original_no = #{originalNo,jdbcType=VARCHAR},
            </if>
            <if test="originalName != null">
                original_name = #{originalName,jdbcType=VARCHAR},
            </if>
            <if test="classify != null">
                classify = #{classify,jdbcType=BIGINT},
            </if>
            <if test="classifyDesc != null">
                classify_desc = #{classifyDesc,jdbcType=VARCHAR},
            </if>
            <if test="identifyLevel != null">
                identify_level = #{identifyLevel,jdbcType=BIGINT},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=BIGINT},
            </if>
            <if test="inTibetanAgeRange != null">
                in_tibetan_age_range = #{inTibetanAgeRange,jdbcType=BIGINT},
            </if>
            <if test="inTibetanDate != null">
                in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="specificInfo != null">
                specific_info = #{specificInfo,jdbcType=VARCHAR},
            </if>
            <if test="era != null">
                era = #{era,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                age = #{age,jdbcType=BIGINT},
            </if>
            <if test="use != null">
                `use` = #{use,jdbcType=VARCHAR},
            </if>
            <if test="color != null">
                color = #{color,jdbcType=VARCHAR},
            </if>
            <if test="author != null">
                author = #{author,jdbcType=VARCHAR},
            </if>
            <if test="authorBiography != null">
                author_biography = #{authorBiography,jdbcType=VARCHAR},
            </if>
            <if test="attachmentDesc != null">
                attachment_desc = #{attachmentDesc,jdbcType=VARCHAR},
            </if>
            <if test="inCupboardStatus != null">
                in_cupboard_status = #{inCupboardStatus,jdbcType=VARCHAR},
            </if>
            <if test="submitter != null">
                submitter = #{submitter,jdbcType=BIGINT},
            </if>
            <if test="submitterName != null">
                submitter_name = #{submitterName,jdbcType=VARCHAR},
            </if>
            <if test="registerStatus != null">
                register_status = #{registerStatus,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoSnapshotDO">
    update scs_collection_register_info_snapshot
    set company_id = #{companyId,jdbcType=BIGINT},
      register_info_id = #{registerInfoId,jdbcType=BIGINT},
      register_no = #{registerNo,jdbcType=VARCHAR},
      rfid = #{rfid,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      original_no = #{originalNo,jdbcType=VARCHAR},
      original_name = #{originalName,jdbcType=VARCHAR},
      classify = #{classify,jdbcType=BIGINT},
      classify_desc = #{classifyDesc,jdbcType=VARCHAR},
      identify_level = #{identifyLevel,jdbcType=BIGINT},
      category = #{category,jdbcType=BIGINT},
      in_tibetan_age_range = #{inTibetanAgeRange,jdbcType=BIGINT},
      in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
      address = #{address,jdbcType=VARCHAR},
      specific_info = #{specificInfo,jdbcType=VARCHAR},
      era = #{era,jdbcType=VARCHAR},
      age = #{age,jdbcType=BIGINT},
      `use` = #{use,jdbcType=VARCHAR},
      color = #{color,jdbcType=VARCHAR},
      author = #{author,jdbcType=VARCHAR},
      author_biography = #{authorBiography,jdbcType=VARCHAR},
      attachment_desc = #{attachmentDesc,jdbcType=VARCHAR},
      in_cupboard_status = #{inCupboardStatus,jdbcType=VARCHAR},
      submitter = #{submitter,jdbcType=BIGINT},
      submitter_name = #{submitterName,jdbcType=VARCHAR},
      register_status = #{registerStatus,jdbcType=VARCHAR},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <select id="listByRegisterInfoId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from scs_collection_register_info_snapshot
    where company_id = #{companyId,jdbcType=BIGINT} and register_info_id = #{registerInfoId,jdbcType=BIGINT}
    order by ${sortBy}
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_info_snapshot
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByRegisterInfoId">
    delete from scs_collection_register_info_snapshot
    where company_id = #{companyId,jdbcType=BIGINT} and id = #{id,jdbcType=BIGINT}
    </delete>
</mapper>