<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionLoginImportMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionLoginImportDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="import_status" jdbcType="TINYINT" property="importStatus" />
    <result column="success_num" jdbcType="BIGINT" property="successNum" />
    <result column="failed_num" jdbcType="BIGINT" property="failedNum" />
    <result column="failed_reason" jdbcType="TINYINT" property="failedReason" />
    <result column="result_confirm" jdbcType="TINYINT" property="resultConfirm" />
    <result column="use_time" jdbcType="BIGINT" property="useTime" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, user_id, import_status, success_num, failed_num, failed_reason, result_confirm,
    use_time, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from scs_collection_login_import
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_login_import
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ScsCollectionLoginImportDO" useGeneratedKeys="true">
    insert into scs_collection_login_import (company_id, user_id, import_status, 
      success_num, failed_num, failed_reason, result_confirm,
      use_time, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{importStatus,jdbcType=TINYINT}, 
      #{successNum,jdbcType=BIGINT}, #{failedNum,jdbcType=BIGINT}, , #{failedReason,jdbcType=TINYINT}, #{resultConfirm,jdbcType=TINYINT},
      #{useTime,jdbcType=BIGINT}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ScsCollectionLoginImportDO" useGeneratedKeys="true">
    insert into scs_collection_login_import
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="importStatus != null">
        import_status,
      </if>
      <if test="successNum != null">
        success_num,
      </if>
      <if test="failedNum != null">
        failed_num,
      </if>
      <if test="failedReason != null">
        failed_reason,
      </if>
      <if test="resultConfirm != null">
        result_confirm,
      </if>
      <if test="useTime != null">
        use_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="importStatus != null">
        #{importStatus,jdbcType=TINYINT},
      </if>
      <if test="successNum != null">
        #{successNum,jdbcType=BIGINT},
      </if>
      <if test="failedNum != null">
        #{failedNum,jdbcType=BIGINT},
      </if>
      <if test="failedReason != null">
        #{failedReason,jdbcType=TINYINT},
      </if>
      <if test="resultConfirm != null">
        #{resultConfirm,jdbcType=TINYINT},
      </if>
      <if test="useTime != null">
        #{useTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsCollectionLoginImportDO">
    update scs_collection_login_import
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="importStatus != null">
        import_status = #{importStatus,jdbcType=TINYINT},
      </if>
      <if test="successNum != null">
        success_num = #{successNum,jdbcType=BIGINT},
      </if>
      <if test="failedNum != null">
        failed_num = #{failedNum,jdbcType=BIGINT},
      </if>
      <if test="failedReason != null">
        failed_reason = #{failedReason,jdbcType=TINYINT},
      </if>
      <if test="resultConfirm != null">
        result_confirm = #{resultConfirm,jdbcType=TINYINT},
      </if>
      <if test="useTime != null">
        use_time = #{useTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionLoginImportDO">
    update scs_collection_login_import
    set company_id = #{companyId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      import_status = #{importStatus,jdbcType=TINYINT},
      success_num = #{successNum,jdbcType=BIGINT},
      failed_num = #{failedNum,jdbcType=BIGINT},
      failed_reason = #{failedReason,jdbcType=TINYINT}
      result_confirm = #{resultConfirm,jdbcType=TINYINT},
      use_time = #{useTime,jdbcType=BIGINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByParams" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from scs_collection_login_import
    where
      company_id = #{companyId,jdbcType=BIGINT}
      and user_id = #{userId,jdbcType=BIGINT}
      and result_confirm = #{resultConfirm,jdbcType=TINYINT}
    order by gmt_create desc
    limit 1;
  </select>


  <update id="updateByCompanyId">
    update scs_collection_login_import
    set
      result_confirm = #{resultConfirm,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </update>
</mapper>