<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.CommAuditWorkflowConfigMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.CommAuditWorkflowConfigDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="flow_name" jdbcType="VARCHAR" property="flowName"/>
        <result column="reviewer" jdbcType="VARCHAR" property="reviewer"/>
        <result column="reviewer_name" jdbcType="VARCHAR" property="reviewerName"/>
        <result column="sort_index" jdbcType="TINYINT" property="sortIndex"/>
        <result column="prev_node_id" jdbcType="BIGINT" property="prevNodeId"/>
        <result column="next_node_id" jdbcType="BIGINT" property="nextNodeId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, biz_id, flow_name, reviewer, reviewer_name, sort_index, prev_node_id, next_node_id,
    remark, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_audit_workflow_config
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_audit_workflow_config
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommAuditWorkflowConfigDO" useGeneratedKeys="true">
    insert into comm_audit_workflow_config (company_id, biz_id, flow_name, 
      reviewer, reviewer_name, sort_index, prev_node_id,
      next_node_id, remark, creator, 
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{bizId,jdbcType=BIGINT}, #{flowName,jdbcType=VARCHAR}, 
      #{reviewer,jdbcType=VARCHAR}, #{reviewerName,jdbcType=VARCHAR}, #{sortIndex,jdbcType=TINYINT}, 
      #{prevNodeId,jdbcType=BIGINT}, #{nextNodeId,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT},
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommAuditWorkflowConfigDO" useGeneratedKeys="true">
        insert into comm_audit_workflow_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="bizId != null">
                biz_id,
            </if>
            <if test="flowName != null">
                flow_name,
            </if>
            <if test="reviewer != null">
                reviewer,
            </if>
            <if test="reviewerName != null">
                reviewer_name,
            </if>
            <if test="sortIndex != null">
                sort_index,
            </if>
            <if test="prevNodeId != null">
                prev_node_id,
            </if>
            <if test="nextNodeId != null">
                next_node_id,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="bizId != null">
                #{bizId,jdbcType=BIGINT},
            </if>
            <if test="flowName != null">
                #{flowName,jdbcType=VARCHAR},
            </if>
            <if test="reviewer != null">
                #{reviewer,jdbcType=VARCHAR},
            </if>
            <if test="reviewerName != null">
                #{reviewerName,jdbcType=VARCHAR},
            </if>
            <if test="sortIndex != null">
                #{sortIndex,jdbcType=TINYINT},
            </if>
            <if test="prevNodeId != null">
                #{prevNodeId,jdbcType=BIGINT},
            </if>
            <if test="nextNodeId != null">
                #{nextNodeId,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.CommAuditWorkflowConfigDO">
        update comm_audit_workflow_config
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="bizId != null">
                biz_id = #{bizId,jdbcType=BIGINT},
            </if>
            <if test="flowName != null">
                flow_name = #{flowName,jdbcType=VARCHAR},
            </if>
            <if test="reviewer != null">
                reviewer = #{reviewer,jdbcType=VARCHAR},
            </if>
            <if test="reviewerName != null">
                reviewer_name = #{reviewerName,jdbcType=VARCHAR},
            </if>
            <if test="sortIndex != null">
                sort_index = #{sortIndex,jdbcType=TINYINT},
            </if>
            <if test="prevNodeId != null">
                prev_node_id = #{prevNodeId,jdbcType=BIGINT},
            </if>
            <if test="nextNodeId != null">
                next_node_id = #{nextNodeId,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.CommAuditWorkflowConfigDO">
    update comm_audit_workflow_config
    set company_id = #{companyId,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=BIGINT},
      flow_name = #{flowName,jdbcType=VARCHAR},
      reviewer = #{reviewer,jdbcType=VARCHAR},
      reviewer_name = #{reviewerName,jdbcType=VARCHAR},
      sort_index = #{sortIndex,jdbcType=TINYINT},
      prev_node_id = #{prevNodeId,jdbcType=BIGINT},
      next_node_id = #{nextNodeId,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <select id="listByBizId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_audit_workflow_config
        where company_id = #{companyId,jdbcType=BIGINT} and biz_id = #{bizId,jdbcType=BIGINT}
    </select>

    <select id="listByBizIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_audit_workflow_config
        where company_id = #{companyId,jdbcType=BIGINT} and biz_id in
        <foreach collection="bizIds" item="id" index="index" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_audit_workflow_config
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="selectByBizIdAndFlowName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_audit_workflow_config
        where company_id = #{companyId,jdbcType=BIGINT} and biz_id = #{bizId,jdbcType=BIGINT} and flow_name = #{flowName,jdbcType=VARCHAR}
    </select>

    <delete id="deleteById">
    delete from comm_audit_workflow_config
    where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </delete>

    <update id="updatePrevNodeIsNull">
    update comm_audit_workflow_config
    set prev_node_id = null
    where company_id = #{companyId,jdbcType=BIGINT} and id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateNextNodeIsNull">
    update comm_audit_workflow_config
    set next_node_id = null
    where company_id = #{companyId,jdbcType=BIGINT} and id = #{id,jdbcType=BIGINT}
    </update>
</mapper>