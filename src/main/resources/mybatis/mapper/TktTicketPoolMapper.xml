<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.TktTicketPoolMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.TktTicketPoolDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="ticket_name" jdbcType="VARCHAR" property="ticketName" />
    <result column="ticket_code" jdbcType="VARCHAR" property="ticketCode" />
    <result column="ticket_type" jdbcType="TINYINT" property="ticketType" />
    <result column="ticket_price" jdbcType="BIGINT" property="ticketPrice" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="reserve_notice" jdbcType="VARCHAR" property="reserveNotice" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, department_id, ticket_name, ticket_code, ticket_type, ticket_price, 
    `status`, reserve_notice, remark, is_delete, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tkt_ticket_pool
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tkt_ticket_pool
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketPoolDO" useGeneratedKeys="true">
    insert into tkt_ticket_pool (company_id, department_id, ticket_name, 
      ticket_code, ticket_type, ticket_price, 
      `status`, reserve_notice, remark, 
      is_delete, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, #{ticketName,jdbcType=VARCHAR}, 
      #{ticketCode,jdbcType=VARCHAR}, #{ticketType,jdbcType=TINYINT}, #{ticketPrice,jdbcType=BIGINT}, 
      #{status,jdbcType=TINYINT}, #{reserveNotice,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=TINYINT}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketPoolDO" useGeneratedKeys="true">
    insert into tkt_ticket_pool
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="ticketName != null">
        ticket_name,
      </if>
      <if test="ticketCode != null">
        ticket_code,
      </if>
      <if test="ticketType != null">
        ticket_type,
      </if>
      <if test="ticketPrice != null">
        ticket_price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="reserveNotice != null">
        reserve_notice,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="ticketName != null">
        #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketType != null">
        #{ticketType,jdbcType=TINYINT},
      </if>
      <if test="ticketPrice != null">
        #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="reserveNotice != null">
        #{reserveNotice,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.TktTicketPoolDO">
    update tkt_ticket_pool
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="ticketName != null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        ticket_code = #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketType != null">
        ticket_type = #{ticketType,jdbcType=TINYINT},
      </if>
      <if test="ticketPrice != null">
        ticket_price = #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="reserveNotice != null">
        reserve_notice = #{reserveNotice,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.TktTicketPoolDO">
    update tkt_ticket_pool
    set company_id = #{companyId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      ticket_name = #{ticketName,jdbcType=VARCHAR},
      ticket_code = #{ticketCode,jdbcType=VARCHAR},
      ticket_type = #{ticketType,jdbcType=TINYINT},
      ticket_price = #{ticketPrice,jdbcType=BIGINT},
      `status` = #{status,jdbcType=TINYINT},
      reserve_notice = #{reserveNotice,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=TINYINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateTicketById" parameterType="com.das.museum.infr.dataobject.TktTicketPoolDO">
    update tkt_ticket_pool
    <set>
      <if test="ticketName != null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketType != null">
        ticket_type = #{ticketType,jdbcType=TINYINT},
      </if>
      <if test="ticketPrice != null">
        ticket_price = #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="reserveNotice != null">
        reserve_notice = #{reserveNotice,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
    and  company_id = #{companyId,jdbcType=BIGINT}
  </update>

  <select id="selectTicketPoolDOByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tkt_ticket_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and ticket_name = #{ticketName,jdbcType=VARCHAR}
    and `status` =  1
  </select>

  <select id="selectTicketPoolDOById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tkt_ticket_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and id = #{id,jdbcType=BIGINT}
  </select>

  <update id="updateTicketStatusById">
    update tkt_ticket_pool
    set `status` = 0
    where id = #{id,jdbcType=BIGINT}
    and  company_id = #{companyId,jdbcType=BIGINT}
  </update>

  <select id="listPageByParam" resultType="com.das.museum.infr.dataobject.TktTicketPoolDO">
    select
        A.id id,
        A.company_id companyId,
        A. department_id departmentId,
        A.ticket_name ticketName,
        A.ticket_code ticketCode,
        A.ticket_type ticketType,
        A.ticket_price ticketPrice,
        A.`status` status,
        A.reserve_notice reserveNotice,
        A.remark remark,
        A.is_delete isDelete,
        A.creator creator,
        A.modifier modifier,
        A.gmt_create gmtCreate,
        A.gmt_modified gmtModified
    from tkt_ticket_pool A
    <if test="channelType != null">
      inner join tkt_ticket_channel_pool B
      on A.company_id = B.company_id and A.id = B.ticket_id
      and B.channel_type = #{channelType,jdbcType=TINYINT}
    </if>
    where A.company_id = #{companyId,jdbcType=BIGINT}
    <if test="ticketName != null and ticketName != '' ">
      and A.ticket_name like CONCAT('%',#{ticketName,jdbcType=VARCHAR},'%')
    </if>
    <if test="status != null ">
      and A.status = #{status,jdbcType=TINYINT}
    </if>
    order by A.${sortBy}
  </select>

  <select id="listNoContainTicketIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tkt_ticket_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and `status` = 1
    <if test="ticketIds != null and ticketIds.size > 0 ">
      and id not in
      <foreach collection="ticketIds" open="(" close=")" separator="," item="id" index="index">
        #{id}
      </foreach>
    </if>
    <if test="ticketName != null and ticketName != '' ">
        and ticket_name like CONCAT('%',#{ticketName,jdbcType=VARCHAR},'%')
    </if>
  </select>

  <select id="listByTicketIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tkt_ticket_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and `status` = 1
    <if test="ticketIds != null ">
      and id in
      <foreach collection="ticketIds" open="(" close=")" separator="," item="id" index="index">
        #{id}
      </foreach>
    </if>
  </select>
</mapper>