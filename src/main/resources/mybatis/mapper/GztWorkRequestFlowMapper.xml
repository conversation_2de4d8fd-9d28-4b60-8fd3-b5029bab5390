<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.GztWorkRequestFlowMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.GztWorkRequestFlowDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="apply_type" jdbcType="TINYINT" property="applyType" />
    <result column="flow_level" jdbcType="INTEGER" property="flowLevel" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, apply_type, flow_level, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gzt_work_request_flow
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gzt_work_request_flow
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.GztWorkRequestFlowDO" useGeneratedKeys="true">
    insert into gzt_work_request_flow (company_id, apply_type, flow_level, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{applyType,jdbcType=TINYINT}, #{flowLevel,jdbcType=INTEGER}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.GztWorkRequestFlowDO" useGeneratedKeys="true">
    insert into gzt_work_request_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="applyType != null">
        apply_type,
      </if>
      <if test="flowLevel != null">
        flow_level,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        #{applyType,jdbcType=TINYINT},
      </if>
      <if test="flowLevel != null">
        #{flowLevel,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.GztWorkRequestFlowDO">
    update gzt_work_request_flow
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        apply_type = #{applyType,jdbcType=TINYINT},
      </if>
      <if test="flowLevel != null">
        flow_level = #{flowLevel,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.GztWorkRequestFlowDO">
    update gzt_work_request_flow
    set company_id = #{companyId,jdbcType=BIGINT},
      apply_type = #{applyType,jdbcType=TINYINT},
      flow_level = #{flowLevel,jdbcType=INTEGER},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectListByCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from gzt_work_request_flow
    where company_id = #{companyId,jdbcType=BIGINT}
    order by apply_type
  </select>

  <select id="selectByCompanyIdAndId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from gzt_work_request_flow
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </select>

  <select id="selectByCompanyIdAndApplyType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from gzt_work_request_flow
    where apply_type = #{applyType,jdbcType=TINYINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </select>

  <update id="updateByCompanyIdAndId" parameterType="com.das.museum.infr.dataobject.GztWorkRequestFlowDO">
    update gzt_work_request_flow
    set apply_type = #{applyType,jdbcType=TINYINT},
        flow_level = #{flowLevel,jdbcType=INTEGER},
        creator = #{creator,jdbcType=BIGINT},
        modifier = #{modifier,jdbcType=BIGINT},
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </update>
</mapper>