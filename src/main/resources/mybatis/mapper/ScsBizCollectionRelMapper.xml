<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsBizCollectionRelMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsBizCollectionRelDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, biz_id, biz_type, collection_id, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_biz_collection_rel
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_biz_collection_rel
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsBizCollectionRelDO" useGeneratedKeys="true">
    insert into scs_biz_collection_rel (biz_id, biz_type, collection_id, 
      gmt_create, gmt_modified)
    values (#{bizId,jdbcType=BIGINT}, #{bizType,jdbcType=VARCHAR}, #{collectionId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsBizCollectionRelDO" useGeneratedKeys="true">
        insert into scs_biz_collection_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizId != null">
                biz_id,
            </if>
            <if test="bizType != null">
                biz_type,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bizId != null">
                #{bizId,jdbcType=BIGINT},
            </if>
            <if test="bizType != null">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsBizCollectionRelDO">
        update scs_biz_collection_rel
        <set>
            <if test="bizId != null">
                biz_id = #{bizId,jdbcType=BIGINT},
            </if>
            <if test="bizType != null">
                biz_type = #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsBizCollectionRelDO">
    update scs_biz_collection_rel
    set biz_id = #{bizId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=VARCHAR},
      collection_id = #{collectionId,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <select id="selectByBizTypeAndCollectionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_biz_collection_rel
        where biz_type = #{bizType,jdbcType=VARCHAR} and collection_id = #{collectionId,jdbcType=BIGINT}
    </select>

    <select id="selectByBizTypeAndBIzIdAndCollectionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_biz_collection_rel
        where biz_id = #{bizId,jdbcType=BIGINT} and biz_type = #{bizType,jdbcType=VARCHAR} and collection_id = #{collectionId,jdbcType=BIGINT}
    </select>

    <select id="listByBizIdAndBizType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_biz_collection_rel
        where biz_id = #{bizId,jdbcType=BIGINT} and biz_type = #{bizType,jdbcType=VARCHAR}
    </select>

    <select id="listByBizType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_biz_collection_rel
        where biz_type = #{bizType,jdbcType=VARCHAR}
    </select>

    <select id="listByBizIdsAndBizType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_biz_collection_rel
        where biz_id in
        <foreach collection="bizIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
        and biz_type = #{bizType,jdbcType=VARCHAR}
    </select>

    <select id="listByBizIdsAndBizTypeExclude" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_biz_collection_rel
        where biz_id in
        <foreach collection="bizIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
        and biz_type = #{bizType,jdbcType=VARCHAR}
        <if test="collectionIds != null">
            and collection_id not in
            <foreach collection="collectionIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="listByBizTypeExclude" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_biz_collection_rel
        where biz_type = #{bizType,jdbcType=VARCHAR}
        <if test="collectionIds != null">
            and collection_id not in
            <foreach collection="collectionIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
    </select>

    <delete id="deleteByBizIdAndBizType">
    delete from scs_biz_collection_rel
    where biz_id = #{bizId,jdbcType=BIGINT} and biz_type = #{bizType,jdbcType=VARCHAR}
  </delete>

    <delete id="deleteByBizIdAndBizTypeAndCollectionIds">
        delete from scs_biz_collection_rel
        where biz_id = #{bizId,jdbcType=BIGINT} and biz_type = #{bizType,jdbcType=VARCHAR}
        <if test="collectionIds != null">
            and collection_id in
            <foreach collection="collectionIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
    </delete>
</mapper>