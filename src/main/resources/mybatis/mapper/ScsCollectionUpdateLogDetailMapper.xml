<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionUpdateLogDetailMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionUpdateLogDetailDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="update_log_id" jdbcType="BIGINT" property="updateLogId"/>
        <result column="field" jdbcType="VARCHAR" property="field"/>
        <result column="field_en" jdbcType="VARCHAR" property="fieldEn"/>
        <result column="old_value" jdbcType="VARCHAR" property="oldValue"/>
        <result column="new_value" jdbcType="VARCHAR" property="newValue"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, update_log_id, field, field_en, old_value, new_value, creator, modifier, gmt_create,
    gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_update_log_detail
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_update_log_detail
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionUpdateLogDetailDO" useGeneratedKeys="true">
    insert into scs_collection_update_log_detail (company_id, update_log_id, field, field_en,
      old_value, new_value, creator, 
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{updateLogId,jdbcType=BIGINT}, #{field,jdbcType=VARCHAR}, #{fieldEn,jdbcType=VARCHAR},
      #{oldValue,jdbcType=VARCHAR}, #{newValue,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionUpdateLogDetailDO" useGeneratedKeys="true">
        insert into scs_collection_update_log_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="updateLogId != null">
                update_log_id,
            </if>
            <if test="field != null">
                field,
            </if>
            <if test="fieldEn != null">
                field_en,
            </if>
            <if test="oldValue != null">
                old_value,
            </if>
            <if test="newValue != null">
                new_value,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="updateLogId != null">
                #{updateLogId,jdbcType=BIGINT},
            </if>
            <if test="field != null">
                #{field,jdbcType=VARCHAR},
            </if>
            <if test="fieldEn != null">
                #{fieldEn,jdbcType=VARCHAR},
            </if>
            <if test="oldValue != null">
                #{oldValue,jdbcType=VARCHAR},
            </if>
            <if test="newValue != null">
                #{newValue,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionUpdateLogDetailDO">
        update scs_collection_update_log_detail
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="updateLogId != null">
                update_log_id = #{updateLogId,jdbcType=BIGINT},
            </if>
            <if test="field != null">
                field = #{field,jdbcType=VARCHAR},
            </if>
            <if test="fieldEn != null">
                field_en = #{fieldEn,jdbcType=VARCHAR},
            </if>
            <if test="oldValue != null">
                old_value = #{oldValue,jdbcType=VARCHAR},
            </if>
            <if test="newValue != null">
                new_value = #{newValue,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionUpdateLogDetailDO">
    update scs_collection_update_log_detail
    set company_id = #{companyId,jdbcType=BIGINT},
      update_log_id = #{updateLogId,jdbcType=BIGINT},
      field = #{field,jdbcType=VARCHAR},
      field_en = #{fieldEn,jdbcType=VARCHAR},
      old_value = #{oldValue,jdbcType=VARCHAR},
      new_value = #{newValue,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <insert id="batchInsert" >
        insert into scs_collection_update_log_detail
        (company_id, update_log_id,
        field, field_en, old_value, new_value,
        creator, modifier)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.companyId},
            #{item.updateLogId},
            #{item.field},
            #{item.fieldEn},
            #{item.oldValue},
            #{item.newValue},
            #{item.creator},
            #{item.modifier})
        </foreach>
    </insert>

    <select id="getUpdateLogDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_update_log_detail
        where company_id = #{companyId,jdbcType=BIGINT}
        and update_log_id = #{updateLogId,jdbcType=BIGINT}
    </select>
</mapper>