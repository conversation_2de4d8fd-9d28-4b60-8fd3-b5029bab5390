<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.CommClassificationMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.CommClassificationDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="biz_code" jdbcType="VARCHAR" property="bizCode"/>
        <result column="biz_number" jdbcType="BIGINT" property="bizNumber"/>
        <result column="biz_type" jdbcType="TINYINT" property="bizType"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="level" jdbcType="TINYINT" property="level"/>
        <result column="sort_index" jdbcType="INTEGER" property="sortIndex"/>
        <result column="permission" jdbcType="VARCHAR" property="permission"/>
        <result column="enabled_status" jdbcType="TINYINT" property="enabledStatus"/>
        <result column="leaf_node" jdbcType="TINYINT" property="leafNode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, biz_id, biz_code, biz_number, biz_type, `name`, parent_id, `level`, sort_index,
    permission, enabled_status, leaf_node, remark, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_classification
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.CommClassificationDO" useGeneratedKeys="true">
    insert into comm_classification (company_id, biz_id, biz_code, biz_number,
      biz_type, `name`, parent_id, 
      `level`, sort_index, permission, 
      enabled_status, leaf_node, remark, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{bizId,jdbcType=BIGINT}, #{bizCode,jdbcType=VARCHAR}, #{bizNumber,jdbcType=BIGINT},
      #{bizType,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT}, 
      #{level,jdbcType=TINYINT}, #{sortIndex,jdbcType=INTEGER}, #{permission,jdbcType=VARCHAR},
      #{enabledStatus,jdbcType=TINYINT}, #{leafNode,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.CommClassificationDO" useGeneratedKeys="true">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into comm_classification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="bizId != null">
                biz_id,
            </if>
            <if test="bizCode != null">
                biz_code,
            </if>
            <if test="bizNumber != null">
                biz_number,
            </if>
            <if test="bizType != null">
                biz_type,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="level != null">
                `level`,
            </if>
            <if test="sortIndex != null">
                sort_index,
            </if>
            <if test="permission != null">
                permission,
            </if>
            <if test="enabledStatus != null">
                enabled_status,
            </if>
            <if test="leafNode != null">
                leaf_node,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="bizId != null">
                #{bizId,jdbcType=BIGINT},
            </if>
            <if test="bizCode != null">
                #{bizCode,jdbcType=VARCHAR},
            </if>
            <if test="bizNumber != null">
                #{bizNumber,jdbcType=BIGINT},
            </if>
            <if test="bizType != null">
                #{bizType,jdbcType=TINYINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="level != null">
                #{level,jdbcType=TINYINT},
            </if>
            <if test="sortIndex != null">
                #{sortIndex,jdbcType=INTEGER},
            </if>
            <if test="permission != null">
                #{permission,jdbcType=VARCHAR},
            </if>
            <if test="enabledStatus != null">
                #{enabledStatus,jdbcType=TINYINT},
            </if>
            <if test="leafNode != null">
                #{leafNode,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.CommClassificationDO">
        update comm_classification
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="bizId != null">
                biz_id = #{bizId,jdbcType=BIGINT},
            </if>
            <if test="bizCode != null">
                biz_code = #{bizCode,jdbcType=VARCHAR},
            </if>
            <if test="bizNumber != null">
                biz_number = #{bizNumber,jdbcType=BIGINT},
            </if>
            <if test="bizType != null">
                biz_type = #{bizType,jdbcType=TINYINT},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="level != null">
                `level` = #{level,jdbcType=TINYINT},
            </if>
            <if test="sortIndex != null">
                sort_index = #{sortIndex,jdbcType=INTEGER},
            </if>
            <if test="permission != null">
                permission = #{permission,jdbcType=VARCHAR},
            </if>
            <if test="enabledStatus != null">
                enabled_status = #{enabledStatus,jdbcType=TINYINT},
            </if>
            <if test="leafNode != null">
                leaf_node = #{leafNode,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.CommClassificationDO">
    update comm_classification
    set company_id = #{companyId,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=BIGINT},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      biz_umber = #{bizNumber,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=TINYINT},
      `name` = #{name,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=BIGINT},
      `level` = #{level,jdbcType=TINYINT},
      sort_index = #{sortIndex,jdbcType=INTEGER},
      permission = #{permission,jdbcType=VARCHAR},
      enabled_status = #{enabledStatus,jdbcType=TINYINT},
      leaf_node = #{leafNode,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <!--  ===========================>自定义方法===========================  -->
    <select id="listByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where company_id = #{companyId,jdbcType=BIGINT}
        <if test="ids != null and ids.size() > 0 ">
            and id in
            <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="selectRootByCompanyIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where company_id = #{companyId,jdbcType=BIGINT} and biz_type = #{bizType,jdbcType=TINYINT}
        and parent_id is null
        <if test="bizId != null">
            and biz_id = #{bizId, jdbcType=BIGINT}
        </if>
        <if test="bizCode != null">
            and biz_code = #{bizCode, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectAllChildById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where company_id = #{companyId,jdbcType=BIGINT} and biz_type = #{bizType,jdbcType=TINYINT}
        <if test="bizId != null">
            and biz_id = #{bizId, jdbcType=BIGINT}
        </if>
        <if test="bizCode != null">
            and biz_code = #{bizCode, jdbcType=VARCHAR}
        </if>
        order by sort_index
    </select>

    <select id="selectIncludeChildById" resultMap="BaseResultMap">
    select
    c.*
    from comm_classification as c join comm_classification_item_rel as r on c.id = r.descendant_id
    where c.company_id = #{companyId,jdbcType=BIGINT} and r.ancestor_id= #{id,jdbcType=INTEGER} and
    r.distance <![CDATA[ >= ]]> 0
  </select>

    <select id="selectChildById" resultMap="BaseResultMap">
    select
    c.*
    from comm_classification as c join comm_classification_item_rel as r on c.id = r.descendant_id
    where c.company_id = #{companyId,jdbcType=BIGINT} and r.ancestor_id= #{id,jdbcType=INTEGER}
    and r.distance <![CDATA[ > ]]> 0;
  </select>

    <select id="selectChildByParentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where company_id = #{companyId,jdbcType=BIGINT} and parent_id = #{parentId,jdbcType=BIGINT}
    </select>

    <select id="selectByParentIdAndName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where company_id = #{companyId,jdbcType=BIGINT} and parent_id = #{parentId,jdbcType=BIGINT} and `name` =
        #{name,jdbcType=VARCHAR}
    </select>

    <select id="selectIncludeParentById" resultMap="BaseResultMap">
    select
    c.*
    from comm_classification as c join comm_classification_item_rel as r on c.id = r.ancestor_id
    where c.company_id = #{companyId,jdbcType=BIGINT} and r.descendant_id= #{id,jdbcType=INTEGER}
     and r.distance <![CDATA[ >= ]]> 0 order by r.distance desc;
  </select>

    <select id="selectParentById" resultMap="BaseResultMap">
    select
    c.*
    from comm_classification as c join comm_classification_item_rel as r on c.id = r.ancestor_id
    where c.company_id = #{companyId,jdbcType=BIGINT} and r.descendant_id= #{id,jdbcType=INTEGER}
     and r.distance <![CDATA[ > ]]> 0 order by r.distance desc;
  </select>

    <select id="selectByTypeAndParentIdAndName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where company_id = #{companyId,jdbcType=BIGINT} and biz_type = #{bizType,jdbcType=TINYINT}
        and `name` = #{name,jdbcType=VARCHAR}
        <if test="parentId != null">
            and parent_id = #{parentId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByBizCodeAndBizType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where company_id = #{companyId,jdbcType=BIGINT} and biz_type = #{bizType,jdbcType=TINYINT}
        and biz_code = #{bizCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByTypeAndNameAndLevel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where company_id = #{companyId,jdbcType=BIGINT} and biz_type = #{bizType,jdbcType=TINYINT}
        and `name` = #{name,jdbcType=VARCHAR} and `level` = #{level,jdbcType=INTEGER}
    </select>

    <delete id="deleteBatchById">
        delete from comm_classification
        where company_id = #{companyId,jdbcType=BIGINT} and id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <select id="selectByTypeAndLevel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where company_id = #{companyId,jdbcType=BIGINT} and biz_type = #{bizType,jdbcType=TINYINT}
        and `level` = #{level,jdbcType=INTEGER}
    </select>

    <select id="selectBybizCodeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where company_id = #{companyId,jdbcType=BIGINT}
        and biz_type = #{bizType,jdbcType=TINYINT}
        and biz_code in
        <foreach collection="bizCodes" item="bizCode" index="index" open="(" close=")" separator=",">
            #{bizCode,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectByParentAndName" resultMap="BaseResultMap">
        select
            c.*
        from comm_classification as c join comm_classification_item_rel as r on c.id = r.descendant_id
        where c.company_id = #{companyId,jdbcType=BIGINT} and r.ancestor_id= #{id,jdbcType=INTEGER}
          and r.distance <![CDATA[ < ]]> 3
          and r.distance > 0
          and c.`name` like concat('%', #{name, jdbcType=VARCHAR}, '%')
        order by r.distance desc;
    </select>

    <update id="updateEnableStatusByIds" >
        update comm_classification
        <set>
            <if test="enabledStatus != null">
                enabled_status = #{enabledStatus,jdbcType=TINYINT},
            </if>
            <if test="userId != null">
                modifier = #{userId,jdbcType=BIGINT},
            </if>
        </set>
        where company_id = #{companyId,jdbcType=BIGINT}
        and id in
        <foreach collection="classificationIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </update>
    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification
        where id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>
</mapper>