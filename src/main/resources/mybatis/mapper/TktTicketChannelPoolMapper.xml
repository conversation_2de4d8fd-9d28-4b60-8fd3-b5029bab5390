<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.TktTicketChannelPoolMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.TktTicketChannelPoolDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="ticket_id" jdbcType="BIGINT" property="ticketId" />
    <result column="ticket_name" jdbcType="VARCHAR" property="ticketName" />
    <result column="ticket_code" jdbcType="VARCHAR" property="ticketCode" />
    <result column="ticket_type" jdbcType="TINYINT" property="ticketType" />
    <result column="ticket_price" jdbcType="BIGINT" property="ticketPrice" />
    <result column="ticket_status" jdbcType="TINYINT" property="ticketStatus" />
    <result column="reserve_notice" jdbcType="VARCHAR" property="reserveNotice" />
    <result column="ticket_remark" jdbcType="VARCHAR" property="ticketRemark" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="put_status" jdbcType="TINYINT" property="putStatus" />
    <result column="put_date" jdbcType="TIMESTAMP" property="putDate" />
    <result column="put_valid_type" jdbcType="TINYINT" property="putValidType" />
    <result column="put_start_date" jdbcType="TIMESTAMP" property="putStartDate" />
    <result column="put_end_date" jdbcType="TIMESTAMP" property="putEndDate" />
    <result column="put_rule_config" jdbcType="VARCHAR" property="putRuleConfig" />
    <result column="put_remark" jdbcType="VARCHAR" property="putRemark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, department_id, ticket_id, ticket_name, ticket_code, ticket_type, 
    ticket_price, ticket_status, reserve_notice, ticket_remark, channel_type, put_status, 
    put_date, put_valid_type, put_start_date, put_end_date, put_rule_config, put_remark, 
    creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tkt_ticket_channel_pool
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tkt_ticket_channel_pool
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketChannelPoolDO" useGeneratedKeys="true">
    insert into tkt_ticket_channel_pool (company_id, department_id, ticket_id, 
      ticket_name, ticket_code, ticket_type, 
      ticket_price, ticket_status, reserve_notice, 
      ticket_remark, channel_type, put_status, 
      put_date, put_valid_type, put_start_date, 
      put_end_date, put_rule_config, put_remark, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, #{ticketId,jdbcType=BIGINT}, 
      #{ticketName,jdbcType=VARCHAR}, #{ticketCode,jdbcType=VARCHAR}, #{ticketType,jdbcType=TINYINT}, 
      #{ticketPrice,jdbcType=BIGINT}, #{ticketStatus,jdbcType=TINYINT}, #{reserveNotice,jdbcType=VARCHAR}, 
      #{ticketRemark,jdbcType=VARCHAR}, #{channelType,jdbcType=TINYINT}, #{putStatus,jdbcType=TINYINT}, 
      #{putDate,jdbcType=TIMESTAMP}, #{putValidType,jdbcType=TINYINT}, #{putStartDate,jdbcType=TIMESTAMP}, 
      #{putEndDate,jdbcType=TIMESTAMP}, #{putRuleConfig,jdbcType=VARCHAR}, #{putRemark,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketChannelPoolDO" useGeneratedKeys="true">
    insert into tkt_ticket_channel_pool
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="ticketId != null">
        ticket_id,
      </if>
      <if test="ticketName != null">
        ticket_name,
      </if>
      <if test="ticketCode != null">
        ticket_code,
      </if>
      <if test="ticketType != null">
        ticket_type,
      </if>
      <if test="ticketPrice != null">
        ticket_price,
      </if>
      <if test="ticketStatus != null">
        ticket_status,
      </if>
      <if test="reserveNotice != null">
        reserve_notice,
      </if>
      <if test="ticketRemark != null">
        ticket_remark,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="putStatus != null">
        put_status,
      </if>
      <if test="putDate != null">
        put_date,
      </if>
      <if test="putValidType != null">
        put_valid_type,
      </if>
      <if test="putStartDate != null">
        put_start_date,
      </if>
      <if test="putEndDate != null">
        put_end_date,
      </if>
      <if test="putRuleConfig != null">
        put_rule_config,
      </if>
      <if test="putRemark != null">
        put_remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="ticketId != null">
        #{ticketId,jdbcType=BIGINT},
      </if>
      <if test="ticketName != null">
        #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketType != null">
        #{ticketType,jdbcType=TINYINT},
      </if>
      <if test="ticketPrice != null">
        #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="ticketStatus != null">
        #{ticketStatus,jdbcType=TINYINT},
      </if>
      <if test="reserveNotice != null">
        #{reserveNotice,jdbcType=VARCHAR},
      </if>
      <if test="ticketRemark != null">
        #{ticketRemark,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=TINYINT},
      </if>
      <if test="putStatus != null">
        #{putStatus,jdbcType=TINYINT},
      </if>
      <if test="putDate != null">
        #{putDate,jdbcType=TIMESTAMP},
      </if>
      <if test="putValidType != null">
        #{putValidType,jdbcType=TINYINT},
      </if>
      <if test="putStartDate != null">
        #{putStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="putEndDate != null">
        #{putEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="putRuleConfig != null">
        #{putRuleConfig,jdbcType=VARCHAR},
      </if>
      <if test="putRemark != null">
        #{putRemark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.TktTicketChannelPoolDO">
    update tkt_ticket_channel_pool
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="ticketId != null">
        ticket_id = #{ticketId,jdbcType=BIGINT},
      </if>
      <if test="ticketName != null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        ticket_code = #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketType != null">
        ticket_type = #{ticketType,jdbcType=TINYINT},
      </if>
      <if test="ticketPrice != null">
        ticket_price = #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="ticketStatus != null">
        ticket_status = #{ticketStatus,jdbcType=TINYINT},
      </if>
      <if test="reserveNotice != null">
        reserve_notice = #{reserveNotice,jdbcType=VARCHAR},
      </if>
      <if test="ticketRemark != null">
        ticket_remark = #{ticketRemark,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=TINYINT},
      </if>
      <if test="putStatus != null">
        put_status = #{putStatus,jdbcType=TINYINT},
      </if>
      <if test="putDate != null">
        put_date = #{putDate,jdbcType=TIMESTAMP},
      </if>
      <if test="putValidType != null">
        put_valid_type = #{putValidType,jdbcType=TINYINT},
      </if>
      <if test="putStartDate != null">
        put_start_date = #{putStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="putEndDate != null">
        put_end_date = #{putEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="putRuleConfig != null">
        put_rule_config = #{putRuleConfig,jdbcType=VARCHAR},
      </if>
      <if test="putRemark != null">
        put_remark = #{putRemark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.TktTicketChannelPoolDO">
    update tkt_ticket_channel_pool
    set company_id = #{companyId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      ticket_id = #{ticketId,jdbcType=BIGINT},
      ticket_name = #{ticketName,jdbcType=VARCHAR},
      ticket_code = #{ticketCode,jdbcType=VARCHAR},
      ticket_type = #{ticketType,jdbcType=TINYINT},
      ticket_price = #{ticketPrice,jdbcType=BIGINT},
      ticket_status = #{ticketStatus,jdbcType=TINYINT},
      reserve_notice = #{reserveNotice,jdbcType=VARCHAR},
      ticket_remark = #{ticketRemark,jdbcType=VARCHAR},
      channel_type = #{channelType,jdbcType=TINYINT},
      put_status = #{putStatus,jdbcType=TINYINT},
      put_date = #{putDate,jdbcType=TIMESTAMP},
      put_valid_type = #{putValidType,jdbcType=TINYINT},
      put_start_date = #{putStartDate,jdbcType=TIMESTAMP},
      put_end_date = #{putEndDate,jdbcType=TIMESTAMP},
      put_rule_config = #{putRuleConfig,jdbcType=VARCHAR},
      put_remark = #{putRemark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateTicketChannelPoolById" parameterType="com.das.museum.infr.dataobject.TktTicketChannelPoolDO">
    update tkt_ticket_channel_pool
    <set>
      <if test="ticketName != null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketType != null">
        ticket_type = #{ticketType,jdbcType=TINYINT},
      </if>
      <if test="ticketPrice != null">
        ticket_price = #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="ticketStatus != null">
        ticket_status = #{ticketStatus,jdbcType=TINYINT},
      </if>
      <if test="reserveNotice != null">
        reserve_notice = #{reserveNotice,jdbcType=VARCHAR},
      </if>
      <if test="ticketRemark != null">
        ticket_remark = #{ticketRemark,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=TINYINT},
      </if>
      <if test="putStatus != null">
        put_status = #{putStatus,jdbcType=TINYINT},
      </if>
      <if test="putDate != null">
        put_date = #{putDate,jdbcType=TIMESTAMP},
      </if>
      <if test="putValidType != null">
        put_valid_type = #{putValidType,jdbcType=TINYINT},
      </if>
      <if test="putStartDate != null">
        put_start_date = #{putStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="putEndDate != null">
        put_end_date = #{putEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="putRuleConfig != null">
        put_rule_config = #{putRuleConfig,jdbcType=VARCHAR},
      </if>
      <if test="putRemark != null">
        put_remark = #{putRemark,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where company_id = #{companyId,jdbcType=BIGINT}
    and id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectChannelsByTicketId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tkt_ticket_channel_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and ticket_id = #{ticketId,jdbcType=BIGINT}
  </select>

  <update id="updateTicketChannelStatusByTicketId">
    update tkt_ticket_channel_pool
    set ticket_status = 0
    where company_id = #{companyId,jdbcType=BIGINT}
    and ticket_id = #{ticketId,jdbcType=BIGINT}
  </update>

  <select id="selectChannelsByTickets" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tkt_ticket_channel_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and ticket_id in
    <foreach collection="ticketIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="selectChannelList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tkt_ticket_channel_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and channel_type = #{channelType,jdbcType=TINYINT}
    and ticket_status = 1
  </select>

  <select id="selectChannelListByChannelType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tkt_ticket_channel_pool
    where channel_type = #{channelType,jdbcType=TINYINT}
    and ticket_status = 1
    and put_valid_type = 1
    and put_status != 2
  </select>

  <select id="selectByIdAndCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tkt_ticket_channel_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByIdsAndCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tkt_ticket_channel_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <delete id="deleteById">
    delete from tkt_ticket_channel_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and id = #{id,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByTicketId">
    delete from tkt_ticket_channel_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and ticket_id = #{ticketId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByIds">
    delete from tkt_ticket_channel_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </delete>

  <select id="selectPageListByParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tkt_ticket_channel_pool
    where company_id = #{companyId,jdbcType=BIGINT}
    and ticket_status = 1
    <if test="channelType != null ">
      and channel_type = #{channelType,jdbcType=TINYINT}
    </if>
    <if test="ticketName != null and ticketName != '' ">
      and ticket_name like CONCAT('%',#{ticketName,jdbcType=VARCHAR},'%')
    </if>
    <if test="putStatus != null">
      and put_status = #{putStatus,jdbcType=TINYINT}
    </if>
    order by ${sortBy}
  </select>
</mapper>