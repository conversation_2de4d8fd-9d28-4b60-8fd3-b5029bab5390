<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.AccRolePermissionRelMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.AccRolePermissionRelDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="permission_code" jdbcType="VARCHAR" property="permissionCode"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , company_id, role_id, permission_code, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_role_permission_rel
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from acc_role_permission_rel
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.AccRolePermissionRelDO" useGeneratedKeys="true">
        insert into acc_role_permission_rel (company_id, role_id, permission_code,
                                             creator, modifier, gmt_create,
                                             gmt_modified)
        values (#{companyId,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT}, #{permissionCode,jdbcType=VARCHAR},
                #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.AccRolePermissionRelDO" useGeneratedKeys="true">
        insert into acc_role_permission_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="roleId != null">
                role_id,
            </if>
            <if test="permissionCode != null">
                permission_code,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
            <if test="permissionCode != null">
                #{permissionCode,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.AccRolePermissionRelDO">
        update acc_role_permission_rel
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
                role_id = #{roleId,jdbcType=BIGINT},
            </if>
            <if test="permissionCode != null">
                permission_code = #{permissionCode,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.AccRolePermissionRelDO">
        update acc_role_permission_rel
        set company_id      = #{companyId,jdbcType=BIGINT},
            role_id         = #{roleId,jdbcType=BIGINT},
            permission_code = #{permissionCode,jdbcType=VARCHAR},
            creator         = #{creator,jdbcType=BIGINT},
            modifier        = #{modifier,jdbcType=BIGINT},
            gmt_create      = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified    = #{gmtModified,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->
    <select id="listByRoleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_role_permission_rel
        where company_id = #{companyId,jdbcType=BIGINT} and role_id = #{roleId,jdbcType=BIGINT}
    </select>

    <select id="listByRoleIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_role_permission_rel
        where role_id in
        <foreach collection="roleIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
        <if test="companyId != null">
            and company_id = #{companyId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="listByPermissionCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_role_permission_rel
        where role_id in ( select id from acc_role where is_delete = 0 )
        and permission_code in
        <foreach collection="permissionCodes" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <delete id="deleteByRoleId">
        delete
        from acc_role_permission_rel
        where company_id = #{companyId,jdbcType=BIGINT} and role_id = #{roleId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByRoleIds">
        delete
        from acc_role_permission_rel
        where role_id in
        <foreach collection="roleIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </delete>
</mapper>