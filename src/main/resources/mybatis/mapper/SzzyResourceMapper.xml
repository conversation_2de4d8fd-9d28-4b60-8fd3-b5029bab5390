<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SzzyResourceMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SzzyResourceDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="resource_pool_code" jdbcType="VARCHAR" property="resourcePoolCode" />
    <result column="resource_code" jdbcType="VARCHAR" property="resourceCode" />
    <result column="resource_name" jdbcType="VARCHAR" property="resourceName" />
    <result column="resource_type" jdbcType="TINYINT" property="resourceType" />
    <result column="resource_keyword" jdbcType="VARCHAR" property="resourceKeyword" />
    <result column="secret_level" jdbcType="TINYINT" property="secretLevel" />
    <result column="resource_uri_id" jdbcType="VARCHAR" property="resourceUriId" />
    <result column="resource_uri_name" jdbcType="VARCHAR" property="resourceUriName" />
    <result column="resource_url" jdbcType="VARCHAR" property="resourceUrl" />
    <result column="resource_route" jdbcType="VARCHAR" property="resourceRoute" />
    <result column="build_id" jdbcType="VARCHAR" property="buildId" />
    <result column="build_count" jdbcType="BIGINT" property="buildCount" />
    <result column="favorite_count" jdbcType="BIGINT" property="favoriteCount" />
    <result column="like_count" jdbcType="BIGINT" property="likeCount" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.das.museum.infr.dataobject.SzzyResourceDOWithBLOBs">
    <result column="resource_body" jdbcType="LONGVARCHAR" property="resourceBody" />
    <result column="front_body" jdbcType="LONGVARCHAR" property="frontBody" />
    <result column="document_id" jdbcType="LONGVARCHAR" property="documentId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, parent_id, resource_pool_code, resource_code, resource_name, resource_type, 
    resource_keyword, secret_level, resource_uri_id, resource_uri_name, resource_url, 
    resource_route, build_id, build_count, favorite_count, like_count, is_delete, remark, 
    creator, modifier, gmt_create, gmt_modified
  </sql>
  <sql id="Blob_Column_List">
    resource_body, front_body, document_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from szzy_resource
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szzy_resource
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyResourceDOWithBLOBs" useGeneratedKeys="true">
    insert into szzy_resource (company_id, parent_id, resource_pool_code, 
      resource_code, resource_name, resource_type, 
      resource_keyword, secret_level, resource_uri_id, 
      resource_uri_name, resource_url, resource_route, 
      build_id, build_count, favorite_count, 
      like_count, is_delete, remark, 
      creator, modifier, gmt_create, 
      gmt_modified, resource_body, front_body, 
      document_id)
    values (#{companyId,jdbcType=BIGINT}, #{parentId,jdbcType=BIGINT}, #{resourcePoolCode,jdbcType=VARCHAR}, 
      #{resourceCode,jdbcType=VARCHAR}, #{resourceName,jdbcType=VARCHAR}, #{resourceType,jdbcType=TINYINT}, 
      #{resourceKeyword,jdbcType=VARCHAR}, #{secretLevel,jdbcType=TINYINT}, #{resourceUriId,jdbcType=VARCHAR}, 
      #{resourceUriName,jdbcType=VARCHAR}, #{resourceUrl,jdbcType=VARCHAR}, #{resourceRoute,jdbcType=VARCHAR}, 
      #{buildId,jdbcType=VARCHAR}, #{buildCount,jdbcType=BIGINT}, #{favoriteCount,jdbcType=BIGINT}, 
      #{likeCount,jdbcType=BIGINT}, #{isDelete,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{resourceBody,jdbcType=LONGVARCHAR}, #{frontBody,jdbcType=LONGVARCHAR}, 
      #{documentId,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyResourceDOWithBLOBs" useGeneratedKeys="true">
    insert into szzy_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="resourcePoolCode != null">
        resource_pool_code,
      </if>
      <if test="resourceCode != null">
        resource_code,
      </if>
      <if test="resourceName != null">
        resource_name,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="resourceKeyword != null">
        resource_keyword,
      </if>
      <if test="secretLevel != null">
        secret_level,
      </if>
      <if test="resourceUriId != null">
        resource_uri_id,
      </if>
      <if test="resourceUriName != null">
        resource_uri_name,
      </if>
      <if test="resourceUrl != null">
        resource_url,
      </if>
      <if test="resourceRoute != null">
        resource_route,
      </if>
      <if test="buildId != null">
        build_id,
      </if>
      <if test="buildCount != null">
        build_count,
      </if>
      <if test="favoriteCount != null">
        favorite_count,
      </if>
      <if test="likeCount != null">
        like_count,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="resourceBody != null">
        resource_body,
      </if>
      <if test="frontBody != null">
        front_body,
      </if>
      <if test="documentId != null">
        document_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="resourcePoolCode != null">
        #{resourcePoolCode,jdbcType=VARCHAR},
      </if>
      <if test="resourceCode != null">
        #{resourceCode,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=TINYINT},
      </if>
      <if test="resourceKeyword != null">
        #{resourceKeyword,jdbcType=VARCHAR},
      </if>
      <if test="secretLevel != null">
        #{secretLevel,jdbcType=TINYINT},
      </if>
      <if test="resourceUriId != null">
        #{resourceUriId,jdbcType=VARCHAR},
      </if>
      <if test="resourceUriName != null">
        #{resourceUriName,jdbcType=VARCHAR},
      </if>
      <if test="resourceUrl != null">
        #{resourceUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceRoute != null">
        #{resourceRoute,jdbcType=VARCHAR},
      </if>
      <if test="buildId != null">
        #{buildId,jdbcType=VARCHAR},
      </if>
      <if test="buildCount != null">
        #{buildCount,jdbcType=BIGINT},
      </if>
      <if test="favoriteCount != null">
        #{favoriteCount,jdbcType=BIGINT},
      </if>
      <if test="likeCount != null">
        #{likeCount,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="resourceBody != null">
        #{resourceBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="frontBody != null">
        #{frontBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="documentId != null">
        #{documentId,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SzzyResourceDOWithBLOBs">
    update szzy_resource
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="resourcePoolCode != null">
        resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR},
      </if>
      <if test="resourceCode != null">
        resource_code = #{resourceCode,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        resource_name = #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=TINYINT},
      </if>
      <if test="resourceKeyword != null">
        resource_keyword = #{resourceKeyword,jdbcType=VARCHAR},
      </if>
      <if test="secretLevel != null">
        secret_level = #{secretLevel,jdbcType=TINYINT},
      </if>
      <if test="resourceUriId != null">
        resource_uri_id = #{resourceUriId,jdbcType=VARCHAR},
      </if>
      <if test="resourceUriName != null">
        resource_uri_name = #{resourceUriName,jdbcType=VARCHAR},
      </if>
      <if test="resourceUrl != null">
        resource_url = #{resourceUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceRoute != null">
        resource_route = #{resourceRoute,jdbcType=VARCHAR},
      </if>
      <if test="buildId != null">
        build_id = #{buildId,jdbcType=VARCHAR},
      </if>
      <if test="buildCount != null">
        build_count = #{buildCount,jdbcType=BIGINT},
      </if>
      <if test="favoriteCount != null">
        favorite_count = #{favoriteCount,jdbcType=BIGINT},
      </if>
      <if test="likeCount != null">
        like_count = #{likeCount,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="resourceBody != null">
        resource_body = #{resourceBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="frontBody != null">
        front_body = #{frontBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="documentId != null">
        document_id = #{documentId,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.das.museum.infr.dataobject.SzzyResourceDOWithBLOBs">
    update szzy_resource
    set company_id = #{companyId,jdbcType=BIGINT},
      parent_id = #{parentId,jdbcType=BIGINT},
      resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR},
      resource_code = #{resourceCode,jdbcType=VARCHAR},
      resource_name = #{resourceName,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=TINYINT},
      resource_keyword = #{resourceKeyword,jdbcType=VARCHAR},
      secret_level = #{secretLevel,jdbcType=TINYINT},
      resource_uri_id = #{resourceUriId,jdbcType=VARCHAR},
      resource_uri_name = #{resourceUriName,jdbcType=VARCHAR},
      resource_url = #{resourceUrl,jdbcType=VARCHAR},
      resource_route = #{resourceRoute,jdbcType=VARCHAR},
      build_id = #{buildId,jdbcType=VARCHAR},
      build_count = #{buildCount,jdbcType=BIGINT},
      favorite_count = #{favoriteCount,jdbcType=BIGINT},
      like_count = #{likeCount,jdbcType=BIGINT},
      is_delete = #{isDelete,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      resource_body = #{resourceBody,jdbcType=LONGVARCHAR},
      front_body = #{frontBody,jdbcType=LONGVARCHAR},
      document_id = #{documentId,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SzzyResourceDO">
    update szzy_resource
    set company_id = #{companyId,jdbcType=BIGINT},
      parent_id = #{parentId,jdbcType=BIGINT},
      resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR},
      resource_code = #{resourceCode,jdbcType=VARCHAR},
      resource_name = #{resourceName,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=TINYINT},
      resource_keyword = #{resourceKeyword,jdbcType=VARCHAR},
      secret_level = #{secretLevel,jdbcType=TINYINT},
      resource_uri_id = #{resourceUriId,jdbcType=VARCHAR},
      resource_uri_name = #{resourceUriName,jdbcType=VARCHAR},
      resource_url = #{resourceUrl,jdbcType=VARCHAR},
      resource_route = #{resourceRoute,jdbcType=VARCHAR},
      build_id = #{buildId,jdbcType=VARCHAR},
      build_count = #{buildCount,jdbcType=BIGINT},
      favorite_count = #{favoriteCount,jdbcType=BIGINT},
      like_count = #{likeCount,jdbcType=BIGINT},
      is_delete = #{isDelete,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByResourceName" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from szzy_resource
    where company_id = #{companyId,jdbcType=BIGINT} and parent_id = #{parentId,jdbcType=BIGINT}
    and resource_name = #{resourceName,jdbcType=VARCHAR} and is_delete = #{isDelete,jdbcType=TINYINT}
  </select>

  <select id="selectByResourcePoolCodeAndType" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from szzy_resource
    where company_id = #{companyId,jdbcType=BIGINT} and resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR}
    and resource_type = #{resourceType,jdbcType=TINYINT} and is_delete = #{isDelete,jdbcType=TINYINT}
  </select>

  <select id="listChildByIdAndResourceType" resultMap="ResultMapWithBLOBs">
    select
      c.*
    from szzy_resource as c join szzy_resource_item_rel as r on c.id = r.descendant_id
    where c.company_id = #{companyId,jdbcType=BIGINT} and r.ancestor_id= #{id,jdbcType=INTEGER}
      and r.distance <![CDATA[ > ]]> 0 and c.is_delete = #{isDelete,jdbcType=TINYINT}
    <if test="resourceTypes != null">
      and c.resource_type in
      <foreach collection="resourceTypes" item="resourceType" index="index" open="(" close=")" separator=",">
        #{resourceType,jdbcType=TINYINT}
      </foreach>
    </if>
  </select>

  <select id="listChildByIdAndParentId" resultMap="ResultMapWithBLOBs">
    select * from
    (
      select
      <include refid="Base_Column_List" />
      ,
      <include refid="Blob_Column_List" />
      from szzy_resource
      where company_id = #{companyId,jdbcType=BIGINT} and parent_id = #{parentId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
      <if test="resourceTypes != null">
        and resource_type in
        <foreach collection="resourceTypes" item="resourceType" index="index" open="(" close=")" separator=",">
          #{resourceType,jdbcType=TINYINT}
        </foreach>
      </if>
      <if test="secretLevels != null and secretLevels.size() > 0">
        and secret_level in
        <foreach collection="secretLevels" item="id" index="index" open="(" close=")" separator=",">
          #{id,jdbcType=TINYINT}
        </foreach>
      </if>
      <if test="resourceName != null">
        and resource_name like CONCAT('%', #{resourceName,jdbcType=VARCHAR},'%')
      </if>
      <if test="secretLevel != null">
        and secret_level = #{secretLevel,jdbcType=TINYINT}
      </if>
      <if test="registerNo != null">
        and resource_body like CONCAT('%registerNo":%', #{registerNo,jdbcType=LONGVARCHAR},'%')
      </if>
      <if test="relCollectionName != null">
        and resource_body like CONCAT('%collectionName":%', #{relCollectionName,jdbcType=LONGVARCHAR},'%')
      </if>
      <if test="showTypeCode != null ">
        and resource_body like CONCAT('%showTypeCode":', #{showTypeCode,jdbcType=INTEGER},'%')
      </if>
      <if test="startModified != null and endModified != null">
        and gmt_modified &gt;= #{startModified,jdbcType=TIMESTAMP} and gmt_modified &lt;= #{endModified,jdbcType=TIMESTAMP}
      </if>
      union
      select
      <include refid="Base_Column_List" />
      ,
      <include refid="Blob_Column_List" />
      from szzy_resource
      where company_id = #{companyId,jdbcType=BIGINT} and parent_id = #{parentId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
      and creator = #{creator,jdbcType=BIGINT}
      <if test="resourceTypes != null">
        and resource_type in
        <foreach collection="resourceTypes" item="resourceType" index="index" open="(" close=")" separator=",">
          #{resourceType,jdbcType=TINYINT}
        </foreach>
      </if>
      <if test="resourceName != null">
        and resource_name like CONCAT('%', #{resourceName,jdbcType=VARCHAR},'%')
      </if>
      <if test="secretLevel != null">
        and secret_level = #{secretLevel,jdbcType=TINYINT}
      </if>
      <if test="registerNo != null">
        and resource_body like CONCAT('%', #{registerNo,jdbcType=LONGVARCHAR},'%')
      </if>
      <if test="relCollectionName != null">
        and resource_body like CONCAT('%', #{relCollectionName,jdbcType=LONGVARCHAR},'%')
      </if>
      <if test="showTypeCode != null ">
        and resource_body like CONCAT('%showTypeCode":', #{showTypeCode,jdbcType=INTEGER},'%')
      </if>
      <if test="startModified != null and endModified != null">
        and gmt_modified &gt;= #{startModified,jdbcType=TIMESTAMP} and gmt_modified &lt;= #{endModified,jdbcType=TIMESTAMP}
      </if>
    ) A
    order by ${sortBy}
  </select>

  <select id="selectByIdAndDelete" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from szzy_resource
    where id = #{id,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
  </select>

  <select id="listByCreate" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from szzy_resource
    where creator = #{createId,jdbcType=BIGINT}
    and is_delete = #{isDelete,jdbcType=TINYINT}
    and resource_pool_code != 'OFF_CLUSTER'
    <if test="resourcePoolCode != null and resourcePoolCode != '' ">
      and resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR}
    </if>
    <if test="sourceName != null and sourceName != '' ">
      and resource_name like CONCAT('%', #{sourceName,jdbcType=VARCHAR},'%')
    </if>
    <if test="startTime != null and startTime != '' ">
      and gmt_create >= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="endTime != null and endTime != '' ">
      and gmt_create <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
    </if>
    <if test="secretLevel != null ">
      and secret_level = #{secretLevel,jdbcType=INTEGER}
    </if>
    order by ${sortBy}
  </select>

  <select id="listResourceClusterByCreate" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from szzy_resource
    where company_id = #{companyId,jdbcType=BIGINT} and parent_id = #{parentId,jdbcType=BIGINT}
        and creator = #{createId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
    <if test="sourceName != null and sourceName != '' ">
      and resource_name like CONCAT('%', #{sourceName,jdbcType=VARCHAR},'%')
    </if>
    <if test="startTime != null and startTime != '' ">
      and gmt_create >= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="endTime != null and endTime != '' ">
      and gmt_create <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
    </if>
    order by ${sortBy}
  </select>

  <update id="updateByIds">
    update szzy_resource
    <set>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.resourcePoolCode != null">
        resource_pool_code = #{record.resourcePoolCode,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceCode != null">
        resource_code = #{record.resourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceName != null">
        resource_name = #{record.resourceName,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=TINYINT},
      </if>
      <if test="record.resourceKeyword != null">
        resource_keyword = #{record.resourceKeyword,jdbcType=VARCHAR},
      </if>
      <if test="record.secretLevel != null">
        secret_level = #{record.secretLevel,jdbcType=TINYINT},
      </if>
      <if test="record.resourceUriId != null">
        resource_uri_id = #{record.resourceUriId,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceUriName != null">
        resource_uri_name = #{record.resourceUriName,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceUrl != null">
        resource_url = #{record.resourceUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceRoute != null">
        resource_route = #{record.resourceRoute,jdbcType=VARCHAR},
      </if>
      <if test="record.buildId != null">
        build_id = #{record.buildId,jdbcType=VARCHAR},
      </if>
      <if test="record.buildCount != null">
        build_count = #{record.buildCount,jdbcType=BIGINT},
      </if>
      <if test="record.favoriteCount != null">
        favorite_count = #{record.favoriteCount,jdbcType=BIGINT},
      </if>
      <if test="record.likeCount != null">
        like_count = #{record.likeCount,jdbcType=BIGINT},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=BIGINT},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.resourceBody != null">
        resource_body = #{record.resourceBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.frontBody != null">
        front_body = #{record.frontBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.documentId != null">
        document_id = #{record.documentId,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id in
    <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
      #{id,jdbcType=TINYINT}
    </foreach>
  </update>

  <select id="selectByIdsAndDelete" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from szzy_resource
    where is_delete = #{isDelete,jdbcType=TINYINT}
    <if test="ids != null and ids.size() > 0 ">
      and id in
      <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
        #{id,jdbcType=BIGINT}
      </foreach>
    </if>
  </select>

  <select id="listResearchList" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from szzy_resource
    where company_id = #{companyId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT} and resource_type != 0
    <if test="resourcePoolCodes != null and resourcePoolCodes.size() > 0 ">
      and resource_pool_code in
      <foreach collection="resourcePoolCodes" item="code" index="index" open="(" close=")" separator=",">
        #{code,jdbcType=VARCHAR}
      </foreach>
    </if>
  </select>

  <update id="addFavoriteCountById">
    update szzy_resource
    set
        favorite_count = favorite_count + 1,
        gmt_modified = gmt_modified
    where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
  </update>
  <update id="minusFavoriteCountById">
    update szzy_resource
    set
      favorite_count = IF (favorite_count <![CDATA[ < ]]> 1, 0, favorite_count - 1),
      gmt_modified = gmt_modified
    where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
  </update>

  <select id="listByResourceIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_resource
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="resourceUriName != null and resourceUriName != ''">
    and resource_uri_name like concat ('%', #{resourceUriName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="resourceName != null and resourceName != ''">
    and resource_name like concat ('%',  #{resourceName,jdbcType=VARCHAR}, '%')
    </if>
    and id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="listByFavoriteResourceIds" resultMap="ResultMapWithBLOBs">
    select
    sr.id,
    sr.company_id,
    sr.parent_id,
    sr.resource_pool_code,
    sr.resource_code,
    sr.resource_name,
    sr.resource_type,
    sr.resource_keyword,
    sr.secret_level,
    sr.resource_uri_id,
    sr.resource_uri_name,
    sr.resource_url,
    sr.resource_route,
    sr.build_id,
    sr.build_count,
    sr.favorite_count,
    sr.like_count,
    sr.is_delete,
    sr.remark,
    sr.creator,
    sr.modifier,
    sr.gmt_create,
    sr.gmt_modified,
    sr.resource_body,
    sr.front_body,
    sr.document_id
    from szzy_resource sr
    JOIN szzy_resource_favorites srf ON sr.id = srf.resource_id
    where sr.company_id = #{companyId,jdbcType=BIGINT}
    AND srf.user_id = #{userId,jdbcType=BIGINT}
    <if test="resourceUriName != null and resourceUriName != ''">
      and sr.resource_uri_name like concat ('%', #{resourceUriName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="resourceName != null and resourceName != ''">
      and sr.resource_name like concat ('%',  #{resourceName,jdbcType=VARCHAR}, '%')
    </if>
    and sr.id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <update id="updateDocumentById" parameterType="com.das.museum.infr.dataobject.SzzyResourceDO">
    update szzy_resource
    set document_id = #{documentId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </update>

  <select id="statisticsDigital" resultType="java.lang.String">
    SELECT resource_body
    FROM szzy_resource
    WHERE resource_body LIKE CONCAT ('%', 'registerNo', '%')
      AND company_id = #{companyId,jdbcType=BIGINT}
      AND parent_id = #{parentId,jdbcType=BIGINT}
      AND resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR}
      AND is_delete = 0
      GROUP BY resource_body;
  </select>
</mapper>