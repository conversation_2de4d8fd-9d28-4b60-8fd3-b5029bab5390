<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.GztUserWorkRequestMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.GztUserWorkRequestDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="apply_type" jdbcType="TINYINT" property="applyType" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="out_days" jdbcType="INTEGER" property="outDays" />
    <result column="destination" jdbcType="VARCHAR" property="destination" />
    <result column="item_description" jdbcType="VARCHAR" property="itemDescription" />
    <result column="leave_type_id" jdbcType="BIGINT" property="leaveTypeId" />
    <result column="leave_reason" jdbcType="VARCHAR" property="leaveReason" />
    <result column="request_content" jdbcType="VARCHAR" property="requestContent" />
    <result column="document_id" jdbcType="VARCHAR" property="documentId" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, user_id, apply_type, start_time, end_time, out_days, destination, 
    item_description, leave_type_id, leave_reason, request_content, document_id, creator, 
    modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gzt_user_work_request
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gzt_user_work_request
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.GztUserWorkRequestDO" useGeneratedKeys="true">
    insert into gzt_user_work_request (company_id, user_id, apply_type, 
      start_time, end_time, out_days, 
      destination, item_description, leave_type_id, 
      leave_reason, request_content, document_id, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{applyType,jdbcType=TINYINT}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{outDays,jdbcType=INTEGER}, 
      #{destination,jdbcType=VARCHAR}, #{itemDescription,jdbcType=VARCHAR}, #{leaveTypeId,jdbcType=BIGINT}, 
      #{leaveReason,jdbcType=VARCHAR}, #{requestContent,jdbcType=VARCHAR}, #{documentId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.GztUserWorkRequestDO" useGeneratedKeys="true">
    insert into gzt_user_work_request
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="applyType != null">
        apply_type,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="outDays != null">
        out_days,
      </if>
      <if test="destination != null">
        destination,
      </if>
      <if test="itemDescription != null">
        item_description,
      </if>
      <if test="leaveTypeId != null">
        leave_type_id,
      </if>
      <if test="leaveReason != null">
        leave_reason,
      </if>
      <if test="requestContent != null">
        request_content,
      </if>
      <if test="documentId != null">
        document_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        #{applyType,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outDays != null">
        #{outDays,jdbcType=INTEGER},
      </if>
      <if test="destination != null">
        #{destination,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null">
        #{itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="leaveTypeId != null">
        #{leaveTypeId,jdbcType=BIGINT},
      </if>
      <if test="leaveReason != null">
        #{leaveReason,jdbcType=VARCHAR},
      </if>
      <if test="requestContent != null">
        #{requestContent,jdbcType=VARCHAR},
      </if>
      <if test="documentId != null">
        #{documentId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.GztUserWorkRequestDO">
    update gzt_user_work_request
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        apply_type = #{applyType,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outDays != null">
        out_days = #{outDays,jdbcType=INTEGER},
      </if>
      <if test="destination != null">
        destination = #{destination,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null">
        item_description = #{itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="leaveTypeId != null">
        leave_type_id = #{leaveTypeId,jdbcType=BIGINT},
      </if>
      <if test="leaveReason != null">
        leave_reason = #{leaveReason,jdbcType=VARCHAR},
      </if>
      <if test="requestContent != null">
        request_content = #{requestContent,jdbcType=VARCHAR},
      </if>
      <if test="documentId != null">
        document_id = #{documentId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.GztUserWorkRequestDO">
    update gzt_user_work_request
    set company_id = #{companyId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      apply_type = #{applyType,jdbcType=TINYINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      out_days = #{outDays,jdbcType=INTEGER},
      destination = #{destination,jdbcType=VARCHAR},
      item_description = #{itemDescription,jdbcType=VARCHAR},
      leave_type_id = #{leaveTypeId,jdbcType=BIGINT},
      leave_reason = #{leaveReason,jdbcType=VARCHAR},
      request_content = #{requestContent,jdbcType=VARCHAR},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectListByCompanyIdAndId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from gzt_user_work_request
    where company_id = #{companyId,jdbcType=BIGINT}
    and id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="selectRequestByUserIds" resultType="com.das.museum.infr.dataobjectexpand.SelectRequestByUserIdsDO">
    select
    A.user_id userId,
    A.apply_type applyType,
    A.start_time startTime,
    A.end_time endTime
    from gzt_user_work_request A
    inner join (select * from comm_check_main where belong_model in ('QJSQ','CCSQ','WCSQ') and check_status = 'ATP' ) B
    on A.id = B.model_id
    where A.company_id = #{companyId,jdbcType=BIGINT}
    and A.user_id in
    <foreach collection="userIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
    and A.start_time &lt;= #{currentTime,jdbcType=TIMESTAMP}
    and A.end_time >= #{currentTime,jdbcType=TIMESTAMP}
    and A.apply_type in (1,2,3)
    order by A.gmt_create
  </select>

</mapper>