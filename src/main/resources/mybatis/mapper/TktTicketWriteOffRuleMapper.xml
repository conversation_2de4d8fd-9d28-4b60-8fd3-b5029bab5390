<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.TktTicketWriteOffRuleMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.TktTicketWriteOffRuleDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="ticket_id" jdbcType="BIGINT" property="ticketId" />
    <result column="ticket_channel_id" jdbcType="BIGINT" property="ticketChannelId" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="is_today_cross" jdbcType="TINYINT" property="isTodayCross" />
    <result column="no_reserve" jdbcType="TINYINT" property="noReserve" />
    <result column="today_stop_time" jdbcType="TIME" property="todayStopTime" />
    <result column="write_off_limit" jdbcType="BIGINT" property="writeOffLimit" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, department_id, ticket_id, ticket_channel_id, channel_type, is_today_cross, 
    no_reserve, today_stop_time, write_off_limit, remark, creator, modifier, gmt_create, 
    gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tkt_ticket_write_off_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tkt_ticket_write_off_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketWriteOffRuleDO" useGeneratedKeys="true">
    insert into tkt_ticket_write_off_rule (company_id, department_id, ticket_id, 
      ticket_channel_id, channel_type, is_today_cross, 
      no_reserve, today_stop_time, write_off_limit, 
      remark, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, #{ticketId,jdbcType=BIGINT}, 
      #{ticketChannelId,jdbcType=BIGINT}, #{channelType,jdbcType=TINYINT}, #{isTodayCross,jdbcType=TINYINT}, 
      #{noReserve,jdbcType=TINYINT}, #{todayStopTime,jdbcType=TIME}, #{writeOffLimit,jdbcType=BIGINT}, 
      #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketWriteOffRuleDO" useGeneratedKeys="true">
    insert into tkt_ticket_write_off_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="ticketId != null">
        ticket_id,
      </if>
      <if test="ticketChannelId != null">
        ticket_channel_id,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="isTodayCross != null">
        is_today_cross,
      </if>
      <if test="noReserve != null">
        no_reserve,
      </if>
      <if test="todayStopTime != null">
        today_stop_time,
      </if>
      <if test="writeOffLimit != null">
        write_off_limit,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="ticketId != null">
        #{ticketId,jdbcType=BIGINT},
      </if>
      <if test="ticketChannelId != null">
        #{ticketChannelId,jdbcType=BIGINT},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=TINYINT},
      </if>
      <if test="isTodayCross != null">
        #{isTodayCross,jdbcType=TINYINT},
      </if>
      <if test="noReserve != null">
        #{noReserve,jdbcType=TINYINT},
      </if>
      <if test="todayStopTime != null">
        #{todayStopTime,jdbcType=TIME},
      </if>
      <if test="writeOffLimit != null">
        #{writeOffLimit,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.TktTicketWriteOffRuleDO">
    update tkt_ticket_write_off_rule
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="ticketId != null">
        ticket_id = #{ticketId,jdbcType=BIGINT},
      </if>
      <if test="ticketChannelId != null">
        ticket_channel_id = #{ticketChannelId,jdbcType=BIGINT},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=TINYINT},
      </if>
      <if test="isTodayCross != null">
        is_today_cross = #{isTodayCross,jdbcType=TINYINT},
      </if>
      <if test="noReserve != null">
        no_reserve = #{noReserve,jdbcType=TINYINT},
      </if>
      <if test="todayStopTime != null">
        today_stop_time = #{todayStopTime,jdbcType=TIME},
      </if>
      <if test="writeOffLimit != null">
        write_off_limit = #{writeOffLimit,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.TktTicketWriteOffRuleDO">
    update tkt_ticket_write_off_rule
    set company_id = #{companyId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      ticket_id = #{ticketId,jdbcType=BIGINT},
      ticket_channel_id = #{ticketChannelId,jdbcType=BIGINT},
      channel_type = #{channelType,jdbcType=TINYINT},
      is_today_cross = #{isTodayCross,jdbcType=TINYINT},
      no_reserve = #{noReserve,jdbcType=TINYINT},
      today_stop_time = #{todayStopTime,jdbcType=TIME},
      write_off_limit = #{writeOffLimit,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByCompanyId" parameterType="com.das.museum.infr.dataobject.TktTicketWriteOffRuleDO">
    update tkt_ticket_write_off_rule
    <set>
      <if test="ticketId != null">
        ticket_id = #{ticketId,jdbcType=BIGINT},
      </if>
      <if test="ticketChannelId != null">
        ticket_channel_id = #{ticketChannelId,jdbcType=BIGINT},
      </if>
      <if test="isTodayCross != null">
        is_today_cross = #{isTodayCross,jdbcType=TINYINT},
      </if>
      <if test="noReserve != null">
        no_reserve = #{noReserve,jdbcType=TINYINT},
      </if>
      <if test="todayStopTime != null">
        today_stop_time = #{todayStopTime,jdbcType=TIME},
      </if>
      <if test="writeOffLimit != null">
        write_off_limit = #{writeOffLimit,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where company_id = #{companyId,jdbcType=BIGINT}
    and channel_type = #{channelType,jdbcType=TINYINT}
  </update>

  <select id="selectByCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_ticket_write_off_rule
    where company_id = #{companyId,jdbcType=BIGINT}
    and channel_type = #{channelType,jdbcType=TINYINT}
  </select>
</mapper>