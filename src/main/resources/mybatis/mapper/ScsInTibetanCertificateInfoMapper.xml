<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsInTibetanCertificateInfoMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsInTibetanCertificateInfoDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="in_tibetan_certificate_no" jdbcType="VARCHAR" property="inTibetanCertificateNo"/>
        <result column="prefix_name" jdbcType="VARCHAR" property="prefixName"/>
        <result column="suffix_number" jdbcType="BIGINT" property="suffixNumber"/>
        <result column="submitter" jdbcType="BIGINT" property="submitter"/>
        <result column="submitter_name" jdbcType="VARCHAR" property="submitterName"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="auto_audit" jdbcType="TINYINT" property="autoAudit"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, in_tibetan_certificate_no, prefix_name, suffix_number, submitter,
    submitter_name, document_id, creator, modifier, gmt_create, gmt_modified, auto_audit
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_in_tibetan_certificate_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_in_tibetan_certificate_info
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsInTibetanCertificateInfoDO" useGeneratedKeys="true">
    insert into scs_in_tibetan_certificate_info (company_id, in_tibetan_certificate_no,
      prefix_name, suffix_number, submitter, 
      submitter_name, document_id, creator, 
      modifier, gmt_create, gmt_modified, auto_audit
      )
    values (#{companyId,jdbcType=BIGINT}, #{inTibetanCertificateNo,jdbcType=VARCHAR},
      #{prefixName,jdbcType=VARCHAR}, #{suffixNumber,jdbcType=BIGINT}, #{submitter,jdbcType=BIGINT}, 
      #{submitterName,jdbcType=VARCHAR}, #{documentId,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP},
      #{autoAudit,jdbcType=TINYINT}
      )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsInTibetanCertificateInfoDO" useGeneratedKeys="true">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into scs_in_tibetan_certificate_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="inTibetanCertificateNo != null">
                in_tibetan_certificate_no,
            </if>
            <if test="prefixName != null">
                prefix_name,
            </if>
            <if test="suffixNumber != null">
                suffix_number,
            </if>
            <if test="submitter != null">
                submitter,
            </if>
            <if test="submitterName != null">
                submitter_name,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="autoAudit != null">
                auto_audit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="inTibetanCertificateNo != null">
                #{inTibetanCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="prefixName != null">
                #{prefixName,jdbcType=VARCHAR},
            </if>
            <if test="suffixNumber != null">
                #{suffixNumber,jdbcType=BIGINT},
            </if>
            <if test="submitter != null">
                #{submitter,jdbcType=BIGINT},
            </if>
            <if test="submitterName != null">
                #{submitterName,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="autoAudit != null">
                #{autoAudit,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.das.museum.infr.dataobject.ScsInTibetanCertificateInfoDO">
        update scs_in_tibetan_certificate_info
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="inTibetanCertificateNo != null">
                in_tibetan_certificate_no = #{inTibetanCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="prefixName != null">
                prefix_name = #{prefixName,jdbcType=VARCHAR},
            </if>
            <if test="suffixNumber != null">
                suffix_number = #{suffixNumber,jdbcType=BIGINT},
            </if>
            <if test="submitter != null">
                submitter = #{submitter,jdbcType=BIGINT},
            </if>
            <if test="submitterName != null">
                submitter_name = #{submitterName,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="autoAudit != null">
                auto_audit = #{autoAudit,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsInTibetanCertificateInfoDO">
    update scs_in_tibetan_certificate_info
    set company_id = #{companyId,jdbcType=BIGINT},
      in_tibetan_certificate_no = #{inTibetanCertificateNo,jdbcType=VARCHAR},
      prefix_name = #{prefixName,jdbcType=VARCHAR},
      suffix_number = #{suffixNumber,jdbcType=BIGINT},
      submitter = #{submitter,jdbcType=BIGINT},
      submitter_name = #{submitterName,jdbcType=VARCHAR},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      auto_audit = #{autoAudit,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <delete id="deleteById">
    delete from scs_in_tibetan_certificate_info
    where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_in_tibetan_certificate_info
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="listPageByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_in_tibetan_certificate_info
        where company_id = #{companyId,jdbcType=BIGINT}
        <if test="inTibetanCertificateNo != null">
            and in_tibetan_certificate_no like concat('%', #{inTibetanCertificateNo,jdbcType=VARCHAR}, '%')
        </if>
        <if test="query != null">
            and id in (
            SELECT DISTINCT
            t2.biz_id
            FROM
            scs_collection_info t1
            LEFT JOIN scs_biz_collection_rel t2 ON t1.id = t2.collection_id
            WHERE
            t1.company_id = #{companyId,jdbcType=BIGINT}
            <if test="query.registerNo != null">
                and t1.register_no like concat('%', #{query.registerNo,jdbcType=VARCHAR}, '%')
            </if>
            <if test="query.collectionName != null">
                and t1.`name` like concat('%', #{query.collectionName,jdbcType=VARCHAR}, '%')
            </if>
            )
        </if>
        order by ${sortBy}
    </select>

    <select id="selectByCollectionId" resultMap="BaseResultMap">
        SELECT DISTINCT
	        t1.*
        FROM
	        scs_in_tibetan_certificate_info t1
	    LEFT JOIN scs_biz_collection_rel t2 ON t1.id = t2.biz_id
        WHERE
	        t1.company_id = #{companyId,jdbcType=BIGINT}
	        AND t2.collection_id = #{collectionId,jdbcType=BIGINT}
	        AND t2.biz_type = 'TBT'
    </select>
</mapper>