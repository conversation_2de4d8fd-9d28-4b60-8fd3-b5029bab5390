<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SwzsCulturalAutoBuildMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SwzsCulturalAutoBuildDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="build_status" jdbcType="TINYINT" property="buildStatus" />
    <result column="build_process" jdbcType="BIGINT" property="buildProcess" />
    <result column="download_url" jdbcType="VARCHAR" property="downloadUrl"/>
    <result column="url_create_time" jdbcType="TIMESTAMP" property="urlCreateTime" />
    <result column="switch_status" jdbcType="TINYINT" property="switchStatus" />
    <result column="model_url" jdbcType="VARCHAR" property="modelUrl" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="cover_url" jdbcType="VARCHAR" property="coverUrl" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, app_id, model_name, build_status, build_process, download_url, url_create_time,
    switch_status, model_url, creator, modifier, gmt_create, gmt_modified, cover_url
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from swzs_cultural_auto_build
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from swzs_cultural_auto_build
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalAutoBuildDO" useGeneratedKeys="true">
    insert into swzs_cultural_auto_build (company_id, app_id, biz_id, 
      model_name, build_status, build_process, 
      url_create_time, switch_status, model_url, 
      creator, modifier, gmt_create, 
      gmt_modified, cover_url)
    values (#{companyId,jdbcType=BIGINT}, #{appId,jdbcType=VARCHAR},
      #{modelName,jdbcType=VARCHAR}, #{buildStatus,jdbcType=TINYINT}, #{buildProcess,jdbcType=BIGINT}, #{downloadUrl, jdbcType=VARCHAR},
      #{urlCreateTime,jdbcType=TIMESTAMP}, #{switchStatus,jdbcType=TINYINT}, #{modelUrl,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{coverUrl,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalAutoBuildDO" useGeneratedKeys="true">
    insert into swzs_cultural_auto_build
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="modelName != null">
        model_name,
      </if>
      <if test="buildStatus != null">
        build_status,
      </if>
      <if test="buildProcess != null">
        build_process,
      </if>
      <if test="downloadUrl != null">
        download_url,
      </if>
      <if test="urlCreateTime != null">
        url_create_time,
      </if>
      <if test="switchStatus != null">
        switch_status,
      </if>
      <if test="modelUrl != null">
        model_url,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="coverUrl != null">
        cover_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="buildStatus != null">
        #{buildStatus,jdbcType=TINYINT},
      </if>
      <if test="buildProcess != null">
        #{buildProcess,jdbcType=BIGINT},
      </if>
      <if test="downloadUrl != null">
        #{downloadUrl, jdbcType=VARCHAR},
      </if>
      <if test="urlCreateTime != null">
        #{urlCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="switchStatus != null">
        #{switchStatus,jdbcType=TINYINT},
      </if>
      <if test="modelUrl != null">
        #{modelUrl,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="coverUrl != null">
        #{coverUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SwzsCulturalAutoBuildDO">
    update swzs_cultural_auto_build
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        model_name = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="buildStatus != null">
        build_status = #{buildStatus,jdbcType=TINYINT},
      </if>
      <if test="buildProcess != null">
        build_process = #{buildProcess,jdbcType=BIGINT},
      </if>
      <if test="downloadUrl != null">
        download_url = #{downloadUrl, jdbcType=VARCHAR},
      </if>
      <if test="urlCreateTime != null">
        url_create_time = #{urlCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="switchStatus != null">
        switch_status = #{switchStatus,jdbcType=TINYINT},
      </if>
      <if test="modelUrl != null">
        model_url = #{modelUrl,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="coverUrl != null">
        cover_url = #{coverUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SwzsCulturalAutoBuildDO">
    update swzs_cultural_auto_build
    set company_id = #{companyId,jdbcType=BIGINT},
      app_id = #{appId,jdbcType=VARCHAR},
      model_name = #{modelName,jdbcType=VARCHAR},
      build_status = #{buildStatus,jdbcType=TINYINT},
      build_process = #{buildProcess,jdbcType=BIGINT},
      download_url = #{downloadUrl, jdbcType=VARCHAR},
      url_create_time = #{urlCreateTime,jdbcType=TIMESTAMP},
      switch_status = #{switchStatus,jdbcType=TINYINT},
      model_url = #{modelUrl,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      cover_url = #{coverUrl,jdbcType=VARCHAR},
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--  ===========================>自定义方法===========================  -->
  <update id="updateByBizIdAndAppId" parameterType="com.das.museum.infr.dataobject.SwzsCulturalAutoBuildDO">
    update swzs_cultural_auto_build
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="modelName != null">
        model_name = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="buildStatus != null">
        build_status = #{buildStatus,jdbcType=TINYINT},
      </if>
      <if test="buildProcess != null">
        build_process = #{buildProcess,jdbcType=BIGINT},
      </if>
      <if test="downloadUrl != null">
        download_url = #{downloadUrl, jdbcType=VARCHAR},
      </if>
      <if test="urlCreateTime != null">
        url_create_time = #{urlCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="switchStatus != null">
        switch_status = #{switchStatus,jdbcType=TINYINT},
      </if>
      <if test="modelUrl != null">
        model_url = #{modelUrl,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="coverUrl != null">
        cover_url = #{coverUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT} and app_id = #{appId,jdbcType=VARCHAR}
  </update>

  <select id="selectByGetCulturalModelUrlAction" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_auto_build
    where
    id = #{bizId,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByBizId">
    delete from swzs_cultural_auto_build
    where
      id = #{bizId,jdbcType=BIGINT}
      and company_id = #{companyId,jdbcType=BIGINT}
  </delete>

  <select id="selectByParams" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_auto_build
    where 1 = 1
    <if test="companyId != null">
      and company_id = #{companyId,jdbcType=BIGINT}
    </if>
    <if test="appId != null">
      and app_id = #{appId,jdbcType=VARCHAR}
    </if>
    <if test="switchStatus != null">
      and switch_status = #{switchStatus,jdbcType=TINYINT}
    </if>
    <if test="modelName != null">
      and model_name like  CONCAT('%', #{modelName,jdbcType=VARCHAR}, '%')
    </if>
    order by ${sortBy}
  </select>
  <select id="selectByModelStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_auto_build
    where build_status = 2
    <if test="appId != null">
      and app_id = #{appId,jdbcType=VARCHAR}
    </if>
      and switch_status in
    <foreach collection="modelSwitchStatusList" open="(" close=")" separator="," item="switchStatus" index="index">
      #{switchStatus}
    </foreach>
  </select>
  <update id="updateBybizIds">
    update swzs_cultural_auto_build
    set switch_status = #{switchStatus,jdbcType=TINYINT}
    where app_id = #{appId,jdbcType=VARCHAR} and id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      id = #{id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>