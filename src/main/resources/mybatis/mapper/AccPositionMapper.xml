<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.AccPositionMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.AccPositionDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="position_name" jdbcType="VARCHAR" property="positionName"/>
        <result column="department_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , company_id, position_name, department_id, remark, is_delete, creator, modifier,
    gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_position
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from acc_position
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.AccPositionDO"
            useGeneratedKeys="true">
        insert into acc_position (company_id, position_name, department_id,
                                  remark, is_delete, creator,
                                  modifier, gmt_create, gmt_modified)
        values (#{companyId,jdbcType=BIGINT}, #{positionName,jdbcType=VARCHAR}, #{departmentId,jdbcType=BIGINT},
                #{remark,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT}, #{creator,jdbcType=BIGINT},
                #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.AccPositionDO" useGeneratedKeys="true">
        insert into acc_position
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="positionName != null">
                position_name,
            </if>
            <if test="departmentId != null">
                department_id,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="positionName != null">
                #{positionName,jdbcType=VARCHAR},
            </if>
            <if test="departmentId != null">
                #{departmentId,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.AccPositionDO">
        update acc_position
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="positionName != null">
                position_name = #{positionName,jdbcType=VARCHAR},
            </if>
            <if test="departmentId != null">
                department_id = #{departmentId,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.AccPositionDO">
        update acc_position
        set company_id    = #{companyId,jdbcType=BIGINT},
            position_name = #{positionName,jdbcType=VARCHAR},
            department_id = #{departmentId,jdbcType=BIGINT},
            remark        = #{remark,jdbcType=VARCHAR},
            is_delete     = #{isDelete,jdbcType=TINYINT},
            creator       = #{creator,jdbcType=BIGINT},
            modifier      = #{modifier,jdbcType=BIGINT},
            gmt_create    = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified  = #{gmtModified,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->
    <select id="selectByDepartmentId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from acc_position
      where company_id = #{companyId,jdbcType=BIGINT} and department_id = #{departmentId,jdbcType=BIGINT}
      and position_name = #{positionName,jdbcType=VARCHAR} and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="selectByPositionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_position
        where company_id = #{companyId,jdbcType=BIGINT} and id = #{positionId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="listByPositionIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_position
        where company_id = #{companyId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
        <if test="positionIds != null and positionIds.size() > 0 ">
            and id in
            <foreach collection="positionIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="listByNotInPositionIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_position
        where company_id = #{companyId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
        <if test=" positionIds != null and positionIds.size() != 0 ">
            and id not in
            <foreach collection="positionIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>

    </select>

    <select id="listByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_position
        where company_id = #{companyId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT} and department_id in
        <foreach collection="departmentIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <select id="listByDepartmentIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_position
        where company_id = #{companyId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT} and department_id in
        <foreach collection="departmentIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <update id="updateByPositionIds">
        update acc_position
        <set>
            <if test="record.positionName != null">
                position_name = #{record.positionName,jdbcType=VARCHAR},
            </if>
            <if test="record.departmentId != null">
                department_id = #{record.departmentId,jdbcType=BIGINT},
            </if>
            <if test="record.remark != null">
                remark = #{record.remark,jdbcType=VARCHAR},
            </if>
            <if test="record.isDelete != null">
                is_delete = #{record.isDelete,jdbcType=TINYINT},
            </if>
            <if test="record.creator != null">
                creator = #{record.creator,jdbcType=BIGINT},
            </if>
            <if test="record.modifier != null">
                modifier = #{record.modifier,jdbcType=BIGINT},
            </if>
            <if test="record.gmtCreate != null">
                gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gmtModified != null">
                gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where company_id = #{companyId,jdbcType=BIGINT} and id in
        <foreach collection="positionIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </update>
</mapper>