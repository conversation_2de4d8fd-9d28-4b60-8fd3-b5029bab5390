<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SzzySourceApplyInfoMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SzzySourceApplyInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="source_name" jdbcType="VARCHAR" property="sourceName" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="security_level" jdbcType="TINYINT" property="securityLevel" />
    <result column="apply_use" jdbcType="TINYINT" property="applyUse" />
    <result column="use_time_limit" jdbcType="TINYINT" property="useTimeLimit" />
    <result column="use_reason" jdbcType="VARCHAR" property="useReason" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="apply_status" jdbcType="TINYINT" property="applyStatus" />
    <result column="pass_time" jdbcType="TIMESTAMP" property="passTime" />
    <result column="apply_user_id" jdbcType="BIGINT" property="applyUserId" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, source_id, source_name, source_path, security_level, apply_use, use_time_limit, 
    use_reason, apply_time, apply_status, pass_time, apply_user_id, apply_user_name, 
    creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from szzy_source_apply_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szzy_source_apply_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzySourceApplyInfoDO" useGeneratedKeys="true">
    insert into szzy_source_apply_info (company_id, source_id, source_name, 
      source_path, security_level, apply_use, 
      use_time_limit, use_reason, apply_time, 
      apply_status, pass_time, apply_user_id, 
      apply_user_name, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{sourceId,jdbcType=BIGINT}, #{sourceName,jdbcType=VARCHAR}, 
      #{sourcePath,jdbcType=VARCHAR}, #{securityLevel,jdbcType=TINYINT}, #{applyUse,jdbcType=TINYINT}, 
      #{useTimeLimit,jdbcType=TINYINT}, #{useReason,jdbcType=VARCHAR}, #{applyTime,jdbcType=TIMESTAMP}, 
      #{applyStatus,jdbcType=TINYINT}, #{passTime,jdbcType=TIMESTAMP}, #{applyUserId,jdbcType=BIGINT}, 
      #{applyUserName,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzySourceApplyInfoDO" useGeneratedKeys="true">
    insert into szzy_source_apply_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="sourceName != null">
        source_name,
      </if>
      <if test="sourcePath != null">
        source_path,
      </if>
      <if test="securityLevel != null">
        security_level,
      </if>
      <if test="applyUse != null">
        apply_use,
      </if>
      <if test="useTimeLimit != null">
        use_time_limit,
      </if>
      <if test="useReason != null">
        use_reason,
      </if>
      <if test="applyTime != null">
        apply_time,
      </if>
      <if test="applyStatus != null">
        apply_status,
      </if>
      <if test="passTime != null">
        pass_time,
      </if>
      <if test="applyUserId != null">
        apply_user_id,
      </if>
      <if test="applyUserName != null">
        apply_user_name,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="sourceName != null">
        #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="securityLevel != null">
        #{securityLevel,jdbcType=TINYINT},
      </if>
      <if test="applyUse != null">
        #{applyUse,jdbcType=TINYINT},
      </if>
      <if test="useTimeLimit != null">
        #{useTimeLimit,jdbcType=TINYINT},
      </if>
      <if test="useReason != null">
        #{useReason,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyStatus != null">
        #{applyStatus,jdbcType=TINYINT},
      </if>
      <if test="passTime != null">
        #{passTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyUserId != null">
        #{applyUserId,jdbcType=BIGINT},
      </if>
      <if test="applyUserName != null">
        #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SzzySourceApplyInfoDO">
    update szzy_source_apply_info
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="sourceName != null">
        source_name = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        source_path = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="securityLevel != null">
        security_level = #{securityLevel,jdbcType=TINYINT},
      </if>
      <if test="applyUse != null">
        apply_use = #{applyUse,jdbcType=TINYINT},
      </if>
      <if test="useTimeLimit != null">
        use_time_limit = #{useTimeLimit,jdbcType=TINYINT},
      </if>
      <if test="useReason != null">
        use_reason = #{useReason,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyStatus != null">
        apply_status = #{applyStatus,jdbcType=TINYINT},
      </if>
      <if test="passTime != null">
        pass_time = #{passTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyUserId != null">
        apply_user_id = #{applyUserId,jdbcType=BIGINT},
      </if>
      <if test="applyUserName != null">
        apply_user_name = #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SzzySourceApplyInfoDO">
    update szzy_source_apply_info
    set company_id = #{companyId,jdbcType=BIGINT},
      source_id = #{sourceId,jdbcType=BIGINT},
      source_name = #{sourceName,jdbcType=VARCHAR},
      source_path = #{sourcePath,jdbcType=VARCHAR},
      security_level = #{securityLevel,jdbcType=TINYINT},
      apply_use = #{applyUse,jdbcType=TINYINT},
      use_time_limit = #{useTimeLimit,jdbcType=TINYINT},
      use_reason = #{useReason,jdbcType=VARCHAR},
      apply_time = #{applyTime,jdbcType=TIMESTAMP},
      apply_status = #{applyStatus,jdbcType=TINYINT},
      pass_time = #{passTime,jdbcType=TIMESTAMP},
      apply_user_id = #{applyUserId,jdbcType=BIGINT},
      apply_user_name = #{applyUserName,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_source_apply_info
    where company_id = #{companyId,jdbcType=BIGINT}
    and id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_source_apply_info
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="ids != null and ids.size() > 0 ">
      and id in
      <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
        #{id,jdbcType=BIGINT}
      </foreach>
    </if>
  </select>

  <update id="updateById" parameterType="com.das.museum.infr.dataobject.SzzySourceApplyInfoDO">
    update szzy_source_apply_info
    <set>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="sourceName != null">
        source_name = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        source_path = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="securityLevel != null">
        security_level = #{securityLevel,jdbcType=TINYINT},
      </if>
      <if test="applyUse != null">
        apply_use = #{applyUse,jdbcType=TINYINT},
      </if>
      <if test="useTimeLimit != null">
        use_time_limit = #{useTimeLimit,jdbcType=TINYINT},
      </if>
      <if test="useReason != null">
        use_reason = #{useReason,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyStatus != null">
        apply_status = #{applyStatus,jdbcType=TINYINT},
      </if>
      <if test="passTime != null">
        pass_time = #{passTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyUserId != null">
        apply_user_id = #{applyUserId,jdbcType=BIGINT},
      </if>
      <if test="applyUserName != null">
        apply_user_name = #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
    </set>
    where company_id = #{companyId,jdbcType=BIGINT}
    and id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByUserIdAndSourceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_source_apply_info
    where company_id = #{companyId,jdbcType=BIGINT}
    and apply_user_id = #{userId,jdbcType=BIGINT}
    and apply_status = #{applyStatus,jdbcType=TINYINT}
    <if test="sourceIds != null and sourceIds.size() > 0 ">
        and source_id in
    <foreach collection="sourceIds" item="sourceId" index="index" open="(" close=")" separator=",">
      #{sourceId,jdbcType=BIGINT}
    </foreach>
    </if>
  </select>

  <select id="listByParams" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_source_apply_info
    where company_id = #{companyId,jdbcType=BIGINT}
    and apply_user_id = #{userId,jdbcType=BIGINT}
    and apply_status = #{applyStatus,jdbcType=TINYINT}
    and source_id = #{sourceId,jdbcType=BIGINT}
    and apply_use = #{applyUse,jdbcType=TINYINT}
  </select>
  <update id="updateBySourceId" parameterType="com.das.museum.infr.dataobject.SzzySourceApplyInfoDO">
    update szzy_source_apply_info
    <set>
      <if test="sourceName != null">
        source_name = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        source_path = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="securityLevel != null">
        security_level = #{securityLevel,jdbcType=TINYINT},
      </if>
    </set>
    where company_id = #{companyId,jdbcType=BIGINT}
    and source_id = #{sourceId,jdbcType=BIGINT}
  </update>
</mapper>