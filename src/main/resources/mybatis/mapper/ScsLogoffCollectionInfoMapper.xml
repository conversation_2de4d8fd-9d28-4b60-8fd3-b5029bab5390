<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsLogoffCollectionInfoMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsLogoffCollectionInfoDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="logoff_id" jdbcType="BIGINT" property="logoffId"/>
        <result column="logoff_certificate_no" jdbcType="VARCHAR" property="logoffCertificateNo"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="register_no" jdbcType="VARCHAR" property="registerNo"/>
        <result column="classify" jdbcType="BIGINT" property="classify"/>
        <result column="classify_name" jdbcType="VARCHAR" property="classifyName"/>
        <result column="collection_name" jdbcType="VARCHAR" property="collectionName"/>
        <result column="era" jdbcType="VARCHAR" property="era"/>
        <result column="age" jdbcType="BIGINT" property="age"/>
        <result column="age_name" jdbcType="VARCHAR" property="ageName"/>
        <result column="collection_count" jdbcType="INTEGER" property="collectionCount"/>
        <result column="count_unit" jdbcType="BIGINT" property="countUnit"/>
        <result column="count_unit_name" jdbcType="VARCHAR" property="countUnitName"/>
        <result column="identify_level" jdbcType="BIGINT" property="identifyLevel"/>
        <result column="identify_level_name" jdbcType="VARCHAR" property="identifyLevelName"/>
        <result column="situation" jdbcType="VARCHAR" property="situation"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="whereabouts" jdbcType="VARCHAR" property="whereabouts"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, logoff_id, logoff_certificate_no, collection_id, register_no, classify, 
    classify_name, collection_name, era, age, age_name, collection_count, count_unit, 
    count_unit_name, identify_level, identify_level_name, situation, reason, whereabouts, 
    remark, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_logoff_collection_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_logoff_collection_info
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsLogoffCollectionInfoDO" useGeneratedKeys="true">
    insert into scs_logoff_collection_info (company_id, logoff_id, logoff_certificate_no, 
      collection_id, register_no, classify, 
      classify_name, collection_name, era, 
      age, age_name, collection_count, 
      count_unit, count_unit_name, identify_level, 
      identify_level_name, situation, reason, 
      whereabouts, remark, creator, 
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{logoffId,jdbcType=BIGINT}, #{logoffCertificateNo,jdbcType=VARCHAR}, 
      #{collectionId,jdbcType=BIGINT}, #{registerNo,jdbcType=VARCHAR}, #{classify,jdbcType=BIGINT}, 
      #{classifyName,jdbcType=VARCHAR}, #{collectionName,jdbcType=VARCHAR}, #{era,jdbcType=VARCHAR}, 
      #{age,jdbcType=BIGINT}, #{ageName,jdbcType=VARCHAR}, #{collectionCount,jdbcType=INTEGER}, 
      #{countUnit,jdbcType=BIGINT}, #{countUnitName,jdbcType=VARCHAR}, #{identifyLevel,jdbcType=BIGINT}, 
      #{identifyLevelName,jdbcType=VARCHAR}, #{situation,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, 
      #{whereabouts,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsLogoffCollectionInfoDO" useGeneratedKeys="true">
        insert into scs_logoff_collection_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="logoffId != null">
                logoff_id,
            </if>
            <if test="logoffCertificateNo != null">
                logoff_certificate_no,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="registerNo != null">
                register_no,
            </if>
            <if test="classify != null">
                classify,
            </if>
            <if test="classifyName != null">
                classify_name,
            </if>
            <if test="collectionName != null">
                collection_name,
            </if>
            <if test="era != null">
                era,
            </if>
            <if test="age != null">
                age,
            </if>
            <if test="ageName != null">
                age_name,
            </if>
            <if test="collectionCount != null">
                collection_count,
            </if>
            <if test="countUnit != null">
                count_unit,
            </if>
            <if test="countUnitName != null">
                count_unit_name,
            </if>
            <if test="identifyLevel != null">
                identify_level,
            </if>
            <if test="identifyLevelName != null">
                identify_level_name,
            </if>
            <if test="situation != null">
                situation,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="whereabouts != null">
                whereabouts,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="logoffId != null">
                #{logoffId,jdbcType=BIGINT},
            </if>
            <if test="logoffCertificateNo != null">
                #{logoffCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="classify != null">
                #{classify,jdbcType=BIGINT},
            </if>
            <if test="classifyName != null">
                #{classifyName,jdbcType=VARCHAR},
            </if>
            <if test="collectionName != null">
                #{collectionName,jdbcType=VARCHAR},
            </if>
            <if test="era != null">
                #{era,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                #{age,jdbcType=BIGINT},
            </if>
            <if test="ageName != null">
                #{ageName,jdbcType=VARCHAR},
            </if>
            <if test="collectionCount != null">
                #{collectionCount,jdbcType=INTEGER},
            </if>
            <if test="countUnit != null">
                #{countUnit,jdbcType=BIGINT},
            </if>
            <if test="countUnitName != null">
                #{countUnitName,jdbcType=VARCHAR},
            </if>
            <if test="identifyLevel != null">
                #{identifyLevel,jdbcType=BIGINT},
            </if>
            <if test="identifyLevelName != null">
                #{identifyLevelName,jdbcType=VARCHAR},
            </if>
            <if test="situation != null">
                #{situation,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="whereabouts != null">
                #{whereabouts,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsLogoffCollectionInfoDO">
        update scs_logoff_collection_info
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="logoffId != null">
                logoff_id = #{logoffId,jdbcType=BIGINT},
            </if>
            <if test="logoffCertificateNo != null">
                logoff_certificate_no = #{logoffCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                register_no = #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="classify != null">
                classify = #{classify,jdbcType=BIGINT},
            </if>
            <if test="classifyName != null">
                classify_name = #{classifyName,jdbcType=VARCHAR},
            </if>
            <if test="collectionName != null">
                collection_name = #{collectionName,jdbcType=VARCHAR},
            </if>
            <if test="era != null">
                era = #{era,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                age = #{age,jdbcType=BIGINT},
            </if>
            <if test="ageName != null">
                age_name = #{ageName,jdbcType=VARCHAR},
            </if>
            <if test="collectionCount != null">
                collection_count = #{collectionCount,jdbcType=INTEGER},
            </if>
            <if test="countUnit != null">
                count_unit = #{countUnit,jdbcType=BIGINT},
            </if>
            <if test="countUnitName != null">
                count_unit_name = #{countUnitName,jdbcType=VARCHAR},
            </if>
            <if test="identifyLevel != null">
                identify_level = #{identifyLevel,jdbcType=BIGINT},
            </if>
            <if test="identifyLevelName != null">
                identify_level_name = #{identifyLevelName,jdbcType=VARCHAR},
            </if>
            <if test="situation != null">
                situation = #{situation,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="whereabouts != null">
                whereabouts = #{whereabouts,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsLogoffCollectionInfoDO">
    update scs_logoff_collection_info
    set company_id = #{companyId,jdbcType=BIGINT},
      logoff_id = #{logoffId,jdbcType=BIGINT},
      logoff_certificate_no = #{logoffCertificateNo,jdbcType=VARCHAR},
      collection_id = #{collectionId,jdbcType=BIGINT},
      register_no = #{registerNo,jdbcType=VARCHAR},
      classify = #{classify,jdbcType=BIGINT},
      classify_name = #{classifyName,jdbcType=VARCHAR},
      collection_name = #{collectionName,jdbcType=VARCHAR},
      era = #{era,jdbcType=VARCHAR},
      age = #{age,jdbcType=BIGINT},
      age_name = #{ageName,jdbcType=VARCHAR},
      collection_count = #{collectionCount,jdbcType=INTEGER},
      count_unit = #{countUnit,jdbcType=BIGINT},
      count_unit_name = #{countUnitName,jdbcType=VARCHAR},
      identify_level = #{identifyLevel,jdbcType=BIGINT},
      identify_level_name = #{identifyLevelName,jdbcType=VARCHAR},
      situation = #{situation,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      whereabouts = #{whereabouts,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <delete id="deleteByLogoffId">
    delete from scs_logoff_collection_info
    where company_id = #{companyId,jdbcType=BIGINT} and logoff_id = #{logoffId,jdbcType=BIGINT}
    </delete>

    <select id="listByLogoffIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_logoff_collection_info
        where company_id = #{companyId,jdbcType=BIGINT} and logoff_id in
        <foreach collection="logoffIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <select id="listByLogoffId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_logoff_collection_info
        where company_id = #{companyId,jdbcType=BIGINT} and logoff_id = #{logoffId,jdbcType=BIGINT}
    </select>

    <update id="updateByLogoffId" parameterType="com.das.museum.infr.dataobject.ScsLogoffCollectionInfoDO">
        update scs_logoff_collection_info
        <set>
            <if test="logoffCertificateNo != null">
                logoff_certificate_no = #{logoffCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                register_no = #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="classify != null">
                classify = #{classify,jdbcType=BIGINT},
            </if>
            <if test="classifyName != null">
                classify_name = #{classifyName,jdbcType=VARCHAR},
            </if>
            <if test="collectionName != null">
                collection_name = #{collectionName,jdbcType=VARCHAR},
            </if>
            <if test="era != null">
                era = #{era,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                age = #{age,jdbcType=BIGINT},
            </if>
            <if test="ageName != null">
                age_name = #{ageName,jdbcType=VARCHAR},
            </if>
            <if test="collectionCount != null">
                collection_count = #{collectionCount,jdbcType=INTEGER},
            </if>
            <if test="countUnit != null">
                count_unit = #{countUnit,jdbcType=BIGINT},
            </if>
            <if test="countUnitName != null">
                count_unit_name = #{countUnitName,jdbcType=VARCHAR},
            </if>
            <if test="identifyLevel != null">
                identify_level = #{identifyLevel,jdbcType=BIGINT},
            </if>
            <if test="identifyLevelName != null">
                identify_level_name = #{identifyLevelName,jdbcType=VARCHAR},
            </if>
            <if test="situation != null">
                situation = #{situation,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="whereabouts != null">
                whereabouts = #{whereabouts,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where company_id = #{companyId,jdbcType=BIGINT} and logoff_id = #{logoffId,jdbcType=BIGINT}
    </update>

    <select id="listByCollectionIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_logoff_collection_info
        where collection_id in
        <foreach collection="collectionIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
        and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="selectByCollectionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_logoff_collection_info
        where collection_id = #{collectionId,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>
</mapper>