<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.GztCommonApplicationMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.GztCommonApplicationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="application_id" jdbcType="BIGINT" property="applicationId" />
    <result column="application_name" jdbcType="VARCHAR" property="applicationName" />
    <result column="application_url" jdbcType="VARCHAR" property="applicationUrl" />
    <result column="application_icon" jdbcType="VARCHAR" property="applicationIcon" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, user_id, application_id, application_name, application_url, application_icon, show_name,
    creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gzt_common_application
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gzt_common_application
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.GztCommonApplicationDO" useGeneratedKeys="true">
    insert into gzt_common_application (company_id, user_id, application_id, 
      application_name, application_url, application_icon, show_name,
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{applicationId,jdbcType=BIGINT}, 
      #{applicationName,jdbcType=VARCHAR}, #{applicationUrl,jdbcType=VARCHAR}, #{applicationIcon,jdbcType=VARCHAR}, #{showName,jdbcType=VARCHAR},
            #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.GztCommonApplicationDO" useGeneratedKeys="true">
    insert into gzt_common_application
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="applicationId != null">
        application_id,
      </if>
      <if test="applicationName != null">
        application_name,
      </if>
      <if test="applicationUrl != null">
        application_url,
      </if>
      <if test="applicationIcon != null">
        application_icon,
      </if>
      <if test="showName != null">
        show_name,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="applicationId != null">
        #{applicationId,jdbcType=BIGINT},
      </if>
      <if test="applicationName != null">
        #{applicationName,jdbcType=VARCHAR},
      </if>
      <if test="applicationUrl != null">
        #{applicationUrl,jdbcType=VARCHAR},
      </if>
      <if test="applicationIcon != null">
        #{applicationIcon,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.GztCommonApplicationDO">
    update gzt_common_application
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="applicationId != null">
        application_id = #{applicationId,jdbcType=BIGINT},
      </if>
      <if test="applicationName != null">
        application_name = #{applicationName,jdbcType=VARCHAR},
      </if>
      <if test="applicationUrl != null">
        application_url = #{applicationUrl,jdbcType=VARCHAR},
      </if>
      <if test="applicationIcon != null">
        application_icon = #{applicationIcon,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        show_name = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.GztCommonApplicationDO">
    update gzt_common_application
    set company_id = #{companyId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      application_id = #{applicationId,jdbcType=BIGINT},
      application_name = #{applicationName,jdbcType=VARCHAR},
      application_url = #{applicationUrl,jdbcType=VARCHAR},
      application_icon = #{applicationIcon,jdbcType=VARCHAR},
      show_name = #{showName,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByUserId">
    delete from gzt_common_application
    where company_id = #{companyId,jdbcType=BIGINT}
    and user_id = #{userId,jdbcType=BIGINT}
  </delete>

  <select id="getApplicationListByUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from gzt_common_application
    where  company_id = #{companyId,jdbcType=BIGINT}
    and user_id = #{userId,jdbcType=BIGINT}
  </select>
</mapper>