<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.CommDocumentMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.CommDocumentDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
        <result column="file_format" jdbcType="VARCHAR" property="fileFormat"/>
        <result column="source_file_name" jdbcType="VARCHAR" property="sourceFileName"/>
        <result column="save_file_name" jdbcType="VARCHAR" property="saveFileName"/>
        <result column="file_size" jdbcType="BIGINT" property="fileSize"/>
        <result column="bucket" jdbcType="VARCHAR" property="bucket"/>
        <result column="path" jdbcType="VARCHAR" property="path"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , company_id, file_type, file_format, source_file_name, save_file_name, file_size,
    bucket, `path`, url, description, is_delete, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_document
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from comm_document
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommDocumentDO"
            useGeneratedKeys="true">
        insert into comm_document (company_id, file_type, file_format,
                                   source_file_name, save_file_name, file_size,
                                   bucket, `path`, url,
                                   description, is_delete, creator,
                                   modifier, gmt_create, gmt_modified)
        values (#{companyId,jdbcType=BIGINT}, #{fileType,jdbcType=VARCHAR}, #{fileFormat,jdbcType=VARCHAR},
                #{sourceFileName,jdbcType=VARCHAR}, #{saveFileName,jdbcType=VARCHAR}, #{fileSize,jdbcType=BIGINT},
                #{bucket,jdbcType=VARCHAR}, #{path,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},
                #{description,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT}, #{creator,jdbcType=BIGINT},
                #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.CommDocumentDO" useGeneratedKeys="true">
        insert into comm_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="fileType != null">
                file_type,
            </if>
            <if test="fileFormat != null">
                file_format,
            </if>
            <if test="sourceFileName != null">
                source_file_name,
            </if>
            <if test="saveFileName != null">
                save_file_name,
            </if>
            <if test="fileSize != null">
                file_size,
            </if>
            <if test="bucket != null">
                bucket,
            </if>
            <if test="path != null">
                `path`,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="fileType != null">
                #{fileType,jdbcType=VARCHAR},
            </if>
            <if test="fileFormat != null">
                #{fileFormat,jdbcType=VARCHAR},
            </if>
            <if test="sourceFileName != null">
                #{sourceFileName,jdbcType=VARCHAR},
            </if>
            <if test="saveFileName != null">
                #{saveFileName,jdbcType=VARCHAR},
            </if>
            <if test="fileSize != null">
                #{fileSize,jdbcType=BIGINT},
            </if>
            <if test="bucket != null">
                #{bucket,jdbcType=VARCHAR},
            </if>
            <if test="path != null">
                #{path,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.CommDocumentDO">
        update comm_document
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="fileType != null">
                file_type = #{fileType,jdbcType=VARCHAR},
            </if>
            <if test="fileFormat != null">
                file_format = #{fileFormat,jdbcType=VARCHAR},
            </if>
            <if test="sourceFileName != null">
                source_file_name = #{sourceFileName,jdbcType=VARCHAR},
            </if>
            <if test="saveFileName != null">
                save_file_name = #{saveFileName,jdbcType=VARCHAR},
            </if>
            <if test="fileSize != null">
                file_size = #{fileSize,jdbcType=BIGINT},
            </if>
            <if test="bucket != null">
                bucket = #{bucket,jdbcType=VARCHAR},
            </if>
            <if test="path != null">
                `path` = #{path,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.CommDocumentDO">
        update comm_document
        set company_id       = #{companyId,jdbcType=BIGINT},
            file_type        = #{fileType,jdbcType=VARCHAR},
            file_format      = #{fileFormat,jdbcType=VARCHAR},
            source_file_name = #{sourceFileName,jdbcType=VARCHAR},
            save_file_name   = #{saveFileName,jdbcType=VARCHAR},
            file_size        = #{fileSize,jdbcType=BIGINT},
            bucket           = #{bucket,jdbcType=VARCHAR},
            `path`           = #{path,jdbcType=VARCHAR},
            url              = #{url,jdbcType=VARCHAR},
            description      = #{description,jdbcType=VARCHAR},
            is_delete        = #{isDelete,jdbcType=TINYINT},
            creator          = #{creator,jdbcType=BIGINT},
            modifier         = #{modifier,jdbcType=BIGINT},
            gmt_create       = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified     = #{gmtModified,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByCompanyIdAndId" parameterType="com.das.museum.infr.dataobject.CommDocumentDO">
        update comm_document
        set file_type        = #{fileType,jdbcType=VARCHAR},
            file_format      = #{fileFormat,jdbcType=VARCHAR},
            source_file_name = #{sourceFileName,jdbcType=VARCHAR},
            save_file_name   = #{saveFileName,jdbcType=VARCHAR},
            file_size        = #{fileSize,jdbcType=BIGINT},
            bucket           = #{bucket,jdbcType=VARCHAR},
            `path`           = #{path,jdbcType=VARCHAR},
            url              = #{url,jdbcType=VARCHAR},
            description      = #{description,jdbcType=VARCHAR},
            is_delete        = #{isDelete,jdbcType=TINYINT},
            creator          = #{creator,jdbcType=BIGINT},
            modifier         = #{modifier,jdbcType=BIGINT},
            gmt_create       = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified     = #{gmtModified,jdbcType=TIMESTAMP}
        where company_id  = #{companyId,jdbcType=BIGINT}
        and id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <select id="listAllByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_document
        where id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <select id="listByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_document
        where 1=1 and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
        <if test="companyId != null">
            and company_id = #{companyId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="listByIdsExcludeCompanyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_document
        where id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_document
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <delete id="deleteById">
    delete from comm_document
    where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
  </delete>

    <delete id="deleteByIds">
        delete from comm_document
        where company_id = #{companyId,jdbcType=BIGINT} and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </delete>

    <update id="updateDeleteStatusByIds" >
        update comm_document
        <set>
            <if test="deleteStatus != null">
                is_delete = #{deleteStatus,jdbcType=TINYINT},
            </if>
            <if test="userId != null">
                modifier = #{userId,jdbcType=BIGINT},
            </if>
        </set>
        where company_id = #{companyId,jdbcType=BIGINT}
        and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </update>
</mapper>