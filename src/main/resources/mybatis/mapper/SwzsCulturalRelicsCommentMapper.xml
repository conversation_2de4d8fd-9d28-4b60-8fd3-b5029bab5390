<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SwzsCulturalRelicsCommentMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SwzsCulturalRelicsCommentDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="cultural_relics_id" jdbcType="VARCHAR" property="culturalRelicsId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, cultural_relics_id, user_id, `type`, content, `status`, modifier, 
    gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_comment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from swzs_cultural_relics_comment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsCommentDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics_comment (company_id, cultural_relics_id, user_id, 
      `type`, content, `status`, 
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{culturalRelicsId,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, 
      #{type,jdbcType=TINYINT}, #{content,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsCommentDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics_comment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="culturalRelicsId != null">
        cultural_relics_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="culturalRelicsId != null">
        #{culturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsCommentDO">
    update swzs_cultural_relics_comment
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="culturalRelicsId != null">
        cultural_relics_id = #{culturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsCommentDO">
    update swzs_cultural_relics_comment
    set company_id = #{companyId,jdbcType=BIGINT},
      cultural_relics_id = #{culturalRelicsId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=TINYINT},
      content = #{content,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="pageList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_comment
    where 1 = 1
      <if test="companyId != null">
        and company_id = #{companyId,jdbcType=BIGINT}
      </if>
      <if test="content != null">
        and content like concat('%', #{content,jdbcType=VARCHAR}, '%')
      </if>
      <if test="status != null">
        and `status` = #{status,jdbcType=TINYINT}
      </if>
    order by ${sortBy}
  </select>

  <select id="listDO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_comment
    where company_id = #{companyId,jdbcType=BIGINT} and gmt_create &lt;= #{end,jdbcType=TIMESTAMP}
    <if test="start != null">
      and gmt_create &gt;= #{start,jdbcType=TIMESTAMP}
    </if>
  </select>
  <select id="selectByCulturalRelicsId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_comment
    where cultural_relics_id = #{culturalRelicsId,jdbcType=VARCHAR}
    and `status` = 1
    order by gmt_create desc
  </select>

  <select id="selectListByCulturalRelicsId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_comment
    where cultural_relics_id = #{culturalRelicsId,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </select>
</mapper>