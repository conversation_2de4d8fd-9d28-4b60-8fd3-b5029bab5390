<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsOutWarehouseLogMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsOutWarehouseLogDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="out_in_warehouse_id" jdbcType="BIGINT" property="outInWarehouseId"/>
        <result column="out_in_warehouse_certificate_no" jdbcType="VARCHAR" property="outInWarehouseCertificateNo"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, out_in_warehouse_id, out_in_warehouse_certificate_no, collection_id, 
    document_id, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_out_warehouse_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_out_warehouse_log
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsOutWarehouseLogDO" useGeneratedKeys="true">
    insert into scs_out_warehouse_log (company_id, out_in_warehouse_id, out_in_warehouse_certificate_no, 
      collection_id, document_id, creator, 
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{outInWarehouseId,jdbcType=BIGINT}, #{outInWarehouseCertificateNo,jdbcType=VARCHAR}, 
      #{collectionId,jdbcType=BIGINT}, #{documentId,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsOutWarehouseLogDO" useGeneratedKeys="true">
        insert into scs_out_warehouse_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="outInWarehouseId != null">
                out_in_warehouse_id,
            </if>
            <if test="outInWarehouseCertificateNo != null">
                out_in_warehouse_certificate_no,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="outInWarehouseId != null">
                #{outInWarehouseId,jdbcType=BIGINT},
            </if>
            <if test="outInWarehouseCertificateNo != null">
                #{outInWarehouseCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsOutWarehouseLogDO">
        update scs_out_warehouse_log
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="outInWarehouseId != null">
                out_in_warehouse_id = #{outInWarehouseId,jdbcType=BIGINT},
            </if>
            <if test="outInWarehouseCertificateNo != null">
                out_in_warehouse_certificate_no = #{outInWarehouseCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsOutWarehouseLogDO">
    update scs_out_warehouse_log
    set company_id = #{companyId,jdbcType=BIGINT},
      out_in_warehouse_id = #{outInWarehouseId,jdbcType=BIGINT},
      out_in_warehouse_certificate_no = #{outInWarehouseCertificateNo,jdbcType=VARCHAR},
      collection_id = #{collectionId,jdbcType=BIGINT},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->
    <select id="listByOutInWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_out_warehouse_log
        where company_id = #{companyId,jdbcType=BIGINT} and out_in_warehouse_id = #{outInWarehouseId,jdbcType=BIGINT}
    </select>

    <select id="listByOutInWarehouseIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_out_warehouse_log
        where company_id = #{companyId,jdbcType=BIGINT}
        and out_in_warehouse_id in
        <foreach collection="outInWarehouseIds" open="(" close=")" separator="," item="outInWarehouseId" index="index">
            #{outInWarehouseId}
        </foreach>
    </select>


    <select id="listCertIdByCollectIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_out_warehouse_log
        where company_id = #{companyId,jdbcType=BIGINT}
        and collection_id in
        <foreach collection="collectionIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>
</mapper>