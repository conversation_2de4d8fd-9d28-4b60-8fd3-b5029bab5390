<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.TktTicketReserveRuleTeamMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.TktTicketReserveRuleTeamDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="reserve_rule_base_id" jdbcType="BIGINT" property="reserveRuleBaseId" />
    <result column="advance_day" jdbcType="BIGINT" property="advanceDay" />
    <result column="upper_limit" jdbcType="BIGINT" property="upperLimit" />
    <result column="lower_limit" jdbcType="BIGINT" property="lowerLimit" />
    <result column="register_require" jdbcType="TINYINT" property="registerRequire" />
    <result column="reserv_rule_config" jdbcType="VARCHAR" property="reservRuleConfig" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, department_id, reserve_rule_base_id, advance_day, upper_limit, lower_limit, 
    register_require, reserv_rule_config, remark, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tkt_ticket_reserve_rule_team
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tkt_ticket_reserve_rule_team
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketReserveRuleTeamDO" useGeneratedKeys="true">
    insert into tkt_ticket_reserve_rule_team (company_id, department_id, reserve_rule_base_id, 
      advance_day, upper_limit, lower_limit, 
      register_require, reserv_rule_config, remark, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, #{reserveRuleBaseId,jdbcType=BIGINT}, 
      #{advanceDay,jdbcType=BIGINT}, #{upperLimit,jdbcType=BIGINT}, #{lowerLimit,jdbcType=BIGINT}, 
      #{registerRequire,jdbcType=TINYINT}, #{reservRuleConfig,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTicketReserveRuleTeamDO" useGeneratedKeys="true">
    insert into tkt_ticket_reserve_rule_team
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="reserveRuleBaseId != null">
        reserve_rule_base_id,
      </if>
      <if test="advanceDay != null">
        advance_day,
      </if>
      <if test="upperLimit != null">
        upper_limit,
      </if>
      <if test="lowerLimit != null">
        lower_limit,
      </if>
      <if test="registerRequire != null">
        register_require,
      </if>
      <if test="reservRuleConfig != null">
        reserv_rule_config,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="reserveRuleBaseId != null">
        #{reserveRuleBaseId,jdbcType=BIGINT},
      </if>
      <if test="advanceDay != null">
        #{advanceDay,jdbcType=BIGINT},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=BIGINT},
      </if>
      <if test="lowerLimit != null">
        #{lowerLimit,jdbcType=BIGINT},
      </if>
      <if test="registerRequire != null">
        #{registerRequire,jdbcType=TINYINT},
      </if>
      <if test="reservRuleConfig != null">
        #{reservRuleConfig,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.TktTicketReserveRuleTeamDO">
    update tkt_ticket_reserve_rule_team
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="reserveRuleBaseId != null">
        reserve_rule_base_id = #{reserveRuleBaseId,jdbcType=BIGINT},
      </if>
      <if test="advanceDay != null">
        advance_day = #{advanceDay,jdbcType=BIGINT},
      </if>
      <if test="upperLimit != null">
        upper_limit = #{upperLimit,jdbcType=BIGINT},
      </if>
      <if test="lowerLimit != null">
        lower_limit = #{lowerLimit,jdbcType=BIGINT},
      </if>
      <if test="registerRequire != null">
        register_require = #{registerRequire,jdbcType=TINYINT},
      </if>
      <if test="reservRuleConfig != null">
        reserv_rule_config = #{reservRuleConfig,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.TktTicketReserveRuleTeamDO">
    update tkt_ticket_reserve_rule_team
    set company_id = #{companyId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      reserve_rule_base_id = #{reserveRuleBaseId,jdbcType=BIGINT},
      advance_day = #{advanceDay,jdbcType=BIGINT},
      upper_limit = #{upperLimit,jdbcType=BIGINT},
      lower_limit = #{lowerLimit,jdbcType=BIGINT},
      register_require = #{registerRequire,jdbcType=TINYINT},
      reserv_rule_config = #{reservRuleConfig,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByRuleBaseId" parameterType="com.das.museum.infr.dataobject.TktTicketReserveRuleTeamDO">
    update tkt_ticket_reserve_rule_team
    <set>
      <if test="advanceDay != null">
        advance_day = #{advanceDay,jdbcType=BIGINT},
      </if>
      <if test="upperLimit != null">
        upper_limit = #{upperLimit,jdbcType=BIGINT},
      </if>
      <if test="lowerLimit != null">
        lower_limit = #{lowerLimit,jdbcType=BIGINT},
      </if>
      <if test="registerRequire != null">
        register_require = #{registerRequire,jdbcType=TINYINT},
      </if>
      <if test="reservRuleConfig != null">
        reserv_rule_config = #{reservRuleConfig,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where  reserve_rule_base_id = #{reserveRuleBaseId,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </update>

  <select id="selectByRuleBaseId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_ticket_reserve_rule_team
    where reserve_rule_base_id = #{reserveRuleBaseId,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </select>
</mapper>