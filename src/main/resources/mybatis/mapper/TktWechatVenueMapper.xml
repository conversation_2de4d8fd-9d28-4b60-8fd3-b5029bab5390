<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.TktWechatVenueMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.TktWechatVenueDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="ticket_channel_id" jdbcType="BIGINT" property="ticketChannelId" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="venue_name" jdbcType="VARCHAR" property="venueName" />
    <result column="venue_phone" jdbcType="VARCHAR" property="venuePhone" />
    <result column="close_day" jdbcType="VARCHAR" property="closeDay" />
    <result column="is_holidays_postpone" jdbcType="TINYINT" property="isHolidaysPostpone" />
    <result column="is_temporary_close" jdbcType="TINYINT" property="isTemporaryClose" />
    <result column="temporary_close_start_date" jdbcType="TIMESTAMP" property="temporaryCloseStartDate" />
    <result column="temporary_close_end_date" jdbcType="TIMESTAMP" property="temporaryCloseEndDate" />
    <result column="temporary_close_notice" jdbcType="VARCHAR" property="temporaryCloseNotice" />
    <result column="open_time" jdbcType="TIME" property="openTime" />
    <result column="close_time" jdbcType="TIME" property="closeTime" />
    <result column="stop_time" jdbcType="TIME" property="stopTime" />
    <result column="ad_code" jdbcType="VARCHAR" property="adCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="ad_code_path" jdbcType="VARCHAR" property="adCodePath" />
    <result column="city_name_path" jdbcType="VARCHAR" property="cityNamePath" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="notice" jdbcType="VARCHAR" property="notice" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="document_id" jdbcType="VARCHAR" property="documentId" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="venue_lng" jdbcType="VARCHAR" property="venueLng" />
    <result column="venue_lat" jdbcType="VARCHAR" property="venueLat" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, department_id, ticket_channel_id, channel_type, venue_name, venue_phone, 
    close_day, is_holidays_postpone, is_temporary_close, temporary_close_start_date, 
    temporary_close_end_date, temporary_close_notice, open_time, close_time, stop_time, 
    ad_code, city_name, ad_code_path,city_name_path,  address, notice, remark, document_id, creator, modifier, gmt_create,
    gmt_modified, venue_lng, venue_lat
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tkt_wechat_venue
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tkt_wechat_venue
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktWechatVenueDO" useGeneratedKeys="true">
    insert into tkt_wechat_venue (company_id, department_id, ticket_channel_id, 
      channel_type, venue_name, venue_phone, 
      close_day, is_holidays_postpone, is_temporary_close, 
      temporary_close_start_date, temporary_close_end_date, 
      temporary_close_notice, open_time, close_time, 
      stop_time, ad_code, city_name, ad_code_path, city_name_path,
      address, notice, remark, 
      document_id, creator, modifier, 
      gmt_create, gmt_modified, venue_lng, venue_lat)
    values (#{companyId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, #{ticketChannelId,jdbcType=BIGINT}, 
      #{channelType,jdbcType=TINYINT}, #{venueName,jdbcType=VARCHAR}, #{venuePhone,jdbcType=VARCHAR}, 
      #{closeDay,jdbcType=VARCHAR}, #{isHolidaysPostpone,jdbcType=TINYINT}, #{isTemporaryClose,jdbcType=TINYINT}, 
      #{temporaryCloseStartDate,jdbcType=TIMESTAMP}, #{temporaryCloseEndDate,jdbcType=TIMESTAMP}, 
      #{temporaryCloseNotice,jdbcType=VARCHAR}, #{openTime,jdbcType=TIME}, #{closeTime,jdbcType=TIME}, 
      #{stopTime,jdbcType=TIME}, #{adCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR},
      #{adCodePath,jdbcType=VARCHAR}, #{cityNamePath,jdbcType=VARCHAR},
      #{address,jdbcType=VARCHAR}, #{notice,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
      #{documentId,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{venueLng,jdbcType=VARCHAR}, #{venueLat,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktWechatVenueDO" useGeneratedKeys="true">
    insert into tkt_wechat_venue
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="ticketChannelId != null">
        ticket_channel_id,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="venueName != null">
        venue_name,
      </if>
      <if test="venuePhone != null">
        venue_phone,
      </if>
      <if test="closeDay != null">
        close_day,
      </if>
      <if test="isHolidaysPostpone != null">
        is_holidays_postpone,
      </if>
      <if test="isTemporaryClose != null">
        is_temporary_close,
      </if>
      <if test="temporaryCloseStartDate != null">
        temporary_close_start_date,
      </if>
      <if test="temporaryCloseEndDate != null">
        temporary_close_end_date,
      </if>
      <if test="temporaryCloseNotice != null">
        temporary_close_notice,
      </if>
      <if test="openTime != null">
        open_time,
      </if>
      <if test="closeTime != null">
        close_time,
      </if>
      <if test="stopTime != null">
        stop_time,
      </if>
      <if test="adCode != null">
        ad_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="adCodePath != null">
        ad_code_path,
      </if>
      <if test="cityNamePath != null">
        city_name_path,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="notice != null">
        notice,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="documentId != null">
        document_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="venueLng != null">
        venue_lng,
      </if>
      <if test="venueLat != null">
        venue_lat,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="ticketChannelId != null">
        #{ticketChannelId,jdbcType=BIGINT},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=TINYINT},
      </if>
      <if test="venueName != null">
        #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="venuePhone != null">
        #{venuePhone,jdbcType=VARCHAR},
      </if>
      <if test="closeDay != null">
        #{closeDay,jdbcType=VARCHAR},
      </if>
      <if test="isHolidaysPostpone != null">
        #{isHolidaysPostpone,jdbcType=TINYINT},
      </if>
      <if test="isTemporaryClose != null">
        #{isTemporaryClose,jdbcType=TINYINT},
      </if>
      <if test="temporaryCloseStartDate != null">
        #{temporaryCloseStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="temporaryCloseEndDate != null">
        #{temporaryCloseEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="temporaryCloseNotice != null">
        #{temporaryCloseNotice,jdbcType=VARCHAR},
      </if>
      <if test="openTime != null">
        #{openTime,jdbcType=TIME},
      </if>
      <if test="closeTime != null">
        #{closeTime,jdbcType=TIME},
      </if>
      <if test="stopTime != null">
        #{stopTime,jdbcType=TIME},
      </if>
      <if test="adCode != null">
        #{adCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="adCodePath != null">
        #{adCodePath,jdbcType=VARCHAR},
      </if>
      <if test="cityNamePath != null">
        #{cityNamePath,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="notice != null">
        #{notice,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="documentId != null">
        #{documentId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="venueLng != null">
        #{venueLng,jdbcType=VARCHAR},
      </if>
      <if test="venueLat != null">
        #{venueLat,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.TktWechatVenueDO">
    update tkt_wechat_venue
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="ticketChannelId != null">
        ticket_channel_id = #{ticketChannelId,jdbcType=BIGINT},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=TINYINT},
      </if>
      <if test="venueName != null">
        venue_name = #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="venuePhone != null">
        venue_phone = #{venuePhone,jdbcType=VARCHAR},
      </if>
      <if test="closeDay != null">
        close_day = #{closeDay,jdbcType=VARCHAR},
      </if>
      <if test="isHolidaysPostpone != null">
        is_holidays_postpone = #{isHolidaysPostpone,jdbcType=TINYINT},
      </if>
      <if test="isTemporaryClose != null">
        is_temporary_close = #{isTemporaryClose,jdbcType=TINYINT},
      </if>
      <if test="temporaryCloseStartDate != null">
        temporary_close_start_date = #{temporaryCloseStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="temporaryCloseEndDate != null">
        temporary_close_end_date = #{temporaryCloseEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="temporaryCloseNotice != null">
        temporary_close_notice = #{temporaryCloseNotice,jdbcType=VARCHAR},
      </if>
      <if test="openTime != null">
        open_time = #{openTime,jdbcType=TIME},
      </if>
      <if test="closeTime != null">
        close_time = #{closeTime,jdbcType=TIME},
      </if>
      <if test="stopTime != null">
        stop_time = #{stopTime,jdbcType=TIME},
      </if>
      <if test="adCode != null">
        ad_code = #{adCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="adCodePath != null">
        ad_code_path = #{adCodePath,jdbcType=VARCHAR},
      </if>
      <if test="cityNamePath != null">
        city_name_path = #{cityNamePath,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="notice != null">
        notice = #{notice,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="documentId != null">
        document_id = #{documentId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="venueLng != null">
        venue_lng = #{venueLng,jdbcType=VARCHAR},
      </if>
      <if test="venueLat != null">
        venue_lat = #{venueLat,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.TktWechatVenueDO">
    update tkt_wechat_venue
    set company_id = #{companyId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      ticket_channel_id = #{ticketChannelId,jdbcType=BIGINT},
      channel_type = #{channelType,jdbcType=TINYINT},
      venue_name = #{venueName,jdbcType=VARCHAR},
      venue_phone = #{venuePhone,jdbcType=VARCHAR},
      close_day = #{closeDay,jdbcType=VARCHAR},
      is_holidays_postpone = #{isHolidaysPostpone,jdbcType=TINYINT},
      is_temporary_close = #{isTemporaryClose,jdbcType=TINYINT},
      temporary_close_start_date = #{temporaryCloseStartDate,jdbcType=TIMESTAMP},
      temporary_close_end_date = #{temporaryCloseEndDate,jdbcType=TIMESTAMP},
      temporary_close_notice = #{temporaryCloseNotice,jdbcType=VARCHAR},
      open_time = #{openTime,jdbcType=TIME},
      close_time = #{closeTime,jdbcType=TIME},
      stop_time = #{stopTime,jdbcType=TIME},
      ad_code = #{adCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      ad_code_path = #{adCodePath,jdbcType=VARCHAR},
      city_name_path = #{cityNamePath,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      notice = #{notice,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      venue_lng = #{venueLng,jdbcType=VARCHAR},
      venue_lat = #{venueLat,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_wechat_venue
    where company_id = #{companyId,jdbcType=BIGINT}
    and channel_type = #{channelType,jdbcType=TINYINT}
  </select>

  <update id="updateByIdAndCompanyId" parameterType="com.das.museum.infr.dataobject.TktWechatVenueDO">
    update tkt_wechat_venue
    <set>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="ticketChannelId != null">
        ticket_channel_id = #{ticketChannelId,jdbcType=BIGINT},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=TINYINT},
      </if>
      <if test="venueName != null">
        venue_name = #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="venuePhone != null">
        venue_phone = #{venuePhone,jdbcType=VARCHAR},
      </if>
      <if test="closeDay != null">
        close_day = #{closeDay,jdbcType=VARCHAR},
      </if>
      <if test="isHolidaysPostpone != null">
        is_holidays_postpone = #{isHolidaysPostpone,jdbcType=TINYINT},
      </if>
      <if test="isTemporaryClose != null">
        is_temporary_close = #{isTemporaryClose,jdbcType=TINYINT},
      </if>
      <if test="temporaryCloseStartDate != null">
        temporary_close_start_date = #{temporaryCloseStartDate,jdbcType=TIMESTAMP},
      </if>
        temporary_close_end_date = #{temporaryCloseEndDate,jdbcType=TIMESTAMP},
      <if test="temporaryCloseNotice != null">
        temporary_close_notice = #{temporaryCloseNotice,jdbcType=VARCHAR},
      </if>
      <if test="openTime != null">
        open_time = #{openTime,jdbcType=TIME},
      </if>
      <if test="closeTime != null">
        close_time = #{closeTime,jdbcType=TIME},
      </if>
      <if test="stopTime != null">
        stop_time = #{stopTime,jdbcType=TIME},
      </if>
      <if test="adCode != null">
        ad_code = #{adCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="adCodePath != null">
        ad_code_path = #{adCodePath,jdbcType=VARCHAR},
      </if>
      <if test="cityNamePath != null">
        city_name_path = #{cityNamePath,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="notice != null">
        notice = #{notice,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="documentId != null">
        document_id = #{documentId,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="venueLng != null">
        venue_lng = #{venueLng,jdbcType=VARCHAR},
      </if>
      <if test="venueLat != null">
        venue_lat = #{venueLat,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </update>

  <select id="listByVenueName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_wechat_venue
    where venue_name like CONCAT('%',#{venueName,jdbcType=VARCHAR},'%')
  </select>

  <select id="listByCompanyIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_wechat_venue
    where company_id in
    <foreach collection="companyIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>
</mapper>