<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.CommClassificationItemRelMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.CommClassificationItemRelDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="ancestor_id" jdbcType="BIGINT" property="ancestorId"/>
        <result column="descendant_id" jdbcType="BIGINT" property="descendantId"/>
        <result column="distance" jdbcType="INTEGER" property="distance"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, ancestor_id, descendant_id, distance, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_classification_item_rel
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_classification_item_rel
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.CommClassificationItemRelDO" useGeneratedKeys="true">
    insert into comm_classification_item_rel (ancestor_id, descendant_id, distance, 
      gmt_create, gmt_modified)
    values (#{ancestorId,jdbcType=BIGINT}, #{descendantId,jdbcType=BIGINT}, #{distance,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.CommClassificationItemRelDO" useGeneratedKeys="true">
        insert into comm_classification_item_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ancestorId != null">
                ancestor_id,
            </if>
            <if test="descendantId != null">
                descendant_id,
            </if>
            <if test="distance != null">
                distance,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ancestorId != null">
                #{ancestorId,jdbcType=BIGINT},
            </if>
            <if test="descendantId != null">
                #{descendantId,jdbcType=BIGINT},
            </if>
            <if test="distance != null">
                #{distance,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.CommClassificationItemRelDO">
        update comm_classification_item_rel
        <set>
            <if test="ancestorId != null">
                ancestor_id = #{ancestorId,jdbcType=BIGINT},
            </if>
            <if test="descendantId != null">
                descendant_id = #{descendantId,jdbcType=BIGINT},
            </if>
            <if test="distance != null">
                distance = #{distance,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.CommClassificationItemRelDO">
    update comm_classification_item_rel
    set ancestor_id = #{ancestorId,jdbcType=BIGINT},
      descendant_id = #{descendantId,jdbcType=BIGINT},
      distance = #{distance,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <insert id="insertParentPath">
    insert into comm_classification_item_rel (ancestor_id, descendant_id, distance)
    (SELECT ancestor_id, #{classificationId}, distance+1 FROM comm_classification_item_rel WHERE descendant_id = #{parentId})
  </insert>

    <delete id="deleteByDescendantId">
    delete from comm_classification_item_rel
    where descendant_id = #{descendantId,jdbcType=INTEGER}
  </delete>

    <delete id="deleteBatchByDescendantId">
        delete from comm_classification_item_rel
        where descendant_id in
        <foreach collection="descendantIds" item="id" index="index" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <select id="listByParentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM comm_classification_item_rel WHERE ancestor_id in (
        (SELECT descendant_id FROM comm_classification_item_rel WHERE ancestor_id = #{parentId} AND distance > 0)) GROUP
        BY
        descendant_id HAVING count(descendant_id) =1
    </select>
</mapper>