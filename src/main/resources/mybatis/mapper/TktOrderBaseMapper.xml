<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.TktOrderBaseMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.TktOrderBaseDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="venue_name" jdbcType="VARCHAR" property="venueName" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="ticket_name" jdbcType="VARCHAR" property="ticketName" />
    <result column="ticket_code" jdbcType="VARCHAR" property="ticketCode" />
    <result column="total_price" jdbcType="BIGINT" property="totalPrice" />
    <result column="total_ticket_count" jdbcType="BIGINT" property="totalTicketCount" />
    <result column="total_people_count" jdbcType="BIGINT" property="totalPeopleCount" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="reserve_type" jdbcType="TINYINT" property="reserveType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="reserve_date" jdbcType="TIMESTAMP" property="reserveDate" />
    <result column="time_period_id" jdbcType="BIGINT" property="timePeriodId" />
    <result column="time_period_start" jdbcType="TIME" property="timePeriodStart" />
    <result column="time_period_end" jdbcType="TIME" property="timePeriodEnd" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, department_id, venue_name, user_id, phone, order_no, ticket_name,
    ticket_code, total_price, total_ticket_count, total_people_count, channel_type, order_type, 
    reserve_type, `status`, reserve_date, time_period_id, time_period_start, time_period_end, 
    remark, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_order_base
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tkt_order_base
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktOrderBaseDO" useGeneratedKeys="true">
    insert into tkt_order_base (company_id, department_id, venue_name,
      user_id, phone, order_no,
      ticket_name, ticket_code, total_price,
      total_ticket_count, total_people_count, channel_type,
      order_type, reserve_type, `status`,
      reserve_date, time_period_id, time_period_start,
      time_period_end, remark, creator,
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, #{venueName,jdbcType=VARCHAR},
      #{userId,jdbcType=BIGINT}, #{phone,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR},
      #{ticketName,jdbcType=VARCHAR}, #{ticketCode,jdbcType=VARCHAR}, #{totalPrice,jdbcType=BIGINT},
      #{totalTicketCount,jdbcType=BIGINT}, #{totalPeopleCount,jdbcType=BIGINT}, #{channelType,jdbcType=TINYINT},
      #{orderType,jdbcType=TINYINT}, #{reserveType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT},
      #{reserveDate,jdbcType=TIMESTAMP}, #{timePeriodId,jdbcType=BIGINT}, #{timePeriodStart,jdbcType=TIME},
      #{timePeriodEnd,jdbcType=TIME}, #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT},
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktOrderBaseDO" useGeneratedKeys="true">
    insert into tkt_order_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="venueName != null">
        venue_name,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="ticketName != null">
        ticket_name,
      </if>
      <if test="ticketCode != null">
        ticket_code,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="totalTicketCount != null">
        total_ticket_count,
      </if>
      <if test="totalPeopleCount != null">
        total_people_count,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="reserveType != null">
        reserve_type,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="reserveDate != null">
        reserve_date,
      </if>
      <if test="timePeriodId != null">
        time_period_id,
      </if>
      <if test="timePeriodStart != null">
        time_period_start,
      </if>
      <if test="timePeriodEnd != null">
        time_period_end,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="venueName != null">
        #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="ticketName != null">
        #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=BIGINT},
      </if>
      <if test="totalTicketCount != null">
        #{totalTicketCount,jdbcType=BIGINT},
      </if>
      <if test="totalPeopleCount != null">
        #{totalPeopleCount,jdbcType=BIGINT},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=TINYINT},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="reserveType != null">
        #{reserveType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="reserveDate != null">
        #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodId != null">
        #{timePeriodId,jdbcType=BIGINT},
      </if>
      <if test="timePeriodStart != null">
        #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.TktOrderBaseDO">
    update tkt_order_base
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="venueName != null">
        venue_name = #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="ticketName != null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        ticket_code = #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=BIGINT},
      </if>
      <if test="totalTicketCount != null">
        total_ticket_count = #{totalTicketCount,jdbcType=BIGINT},
      </if>
      <if test="totalPeopleCount != null">
        total_people_count = #{totalPeopleCount,jdbcType=BIGINT},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=TINYINT},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="reserveType != null">
        reserve_type = #{reserveType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="reserveDate != null">
        reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodId != null">
        time_period_id = #{timePeriodId,jdbcType=BIGINT},
      </if>
      <if test="timePeriodStart != null">
        time_period_start = #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        time_period_end = #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.TktOrderBaseDO">
    update tkt_order_base
    set company_id = #{companyId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      venue_name = #{venueName,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      phone = #{phone,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      ticket_name = #{ticketName,jdbcType=VARCHAR},
      ticket_code = #{ticketCode,jdbcType=VARCHAR},
      total_price = #{totalPrice,jdbcType=BIGINT},
      total_ticket_count = #{totalTicketCount,jdbcType=BIGINT},
      total_people_count = #{totalPeopleCount,jdbcType=BIGINT},
      channel_type = #{channelType,jdbcType=TINYINT},
      order_type = #{orderType,jdbcType=TINYINT},
      reserve_type = #{reserveType,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      time_period_id = #{timePeriodId,jdbcType=BIGINT},
      time_period_start = #{timePeriodStart,jdbcType=TIME},
      time_period_end = #{timePeriodEnd,jdbcType=TIME},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getPageList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_order_base
    where company_id = #{companyId,jdbcType=BIGINT}
    and order_type = #{orderType,jdbcType=TINYINT}
    <if test="keyword != null and keyword != '' ">
      and (order_no like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')  or  ticket_name like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%') )
    </if>
    <if test="channelType != null">
      and channel_type = #{channelType,jdbcType=TINYINT}
    </if>
    <if test="createTimeStart != null and createTimeStart != '' ">
      and gmt_create >= #{createTimeStart,jdbcType=TIMESTAMP}
    </if>
    <if test="createTimeEnd != null and createTimeEnd != '' ">
      and gmt_create <![CDATA[<=]]> #{createTimeEnd,jdbcType=TIMESTAMP}
    </if>
    order by ${sortBy}
  </select>

  <select id="selectListByTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_order_base
    where company_id = #{companyId,jdbcType=BIGINT}
    and order_type = #{orderType,jdbcType=TINYINT}
    <if test="startTime != null">
      and reserve_date >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and reserve_date <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="selectByOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_order_base
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </select>

  <select id="listByOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_order_base
    where company_id = #{companyId,jdbcType=BIGINT}
    and id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="queryDataTimeList" resultType="string">
    select
    DISTINCT left(reserve_date,#{dateType,jdbcType=INTEGER}) reserveDate
    from tkt_order_base
    where status = 2
    <if test="startTime != null and startTime !='' ">
      and reserve_date >= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="endTime != null and endTime !='' ">
      and reserve_date <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
    </if>
    order by reserveDate
  </select>

  <select id="getFirstOrderTime" resultType="java.util.Date">
    select
        reserve_date
    from tkt_order_base
    where company_id = #{companyId,jdbcType=BIGINT}
    order by reserve_date limit 1
  </select>

  <select id="statisticsOnNumList" resultType="com.das.museum.infr.dataobjectexpand.OrderStatisticsByNumDO">
    select
      left(B.write_off_date,#{dataType,jdbcType=INTEGER}) dataTime,
      sum(A.total_people_count) manNum
    from tkt_order_base A
    inner join tkt_team_order_detail B on A.order_no = B.order_no
    where
    A.company_id = #{companyId,jdbcType=BIGINT}
    and B.status = 2
    and A.order_type = #{orderType,jdbcType=TINYINT}
    <if test="startTime != null and startTime !='' ">
      and B.write_off_date >= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="endTime != null and endTime !='' ">
      and B.write_off_date <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
    </if>
    group by left(B.write_off_date,#{dataType,jdbcType=INTEGER})
  </select>

  <select id="listByReserveDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_order_base
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="orderType != null">
      and order_type = #{orderType,jdbcType=TINYINT}
    </if>
    <if test="status != null">
      and `status` = #{status,jdbcType=TINYINT}
    </if>
    <if test="reserveDateStart != null and reserveDateEnd != null">
      and reserve_date &gt;= #{reserveDateStart,jdbcType=TIMESTAMP} and reserve_date &lt;= #{reserveDateEnd,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="selectByOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_order_base
    where id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>
</mapper>