<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SwzsCulturalRelicsMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SwzsCulturalRelicsDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="level_id" jdbcType="BIGINT" property="levelId" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="complete_degree_id" jdbcType="BIGINT" property="completeDegreeId" />
    <result column="complete_degree_name" jdbcType="VARCHAR" property="completeDegreeName" />
    <result column="dynasty_id" jdbcType="BIGINT" property="dynastyId" />
    <result column="dynasty_name" jdbcType="VARCHAR" property="dynastyName" />
    <result column="ad_year_id" jdbcType="BIGINT" property="adYearId" />
    <result column="ad_year_name" jdbcType="VARCHAR" property="adYearName" />
    <result column="specific_age" jdbcType="VARCHAR" property="specificAge" />
    <result column="texture_id" jdbcType="BIGINT" property="textureId" />
    <result column="texture_name" jdbcType="VARCHAR" property="textureName" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="source_name" jdbcType="VARCHAR" property="sourceName" />
    <result column="size" jdbcType="VARCHAR" property="size" />
    <result column="keyword" jdbcType="VARCHAR" property="keyword" />
    <result column="cover_id" jdbcType="VARCHAR" property="coverId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="three_model_url" jdbcType="VARCHAR" property="threeModelUrl" />
    <result column="three_model_status" jdbcType="TINYINT" property="threeModelStatus" />
    <result column="two_model_id" jdbcType="VARCHAR" property="twoModelId" />
    <result column="compared_cultural_relics_id" jdbcType="VARCHAR" property="comparedCulturalRelicsId" />
    <result column="similar_cultural_relics_id" jdbcType="VARCHAR" property="similarCulturalRelicsId" />
    <result column="audio_id" jdbcType="VARCHAR" property="audioId" />
    <result column="video_id" jdbcType="VARCHAR" property="videoId" />
    <result column="academic_literature_id" jdbcType="VARCHAR" property="academicLiteratureId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="data_status" jdbcType="TINYINT" property="dataStatus" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="auto_display" jdbcType="TINYINT" property="autoDisplay" />
    <result column="is_new" jdbcType="TINYINT" property="isNew" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, `number`, `name`, level_id, level_name, complete_degree_id, complete_degree_name, 
    dynasty_id, dynasty_name, ad_year_id, ad_year_name, specific_age, texture_id, texture_name, 
    source_id, source_name, `size`, keyword, cover_id, remark, three_model_url, three_model_status, 
    two_model_id, compared_cultural_relics_id, similar_cultural_relics_id, audio_id, 
    video_id, academic_literature_id, `status`, data_status, creator, modifier, gmt_create, auto_display,
    gmt_modified, is_new
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from swzs_cultural_relics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics (company_id, `number`, `name`, 
      level_id, level_name, complete_degree_id, 
      complete_degree_name, dynasty_id, dynasty_name, 
      ad_year_id, ad_year_name, specific_age, 
      texture_id, texture_name, source_id, 
      source_name, `size`, keyword, 
      cover_id, remark, three_model_url, 
      three_model_status, two_model_id, compared_cultural_relics_id, 
      similar_cultural_relics_id, audio_id, video_id, 
      academic_literature_id, `status`, data_status, 
      creator, modifier, gmt_create, 
      gmt_modified, auto_display, is_new)
    values (#{companyId,jdbcType=BIGINT}, #{number,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{levelId,jdbcType=BIGINT}, #{levelName,jdbcType=VARCHAR}, #{completeDegreeId,jdbcType=BIGINT}, 
      #{completeDegreeName,jdbcType=VARCHAR}, #{dynastyId,jdbcType=BIGINT}, #{dynastyName,jdbcType=VARCHAR}, 
      #{adYearId,jdbcType=BIGINT}, #{adYearName,jdbcType=VARCHAR}, #{specificAge,jdbcType=VARCHAR}, 
      #{textureId,jdbcType=BIGINT}, #{textureName,jdbcType=VARCHAR}, #{sourceId,jdbcType=BIGINT}, 
      #{sourceName,jdbcType=VARCHAR}, #{size,jdbcType=VARCHAR}, #{keyword,jdbcType=VARCHAR}, 
      #{coverId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{threeModelUrl,jdbcType=VARCHAR}, 
      #{threeModelStatus,jdbcType=TINYINT}, #{twoModelId,jdbcType=VARCHAR}, #{comparedCulturalRelicsId,jdbcType=VARCHAR}, 
      #{similarCulturalRelicsId,jdbcType=VARCHAR}, #{audioId,jdbcType=VARCHAR}, #{videoId,jdbcType=VARCHAR}, 
      #{academicLiteratureId,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{dataStatus,jdbcType=TINYINT}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{autoDisplay,jdbcType=TINYINT}, #{isNew,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="levelId != null">
        level_id,
      </if>
      <if test="levelName != null">
        level_name,
      </if>
      <if test="completeDegreeId != null">
        complete_degree_id,
      </if>
      <if test="completeDegreeName != null">
        complete_degree_name,
      </if>
      <if test="dynastyId != null">
        dynasty_id,
      </if>
      <if test="dynastyName != null">
        dynasty_name,
      </if>
      <if test="adYearId != null">
        ad_year_id,
      </if>
      <if test="adYearName != null">
        ad_year_name,
      </if>
      <if test="specificAge != null">
        specific_age,
      </if>
      <if test="textureId != null">
        texture_id,
      </if>
      <if test="textureName != null">
        texture_name,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="sourceName != null">
        source_name,
      </if>
      <if test="size != null">
        `size`,
      </if>
      <if test="keyword != null">
        keyword,
      </if>
      <if test="coverId != null">
        cover_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="threeModelUrl != null">
        three_model_url,
      </if>
      <if test="threeModelStatus != null">
        three_model_status,
      </if>
      <if test="twoModelId != null">
        two_model_id,
      </if>
      <if test="comparedCulturalRelicsId != null">
        compared_cultural_relics_id,
      </if>
      <if test="similarCulturalRelicsId != null">
        similar_cultural_relics_id,
      </if>
      <if test="audioId != null">
        audio_id,
      </if>
      <if test="videoId != null">
        video_id,
      </if>
      <if test="academicLiteratureId != null">
        academic_literature_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="dataStatus != null">
        data_status,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="autoDisplay != null">
        auto_display,
      </if>
      <if test="isNew != null">
        is_new,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="number != null">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="levelId != null">
        #{levelId,jdbcType=BIGINT},
      </if>
      <if test="levelName != null">
        #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="completeDegreeId != null">
        #{completeDegreeId,jdbcType=BIGINT},
      </if>
      <if test="completeDegreeName != null">
        #{completeDegreeName,jdbcType=VARCHAR},
      </if>
      <if test="dynastyId != null">
        #{dynastyId,jdbcType=BIGINT},
      </if>
      <if test="dynastyName != null">
        #{dynastyName,jdbcType=VARCHAR},
      </if>
      <if test="adYearId != null">
        #{adYearId,jdbcType=BIGINT},
      </if>
      <if test="adYearName != null">
        #{adYearName,jdbcType=VARCHAR},
      </if>
      <if test="specificAge != null">
        #{specificAge,jdbcType=VARCHAR},
      </if>
      <if test="textureId != null">
        #{textureId,jdbcType=BIGINT},
      </if>
      <if test="textureName != null">
        #{textureName,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="sourceName != null">
        #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="keyword != null">
        #{keyword,jdbcType=VARCHAR},
      </if>
      <if test="coverId != null">
        #{coverId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="threeModelUrl != null">
        #{threeModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="threeModelStatus != null">
        #{threeModelStatus,jdbcType=TINYINT},
      </if>
      <if test="twoModelId != null">
        #{twoModelId,jdbcType=VARCHAR},
      </if>
      <if test="comparedCulturalRelicsId != null">
        #{comparedCulturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="similarCulturalRelicsId != null">
        #{similarCulturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="audioId != null">
        #{audioId,jdbcType=VARCHAR},
      </if>
      <if test="videoId != null">
        #{videoId,jdbcType=VARCHAR},
      </if>
      <if test="academicLiteratureId != null">
        #{academicLiteratureId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="dataStatus != null">
        #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="autoDisplay != null">
        #{autoDisplay,jdbcType=TINYINT},
      </if>
      <if test="isNew != null">
        #{isNew,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsDO">
    update swzs_cultural_relics
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="levelId != null">
        level_id = #{levelId,jdbcType=BIGINT},
      </if>
      <if test="levelName != null">
        level_name = #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="completeDegreeId != null">
        complete_degree_id = #{completeDegreeId,jdbcType=BIGINT},
      </if>
      <if test="completeDegreeName != null">
        complete_degree_name = #{completeDegreeName,jdbcType=VARCHAR},
      </if>
      <if test="dynastyId != null">
        dynasty_id = #{dynastyId,jdbcType=BIGINT},
      </if>
      <if test="dynastyName != null">
        dynasty_name = #{dynastyName,jdbcType=VARCHAR},
      </if>
      <if test="adYearId != null">
        ad_year_id = #{adYearId,jdbcType=BIGINT},
      </if>
      <if test="adYearName != null">
        ad_year_name = #{adYearName,jdbcType=VARCHAR},
      </if>
      <if test="specificAge != null">
        specific_age = #{specificAge,jdbcType=VARCHAR},
      </if>
      <if test="textureId != null">
        texture_id = #{textureId,jdbcType=BIGINT},
      </if>
      <if test="textureName != null">
        texture_name = #{textureName,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="sourceName != null">
        source_name = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        `size` = #{size,jdbcType=VARCHAR},
      </if>
      <if test="keyword != null">
        keyword = #{keyword,jdbcType=VARCHAR},
      </if>
      <if test="coverId != null">
        cover_id = #{coverId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="threeModelUrl != null">
        three_model_url = #{threeModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="threeModelStatus != null">
        three_model_status = #{threeModelStatus,jdbcType=TINYINT},
      </if>
      <if test="twoModelId != null">
        two_model_id = #{twoModelId,jdbcType=VARCHAR},
      </if>
      <if test="comparedCulturalRelicsId != null">
        compared_cultural_relics_id = #{comparedCulturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="similarCulturalRelicsId != null">
        similar_cultural_relics_id = #{similarCulturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="audioId != null">
        audio_id = #{audioId,jdbcType=VARCHAR},
      </if>
      <if test="videoId != null">
        video_id = #{videoId,jdbcType=VARCHAR},
      </if>
      <if test="academicLiteratureId != null">
        academic_literature_id = #{academicLiteratureId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="dataStatus != null">
        data_status = #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="autoDisplay != null">
        auto_display = #{autoDisplay,jdbcType=TINYINT},
      </if>
      <if test="isNew != null">
        is_new = #{isNew,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsDO">
    update swzs_cultural_relics
    set company_id = #{companyId,jdbcType=BIGINT},
      `number` = #{number,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      level_id = #{levelId,jdbcType=BIGINT},
      level_name = #{levelName,jdbcType=VARCHAR},
      complete_degree_id = #{completeDegreeId,jdbcType=BIGINT},
      complete_degree_name = #{completeDegreeName,jdbcType=VARCHAR},
      dynasty_id = #{dynastyId,jdbcType=BIGINT},
      dynasty_name = #{dynastyName,jdbcType=VARCHAR},
      ad_year_id = #{adYearId,jdbcType=BIGINT},
      ad_year_name = #{adYearName,jdbcType=VARCHAR},
      specific_age = #{specificAge,jdbcType=VARCHAR},
      texture_id = #{textureId,jdbcType=BIGINT},
      texture_name = #{textureName,jdbcType=VARCHAR},
      source_id = #{sourceId,jdbcType=BIGINT},
      source_name = #{sourceName,jdbcType=VARCHAR},
      `size` = #{size,jdbcType=VARCHAR},
      keyword = #{keyword,jdbcType=VARCHAR},
      cover_id = #{coverId,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      three_model_url = #{threeModelUrl,jdbcType=VARCHAR},
      three_model_status = #{threeModelStatus,jdbcType=TINYINT},
      two_model_id = #{twoModelId,jdbcType=VARCHAR},
      compared_cultural_relics_id = #{comparedCulturalRelicsId,jdbcType=VARCHAR},
      similar_cultural_relics_id = #{similarCulturalRelicsId,jdbcType=VARCHAR},
      audio_id = #{audioId,jdbcType=VARCHAR},
      video_id = #{videoId,jdbcType=VARCHAR},
      academic_literature_id = #{academicLiteratureId,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      data_status = #{dataStatus,jdbcType=TINYINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      auto_display = #{autoDisplay,jdbcType=TINYINT},
      is_new = #{isNew,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    where id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics where company_id = #{companyId,jdbcType=BIGINT}
  </select>

  <select id="selectByNumber" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    where `number` = #{number,jdbcType=VARCHAR}
    and company_id = #{companyId,jdbcType=BIGINT}
  </select>

  <select id="selectByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    where `name` = #{name,jdbcType=VARCHAR}
  </select>

  <select id="getPageByConditionQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    <where>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT}
      </if>
      <if test="number != null and number != ''">
        and `number` like concat('%', #{number,jdbcType=VARCHAR}, '%')
      </if>
      <if test="name != null and name != ''">
        and `name` like concat('%', #{name,jdbcType=VARCHAR}, '%')
      </if>
      <if test="levelId != null">
        and level_id = #{levelId,jdbcType=BIGINT}
      </if>
      <if test="status != null">
        and `status` = #{status,jdbcType=TINYINT}
      </if>
      <if test="dataStatus != null">
        and data_status = #{dataStatus,jdbcType=TINYINT}
      </if>
      <if test="threeModelStatus != null">
        and three_model_status = #{threeModelStatus,jdbcType=TINYINT}
      </if>
    </where>
    order by ${sortBy}
  </select>

  <select id="getOtherCulturalRelics" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    <where>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT}
      </if>
      <if test="number != null">
        and `number` like concat('%', #{number,jdbcType=VARCHAR}, '%')
      </if>
      <if test="name != null">
        and `name` like concat('%', #{name,jdbcType=VARCHAR}, '%')
      </if>
      <if test="levelId != null">
        and level_id = #{levelId,jdbcType=BIGINT}
      </if>
      <if test="textureId != null">
        and texture_id = #{textureId,jdbcType=BIGINT}
      </if>
      <if test="dynastyId != null">
        and dynasty_id = #{dynastyId,jdbcType=BIGINT}
      </if>
      <if test="adYearId != null">
        and ad_year_id = #{adYearId,jdbcType=BIGINT}
      </if>
      <if test="status != null">
        and `status` = #{status,jdbcType=TINYINT}
      </if>
    </where>
    order by ${sortBy}
  </select>

  <select id="selectCulturalListByParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    where 1 = 1
    <if test="companyId != null">
      and company_id = #{companyId,jdbcType=BIGINT}
    </if>
<!--    <if test="textureIds != null and textureIds.size() > 0 ">-->
<!--      and texture_id in-->
<!--      <foreach collection="textureIds" open="(" close=")" separator="," item="textureId" index="index">-->
<!--        #{textureId}-->
<!--      </foreach>-->
<!--    </if>-->
    <if test="textureNames != null and textureNames.size() > 0 ">
      and texture_name in
      <foreach collection="textureNames" open="(" close=")" separator="," item="textureName" index="index">
        #{textureName}
      </foreach>
    </if>
    <if test="levelIds != null and levelIds.size() > 0 ">
      and level_id in
      <foreach collection="levelIds" open="(" close=")" separator="," item="levelId" index="index">
        #{levelId}
      </foreach>
    </if>
    <if test="dynastyIds != null and dynastyIds.size() > 0 ">
      and dynasty_id in
      <foreach collection="dynastyIds" open="(" close=")" separator="," item="dynastyId" index="index">
        #{dynastyId}
      </foreach>
    </if>
    <if test="adYearIds != null and adYearIds.size() > 0 ">
      and ad_year_id in
      <foreach collection="adYearIds" open="(" close=")" separator="," item="adYearId" index="index">
        #{adYearId}
      </foreach>
    </if>
    <if test="only3D == 1 ">
      and data_status in (1,3)
    </if>
    <if test="status != null">
      and `status` = #{status,jdbcType=TINYINT}
    </if>
    <if test="keyword != null and keyword != '' ">
      and (`name` like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%') or keyword like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%') )
    </if>
    order by ${sortBy}
  </select>

  <select id="getCollectionInfoById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </select>

  <select id="getCollectionListByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    where company_id = #{companyId,jdbcType=BIGINT}
    and id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="getCollectionListByThreeModelStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    where data_status in (1,3) and three_model_status in
    <foreach collection="threeModelStatusList" open="(" close=")" separator="," item="threeModelStatus" index="index">
      #{threeModelStatus}
    </foreach>
  </select>

  <update id="updateThreeModelStatusByIds">
    update swzs_cultural_relics
    set three_model_status = #{threeModelStatus,jdbcType=TINYINT}
    where id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </update>

  <update id="updateThreeModelStatusById">
    update swzs_cultural_relics
    <set>
      <if test="threeModelStatus != null">
        three_model_status = #{threeModelStatus,jdbcType=TINYINT},
      </if>
      <if test="modelPath != null and modelPath != '' ">
        three_model_url = #{modelPath,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT}
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByNumberAndCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    where company_id = #{companyId,jdbcType=BIGINT} and `number` = #{number,jdbcType=VARCHAR}
  </select>

  <select id="listByStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    where `status` = #{status,jdbcType=TINYINT}
  </select>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics
    where company_id = #{companyId,jdbcType=BIGINT} 
    <if test="start != null ">
      and gmt_create &gt;= #{start,jdbcType=TIMESTAMP}
    </if>
    <if test="end != null ">
      and gmt_create &lt;= #{end,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="listByNumbers" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics where `number` in
    <foreach collection="numbers" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>
</mapper>