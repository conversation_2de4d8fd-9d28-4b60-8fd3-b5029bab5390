<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionRegisterInfoMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="register_no" jdbcType="VARCHAR" property="registerNo"/>
        <result column="rfid" jdbcType="VARCHAR" property="rfid"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="original_no" jdbcType="VARCHAR" property="originalNo"/>
        <result column="original_name" jdbcType="VARCHAR" property="originalName"/>
        <result column="classify" jdbcType="BIGINT" property="classify"/>
        <result column="classify_desc" jdbcType="VARCHAR" property="classifyDesc"/>
        <result column="identify_level" jdbcType="BIGINT" property="identifyLevel"/>
        <result column="category" jdbcType="BIGINT" property="category"/>
        <result column="in_tibetan_age_range" jdbcType="BIGINT" property="inTibetanAgeRange"/>
        <result column="in_tibetan_date" jdbcType="TIMESTAMP" property="inTibetanDate"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="specific_info" jdbcType="VARCHAR" property="specificInfo"/>
        <result column="era" jdbcType="VARCHAR" property="era"/>
        <result column="age" jdbcType="BIGINT" property="age"/>
        <result column="use" jdbcType="VARCHAR" property="use"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="author" jdbcType="VARCHAR" property="author"/>
        <result column="author_biography" jdbcType="VARCHAR" property="authorBiography"/>
        <result column="attachment_desc" jdbcType="VARCHAR" property="attachmentDesc"/>
        <result column="in_cupboard_status" jdbcType="VARCHAR" property="inCupboardStatus"/>
        <result column="submitter" jdbcType="BIGINT" property="submitter"/>
        <result column="submitter_name" jdbcType="VARCHAR" property="submitterName"/>
        <result column="register_status" jdbcType="VARCHAR" property="registerStatus"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="auto_audit" jdbcType="TINYINT" property="autoAudit"/>
    </resultMap>

    <resultMap id="BaseResultExtMap" extends="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO">
        <result column="texture" jdbcType="VARCHAR" property="texture"/>
        <result column="size" jdbcType="VARCHAR" property="size"/>
        <result column="complete_degree" jdbcType="BIGINT" property="completeDegree"/>
        <result column="source" jdbcType="BIGINT" property="source"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, collection_id, register_no, rfid, `name`, original_no, original_name, classify,
    classify_desc, identify_level, category, in_tibetan_age_range, in_tibetan_date, address, 
    specific_info, era, age, `use`, color, author, author_biography, attachment_desc, 
    in_cupboard_status, submitter, submitter_name, register_status, document_id, creator, 
    modifier, gmt_create, gmt_modified, auto_audit
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_register_info
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO" useGeneratedKeys="true">
    insert into scs_collection_register_info (company_id, collection_id, register_no, rfid,
      `name`, original_no, original_name, 
      classify, classify_desc, identify_level, 
      category, in_tibetan_age_range, in_tibetan_date, 
      address, specific_info, era, 
      age, `use`, color, author, 
      author_biography, attachment_desc, in_cupboard_status, 
      submitter, submitter_name, register_status, 
      document_id, creator, modifier, 
      gmt_create, gmt_modified, auto_audit)
    values (#{companyId,jdbcType=BIGINT}, #{collectionId,jdbcType=BIGINT}, #{registerNo,jdbcType=VARCHAR}, #{rfid,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR}, #{originalNo,jdbcType=VARCHAR}, #{originalName,jdbcType=VARCHAR}, 
      #{classify,jdbcType=BIGINT}, #{classifyDesc,jdbcType=VARCHAR}, #{identifyLevel,jdbcType=BIGINT}, 
      #{category,jdbcType=BIGINT}, #{inTibetanAgeRange,jdbcType=BIGINT}, #{inTibetanDate,jdbcType=TIMESTAMP}, 
      #{address,jdbcType=VARCHAR}, #{specificInfo,jdbcType=VARCHAR}, #{era,jdbcType=VARCHAR}, 
      #{age,jdbcType=BIGINT}, #{use,jdbcType=VARCHAR}, #{color,jdbcType=VARCHAR}, #{author,jdbcType=VARCHAR}, 
      #{authorBiography,jdbcType=VARCHAR}, #{attachmentDesc,jdbcType=VARCHAR}, #{inCupboardStatus,jdbcType=VARCHAR}, 
      #{submitter,jdbcType=BIGINT}, #{submitterName,jdbcType=VARCHAR}, #{registerStatus,jdbcType=VARCHAR}, 
      #{documentId,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{autoAudit,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO" useGeneratedKeys="true">
        insert into scs_collection_register_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="registerNo != null">
                register_no,
            </if>
            <if test="rfid != null">
                rfid,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="originalNo != null">
                original_no,
            </if>
            <if test="originalName != null">
                original_name,
            </if>
            <if test="classify != null">
                classify,
            </if>
            <if test="classifyDesc != null">
                classify_desc,
            </if>
            <if test="identifyLevel != null">
                identify_level,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="inTibetanAgeRange != null">
                in_tibetan_age_range,
            </if>
            <if test="inTibetanDate != null">
                in_tibetan_date,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="specificInfo != null">
                specific_info,
            </if>
            <if test="era != null">
                era,
            </if>
            <if test="age != null">
                age,
            </if>
            <if test="use != null">
                `use`,
            </if>
            <if test="color != null">
                color,
            </if>
            <if test="author != null">
                author,
            </if>
            <if test="authorBiography != null">
                author_biography,
            </if>
            <if test="attachmentDesc != null">
                attachment_desc,
            </if>
            <if test="inCupboardStatus != null">
                in_cupboard_status,
            </if>
            <if test="submitter != null">
                submitter,
            </if>
            <if test="submitterName != null">
                submitter_name,
            </if>
            <if test="registerStatus != null">
                register_status,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="autoAudit != null">
                auto_audit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="rfid != null">
                #{rfid,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="originalNo != null">
                #{originalNo,jdbcType=VARCHAR},
            </if>
            <if test="originalName != null">
                #{originalName,jdbcType=VARCHAR},
            </if>
            <if test="classify != null">
                #{classify,jdbcType=BIGINT},
            </if>
            <if test="classifyDesc != null">
                #{classifyDesc,jdbcType=VARCHAR},
            </if>
            <if test="identifyLevel != null">
                #{identifyLevel,jdbcType=BIGINT},
            </if>
            <if test="category != null">
                #{category,jdbcType=BIGINT},
            </if>
            <if test="inTibetanAgeRange != null">
                #{inTibetanAgeRange,jdbcType=BIGINT},
            </if>
            <if test="inTibetanDate != null">
                #{inTibetanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="specificInfo != null">
                #{specificInfo,jdbcType=VARCHAR},
            </if>
            <if test="era != null">
                #{era,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                #{age,jdbcType=BIGINT},
            </if>
            <if test="use != null">
                #{use,jdbcType=VARCHAR},
            </if>
            <if test="color != null">
                #{color,jdbcType=VARCHAR},
            </if>
            <if test="author != null">
                #{author,jdbcType=VARCHAR},
            </if>
            <if test="authorBiography != null">
                #{authorBiography,jdbcType=VARCHAR},
            </if>
            <if test="attachmentDesc != null">
                #{attachmentDesc,jdbcType=VARCHAR},
            </if>
            <if test="inCupboardStatus != null">
                #{inCupboardStatus,jdbcType=VARCHAR},
            </if>
            <if test="submitter != null">
                #{submitter,jdbcType=BIGINT},
            </if>
            <if test="submitterName != null">
                #{submitterName,jdbcType=VARCHAR},
            </if>
            <if test="registerStatus != null">
                #{registerStatus,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="autoAudit != null">
                #{autoAudit,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO">
        update scs_collection_register_info
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                register_no = #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="rfid != null">
                rfid = #{rfid,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="originalNo != null">
                original_no = #{originalNo,jdbcType=VARCHAR},
            </if>
            <if test="originalName != null">
                original_name = #{originalName,jdbcType=VARCHAR},
            </if>
            <if test="classify != null">
                classify = #{classify,jdbcType=BIGINT},
            </if>
            <if test="classifyDesc != null">
                classify_desc = #{classifyDesc,jdbcType=VARCHAR},
            </if>
            <if test="identifyLevel != null">
                identify_level = #{identifyLevel,jdbcType=BIGINT},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=BIGINT},
            </if>
            <if test="inTibetanAgeRange != null">
                in_tibetan_age_range = #{inTibetanAgeRange,jdbcType=BIGINT},
            </if>
            <if test="inTibetanDate != null">
                in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="specificInfo != null">
                specific_info = #{specificInfo,jdbcType=VARCHAR},
            </if>
            <if test="era != null">
                era = #{era,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                age = #{age,jdbcType=BIGINT},
            </if>
            <if test="use != null">
                `use` = #{use,jdbcType=VARCHAR},
            </if>
            <if test="color != null">
                color = #{color,jdbcType=VARCHAR},
            </if>
            <if test="author != null">
                author = #{author,jdbcType=VARCHAR},
            </if>
            <if test="authorBiography != null">
                author_biography = #{authorBiography,jdbcType=VARCHAR},
            </if>
            <if test="attachmentDesc != null">
                attachment_desc = #{attachmentDesc,jdbcType=VARCHAR},
            </if>
            <if test="inCupboardStatus != null">
                in_cupboard_status = #{inCupboardStatus,jdbcType=VARCHAR},
            </if>
            <if test="submitter != null">
                submitter = #{submitter,jdbcType=BIGINT},
            </if>
            <if test="submitterName != null">
                submitter_name = #{submitterName,jdbcType=VARCHAR},
            </if>
            <if test="registerStatus != null">
                register_status = #{registerStatus,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="autoAudit != null">
                auto_audit = #{autoAudit,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO">
    update scs_collection_register_info
    set company_id = #{companyId,jdbcType=BIGINT},
      collection_id = #{collectionId,jdbcType=BIGINT},
      register_no = #{registerNo,jdbcType=VARCHAR},
      rfid = #{rfid,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      original_no = #{originalNo,jdbcType=VARCHAR},
      original_name = #{originalName,jdbcType=VARCHAR},
      classify = #{classify,jdbcType=BIGINT},
      classify_desc = #{classifyDesc,jdbcType=VARCHAR},
      identify_level = #{identifyLevel,jdbcType=BIGINT},
      category = #{category,jdbcType=BIGINT},
      in_tibetan_age_range = #{inTibetanAgeRange,jdbcType=BIGINT},
      in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
      address = #{address,jdbcType=VARCHAR},
      specific_info = #{specificInfo,jdbcType=VARCHAR},
      era = #{era,jdbcType=VARCHAR},
      age = #{age,jdbcType=BIGINT},
      `use` = #{use,jdbcType=VARCHAR},
      color = #{color,jdbcType=VARCHAR},
      author = #{author,jdbcType=VARCHAR},
      author_biography = #{authorBiography,jdbcType=VARCHAR},
      attachment_desc = #{attachmentDesc,jdbcType=VARCHAR},
      in_cupboard_status = #{inCupboardStatus,jdbcType=VARCHAR},
      submitter = #{submitter,jdbcType=BIGINT},
      submitter_name = #{submitterName,jdbcType=VARCHAR},
      register_status = #{registerStatus,jdbcType=VARCHAR},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      autoAudit = #{autoAudit,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <update id="updateByCompanyIdAndId" parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO">
        update scs_collection_register_info
        <set>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                register_no = #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="rfid != null">
                rfid = #{rfid,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="originalNo != null">
                original_no = #{originalNo,jdbcType=VARCHAR},
            </if>
            <if test="originalName != null">
                original_name = #{originalName,jdbcType=VARCHAR},
            </if>
            <if test="classify != null">
                classify = #{classify,jdbcType=BIGINT},
            </if>
            <if test="classifyDesc != null">
                classify_desc = #{classifyDesc,jdbcType=VARCHAR},
            </if>
            <if test="identifyLevel != null">
                identify_level = #{identifyLevel,jdbcType=BIGINT},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=BIGINT},
            </if>
            <if test="inTibetanAgeRange != null">
                in_tibetan_age_range = #{inTibetanAgeRange,jdbcType=BIGINT},
            </if>
            <if test="inTibetanDate != null">
                in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="specificInfo != null">
                specific_info = #{specificInfo,jdbcType=VARCHAR},
            </if>
            <if test="era != null">
                era = #{era,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                age = #{age,jdbcType=BIGINT},
            </if>
            <if test="use != null">
                `use` = #{use,jdbcType=VARCHAR},
            </if>
            <if test="color != null">
                color = #{color,jdbcType=VARCHAR},
            </if>
            <if test="author != null">
                author = #{author,jdbcType=VARCHAR},
            </if>
            <if test="authorBiography != null">
                author_biography = #{authorBiography,jdbcType=VARCHAR},
            </if>
            <if test="attachmentDesc != null">
                attachment_desc = #{attachmentDesc,jdbcType=VARCHAR},
            </if>
            <if test="inCupboardStatus != null">
                in_cupboard_status = #{inCupboardStatus,jdbcType=VARCHAR},
            </if>
            <if test="submitter != null">
                submitter = #{submitter,jdbcType=BIGINT},
            </if>
            <if test="submitterName != null">
                submitter_name = #{submitterName,jdbcType=VARCHAR},
            </if>
            <if test="registerStatus != null">
                register_status = #{registerStatus,jdbcType=VARCHAR},
            </if>
                document_id = #{documentId,jdbcType=VARCHAR},
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="autoAudit != null">
                auto_audit = #{autoAudit,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
        and company_id = #{companyId,jdbcType=BIGINT}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_info
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="selectByRegisterNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_info
        where register_no = #{registerNo,jdbcType=VARCHAR} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByRegisterNo">
    delete from scs_collection_register_info
    where register_no = #{registerNo,jdbcType=VARCHAR} and company_id = #{companyId,jdbcType=BIGINT}
    </delete>

    <select id="listPageByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_info
        where company_id = #{companyId,jdbcType=BIGINT}
        <if test="identifyLevel != null">
            and identify_level = #{identifyLevel,jdbcType=BIGINT}
        </if>
        <if test="category != null">
            and category = #{category,jdbcType=BIGINT}
        </if>
        <if test="age != null">
            and age = #{age,jdbcType=BIGINT}
        </if>
        <if test="registerNo != null and registerNo != '' ">
            and register_no like concat('%', #{registerNo,jdbcType=VARCHAR}, '%')
        </if>
        <if test="name != null and name != '' ">
            and `name` like concat('%', #{name,jdbcType=VARCHAR}, '%')
        </if>
        <if test="classifyDesc != null">
            and classify_desc like concat('%', #{classifyDesc,jdbcType=VARCHAR}, '%')
        </if>
        <if test="registerStatus != null">
            and register_status in
            <foreach collection="registerStatus" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
        <if test="collectionIds != null">
            and collection_id in
            <foreach collection="collectionIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
        <if test="startModified != null ">
            and gmt_modified &gt;= #{startModified,jdbcType=TIMESTAMP}
        </if>
        <if test="endModified != null">
            and gmt_modified &lt;= #{endModified,jdbcType=TIMESTAMP}
        </if>
        <if test="attrInfo != null">
            and id in (
                select register_info_id from scs_collection_register_attr_info
                where company_id = #{companyId,jdbcType=BIGINT}
                <if test="completeDegree != null">
                    and complete_degree = #{completeDegree,jdbcType=BIGINT}
                </if>
                <if test="source != null">
                    and source = #{source,jdbcType=BIGINT}
                </if>
            )
        </if>
        <if test="selectTexture != null">
            and id in
            <foreach collection="registerInfoIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
        <if test="checkStatus != null and checkStatus != '' and checkStatus != 'USB' ">
            and id in (
                select model_id from comm_check_main  where company_id = #{companyId,jdbcType=BIGINT}
                and belong_model = 'LOGIN' and check_status = #{checkStatus,jdbcType=VARCHAR}
            )
        </if>
        <if test="checkStatus != null and checkStatus == 'USB' ">
            and id not in (
            select model_id from comm_check_main  where company_id = #{companyId,jdbcType=BIGINT}
            and belong_model = 'LOGIN'
            )
        </if>

        <if test="isUserChecked != null and isUserChecked == 1 ">
            and id in (
            select B.model_id from comm_check_child A
                inner join comm_check_main B on A.main_id = B.id
            where B.company_id = #{companyId,jdbcType=BIGINT}
              and B.belong_model = 'LOGIN'
              and A.check_status = 'ATD'
              and CONCAT (',' ,A.need_man_id , ',') REGEXP concat(',(',#{userId,jdbcType=BIGINT},'),')
             )
        </if>
        <if test="warehouseId != null ">
            and id in (
                select register_info_id from scs_collection_register_manage_info
                where company_id = #{companyId,jdbcType=BIGINT}
                and warehouse_id = #{warehouseId,jdbcType=BIGINT}
            )
        </if>
        order by ${sortBy}
    </select>

    <update id="updateBatchRegisterStatusById">
        update scs_collection_register_info
            set register_status = #{registerStatus,jdbcType=VARCHAR}
        where company_id = #{companyId,jdbcType=BIGINT}
        and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </update>

    <update id="updateBatchRegisterStatusByCollectionId">
        update scs_collection_register_info
        set register_status = #{registerStatus,jdbcType=VARCHAR}
        where company_id = #{companyId,jdbcType=BIGINT}
        and collection_id in
        <foreach collection="collectionIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateRegisterStatusByCollectionIds">
    update scs_collection_register_info
    set register_status = #{registerStatus,jdbcType=VARCHAR}, modifier = #{modifier,jdbcType=BIGINT}
    where company_id = #{companyId,jdbcType=BIGINT} and collection_id in
        <foreach collection="collectionIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </update>

    <update id="updateDocumentIdById">
        update scs_collection_register_info
        set document_id = #{documentId,jdbcType=VARCHAR}
        where company_id = #{companyId,jdbcType=BIGINT}
        and id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listByFavoriteCount" resultType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoByFavoriteDO">
        select
        scri.id id,
        scri.company_id companyId,
        scri.collection_id collectionId,
        scri.register_no registerNo,
        scri.rfid rfid,
        scri.name name,
        scri.original_no originalNo,
        scri.original_name originalName,
        scri.classify classify,
        scri.classify_desc classifyDesc,
        scri.identify_level identifyLevel,
        scri.category category,
        scri.in_tibetan_age_range inTibetanAgeRange,
        scri.in_tibetan_date inTibetanDate,
        scri.address address,
        scri.specific_info specificInfo,
        scri.era era,
        scri.age age,
        scri.`use` `use`,
        scri.color color,
        scri.author author,
        scri.author_biography authorBiography,
        scri.attachment_desc attachmentDesc,
        scri.in_cupboard_status inCupboardStatus,
        scri.submitter submitter,
        scri.submitter_name submitterName,
        scri.register_status registerStatus,
        scri.document_id documentId,
        scri.creator creator,
        scri.modifier modifier,
        scri.gmt_create gmtCreate,
        scri.gmt_modified gmtModified,
        scf.favorite_count favoriteCount
        from
        scs_collection_register_info scri
        join szzy_collection_favorite scf ON scri.collection_id = scf.collection_id
        where scri.company_id = #{companyId,jdbcType=BIGINT}
        <if test="registerStatus != null">
            and register_status in
            <foreach collection="registerStatus" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
        ORDER BY scf.favorite_count DESC
        LIMIT 10;
    </select>
    <select id="listByGmtCreate" resultType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO">
        select
        scri.id id,
        scri.company_id companyId,
        scri.collection_id collectionId,
        scri.register_no registerNo,
        scri.rfid rfid,
        scri.name name,
        scri.original_no originalNo,
        scri.original_name originalName,
        scri.classify classify,
        scri.classify_desc classifyDesc,
        scri.identify_level identifyLevel,
        scri.category category,
        scri.in_tibetan_age_range inTibetanAgeRange,
        scri.in_tibetan_date inTibetanDate,
        scri.address address,
        scri.specific_info specificInfo,
        scri.era era,
        scri.age age,
        scri.`use` `use`,
        scri.color color,
        scri.author author,
        scri.author_biography authorBiography,
        scri.attachment_desc attachmentDesc,
        scri.in_cupboard_status inCupboardStatus,
        scri.submitter submitter,
        scri.submitter_name submitterName,
        scri.register_status registerStatus,
        scri.document_id documentId,
        scri.creator creator,
        scri.modifier modifier,
        scri.gmt_create gmtCreate,
        scri.gmt_modified gmtModified,
        scf.favorite_count favoriteCount
        from
        scs_collection_register_info scri
        join szzy_collection_favorite scf ON scri.collection_id = scf.collection_id
        where scri.company_id = #{companyId,jdbcType=BIGINT}
        <if test="registerStatus != null">
            and register_status in
            <foreach collection="registerStatus" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
        ORDER BY scri.gmt_create DESC
        LIMIT 10;
    </select>

    <select id="listByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from scs_collection_register_info
        where company_id = #{companyId,jdbcType=BIGINT}
        and collection_id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <select id="listByRegisterIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from scs_collection_register_info
        where company_id = #{companyId,jdbcType=BIGINT}
        and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <select id="selectByCollectionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_info
        where collection_id = #{collectionId,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>
    <select id="selectByDocumentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_info
        where company_id = #{companyId,jdbcType=BIGINT}
        and document_id like concat ('%', #{documentId,jdbcType=VARCHAR}, '%')
        limit 1
    </select>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into cj_ruins_info (
        name,
        category,
        import_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.name},
            #{item.category},
            #{item.importTime}
            )
        </foreach>
    </insert>

    <select id="statisticsAllByCompanyId" resultType="java.lang.Long">
        select
        count(id) as num
        from scs_collection_register_info
        where company_id = #{companyId,jdbcType=BIGINT}
        and register_status != 'ALGF'
    </select>
    <select id="statisticsInWarehouseByCompanyId" resultType="java.lang.Long">
        select
            count(id) as num
        from scs_collection_register_info
        where company_id = #{companyId,jdbcType=BIGINT}
          and register_status in ('POWH', 'ARTS', 'AIWH', 'WLGF', 'PPOWH')
    </select>
    <select id="statisticsOutWarehouseByCompanyId" resultType="java.lang.Long">
        select
            count(id) as num
        from scs_collection_register_info
        where company_id = #{companyId,jdbcType=BIGINT}
          and register_status = 'AOWH'
    </select>
    <select id="statisticsInShowByCompanyId" resultType="java.lang.Long">
        select
            count(DISTINCT scri.collection_id) as num
        FROM scs_collection_register_info scri
                 JOIN scs_out_warehouse_log sowl ON scri.collection_id = sowl.collection_id
                 JOIN scs_out_in_warehouse_certificate_info soiwci ON soiwci.id = sowl.out_in_warehouse_id
        WHERE scri.register_status = 'AOWH'
          and scri.company_id = #{companyId,jdbcType=BIGINT}
          and soiwci.reason in
            <foreach collection="outReasonIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
    </select>

    <select id="statisticsByIdentify" resultType="com.das.museum.service.biz.collection.dto.StatisticsByTypeDTO">
        SELECT identify_level as typeName,
               COUNT(`id`) as collectionCount
        FROM scs_collection_register_info
        WHERE company_id = #{companyId,jdbcType=BIGINT}
        and register_status != 'ALGF'
        GROUP BY identify_level;
    </select>

    <select id="statisticsByAge" resultType="com.das.museum.service.biz.collection.dto.StatisticsByTypeDTO">
        SELECT age as typeName,
               COUNT(`id`) as collectionCount
        FROM scs_collection_register_info
        WHERE company_id = #{companyId,jdbcType=BIGINT}
        and register_status != 'ALGF'
        GROUP BY age;
    </select>

    <select id="statisticsByTexture" resultType="com.das.museum.service.biz.collection.dto.StatisticsByTypeDTO">
        SELECT scrai.`texture` as typeName,
               COUNT(scri.`id`) as collectionCount
        FROM scs_collection_register_attr_info scrai
                 JOIN scs_collection_register_info scri ON scrai.register_info_id = scri.id
        WHERE scri.company_id = #{companyId,jdbcType=BIGINT}
        AND scri.register_status != 'ALGF'
        GROUP BY scrai.`texture`;
    </select>

    <select id="selectCountByRecord" parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterInfoDO" resultType="java.lang.Long">
        select
            <if test="classify != null">
                count(classify) as num
            </if>
            <if test="identifyLevel != null">
                count(identify_level) as num
            </if>
            <if test="category != null">
                count(category) as num
            </if>
            <if test="inTibetanAgeRange != null">
                count(in_tibetan_age_range) as num
            </if>
            <if test="age != null">
                count(`age`) as num
            </if>
            from scs_collection_register_info
            where company_id = #{companyId,jdbcType=BIGINT}
        <if test="classify != null">
            and classify = #{classify,jdbcType=BIGINT}
        </if>
        <if test="identifyLevel != null">
            and identify_level = #{identifyLevel,jdbcType=BIGINT}
        </if>
        <if test="category != null">
            and category = #{category,jdbcType=BIGINT}
        </if>
        <if test="inTibetanAgeRange != null">
            and in_tibetan_age_range = #{inTibetanAgeRange,jdbcType=BIGINT}
        </if>
        <if test="age != null">
            and `age` = #{age,jdbcType=BIGINT}
        </if>
    </select>
    <select id="selectOutWarehouseByCompanyId" resultType="java.lang.Long">
        select
            collection_id
        from scs_collection_register_info
        where company_id = #{companyId,jdbcType=BIGINT}
          and register_status = 'AOWH'
    </select>
    <select id="queryByRegisterNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_info
        where company_id = #{companyId,jdbcType=BIGINT}
        <if test="registerNos != null and registerNos.size() > 0 ">
            and register_no in
            <foreach collection="registerNos" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
        <if test="registerNo != null and registerNo != '' ">
            and register_no like concat('%', #{registerNo,jdbcType=VARCHAR}, '%')
        </if>
        <if test="classifyDesc != null">
            and classify_desc like concat('%', #{classifyDesc,jdbcType=VARCHAR}, '%')
        </if>
        <if test="name != null and name != '' ">
            and `name` like concat('%', #{name,jdbcType=VARCHAR}, '%')
        </if>
        <if test="age != null">
            and age = #{age,jdbcType=BIGINT}
        </if>
        <if test="identifyLevel != null">
            and identify_level = #{identifyLevel,jdbcType=BIGINT}
        </if>
        <if test="category != null">
            and category = #{category,jdbcType=BIGINT}
        </if>
        <if test="query == 1">
            and id in (
                select B.register_info_id from scs_collection_register_attr_info B
                where 1 = 1
                <if test="texture != null and texture != '' ">
                    and B.texture = #{identifyLevel,jdbcType=VARCHAR}
                </if>
                <if test="source != null">
                    and B.source = #{source,jdbcType=BIGINT}
                </if>
                <if test="completeDegree != null">
                    and B.complete_degree = #{complete_degree,jdbcType=BIGINT}
                </if>
        </if>
    </select>

    <select id="listByRegisterStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_info
        where company_id = #{companyId,jdbcType=BIGINT} and register_status in
        <foreach collection="registerStatus" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <select id="listIdentifyLevelIds" resultType="java.lang.Long">
        select identify_level from scs_collection_register_info
        where company_id = #{companyId,jdbcType=BIGINT}
        and register_status != 'ALGF'
    </select>

    <select id="listExhibitRegister" resultMap="BaseResultExtMap">
        select a.id, a.company_id, a.collection_id, a.register_no, a.rfid, a.`name`, a.original_no, a.original_name, a.classify,
        a.classify_desc, a.identify_level, a.category, a.in_tibetan_age_range, a.in_tibetan_date, a.address,
        a.specific_info, a.era, a.age, a.`use`, a.color, a.author, a.author_biography, a.attachment_desc,
        a.in_cupboard_status, a.submitter, a.submitter_name, a.register_status, a.document_id, a.creator,
        a.modifier, a.gmt_create, a.gmt_modified, a.auto_audit, s.size , s.`source`, s.complete_degree, s.texture
        from scs_collection_register_info a
        join scs_collection_register_attr_info s ON a.id = s.register_info_id
        <if test="digitalType != null">
            left join (select DISTINCT register_info_id twoImage from scs_collection_two_image where company_id = #{companyId,jdbcType=BIGINT} ) C on a.id = C.twoImage
            left join (select DISTINCT register_info_id threeModel from scs_collection_three_model where company_id = #{companyId,jdbcType=BIGINT} ) D on a.id = D.threeModel
        </if>
        where a.company_id = #{companyId,jdbcType=BIGINT}
          and a. register_status not in
        <foreach collection="registerStatus" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
        <if test="dataFlag != null and dataFlag == 1 ">
            and a.register_no not in (select number from swzs_cultural_relics where company_id = #{companyId,jdbcType=BIGINT})
        </if>
        <if test="keyword != null and keyword != '' ">
            and ( a.register_no like concat('%', #{keyword,jdbcType=VARCHAR}, '%') or a.`name` like concat('%', #{keyword,jdbcType=VARCHAR}, '%') )
        </if>
        <if test="age != null">
            and a.age = #{age,jdbcType=BIGINT}
        </if>
        <if test="identifyLevel != null">
            and a.identify_level = #{identifyLevel,jdbcType=BIGINT}
        </if>
        <if test="source != null">
            and s.`source` = #{source,jdbcType=BIGINT}
        </if>
        <if test="completeDegree != null">
            and s.complete_degree = #{completeDegree,jdbcType=BIGINT}
        </if>
        <if test="texture != null and texture != '' ">
            and s.texture = #{texture,jdbcType=VARCHAR}
        </if>
        <if test ="digitalType == 1">
            and C.twoImage is not null
            and D.threeModel is null
        </if>
        <if test ="digitalType == 2">
            and C.twoImage is null
            and D.threeModel is not null
        </if>
        <if test ="digitalType == 3">
            and C.twoImage is not null
            and D.threeModel is not null
        </if>
        order by a.gmt_create desc
    </select>
</mapper>