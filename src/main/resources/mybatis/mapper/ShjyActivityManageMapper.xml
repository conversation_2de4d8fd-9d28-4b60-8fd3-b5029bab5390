<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ShjyActivityManageMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ShjyActivityManageDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_unique_code" jdbcType="VARCHAR" property="companyUniqueCode" />
    <result column="activity_unique_code" jdbcType="VARCHAR" property="activityUniqueCode" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="apply_type" jdbcType="BIGINT" property="applyType" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="activity_address" jdbcType="VARCHAR" property="activityAddress" />
    <result column="limit_people" jdbcType="TINYINT" property="limitPeople" />
    <result column="limit_num" jdbcType="INTEGER" property="limitNum" />
    <result column="apply_num" jdbcType="INTEGER" property="applyNum" />
    <result column="cover_picture" jdbcType="BIGINT" property="coverPicture" />
    <result column="process_files" jdbcType="VARCHAR" property="processFiles" />
    <result column="publish_status" jdbcType="TINYINT" property="publishStatus" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.das.museum.infr.dataobject.ShjyActivityManageDOWithBLOBs">
    <result column="apply_notice" jdbcType="LONGVARCHAR" property="applyNotice" />
    <result column="activity_introduce" jdbcType="LONGVARCHAR" property="activityIntroduce" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, company_unique_code, activity_unique_code, activity_name, apply_type, 
    start_time, end_time, activity_address, limit_people, limit_num, apply_num, cover_picture, 
    process_files, publish_status, creator, modifier, gmt_create, gmt_modified
  </sql>
  <sql id="Blob_Column_List">
    apply_notice, activity_introduce
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_manage
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from shjy_activity_manage
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ShjyActivityManageDOWithBLOBs" useGeneratedKeys="true">
    insert into shjy_activity_manage (company_id, company_unique_code, activity_unique_code, 
      activity_name, apply_type, start_time, 
      end_time, activity_address, limit_people, 
      limit_num, apply_num, cover_picture, 
      process_files, publish_status, creator, 
      modifier, gmt_create, gmt_modified, 
      apply_notice, activity_introduce)
    values (#{companyId,jdbcType=BIGINT}, #{companyUniqueCode,jdbcType=VARCHAR}, #{activityUniqueCode,jdbcType=VARCHAR}, 
      #{activityName,jdbcType=VARCHAR}, #{applyType,jdbcType=BIGINT}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{activityAddress,jdbcType=VARCHAR}, #{limitPeople,jdbcType=TINYINT},
      #{limitNum,jdbcType=INTEGER}, #{applyNum,jdbcType=INTEGER}, #{coverPicture,jdbcType=BIGINT}, 
      #{processFiles,jdbcType=VARCHAR}, #{publishStatus,jdbcType=TINYINT}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{applyNotice,jdbcType=LONGVARCHAR}, #{activityIntroduce,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ShjyActivityManageDOWithBLOBs" useGeneratedKeys="true">
    insert into shjy_activity_manage
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyUniqueCode != null">
        company_unique_code,
      </if>
      <if test="activityUniqueCode != null">
        activity_unique_code,
      </if>
      <if test="activityName != null">
        activity_name,
      </if>
      <if test="applyType != null">
        apply_type,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="activityAddress != null">
        activity_address,
      </if>
      <if test="limitPeople != null">
        limit_people,
      </if>
      <if test="limitNum != null">
        limit_num,
      </if>
      <if test="applyNum != null">
        apply_num,
      </if>
      <if test="coverPicture != null">
        cover_picture,
      </if>
      <if test="processFiles != null">
        process_files,
      </if>
      <if test="publishStatus != null">
        publish_status,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="applyNotice != null">
        apply_notice,
      </if>
      <if test="activityIntroduce != null">
        activity_introduce,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyUniqueCode != null">
        #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityUniqueCode != null">
        #{activityUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="applyType != null">
        #{applyType,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activityAddress != null">
        #{activityAddress,jdbcType=VARCHAR},
      </if>
      <if test="limitPeople != null">
        #{limitPeople,jdbcType=TINYINT},
      </if>
      <if test="limitNum != null">
        #{limitNum,jdbcType=INTEGER},
      </if>
      <if test="applyNum != null">
        #{applyNum,jdbcType=INTEGER},
      </if>
      <if test="coverPicture != null">
        #{coverPicture,jdbcType=BIGINT},
      </if>
      <if test="processFiles != null">
        #{processFiles,jdbcType=VARCHAR},
      </if>
      <if test="publishStatus != null">
        #{publishStatus,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="applyNotice != null">
        #{applyNotice,jdbcType=LONGVARCHAR},
      </if>
      <if test="activityIntroduce != null">
        #{activityIntroduce,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ShjyActivityManageDOWithBLOBs">
    update shjy_activity_manage
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyUniqueCode != null">
        company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityUniqueCode != null">
        activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        activity_name = #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="applyType != null">
        apply_type = #{applyType,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activityAddress != null">
        activity_address = #{activityAddress,jdbcType=VARCHAR},
      </if>
      <if test="limitPeople != null">
        limit_people = #{limitPeople,jdbcType=TINYINT},
      </if>
      <if test="limitNum != null">
        limit_num = #{limitNum,jdbcType=INTEGER},
      </if>
      <if test="applyNum != null">
        apply_num = #{applyNum,jdbcType=INTEGER},
      </if>
      <if test="coverPicture != null">
        cover_picture = #{coverPicture,jdbcType=BIGINT},
      </if>
      <if test="processFiles != null">
        process_files = #{processFiles,jdbcType=VARCHAR},
      </if>
      <if test="publishStatus != null">
        publish_status = #{publishStatus,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="applyNotice != null">
        apply_notice = #{applyNotice,jdbcType=LONGVARCHAR},
      </if>
      <if test="activityIntroduce != null">
        activity_introduce = #{activityIntroduce,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.das.museum.infr.dataobject.ShjyActivityManageDOWithBLOBs">
    update shjy_activity_manage
    set company_id = #{companyId,jdbcType=BIGINT},
      company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR},
      activity_name = #{activityName,jdbcType=VARCHAR},
      apply_type = #{applyType,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      activity_address = #{activityAddress,jdbcType=VARCHAR},
      limit_people = #{limitPeople,jdbcType=TINYINT},
      limit_num = #{limitNum,jdbcType=INTEGER},
      apply_num = #{applyNum,jdbcType=INTEGER},
      cover_picture = #{coverPicture,jdbcType=BIGINT},
      process_files = #{processFiles,jdbcType=VARCHAR},
      publish_status = #{publishStatus,jdbcType=TINYINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      apply_notice = #{applyNotice,jdbcType=LONGVARCHAR},
      activity_introduce = #{activityIntroduce,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ShjyActivityManageDO">
    update shjy_activity_manage
    set company_id = #{companyId,jdbcType=BIGINT},
      company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR},
      activity_name = #{activityName,jdbcType=VARCHAR},
      apply_type = #{applyType,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      activity_address = #{activityAddress,jdbcType=VARCHAR},
      limit_people = #{limitPeople,jdbcType=TINYINT},
      limit_num = #{limitNum,jdbcType=INTEGER},
      apply_num = #{applyNum,jdbcType=INTEGER},
      cover_picture = #{coverPicture,jdbcType=BIGINT},
      process_files = #{processFiles,jdbcType=VARCHAR},
      publish_status = #{publishStatus,jdbcType=TINYINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByActivityUniqueCode" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_manage
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    and activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR}
  </select>

  <select id="selectListByParams" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_manage
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    <if test="activityName != null and activityName != '' ">
      and activity_name like CONCAT('%',#{activityName,jdbcType=VARCHAR},'%')
    </if>
    <if test="applyType != null ">
      and apply_type = #{applyType,jdbcType=BIGINT}
    </if>
    <if test="publishStatus != null ">
      and publish_status = #{publishStatus,jdbcType=TINYINT}
    </if>
    <if test="activityStatus == 1 ">
      and start_time > #{currentTime,jdbcType=TIMESTAMP}
    </if>
    <if test="activityStatus == 2 ">
      and start_time &lt;= #{currentTime,jdbcType=TIMESTAMP}
      and end_time >= #{currentTime,jdbcType=TIMESTAMP}
    </if>
    <if test="activityStatus == 3 ">
      and end_time &lt; #{currentTime,jdbcType=TIMESTAMP}
    </if>
    <if test="startTime != null ">
      and start_time >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null ">
      and start_time &lt;= #{endTime,jdbcType=TIMESTAMP}
    </if>
  </select>

  <update id="updatePublishStatus">
    update shjy_activity_manage
    set
        publish_status = #{publishStatus,jdbcType=TINYINT},
        modifier = #{modifier,jdbcType=BIGINT}
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    and activity_unique_code in
    <foreach collection="activityUniqueCodes" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </update>

  <delete id="deleteByActivityUniqueCodes">
    delete from shjy_activity_manage
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    and activity_unique_code in
    <foreach collection="activityUniqueCodes" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </delete>

  <select id="listByIds" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_manage
    where id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="selectAppletByActivityUniqueCode" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_manage
    where activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR}
    <if test="publishStatus != null ">
      and publish_status = #{publishStatus,jdbcType=TINYINT}
    </if>
  </select>

  <select id="listByCondition" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_manage
    where activity_name like CONCAT('%',#{activityName,jdbcType=VARCHAR},'%')
    <if test="publishStatus != null ">
      and publish_status = #{publishStatus,jdbcType=TINYINT}
    </if>
  </select>

  <select id="listByPublishStatus" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_manage
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR} and publish_status = #{publishStatus,jdbcType=TINYINT}
  </select>
</mapper>