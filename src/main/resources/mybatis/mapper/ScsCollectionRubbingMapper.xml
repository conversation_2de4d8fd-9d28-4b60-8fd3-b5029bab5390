<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionRubbingMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionRubbingDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="book_style" jdbcType="VARCHAR" property="bookStyle"/>
        <result column="mount_situation" jdbcType="VARCHAR" property="mountSituation"/>
        <result column="rubbing_no" jdbcType="VARCHAR" property="rubbingNo"/>
        <result column="recognition" jdbcType="VARCHAR" property="recognition"/>
        <result column="seal" jdbcType="VARCHAR" property="seal"/>
        <result column="postscript" jdbcType="VARCHAR" property="postscript"/>
        <result column="interpretation" jdbcType="VARCHAR" property="interpretation"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, collection_id, book_style, mount_situation, rubbing_no, recognition, 
    seal, postscript, interpretation, document_id, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_rubbing
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_rubbing
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRubbingDO" useGeneratedKeys="true">
    insert into scs_collection_rubbing (company_id, collection_id, book_style, 
      mount_situation, rubbing_no, recognition, 
      seal, postscript, interpretation, 
      document_id, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{collectionId,jdbcType=BIGINT}, #{bookStyle,jdbcType=VARCHAR}, 
      #{mountSituation,jdbcType=VARCHAR}, #{rubbingNo,jdbcType=VARCHAR}, #{recognition,jdbcType=VARCHAR}, 
      #{seal,jdbcType=VARCHAR}, #{postscript,jdbcType=VARCHAR}, #{interpretation,jdbcType=VARCHAR}, 
      #{documentId,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRubbingDO" useGeneratedKeys="true">
        insert into scs_collection_rubbing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="bookStyle != null">
                book_style,
            </if>
            <if test="mountSituation != null">
                mount_situation,
            </if>
            <if test="rubbingNo != null">
                rubbing_no,
            </if>
            <if test="recognition != null">
                recognition,
            </if>
            <if test="seal != null">
                seal,
            </if>
            <if test="postscript != null">
                postscript,
            </if>
            <if test="interpretation != null">
                interpretation,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="bookStyle != null">
                #{bookStyle,jdbcType=VARCHAR},
            </if>
            <if test="mountSituation != null">
                #{mountSituation,jdbcType=VARCHAR},
            </if>
            <if test="rubbingNo != null">
                #{rubbingNo,jdbcType=VARCHAR},
            </if>
            <if test="recognition != null">
                #{recognition,jdbcType=VARCHAR},
            </if>
            <if test="seal != null">
                #{seal,jdbcType=VARCHAR},
            </if>
            <if test="postscript != null">
                #{postscript,jdbcType=VARCHAR},
            </if>
            <if test="interpretation != null">
                #{interpretation,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsCollectionRubbingDO">
        update scs_collection_rubbing
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="bookStyle != null">
                book_style = #{bookStyle,jdbcType=VARCHAR},
            </if>
            <if test="mountSituation != null">
                mount_situation = #{mountSituation,jdbcType=VARCHAR},
            </if>
            <if test="rubbingNo != null">
                rubbing_no = #{rubbingNo,jdbcType=VARCHAR},
            </if>
            <if test="recognition != null">
                recognition = #{recognition,jdbcType=VARCHAR},
            </if>
            <if test="seal != null">
                seal = #{seal,jdbcType=VARCHAR},
            </if>
            <if test="postscript != null">
                postscript = #{postscript,jdbcType=VARCHAR},
            </if>
            <if test="interpretation != null">
                interpretation = #{interpretation,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionRubbingDO">
    update scs_collection_rubbing
    set company_id = #{companyId,jdbcType=BIGINT},
      collection_id = #{collectionId,jdbcType=BIGINT},
      book_style = #{bookStyle,jdbcType=VARCHAR},
      mount_situation = #{mountSituation,jdbcType=VARCHAR},
      rubbing_no = #{rubbingNo,jdbcType=VARCHAR},
      recognition = #{recognition,jdbcType=VARCHAR},
      seal = #{seal,jdbcType=VARCHAR},
      postscript = #{postscript,jdbcType=VARCHAR},
      interpretation = #{interpretation,jdbcType=VARCHAR},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <delete id="deleteById">
        delete from scs_collection_rubbing
        where company_id = #{companyId,jdbcType=BIGINT} and id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_rubbing
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="listPageByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_rubbing
        where company_id = #{companyId,jdbcType=BIGINT} and collection_id = #{collectionId,jdbcType=BIGINT}
        <if test="bookStyle != null and bookStyle != '' ">
          and book_style like concat('%', #{bookStyle,jdbcType=VARCHAR}, '%')
        </if>
        <if test="mountSituation != null and mountSituation != '' ">
          and mount_situation like concat('%', #{mountSituation,jdbcType=VARCHAR}, '%')
        </if>
        order by ${sortBy}
    </select>
</mapper>