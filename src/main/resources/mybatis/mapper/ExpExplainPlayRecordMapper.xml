<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ExpExplainPlayRecordMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ExpExplainPlayRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_unique_code" jdbcType="VARCHAR" property="companyUniqueCode" />
    <result column="explain_unique_code" jdbcType="VARCHAR" property="explainUniqueCode" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_unique_code, explain_unique_code, open_id, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from exp_explain_play_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from exp_explain_play_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ExpExplainPlayRecordDO" useGeneratedKeys="true">
    insert into exp_explain_play_record (company_unique_code, explain_unique_code, 
      open_id, gmt_create, gmt_modified
      )
    values (#{companyUniqueCode,jdbcType=VARCHAR}, #{explainUniqueCode,jdbcType=VARCHAR}, 
      #{openId,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ExpExplainPlayRecordDO" useGeneratedKeys="true">
    insert into exp_explain_play_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyUniqueCode != null">
        company_unique_code,
      </if>
      <if test="explainUniqueCode != null">
        explain_unique_code,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyUniqueCode != null">
        #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="explainUniqueCode != null">
        #{explainUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ExpExplainPlayRecordDO">
    update exp_explain_play_record
    <set>
      <if test="companyUniqueCode != null">
        company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="explainUniqueCode != null">
        explain_unique_code = #{explainUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ExpExplainPlayRecordDO">
    update exp_explain_play_record
    set company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      explain_unique_code = #{explainUniqueCode,jdbcType=VARCHAR},
      open_id = #{openId,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="playNumByTime" resultType="java.lang.Integer">
    select
     count(id) playNum
    from exp_explain_play_record
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    <if test="startTime != null ">
      and gmt_create >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null ">
      and gmt_create &lt;=  #{endTime,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="peopleNumByTime" resultType="java.lang.Integer">
    select
    count(*)
    from (
      select
      DISTINCT open_id
      from exp_explain_play_record
      where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
      <if test="startTime != null ">
        and gmt_create >= #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null ">
        and gmt_create &lt;=  #{endTime,jdbcType=TIMESTAMP}
      </if>
    ) aa
  </select>
</mapper>