<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.CommIncrSegmentMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.CommIncrSegmentDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="biz_dimension" jdbcType="VARCHAR" property="bizDimension" />
    <result column="biz_curr_index" jdbcType="BIGINT" property="bizCurrIndex" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, unique_code, biz_code, biz_dimension, biz_curr_index, gmt_create, 
    gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from comm_incr_segment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_incr_segment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommIncrSegmentDO" useGeneratedKeys="true">
    insert into comm_incr_segment (company_id, unique_code, biz_code, 
      biz_dimension, biz_curr_index, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{uniqueCode,jdbcType=VARCHAR}, #{bizCode,jdbcType=VARCHAR}, 
      #{bizDimension,jdbcType=VARCHAR}, #{bizCurrIndex,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommIncrSegmentDO" useGeneratedKeys="true">
    insert into comm_incr_segment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="bizDimension != null">
        biz_dimension,
      </if>
      <if test="bizCurrIndex != null">
        biz_curr_index,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizDimension != null">
        #{bizDimension,jdbcType=VARCHAR},
      </if>
      <if test="bizCurrIndex != null">
        #{bizCurrIndex,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.CommIncrSegmentDO">
    update comm_incr_segment
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizDimension != null">
        biz_dimension = #{bizDimension,jdbcType=VARCHAR},
      </if>
      <if test="bizCurrIndex != null">
        biz_curr_index = #{bizCurrIndex,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.CommIncrSegmentDO">
    update comm_incr_segment
    set company_id = #{companyId,jdbcType=BIGINT},
      unique_code = #{uniqueCode,jdbcType=VARCHAR},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      biz_dimension = #{bizDimension,jdbcType=VARCHAR},
      biz_curr_index = #{bizCurrIndex,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCompanyIdAndBizCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from comm_incr_segment
    where company_id = #{companyId,jdbcType=BIGINT} and biz_code = #{bizCode,jdbcType=VARCHAR}
    and biz_dimension = #{bizDimension,jdbcType=VARCHAR} for update
  </select>

  <update id="updateByBizCurrIndex" parameterType="com.das.museum.infr.dataobject.CommIncrSegmentDO">
    update comm_incr_segment set biz_curr_index = biz_curr_index + 1
    where id = #{id,jdbcType=BIGINT} and biz_curr_index = #{bizCurrIndex,jdbcType=BIGINT}
  </update>
</mapper>