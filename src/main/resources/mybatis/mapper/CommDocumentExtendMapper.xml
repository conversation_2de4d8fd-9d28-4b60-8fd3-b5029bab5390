<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.CommDocumentExtendMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.CommDocumentExtendDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="source_doc_id" jdbcType="BIGINT" property="sourceDocId" />
    <result column="process_type" jdbcType="TINYINT" property="processType" />
    <result column="file_format" jdbcType="VARCHAR" property="fileFormat" />
    <result column="save_file_name" jdbcType="VARCHAR" property="saveFileName" />
    <result column="file_size" jdbcType="BIGINT" property="fileSize" />
    <result column="bucket" jdbcType="VARCHAR" property="bucket" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, source_doc_id, process_type, file_format, save_file_name, file_size, 
    bucket, `path`, url, description, is_delete, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from comm_document_extend
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_document_extend
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommDocumentExtendDO" useGeneratedKeys="true">
    insert into comm_document_extend (company_id, source_doc_id, process_type, 
      file_format, save_file_name, file_size, 
      bucket, `path`, url, 
      description, is_delete, creator, 
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{sourceDocId,jdbcType=BIGINT}, #{processType,jdbcType=TINYINT}, 
      #{fileFormat,jdbcType=VARCHAR}, #{saveFileName,jdbcType=VARCHAR}, #{fileSize,jdbcType=BIGINT}, 
      #{bucket,jdbcType=VARCHAR}, #{path,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommDocumentExtendDO" useGeneratedKeys="true">
    insert into comm_document_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="sourceDocId != null">
        source_doc_id,
      </if>
      <if test="processType != null">
        process_type,
      </if>
      <if test="fileFormat != null">
        file_format,
      </if>
      <if test="saveFileName != null">
        save_file_name,
      </if>
      <if test="fileSize != null">
        file_size,
      </if>
      <if test="bucket != null">
        bucket,
      </if>
      <if test="path != null">
        `path`,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="sourceDocId != null">
        #{sourceDocId,jdbcType=BIGINT},
      </if>
      <if test="processType != null">
        #{processType,jdbcType=TINYINT},
      </if>
      <if test="fileFormat != null">
        #{fileFormat,jdbcType=VARCHAR},
      </if>
      <if test="saveFileName != null">
        #{saveFileName,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        #{fileSize,jdbcType=BIGINT},
      </if>
      <if test="bucket != null">
        #{bucket,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.CommDocumentExtendDO">
    update comm_document_extend
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="sourceDocId != null">
        source_doc_id = #{sourceDocId,jdbcType=BIGINT},
      </if>
      <if test="processType != null">
        process_type = #{processType,jdbcType=TINYINT},
      </if>
      <if test="fileFormat != null">
        file_format = #{fileFormat,jdbcType=VARCHAR},
      </if>
      <if test="saveFileName != null">
        save_file_name = #{saveFileName,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        file_size = #{fileSize,jdbcType=BIGINT},
      </if>
      <if test="bucket != null">
        bucket = #{bucket,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        `path` = #{path,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.CommDocumentExtendDO">
    update comm_document_extend
    set company_id = #{companyId,jdbcType=BIGINT},
      source_doc_id = #{sourceDocId,jdbcType=BIGINT},
      process_type = #{processType,jdbcType=TINYINT},
      file_format = #{fileFormat,jdbcType=VARCHAR},
      save_file_name = #{saveFileName,jdbcType=VARCHAR},
      file_size = #{fileSize,jdbcType=BIGINT},
      bucket = #{bucket,jdbcType=VARCHAR},
      `path` = #{path,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=TINYINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--  ===========================>自定义方法===========================  -->

  <select id="listBySourceDocIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from comm_document_extend
    where 1=1 and source_doc_id in
    <foreach collection="sourceDocIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
    <if test="companyId != null">
      and company_id = #{companyId,jdbcType=BIGINT}
    </if>
  </select>
</mapper>