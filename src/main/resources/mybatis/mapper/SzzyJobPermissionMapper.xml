<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SzzyJobPermissionMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SzzyJobPermissionDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="permission_group_id" jdbcType="BIGINT" property="permissionGroupId" />
    <result column="job_id" jdbcType="BIGINT" property="jobId" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, permission_group_id, job_id, creator,
    modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from szzy_job_permission
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szzy_job_permission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyJobPermissionDO" useGeneratedKeys="true">
    insert into szzy_job_permission (company_id, permission_group_id,
      job_id, creator,
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{permissionGroupId,jdbcType=BIGINT},
      #{jobId,jdbcType=BIGINT}, #{creator,jdbcType=BIGINT},
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyJobPermissionDO" useGeneratedKeys="true">
    insert into szzy_job_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="permissionGroupId != null">
        permission_group_id,
      </if>
      <if test="jobId != null">
        job_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="permissionGroupId != null">
        #{permissionGroupId,jdbcType=BIGINT},
      </if>
      <if test="jobId != null">
        #{jobId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SzzyJobPermissionDO">
    update szzy_job_permission
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="permissionGroupId != null">
        permission_group_id = #{permissionGroupId,jdbcType=BIGINT},
      </if>
      <if test="jobId != null">
        job_id = #{jobId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SzzyJobPermissionDO">
    update szzy_job_permission
    set company_id = #{companyId,jdbcType=BIGINT},
      permission_group_id = #{permissionGroupId,jdbcType=BIGINT},
      job_id = #{jobId,jdbcType=BIGINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByPermissionGroupId">
    delete from szzy_job_permission
    where company_id = #{companyId,jdbcType=BIGINT}
    and permission_group_id = #{permissionGroupId,jdbcType=BIGINT}
  </delete>

  <select id="selectByPermissionGroupIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_job_permission
    where company_id = #{companyId,jdbcType=BIGINT}
    and permission_group_id in
    <foreach collection="permissionGroupIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="selectByPermissionGroupId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_job_permission
    where company_id = #{companyId,jdbcType=BIGINT}
    and permission_group_id = #{permissionGroupId,jdbcType=BIGINT}
  </select>

</mapper>