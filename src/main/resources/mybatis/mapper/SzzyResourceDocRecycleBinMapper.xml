<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SzzyResourceDocRecycleBinMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SzzyResourceDocRecycleBinDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="source_doc_id" jdbcType="BIGINT" property="sourceDocId" />
    <result column="resource_id" jdbcType="BIGINT" property="resourceId" />
    <result column="resource_name" jdbcType="VARCHAR" property="resourceName" />
    <result column="file_format" jdbcType="VARCHAR" property="fileFormat" />
    <result column="save_file_name" jdbcType="VARCHAR" property="saveFileName" />
    <result column="file_size" jdbcType="BIGINT" property="fileSize" />
    <result column="resource_uri_id" jdbcType="VARCHAR" property="resourceUriId" />
    <result column="resource_uri_name" jdbcType="VARCHAR" property="resourceUriName" />
    <result column="resource_url" jdbcType="VARCHAR" property="resourceUrl" />
    <result column="resource_route" jdbcType="VARCHAR" property="resourceRoute" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="three_model_url" jdbcType="VARCHAR" property="threeModelUrl"/>
    <result column="three_model_status" jdbcType="TINYINT" property="threeModelStatus"/>
    <result column="error_info" jdbcType="VARCHAR" property="errorInfo"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, source_doc_id, resource_id, resource_name, file_format, save_file_name, 
    file_size, resource_uri_id, resource_uri_name, resource_url, resource_route, creator, 
    modifier, gmt_create, gmt_modified, three_model_url, three_model_status, error_info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from szzy_resource_doc_recycle_bin
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szzy_resource_doc_recycle_bin
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyResourceDocRecycleBinDO" useGeneratedKeys="true">
    insert into szzy_resource_doc_recycle_bin (company_id, source_doc_id, resource_id, 
      resource_name, file_format, save_file_name, 
      file_size, resource_uri_id, resource_uri_name, 
      resource_url, resource_route, creator, 
      modifier, gmt_create, gmt_modified,
      three_model_url, three_model_status, error_info
      )
    values (#{companyId,jdbcType=BIGINT}, #{sourceDocId,jdbcType=BIGINT}, #{resourceId,jdbcType=BIGINT}, 
      #{resourceName,jdbcType=VARCHAR}, #{fileFormat,jdbcType=VARCHAR}, #{saveFileName,jdbcType=VARCHAR}, 
      #{fileSize,jdbcType=BIGINT}, #{resourceUriId,jdbcType=VARCHAR}, #{resourceUriName,jdbcType=VARCHAR}, 
      #{resourceUrl,jdbcType=VARCHAR}, #{resourceRoute,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP},
      #{threeModelUrl,jdbcType=VARCHAR}, #{threeModelStatus,jdbcType=TINYINT}, #{errorInfo,jdbcType=VARCHAR})
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyResourceDocRecycleBinDO" useGeneratedKeys="true">
    insert into szzy_resource_doc_recycle_bin
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="sourceDocId != null">
        source_doc_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="resourceName != null">
        resource_name,
      </if>
      <if test="fileFormat != null">
        file_format,
      </if>
      <if test="saveFileName != null">
        save_file_name,
      </if>
      <if test="fileSize != null">
        file_size,
      </if>
      <if test="resourceUriId != null">
        resource_uri_id,
      </if>
      <if test="resourceUriName != null">
        resource_uri_name,
      </if>
      <if test="resourceUrl != null">
        resource_url,
      </if>
      <if test="resourceRoute != null">
        resource_route,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="threeModelUrl != null">
        three_model_url,
      </if>
      <if test="threeModelStatus != null">
        three_model_status,
      </if>
      <if test="errorInfo != null">
        error_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="sourceDocId != null">
        #{sourceDocId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceName != null">
        #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="fileFormat != null">
        #{fileFormat,jdbcType=VARCHAR},
      </if>
      <if test="saveFileName != null">
        #{saveFileName,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        #{fileSize,jdbcType=BIGINT},
      </if>
      <if test="resourceUriId != null">
        #{resourceUriId,jdbcType=VARCHAR},
      </if>
      <if test="resourceUriName != null">
        #{resourceUriName,jdbcType=VARCHAR},
      </if>
      <if test="resourceUrl != null">
        #{resourceUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceRoute != null">
        #{resourceRoute,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="threeModelUrl != null">
        #{threeModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="threeModelStatus != null">
        #{threeModelStatus,jdbcType=TINYINT},
      </if>
      <if test="errorInfo != null">
        #{errorInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SzzyResourceDocRecycleBinDO">
    update szzy_resource_doc_recycle_bin
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="sourceDocId != null">
        source_doc_id = #{sourceDocId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceName != null">
        resource_name = #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="fileFormat != null">
        file_format = #{fileFormat,jdbcType=VARCHAR},
      </if>
      <if test="saveFileName != null">
        save_file_name = #{saveFileName,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        file_size = #{fileSize,jdbcType=BIGINT},
      </if>
      <if test="resourceUriId != null">
        resource_uri_id = #{resourceUriId,jdbcType=VARCHAR},
      </if>
      <if test="resourceUriName != null">
        resource_uri_name = #{resourceUriName,jdbcType=VARCHAR},
      </if>
      <if test="resourceUrl != null">
        resource_url = #{resourceUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceRoute != null">
        resource_route = #{resourceRoute,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="threeModelUrl != null">
        three_model_url = #{threeModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="threeModelStatus != null">
        three_model_status = #{threeModelStatus,jdbcType=TINYINT},
      </if>
      <if test="errorInfo != null">
        error_info = #{errorInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SzzyResourceDocRecycleBinDO">
    update szzy_resource_doc_recycle_bin
    set company_id = #{companyId,jdbcType=BIGINT},
      source_doc_id = #{sourceDocId,jdbcType=BIGINT},
      resource_id = #{resourceId,jdbcType=BIGINT},
      resource_name = #{resourceName,jdbcType=VARCHAR},
      file_format = #{fileFormat,jdbcType=VARCHAR},
      save_file_name = #{saveFileName,jdbcType=VARCHAR},
      file_size = #{fileSize,jdbcType=BIGINT},
      resource_uri_id = #{resourceUriId,jdbcType=VARCHAR},
      resource_uri_name = #{resourceUriName,jdbcType=VARCHAR},
      resource_url = #{resourceUrl,jdbcType=VARCHAR},
      resource_route = #{resourceRoute,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      three_model_url = #{threeModelUrl,jdbcType=VARCHAR},
      three_model_status = #{threeModelStatus,jdbcType=TINYINT},
      error_info = #{errorInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_resource_doc_recycle_bin
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="resourceName != null and resourceName != '' ">
      and resource_name like CONCAT('%',#{resourceName,jdbcType=VARCHAR},'%')
    </if>
    <if test="fileName != null and fileName != '' ">
      and save_file_name like CONCAT('%',#{fileName,jdbcType=VARCHAR},'%')
    </if>
    <if test="resourceUriName != null and resourceUriName != ''">
      and resource_uri_name like CONCAT('%',#{resourceUriName,jdbcType=VARCHAR},'%')
    </if>
    <if test="startTime != null and startTime != '' ">
      and gmt_create >= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="endTime != null and endTime != '' ">
      and gmt_create <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
    </if>
  </select>

  <update id="updateThreeModelStatusBySourceId" parameterType="com.das.museum.infr.dataobject.SzzyResourceDocRecycleBinDO">
    update szzy_resource_doc_recycle_bin
    <set>
      <if test="threeModelUrl != null">
        three_model_url = #{threeModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="threeModelStatus != null">
        three_model_status = #{threeModelStatus,jdbcType=TINYINT},
      </if>
      <if test="errorInfo != null">
        error_info = #{errorInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where company_id = #{companyId,jdbcType=BIGINT} and
        source_doc_id = #{sourceDocId,jdbcType=BIGINT} and
      resource_id = #{resourceId,jdbcType=BIGINT}
  </update>
</mapper>