<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ShjyActivityApplyInfoMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ShjyActivityApplyInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_unique_code" jdbcType="VARCHAR" property="companyUniqueCode" />
    <result column="activity_unique_code" jdbcType="VARCHAR" property="activityUniqueCode" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="venue_name" jdbcType="VARCHAR" property="venueName" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="activity_no" jdbcType="VARCHAR" property="activityNo" />
    <result column="apply_name" jdbcType="VARCHAR" property="applyName" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.das.museum.infr.dataobject.ShjyActivityApplyInfoDO">
    <result column="apply_user_info" jdbcType="LONGVARCHAR" property="applyUserInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, company_unique_code, activity_unique_code, activity_id, venue_name, 
    activity_name, activity_no, apply_name, creator, modifier, gmt_create, gmt_modified
  </sql>
  <sql id="Blob_Column_List">
    apply_user_info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_apply_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from shjy_activity_apply_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ShjyActivityApplyInfoDO" useGeneratedKeys="true">
    insert into shjy_activity_apply_info (company_id, company_unique_code, activity_unique_code, 
      activity_id, venue_name, activity_name, 
      activity_no, apply_name, creator, 
      modifier, gmt_create, gmt_modified, 
      apply_user_info)
    values (#{companyId,jdbcType=BIGINT}, #{companyUniqueCode,jdbcType=VARCHAR}, #{activityUniqueCode,jdbcType=VARCHAR}, 
      #{activityId,jdbcType=BIGINT}, #{venueName,jdbcType=VARCHAR}, #{activityName,jdbcType=VARCHAR}, 
      #{activityNo,jdbcType=VARCHAR}, #{applyName,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{applyUserInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ShjyActivityApplyInfoDO" useGeneratedKeys="true">
    insert into shjy_activity_apply_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyUniqueCode != null">
        company_unique_code,
      </if>
      <if test="activityUniqueCode != null">
        activity_unique_code,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="venueName != null">
        venue_name,
      </if>
      <if test="activityName != null">
        activity_name,
      </if>
      <if test="activityNo != null">
        activity_no,
      </if>
      <if test="applyName != null">
        apply_name,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="applyUserInfo != null">
        apply_user_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyUniqueCode != null">
        #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityUniqueCode != null">
        #{activityUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="venueName != null">
        #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="activityNo != null">
        #{activityNo,jdbcType=VARCHAR},
      </if>
      <if test="applyName != null">
        #{applyName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="applyUserInfo != null">
        #{applyUserInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ShjyActivityApplyInfoDO">
    update shjy_activity_apply_info
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyUniqueCode != null">
        company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityUniqueCode != null">
        activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="venueName != null">
        venue_name = #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        activity_name = #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="activityNo != null">
        activity_no = #{activityNo,jdbcType=VARCHAR},
      </if>
      <if test="applyName != null">
        apply_name = #{applyName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="applyUserInfo != null">
        apply_user_info = #{applyUserInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.das.museum.infr.dataobject.ShjyActivityApplyInfoDO">
    update shjy_activity_apply_info
    set company_id = #{companyId,jdbcType=BIGINT},
      company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR},
      activity_id = #{activityId,jdbcType=BIGINT},
      venue_name = #{venueName,jdbcType=VARCHAR},
      activity_name = #{activityName,jdbcType=VARCHAR},
      activity_no = #{activityNo,jdbcType=VARCHAR},
      apply_name = #{applyName,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      apply_user_info = #{applyUserInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ShjyActivityApplyInfoDO">
    update shjy_activity_apply_info
    set company_id = #{companyId,jdbcType=BIGINT},
      company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR},
      activity_id = #{activityId,jdbcType=BIGINT},
      venue_name = #{venueName,jdbcType=VARCHAR},
      activity_name = #{activityName,jdbcType=VARCHAR},
      activity_no = #{activityNo,jdbcType=VARCHAR},
      apply_name = #{applyName,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectListByParams" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_apply_info
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    and activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR}
    order by gmt_create desc
  </select>

  <select id="selectByActivityNo" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_apply_info
    where activity_no = #{activityNo,jdbcType=VARCHAR}
  </select>

  <select id="listByCondition" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_apply_info
    where creator = #{creatorId,jdbcType=BIGINT}
    <if test="activityName != null">
      and (activity_name like CONCAT('%',#{activityName,jdbcType=VARCHAR},'%') or venue_name like CONCAT('%',#{activityName,jdbcType=VARCHAR},'%'))
    </if>
  </select>

  <select id="listByCreatorId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from shjy_activity_apply_info
    where creator = #{creatorId,jdbcType=BIGINT} and company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    and activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR}
  </select>
</mapper>