<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionTwoImageMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionTwoImageDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="register_info_id" jdbcType="BIGINT" property="registerInfoId" />
    <result column="document_id" jdbcType="BIGINT" property="documentId" />
    <result column="file_batch" jdbcType="VARCHAR" property="fileBatch" />
    <result column="make_unit" jdbcType="VARCHAR" property="makeUnit" />
    <result column="make_time" jdbcType="TIMESTAMP" property="makeTime" />
    <result column="resolution_ratio" jdbcType="VARCHAR" property="resolutionRatio" />
    <result column="collect_device" jdbcType="VARCHAR" property="collectDevice" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator"/>
    <result column="modifier" jdbcType="BIGINT" property="modifier"/>
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, register_info_id, document_id, file_batch, make_unit, make_time, 
    resolution_ratio, collect_device, remark, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from scs_collection_two_image
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_two_image
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ScsCollectionTwoImageDO" useGeneratedKeys="true">
    insert into scs_collection_two_image (company_id, register_info_id, document_id, 
      file_batch, make_unit, make_time, 
      resolution_ratio, collect_device, remark,
      creator, modifier, gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{registerInfoId,jdbcType=BIGINT}, #{documentId,jdbcType=BIGINT}, 
      #{fileBatch,jdbcType=VARCHAR}, #{makeUnit,jdbcType=VARCHAR}, #{makeTime,jdbcType=TIMESTAMP}, 
      #{resolutionRatio,jdbcType=VARCHAR}, #{collectDevice,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ScsCollectionTwoImageDO" useGeneratedKeys="true">
    insert into scs_collection_two_image
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="registerInfoId != null">
        register_info_id,
      </if>
      <if test="documentId != null">
        document_id,
      </if>
      <if test="fileBatch != null">
        file_batch,
      </if>
      <if test="makeUnit != null">
        make_unit,
      </if>
      <if test="makeTime != null">
        make_time,
      </if>
      <if test="resolutionRatio != null">
        resolution_ratio,
      </if>
      <if test="collectDevice != null">
        collect_device,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="registerInfoId != null">
        #{registerInfoId,jdbcType=BIGINT},
      </if>
      <if test="documentId != null">
        #{documentId,jdbcType=BIGINT},
      </if>
      <if test="fileBatch != null">
        #{fileBatch,jdbcType=VARCHAR},
      </if>
      <if test="makeUnit != null">
        #{makeUnit,jdbcType=VARCHAR},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resolutionRatio != null">
        #{resolutionRatio,jdbcType=VARCHAR},
      </if>
      <if test="collectDevice != null">
        #{collectDevice,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsCollectionTwoImageDO">
    update scs_collection_two_image
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="registerInfoId != null">
        register_info_id = #{registerInfoId,jdbcType=BIGINT},
      </if>
      <if test="documentId != null">
        document_id = #{documentId,jdbcType=BIGINT},
      </if>
      <if test="fileBatch != null">
        file_batch = #{fileBatch,jdbcType=VARCHAR},
      </if>
      <if test="makeUnit != null">
        make_unit = #{makeUnit,jdbcType=VARCHAR},
      </if>
      <if test="makeTime != null">
        make_time = #{makeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resolutionRatio != null">
        resolution_ratio = #{resolutionRatio,jdbcType=VARCHAR},
      </if>
      <if test="collectDevice != null">
        collect_device = #{collectDevice,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionTwoImageDO">
    update scs_collection_two_image
    set company_id = #{companyId,jdbcType=BIGINT},
      register_info_id = #{registerInfoId,jdbcType=BIGINT},
      document_id = #{documentId,jdbcType=BIGINT},
      file_batch = #{fileBatch,jdbcType=VARCHAR},
      make_unit = #{makeUnit,jdbcType=VARCHAR},
      make_time = #{makeTime,jdbcType=TIMESTAMP},
      resolution_ratio = #{resolutionRatio,jdbcType=VARCHAR},
      collect_device = #{collectDevice,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listPageByCondition" resultType="com.das.museum.infr.dataobjectexpand.QueryTwoImagePageDO">
    select
    sc.id twoImageId,
    cd.url url,
    cd.source_file_name sourceFileName,
    sc.file_batch fileBatch,
    cd.file_format fileFormat,
    cd.file_size fileSize,
    sc.creator creator,
    sc.modifier modifier,
    sc.gmt_create gmtCreate,
    sc.gmt_modified gmtModified
    from scs_collection_two_image sc
    inner join comm_document cd
    on sc.company_id = cd.company_id and sc.document_id = cd.id
    where sc.company_id = #{companyId,jdbcType=BIGINT}
    and sc.register_info_id = #{registerInfoId,jdbcType=BIGINT}
    <if test="keyword != null and keyword != '' ">
      and ( cd.source_file_name like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
      or
      sc.file_batch like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
      )
    </if>
    <if test="createStartTime != null and createStartTime != '' ">
      and sc.gmt_create &gt; #{createStartTime,jdbcType=VARCHAR}
    </if>
    <if test="createEndTime != null and createEndTime != '' ">
      and sc.gmt_create &lt; #{createEndTime,jdbcType=VARCHAR}
    </if>
    <if test="updateStartTime != null and updateStartTime != '' ">
      and sc.gmt_modified &gt; #{updateStartTime,jdbcType=VARCHAR}
    </if>
    <if test="updateEndTime != null and updateEndTime != '' ">
      and sc.gmt_modified &lt; #{updateEndTime,jdbcType=VARCHAR}
    </if>
    order by sc.gmt_modified desc
  </select>

  <select id="selectByCompanyIdAndId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from scs_collection_two_image
    where
    company_id = #{companyId,jdbcType=BIGINT}
    and id = #{id,jdbcType=BIGINT}
  </select>

  <update id="updateByCompanyIdAndId" parameterType="com.das.museum.infr.dataobject.ScsCollectionTwoImageDO">
    update scs_collection_two_image
    set register_info_id = #{registerInfoId,jdbcType=BIGINT},
        document_id = #{documentId,jdbcType=BIGINT},
        file_batch = #{fileBatch,jdbcType=VARCHAR},
        make_unit = #{makeUnit,jdbcType=VARCHAR},
        make_time = #{makeTime,jdbcType=TIMESTAMP},
        resolution_ratio = #{resolutionRatio,jdbcType=VARCHAR},
        collect_device = #{collectDevice,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=BIGINT},
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where company_id = #{companyId,jdbcType=BIGINT} and  id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByCompanyIdAndId">
    delete from scs_collection_two_image
    where company_id = #{companyId,jdbcType=BIGINT} and  id = #{id,jdbcType=BIGINT}
  </delete>

  <select id="selectListByRegisterInfoIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from scs_collection_two_image
    where register_info_id in
    <foreach collection="registerInfoIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="groupByRegisterInfoId" resultType="com.das.museum.infr.dataobjectexpand.GroupByRegisterInfoIdDO">
    select
      register_info_id registerInfoId,
      count(register_info_id) num
    from scs_collection_two_image
    where company_id = #{companyId,jdbcType=BIGINT} and register_info_id in
    <foreach collection="registerInfoIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
    group by register_info_id
  </select>

</mapper>