<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.GztWaitDealCalendarMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.GztWaitDealCalendarDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="wait_deal_day" jdbcType="DATE" property="waitDealDay" />
    <result column="wait_deal_content" jdbcType="VARCHAR" property="waitDealContent" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, user_id, wait_deal_day, wait_deal_content, creator, modifier, gmt_create, 
    gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gzt_wait_deal_calendar
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gzt_wait_deal_calendar
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.GztWaitDealCalendarDO" useGeneratedKeys="true">
    insert into gzt_wait_deal_calendar (company_id, user_id, wait_deal_day, 
      wait_deal_content, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{waitDealDay,jdbcType=DATE}, 
      #{waitDealContent,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.GztWaitDealCalendarDO" useGeneratedKeys="true">
    insert into gzt_wait_deal_calendar
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="waitDealDay != null">
        wait_deal_day,
      </if>
      <if test="waitDealContent != null">
        wait_deal_content,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="waitDealDay != null">
        #{waitDealDay,jdbcType=DATE},
      </if>
      <if test="waitDealContent != null">
        #{waitDealContent,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.GztWaitDealCalendarDO">
    update gzt_wait_deal_calendar
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="waitDealDay != null">
        wait_deal_day = #{waitDealDay,jdbcType=DATE},
      </if>
      <if test="waitDealContent != null">
        wait_deal_content = #{waitDealContent,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.GztWaitDealCalendarDO">
    update gzt_wait_deal_calendar
    set company_id = #{companyId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      wait_deal_day = #{waitDealDay,jdbcType=DATE},
      wait_deal_content = #{waitDealContent,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectListByParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from gzt_wait_deal_calendar
    where user_id = #{userId,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
    and wait_deal_day >= #{startDay,jdbcType=DATE}
    and wait_deal_day &lt;=  #{endDay,jdbcType=DATE}
  </select>

  <select id="selectByCompanyIdAndId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from gzt_wait_deal_calendar
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByCompanyIdAndId">
    delete from gzt_wait_deal_calendar
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </delete>
</mapper>