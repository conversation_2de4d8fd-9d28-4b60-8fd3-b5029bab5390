<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ExpExplainInfoMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ExpExplainInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_unique_code" jdbcType="VARCHAR" property="companyUniqueCode" />
    <result column="explain_unique_code" jdbcType="VARCHAR" property="explainUniqueCode" />
    <result column="record_unique_code" jdbcType="VARCHAR" property="recordUniqueCode" />
    <result column="explain_name" jdbcType="VARCHAR" property="explainName" />
    <result column="explain_index" jdbcType="BIGINT" property="explainIndex" />
    <result column="explain_words" jdbcType="VARCHAR" property="explainWords" />
    <result column="explain_illustration_ids" jdbcType="VARCHAR" property="explainIllustrationIds" />
    <result column="explain_video_ids" jdbcType="VARCHAR" property="explainVideoIds" />
    <result column="explain_type" jdbcType="TINYINT" property="explainType" />
    <result column="explain_status" jdbcType="TINYINT" property="explainStatus" />
    <result column="exhibition_name" jdbcType="VARCHAR" property="exhibitionName" />
    <result column="showroom_name" jdbcType="VARCHAR" property="showroomName" />
    <result column="unit_name" jdbcType="VARCHAR" property="unitName" />
    <result column="showpiece_name" jdbcType="VARCHAR" property="showpieceName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, company_unique_code, explain_unique_code, record_unique_code, explain_name, 
    explain_index, explain_words, explain_illustration_ids, explain_video_ids, explain_type, 
    explain_status, exhibition_name, showroom_name, unit_name, showpiece_name, remark, 
    creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from exp_explain_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from exp_explain_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ExpExplainInfoDO" useGeneratedKeys="true">
    insert into exp_explain_info (company_id, company_unique_code, explain_unique_code, 
      record_unique_code, explain_name, explain_index, 
      explain_words, explain_illustration_ids, explain_video_ids, 
      explain_type, explain_status, exhibition_name, 
      showroom_name, unit_name, showpiece_name, 
      remark, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{companyUniqueCode,jdbcType=VARCHAR}, #{explainUniqueCode,jdbcType=VARCHAR}, 
      #{recordUniqueCode,jdbcType=VARCHAR}, #{explainName,jdbcType=VARCHAR}, #{explainIndex,jdbcType=BIGINT}, 
      #{explainWords,jdbcType=VARCHAR}, #{explainIllustrationIds,jdbcType=VARCHAR}, #{explainVideoIds,jdbcType=VARCHAR}, 
      #{explainType,jdbcType=TINYINT}, #{explainStatus,jdbcType=TINYINT}, #{exhibitionName,jdbcType=VARCHAR}, 
      #{showroomName,jdbcType=VARCHAR}, #{unitName,jdbcType=VARCHAR}, #{showpieceName,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ExpExplainInfoDO" useGeneratedKeys="true">
    insert into exp_explain_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyUniqueCode != null">
        company_unique_code,
      </if>
      <if test="explainUniqueCode != null">
        explain_unique_code,
      </if>
      <if test="recordUniqueCode != null">
        record_unique_code,
      </if>
      <if test="explainName != null">
        explain_name,
      </if>
      <if test="explainIndex != null">
        explain_index,
      </if>
      <if test="explainWords != null">
        explain_words,
      </if>
      <if test="explainIllustrationIds != null">
        explain_illustration_ids,
      </if>
      <if test="explainVideoIds != null">
        explain_video_ids,
      </if>
      <if test="explainType != null">
        explain_type,
      </if>
      <if test="explainStatus != null">
        explain_status,
      </if>
      <if test="exhibitionName != null">
        exhibition_name,
      </if>
      <if test="showroomName != null">
        showroom_name,
      </if>
      <if test="unitName != null">
        unit_name,
      </if>
      <if test="showpieceName != null">
        showpiece_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyUniqueCode != null">
        #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="explainUniqueCode != null">
        #{explainUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="recordUniqueCode != null">
        #{recordUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="explainName != null">
        #{explainName,jdbcType=VARCHAR},
      </if>
      <if test="explainIndex != null">
        #{explainIndex,jdbcType=BIGINT},
      </if>
      <if test="explainWords != null">
        #{explainWords,jdbcType=VARCHAR},
      </if>
      <if test="explainIllustrationIds != null">
        #{explainIllustrationIds,jdbcType=VARCHAR},
      </if>
      <if test="explainVideoIds != null">
        #{explainVideoIds,jdbcType=VARCHAR},
      </if>
      <if test="explainType != null">
        #{explainType,jdbcType=TINYINT},
      </if>
      <if test="explainStatus != null">
        #{explainStatus,jdbcType=TINYINT},
      </if>
      <if test="exhibitionName != null">
        #{exhibitionName,jdbcType=VARCHAR},
      </if>
      <if test="showroomName != null">
        #{showroomName,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="showpieceName != null">
        #{showpieceName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ExpExplainInfoDO">
    update exp_explain_info
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyUniqueCode != null">
        company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="explainUniqueCode != null">
        explain_unique_code = #{explainUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="recordUniqueCode != null">
        record_unique_code = #{recordUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="explainName != null">
        explain_name = #{explainName,jdbcType=VARCHAR},
      </if>
      <if test="explainIndex != null">
        explain_index = #{explainIndex,jdbcType=BIGINT},
      </if>
      <if test="explainWords != null">
        explain_words = #{explainWords,jdbcType=VARCHAR},
      </if>
      <if test="explainIllustrationIds != null">
        explain_illustration_ids = #{explainIllustrationIds,jdbcType=VARCHAR},
      </if>
      <if test="explainVideoIds != null">
        explain_video_ids = #{explainVideoIds,jdbcType=VARCHAR},
      </if>
      <if test="explainType != null">
        explain_type = #{explainType,jdbcType=TINYINT},
      </if>
      <if test="explainStatus != null">
        explain_status = #{explainStatus,jdbcType=TINYINT},
      </if>
      <if test="exhibitionName != null">
        exhibition_name = #{exhibitionName,jdbcType=VARCHAR},
      </if>
      <if test="showroomName != null">
        showroom_name = #{showroomName,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        unit_name = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="showpieceName != null">
        showpiece_name = #{showpieceName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ExpExplainInfoDO">
    update exp_explain_info
    set company_id = #{companyId,jdbcType=BIGINT},
      company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      explain_unique_code = #{explainUniqueCode,jdbcType=VARCHAR},
      record_unique_code = #{recordUniqueCode,jdbcType=VARCHAR},
      explain_name = #{explainName,jdbcType=VARCHAR},
      explain_index = #{explainIndex,jdbcType=BIGINT},
      explain_words = #{explainWords,jdbcType=VARCHAR},
      explain_illustration_ids = #{explainIllustrationIds,jdbcType=VARCHAR},
      explain_video_ids = #{explainVideoIds,jdbcType=VARCHAR},
      explain_type = #{explainType,jdbcType=TINYINT},
      explain_status = #{explainStatus,jdbcType=TINYINT},
      exhibition_name = #{exhibitionName,jdbcType=VARCHAR},
      showroom_name = #{showroomName,jdbcType=VARCHAR},
      unit_name = #{unitName,jdbcType=VARCHAR},
      showpiece_name = #{showpieceName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listPageByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from exp_explain_info
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    <if test="explainName != null">
      and explain_name like CONCAT('%',#{explainName,jdbcType=VARCHAR},'%')
    </if>
    <if test="explainType != null">
      and explain_type = #{explainType,jdbcType=TINYINT}
    </if>
    <if test="explainStatus != null">
      and explain_status = #{explainStatus,jdbcType=TINYINT}
    </if>
    order by explain_index
  </select>

  <select id="selectListByCompanyUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from exp_explain_info
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    order by explain_index
  </select>

  <select id="selectByExplainUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from exp_explain_info
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR} and explain_unique_code = #{explainUniqueCode,jdbcType=VARCHAR}
  </select>

  <select id="listByRecordUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from exp_explain_info
    where record_unique_code = #{recordUniqueCode,jdbcType=VARCHAR}
  </select>

  <select id="listByRecordUniqueCodes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from exp_explain_info
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR} and record_unique_code in
    <foreach collection="recordUniqueCodes" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <update id="updateIndex">
    update exp_explain_info
    set explain_index = explain_index + 1
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR} and explain_index &gt;= #{explainIndex,jdbcType=BIGINT}
  </update>

  <update id="updateIndexBefore">
    update exp_explain_info
    set explain_index = explain_index - 1
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
      and explain_index &gt; #{currentIndex,jdbcType=BIGINT}
      and explain_index &lt;= #{targetIndex,jdbcType=BIGINT}
  </update>

  <update id="updateIndexAfter">
    update exp_explain_info
    set explain_index = explain_index + 1
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
      and explain_index &gt;= #{currentIndex,jdbcType=BIGINT}
      and explain_index &lt; #{targetIndex,jdbcType=BIGINT}
  </update>

  <update id="updateStatus">
    update exp_explain_info
    set explain_status = #{explainStatus,jdbcType=TINYINT}
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR} and explain_unique_code in
    <foreach collection="explainUniqueCodes" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </update>

  <delete id="deleteByExplainUniqueCodes">
    delete from exp_explain_info
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR} and explain_unique_code in
    <foreach collection="explainUniqueCodes" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </delete>

  <select id="selectMaxIndex" resultType="long">
    select MAX(explain_index) from exp_explain_info
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
  </select>
</mapper>