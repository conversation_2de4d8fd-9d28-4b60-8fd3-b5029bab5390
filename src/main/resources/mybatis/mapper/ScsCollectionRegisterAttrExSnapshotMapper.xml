<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionRegisterAttrExSnapshotMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionRegisterAttrExSnapshotDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="register_info_snapshot_id" jdbcType="BIGINT" property="registerInfoSnapshotId"/>
        <result column="register_no" jdbcType="VARCHAR" property="registerNo"/>
        <result column="seller" jdbcType="VARCHAR" property="seller"/>
        <result column="buy_date" jdbcType="TIMESTAMP" property="buyDate"/>
        <result column="buy_handler" jdbcType="VARCHAR" property="buyHandler"/>
        <result column="buy_address" jdbcType="VARCHAR" property="buyAddress"/>
        <result column="donors" jdbcType="VARCHAR" property="donors"/>
        <result column="donate_date" jdbcType="TIMESTAMP" property="donateDate"/>
        <result column="donate_handler" jdbcType="VARCHAR" property="donateHandler"/>
        <result column="donate_address" jdbcType="VARCHAR" property="donateAddress"/>
        <result column="collector" jdbcType="VARCHAR" property="collector"/>
        <result column="collect_handler" jdbcType="VARCHAR" property="collectHandler"/>
        <result column="collect_address" jdbcType="VARCHAR" property="collectAddress"/>
        <result column="excavator" jdbcType="VARCHAR" property="excavator"/>
        <result column="excavation_date" jdbcType="TIMESTAMP" property="excavationDate"/>
        <result column="excavator_address" jdbcType="VARCHAR" property="excavatorAddress"/>
        <result column="transfer_unit" jdbcType="VARCHAR" property="transferUnit"/>
        <result column="transfer_date" jdbcType="TIMESTAMP" property="transferDate"/>
        <result column="transfer_handler" jdbcType="VARCHAR" property="transferHandler"/>
        <result column="appropriation_unit" jdbcType="VARCHAR" property="appropriationUnit"/>
        <result column="appropriation_date" jdbcType="TIMESTAMP" property="appropriationDate"/>
        <result column="appropriation_handler" jdbcType="VARCHAR" property="appropriationHandler"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="process" jdbcType="VARCHAR" property="process"/>
        <result column="hand_down" jdbcType="VARCHAR" property="handDown"/>
        <result column="status_record" jdbcType="VARCHAR" property="statusRecord"/>
        <result column="appendix" jdbcType="VARCHAR" property="appendix"/>
        <result column="synopsis" jdbcType="VARCHAR" property="synopsis"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, register_info_snapshot_id, register_no, seller, buy_date, buy_handler, buy_address,
    donors, donate_date, donate_handler, donate_address, collector, collect_handler, 
    collect_address, excavator, excavation_date, excavator_address, transfer_unit, transfer_date, 
    transfer_handler, appropriation_unit, appropriation_date, appropriation_handler, 
    description, `process`, hand_down, status_record, appendix, synopsis, creator, modifier, 
    gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_register_attr_ex_snapshot
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_register_attr_ex_snapshot
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterAttrExSnapshotDO"
            useGeneratedKeys="true">
    insert into scs_collection_register_attr_ex_snapshot (company_id, register_info_snapshot_id, register_no,
      seller, buy_date, buy_handler, 
      buy_address, donors, donate_date, 
      donate_handler, donate_address, collector, 
      collect_handler, collect_address, excavator, 
      excavation_date, excavator_address, transfer_unit, 
      transfer_date, transfer_handler, appropriation_unit, 
      appropriation_date, appropriation_handler, 
      description, `process`, hand_down, 
      status_record, appendix, synopsis, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{registerInfoSnapshotId,jdbcType=BIGINT}, #{registerNo,jdbcType=VARCHAR},
      #{seller,jdbcType=VARCHAR}, #{buyDate,jdbcType=TIMESTAMP}, #{buyHandler,jdbcType=VARCHAR}, 
      #{buyAddress,jdbcType=VARCHAR}, #{donors,jdbcType=VARCHAR}, #{donateDate,jdbcType=TIMESTAMP}, 
      #{donateHandler,jdbcType=VARCHAR}, #{donateAddress,jdbcType=VARCHAR}, #{collector,jdbcType=VARCHAR}, 
      #{collectHandler,jdbcType=VARCHAR}, #{collectAddress,jdbcType=VARCHAR}, #{excavator,jdbcType=VARCHAR}, 
      #{excavationDate,jdbcType=TIMESTAMP}, #{excavatorAddress,jdbcType=VARCHAR}, #{transferUnit,jdbcType=VARCHAR}, 
      #{transferDate,jdbcType=TIMESTAMP}, #{transferHandler,jdbcType=VARCHAR}, #{appropriationUnit,jdbcType=VARCHAR}, 
      #{appropriationDate,jdbcType=TIMESTAMP}, #{appropriationHandler,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{process,jdbcType=VARCHAR}, #{handDown,jdbcType=VARCHAR}, 
      #{statusRecord,jdbcType=VARCHAR}, #{appendix,jdbcType=VARCHAR}, #{synopsis,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterAttrExSnapshotDO"
            useGeneratedKeys="true">
        insert into scs_collection_register_attr_ex_snapshot
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="registerInfoSnapshotId != null">
                register_info_snapshot_id,
            </if>
            <if test="registerNo != null">
                register_no,
            </if>
            <if test="seller != null">
                seller,
            </if>
            <if test="buyDate != null">
                buy_date,
            </if>
            <if test="buyHandler != null">
                buy_handler,
            </if>
            <if test="buyAddress != null">
                buy_address,
            </if>
            <if test="donors != null">
                donors,
            </if>
            <if test="donateDate != null">
                donate_date,
            </if>
            <if test="donateHandler != null">
                donate_handler,
            </if>
            <if test="donateAddress != null">
                donate_address,
            </if>
            <if test="collector != null">
                collector,
            </if>
            <if test="collectHandler != null">
                collect_handler,
            </if>
            <if test="collectAddress != null">
                collect_address,
            </if>
            <if test="excavator != null">
                excavator,
            </if>
            <if test="excavationDate != null">
                excavation_date,
            </if>
            <if test="excavatorAddress != null">
                excavator_address,
            </if>
            <if test="transferUnit != null">
                transfer_unit,
            </if>
            <if test="transferDate != null">
                transfer_date,
            </if>
            <if test="transferHandler != null">
                transfer_handler,
            </if>
            <if test="appropriationUnit != null">
                appropriation_unit,
            </if>
            <if test="appropriationDate != null">
                appropriation_date,
            </if>
            <if test="appropriationHandler != null">
                appropriation_handler,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="process != null">
                `process`,
            </if>
            <if test="handDown != null">
                hand_down,
            </if>
            <if test="statusRecord != null">
                status_record,
            </if>
            <if test="appendix != null">
                appendix,
            </if>
            <if test="synopsis != null">
                synopsis,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="registerInfoSnapshotId != null">
                #{registerInfoSnapshotId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="seller != null">
                #{seller,jdbcType=VARCHAR},
            </if>
            <if test="buyDate != null">
                #{buyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="buyHandler != null">
                #{buyHandler,jdbcType=VARCHAR},
            </if>
            <if test="buyAddress != null">
                #{buyAddress,jdbcType=VARCHAR},
            </if>
            <if test="donors != null">
                #{donors,jdbcType=VARCHAR},
            </if>
            <if test="donateDate != null">
                #{donateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="donateHandler != null">
                #{donateHandler,jdbcType=VARCHAR},
            </if>
            <if test="donateAddress != null">
                #{donateAddress,jdbcType=VARCHAR},
            </if>
            <if test="collector != null">
                #{collector,jdbcType=VARCHAR},
            </if>
            <if test="collectHandler != null">
                #{collectHandler,jdbcType=VARCHAR},
            </if>
            <if test="collectAddress != null">
                #{collectAddress,jdbcType=VARCHAR},
            </if>
            <if test="excavator != null">
                #{excavator,jdbcType=VARCHAR},
            </if>
            <if test="excavationDate != null">
                #{excavationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="excavatorAddress != null">
                #{excavatorAddress,jdbcType=VARCHAR},
            </if>
            <if test="transferUnit != null">
                #{transferUnit,jdbcType=VARCHAR},
            </if>
            <if test="transferDate != null">
                #{transferDate,jdbcType=TIMESTAMP},
            </if>
            <if test="transferHandler != null">
                #{transferHandler,jdbcType=VARCHAR},
            </if>
            <if test="appropriationUnit != null">
                #{appropriationUnit,jdbcType=VARCHAR},
            </if>
            <if test="appropriationDate != null">
                #{appropriationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="appropriationHandler != null">
                #{appropriationHandler,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="process != null">
                #{process,jdbcType=VARCHAR},
            </if>
            <if test="handDown != null">
                #{handDown,jdbcType=VARCHAR},
            </if>
            <if test="statusRecord != null">
                #{statusRecord,jdbcType=VARCHAR},
            </if>
            <if test="appendix != null">
                #{appendix,jdbcType=VARCHAR},
            </if>
            <if test="synopsis != null">
                #{synopsis,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterAttrExSnapshotDO">
        update scs_collection_register_attr_ex_snapshot
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="registerInfoSnapshotId != null">
                register_info_snapshot_id = #{registerInfoSnapshotId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                register_no = #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="seller != null">
                seller = #{seller,jdbcType=VARCHAR},
            </if>
            <if test="buyDate != null">
                buy_date = #{buyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="buyHandler != null">
                buy_handler = #{buyHandler,jdbcType=VARCHAR},
            </if>
            <if test="buyAddress != null">
                buy_address = #{buyAddress,jdbcType=VARCHAR},
            </if>
            <if test="donors != null">
                donors = #{donors,jdbcType=VARCHAR},
            </if>
            <if test="donateDate != null">
                donate_date = #{donateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="donateHandler != null">
                donate_handler = #{donateHandler,jdbcType=VARCHAR},
            </if>
            <if test="donateAddress != null">
                donate_address = #{donateAddress,jdbcType=VARCHAR},
            </if>
            <if test="collector != null">
                collector = #{collector,jdbcType=VARCHAR},
            </if>
            <if test="collectHandler != null">
                collect_handler = #{collectHandler,jdbcType=VARCHAR},
            </if>
            <if test="collectAddress != null">
                collect_address = #{collectAddress,jdbcType=VARCHAR},
            </if>
            <if test="excavator != null">
                excavator = #{excavator,jdbcType=VARCHAR},
            </if>
            <if test="excavationDate != null">
                excavation_date = #{excavationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="excavatorAddress != null">
                excavator_address = #{excavatorAddress,jdbcType=VARCHAR},
            </if>
            <if test="transferUnit != null">
                transfer_unit = #{transferUnit,jdbcType=VARCHAR},
            </if>
            <if test="transferDate != null">
                transfer_date = #{transferDate,jdbcType=TIMESTAMP},
            </if>
            <if test="transferHandler != null">
                transfer_handler = #{transferHandler,jdbcType=VARCHAR},
            </if>
            <if test="appropriationUnit != null">
                appropriation_unit = #{appropriationUnit,jdbcType=VARCHAR},
            </if>
            <if test="appropriationDate != null">
                appropriation_date = #{appropriationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="appropriationHandler != null">
                appropriation_handler = #{appropriationHandler,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="process != null">
                `process` = #{process,jdbcType=VARCHAR},
            </if>
            <if test="handDown != null">
                hand_down = #{handDown,jdbcType=VARCHAR},
            </if>
            <if test="statusRecord != null">
                status_record = #{statusRecord,jdbcType=VARCHAR},
            </if>
            <if test="appendix != null">
                appendix = #{appendix,jdbcType=VARCHAR},
            </if>
            <if test="synopsis != null">
                synopsis = #{synopsis,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionRegisterAttrExSnapshotDO">
    update scs_collection_register_attr_ex_snapshot
    set company_id = #{companyId,jdbcType=BIGINT},
      register_info_snapshot_id = #{registerInfoSnapshotId,jdbcType=BIGINT},
      register_no = #{registerNo,jdbcType=VARCHAR},
      seller = #{seller,jdbcType=VARCHAR},
      buy_date = #{buyDate,jdbcType=TIMESTAMP},
      buy_handler = #{buyHandler,jdbcType=VARCHAR},
      buy_address = #{buyAddress,jdbcType=VARCHAR},
      donors = #{donors,jdbcType=VARCHAR},
      donate_date = #{donateDate,jdbcType=TIMESTAMP},
      donate_handler = #{donateHandler,jdbcType=VARCHAR},
      donate_address = #{donateAddress,jdbcType=VARCHAR},
      collector = #{collector,jdbcType=VARCHAR},
      collect_handler = #{collectHandler,jdbcType=VARCHAR},
      collect_address = #{collectAddress,jdbcType=VARCHAR},
      excavator = #{excavator,jdbcType=VARCHAR},
      excavation_date = #{excavationDate,jdbcType=TIMESTAMP},
      excavator_address = #{excavatorAddress,jdbcType=VARCHAR},
      transfer_unit = #{transferUnit,jdbcType=VARCHAR},
      transfer_date = #{transferDate,jdbcType=TIMESTAMP},
      transfer_handler = #{transferHandler,jdbcType=VARCHAR},
      appropriation_unit = #{appropriationUnit,jdbcType=VARCHAR},
      appropriation_date = #{appropriationDate,jdbcType=TIMESTAMP},
      appropriation_handler = #{appropriationHandler,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      `process` = #{process,jdbcType=VARCHAR},
      hand_down = #{handDown,jdbcType=VARCHAR},
      status_record = #{statusRecord,jdbcType=VARCHAR},
      appendix = #{appendix,jdbcType=VARCHAR},
      synopsis = #{synopsis,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <select id="selectByRegisterInfoId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from scs_collection_register_attr_ex_snapshot
    where register_info_snapshot_id = #{registerInfoSnapshotId,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByRegisterInfoId">
    delete from scs_collection_register_attr_ex_snapshot
    where register_info_snapshot_id = #{registerInfoSnapshotId,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </delete>
</mapper>