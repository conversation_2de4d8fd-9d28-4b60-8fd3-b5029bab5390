<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SzzyOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SzzyOperateLogDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="content_id" jdbcType="BIGINT" property="contentId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, content_id, operate_name, operate_time, operator_id, operator_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from szzy_operate_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szzy_operate_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyOperateLogDO" useGeneratedKeys="true">
    insert into szzy_operate_log (company_id, content_id, operate_name, 
      operate_time, operator_id, operator_name
      )
    values (#{companyId,jdbcType=BIGINT}, #{contentId,jdbcType=BIGINT}, #{operateName,jdbcType=VARCHAR}, 
      #{operateTime,jdbcType=TIMESTAMP}, #{operatorId,jdbcType=BIGINT}, #{operatorName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyOperateLogDO" useGeneratedKeys="true">
    insert into szzy_operate_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="contentId != null">
        content_id,
      </if>
      <if test="operateName != null">
        operate_name,
      </if>
      <if test="operateTime != null">
        operate_time,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="contentId != null">
        #{contentId,jdbcType=BIGINT},
      </if>
      <if test="operateName != null">
        #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SzzyOperateLogDO">
    update szzy_operate_log
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="contentId != null">
        content_id = #{contentId,jdbcType=BIGINT},
      </if>
      <if test="operateName != null">
        operate_name = #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        operate_time = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SzzyOperateLogDO">
    update szzy_operate_log
    set company_id = #{companyId,jdbcType=BIGINT},
      content_id = #{contentId,jdbcType=BIGINT},
      operate_name = #{operateName,jdbcType=VARCHAR},
      operate_time = #{operateTime,jdbcType=TIMESTAMP},
      operator_id = #{operatorId,jdbcType=BIGINT},
      operator_name = #{operatorName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectBySourceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_operate_log
    where company_id = #{companyId,jdbcType=BIGINT}
    and content_id = #{resourceId,jdbcType=BIGINT}
    order by operate_time desc
  </select>
</mapper>