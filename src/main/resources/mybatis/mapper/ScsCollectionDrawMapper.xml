<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionDrawMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionDrawDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="draw_no" jdbcType="VARCHAR" property="drawNo"/>
        <result column="draughtsman_id" jdbcType="BIGINT" property="draughtsmanId"/>
        <result column="draughtsman_name" jdbcType="VARCHAR" property="draughtsmanName"/>
        <result column="proportion" jdbcType="VARCHAR" property="proportion"/>
        <result column="draw_date" jdbcType="TIMESTAMP" property="drawDate"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, collection_id, title, draw_no, draughtsman_id, draughtsman_name, 
    proportion, draw_date, document_id, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_draw
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_draw
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionDrawDO" useGeneratedKeys="true">
    insert into scs_collection_draw (company_id, collection_id, title, 
      draw_no, draughtsman_id, draughtsman_name, 
      proportion, draw_date, document_id, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{collectionId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, 
      #{drawNo,jdbcType=VARCHAR}, #{draughtsmanId,jdbcType=BIGINT}, #{draughtsmanName,jdbcType=VARCHAR}, 
      #{proportion,jdbcType=VARCHAR}, #{drawDate,jdbcType=TIMESTAMP}, #{documentId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionDrawDO" useGeneratedKeys="true">
        insert into scs_collection_draw
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="drawNo != null">
                draw_no,
            </if>
            <if test="draughtsmanId != null">
                draughtsman_id,
            </if>
            <if test="draughtsmanName != null">
                draughtsman_name,
            </if>
            <if test="proportion != null">
                proportion,
            </if>
            <if test="drawDate != null">
                draw_date,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="drawNo != null">
                #{drawNo,jdbcType=VARCHAR},
            </if>
            <if test="draughtsmanId != null">
                #{draughtsmanId,jdbcType=BIGINT},
            </if>
            <if test="draughtsmanName != null">
                #{draughtsmanName,jdbcType=VARCHAR},
            </if>
            <if test="proportion != null">
                #{proportion,jdbcType=VARCHAR},
            </if>
            <if test="drawDate != null">
                #{drawDate,jdbcType=TIMESTAMP},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsCollectionDrawDO">
        update scs_collection_draw
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="drawNo != null">
                draw_no = #{drawNo,jdbcType=VARCHAR},
            </if>
            <if test="draughtsmanId != null">
                draughtsman_id = #{draughtsmanId,jdbcType=BIGINT},
            </if>
            <if test="draughtsmanName != null">
                draughtsman_name = #{draughtsmanName,jdbcType=VARCHAR},
            </if>
            <if test="proportion != null">
                proportion = #{proportion,jdbcType=VARCHAR},
            </if>
            <if test="drawDate != null">
                draw_date = #{drawDate,jdbcType=TIMESTAMP},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionDrawDO">
    update scs_collection_draw
    set company_id = #{companyId,jdbcType=BIGINT},
      collection_id = #{collectionId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      draw_no = #{drawNo,jdbcType=VARCHAR},
      draughtsman_id = #{draughtsmanId,jdbcType=BIGINT},
      draughtsman_name = #{draughtsmanName,jdbcType=VARCHAR},
      proportion = #{proportion,jdbcType=VARCHAR},
      draw_date = #{drawDate,jdbcType=TIMESTAMP},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <delete id="deleteById">
        delete from scs_collection_draw
        where company_id = #{companyId,jdbcType=BIGINT} and id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_draw
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="listPageByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_draw
        where company_id = #{companyId,jdbcType=BIGINT} and collection_id = #{collectionId,jdbcType=BIGINT}
        <if test="title != null">
            and title like concat('%', #{title,jdbcType=VARCHAR}, '%')
        </if>
        <if test="drawNo != null">
            and draw_no like concat('%', #{drawNo,jdbcType=VARCHAR}, '%')
        </if>
        order by ${sortBy}
    </select>
</mapper>