<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.TktTimePeriodMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.TktTimePeriodDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="time_start" jdbcType="TIME" property="timeStart" />
    <result column="time_end" jdbcType="TIME" property="timeEnd" />
    <result column="upper_limit" jdbcType="BIGINT" property="upperLimit" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, department_id, biz_id, biz_type, time_start, time_end, upper_limit, 
    remark, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tkt_time_period
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tkt_time_period
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTimePeriodDO" useGeneratedKeys="true">
    insert into tkt_time_period (company_id, department_id, biz_id, 
      biz_type, time_start, time_end, 
      upper_limit, remark, creator, 
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, #{bizId,jdbcType=BIGINT}, 
      #{bizType,jdbcType=TINYINT}, #{timeStart,jdbcType=TIME}, #{timeEnd,jdbcType=TIME}, 
      #{upperLimit,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTimePeriodDO" useGeneratedKeys="true">
    insert into tkt_time_period
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="timeStart != null">
        time_start,
      </if>
      <if test="timeEnd != null">
        time_end,
      </if>
      <if test="upperLimit != null">
        upper_limit,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="timeStart != null">
        #{timeStart,jdbcType=TIME},
      </if>
      <if test="timeEnd != null">
        #{timeEnd,jdbcType=TIME},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.TktTimePeriodDO">
    update tkt_time_period
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="timeStart != null">
        time_start = #{timeStart,jdbcType=TIME},
      </if>
      <if test="timeEnd != null">
        time_end = #{timeEnd,jdbcType=TIME},
      </if>
      <if test="upperLimit != null">
        upper_limit = #{upperLimit,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.TktTimePeriodDO">
    update tkt_time_period
    set company_id = #{companyId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=TINYINT},
      time_start = #{timeStart,jdbcType=TIME},
      time_end = #{timeEnd,jdbcType=TIME},
      upper_limit = #{upperLimit,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByRuleBaseId">
    delete from tkt_time_period
    where
    company_id = #{companyId,jdbcType=BIGINT}
    and biz_id = #{bizId,jdbcType=BIGINT}
    and biz_type = #{bizType,jdbcType=TINYINT}
  </delete>

  <select id="selectByRuleBaseId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_time_period
    where company_id = #{companyId,jdbcType=BIGINT}
    and biz_id = #{bizId,jdbcType=BIGINT}
    and biz_type = #{bizType,jdbcType=TINYINT}
  </select>

  <select id="selectByTimePeriodIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_time_period
    where company_id = #{companyId,jdbcType=BIGINT}
    and id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>
</mapper>