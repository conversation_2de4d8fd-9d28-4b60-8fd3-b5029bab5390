<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SwzsCulturalRelicsShareMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SwzsCulturalRelicsShareDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="cultural_relics_id" jdbcType="VARCHAR" property="culturalRelicsId" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="share_count" jdbcType="BIGINT" property="shareCount" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, cultural_relics_id, `number`, `type`, share_count, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_share
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from swzs_cultural_relics_share
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsShareDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics_share (company_id, cultural_relics_id, `number`, 
      `type`, share_count, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{culturalRelicsId,jdbcType=VARCHAR}, #{number,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{shareCount,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsShareDO" useGeneratedKeys="true">
    insert into swzs_cultural_relics_share
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="culturalRelicsId != null">
        cultural_relics_id,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="shareCount != null">
        share_count,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="culturalRelicsId != null">
        #{culturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="shareCount != null">
        #{shareCount,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsShareDO">
    update swzs_cultural_relics_share
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="culturalRelicsId != null">
        cultural_relics_id = #{culturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="shareCount != null">
        share_count = #{shareCount,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsShareDO">
    update swzs_cultural_relics_share
    set company_id = #{companyId,jdbcType=BIGINT},
      cultural_relics_id = #{culturalRelicsId,jdbcType=VARCHAR},
      `number` = #{number,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=TINYINT},
      share_count = #{shareCount,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listDO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_share
    where company_id = #{companyId,jdbcType=BIGINT} and gmt_create &lt;= #{end,jdbcType=TIMESTAMP}
    <if test="start != null">
      and gmt_create &gt;= #{start,jdbcType=TIMESTAMP}
    </if>
  </select>
  <select id="selectByShareDO" parameterType="com.das.museum.infr.dataobject.SwzsCulturalRelicsShareDO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_share
    <where>
      <if test="companyId != null">
        and company_id = #{companyId,jdbcType=BIGINT}
      </if>
      <if test="culturalRelicsId != null">
        and cultural_relics_id = #{culturalRelicsId,jdbcType=VARCHAR}
      </if>
      <if test="number != null">
        and `number` = #{number,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=TINYINT}
      </if>
      <if test="shareCount != null">
        and share_count = #{shareCount,jdbcType=BIGINT}
      </if>
      <if test="gmtCreate != null">
        and gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
      </if>
      <if test="gmtModified != null">
        and gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>
  <update id="updateShareCountByCulturalRelicsId" >
    update swzs_cultural_relics_share
    set share_count = #{shareCount,jdbcType=BIGINT}
    where cultural_relics_id = #{culturalRelicsId,jdbcType=VARCHAR}
  </update>

  <select id="selectListByCulturalRelicsId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from swzs_cultural_relics_share
    where cultural_relics_id = #{culturalRelicsId,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </select>
</mapper>