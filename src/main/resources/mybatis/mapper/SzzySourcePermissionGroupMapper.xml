<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SzzySourcePermissionGroupMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SzzySourcePermissionGroupDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="security_level" jdbcType="TINYINT" property="securityLevel" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="data_permission" jdbcType="TINYINT" property="dataPermission" />
    <result column="function_permission" jdbcType="TINYINT" property="functionPermission" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, security_level, group_name, data_permission, function_permission, 
    creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from szzy_source_permission_group
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szzy_source_permission_group
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzySourcePermissionGroupDO" useGeneratedKeys="true">
    insert into szzy_source_permission_group (company_id, security_level, group_name, 
      data_permission, function_permission, creator, 
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{securityLevel,jdbcType=TINYINT}, #{groupName,jdbcType=VARCHAR}, 
      #{dataPermission,jdbcType=TINYINT}, #{functionPermission,jdbcType=TINYINT}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzySourcePermissionGroupDO" useGeneratedKeys="true">
    insert into szzy_source_permission_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="securityLevel != null">
        security_level,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="dataPermission != null">
        data_permission,
      </if>
      <if test="functionPermission != null">
        function_permission,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="securityLevel != null">
        #{securityLevel,jdbcType=TINYINT},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="dataPermission != null">
        #{dataPermission,jdbcType=TINYINT},
      </if>
      <if test="functionPermission != null">
        #{functionPermission,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SzzySourcePermissionGroupDO">
    update szzy_source_permission_group
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="securityLevel != null">
        security_level = #{securityLevel,jdbcType=TINYINT},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="dataPermission != null">
        data_permission = #{dataPermission,jdbcType=TINYINT},
      </if>
      <if test="functionPermission != null">
        function_permission = #{functionPermission,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SzzySourcePermissionGroupDO">
    update szzy_source_permission_group
    set company_id = #{companyId,jdbcType=BIGINT},
      security_level = #{securityLevel,jdbcType=TINYINT},
      group_name = #{groupName,jdbcType=VARCHAR},
      data_permission = #{dataPermission,jdbcType=TINYINT},
      function_permission = #{functionPermission,jdbcType=TINYINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_source_permission_group
    where id = #{id,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </select>

  <update id="updateById" parameterType="com.das.museum.infr.dataobject.SzzySourcePermissionGroupDO">
    update szzy_source_permission_group
    <set>
      <if test="securityLevel != null">
        security_level = #{securityLevel,jdbcType=TINYINT},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="dataPermission != null">
        data_permission = #{dataPermission,jdbcType=TINYINT},
      </if>
      <if test="functionPermission != null">
        function_permission = #{functionPermission,jdbcType=TINYINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
    and  company_id = #{companyId,jdbcType=BIGINT}
  </update>

  <select id="selectBySecurityLevel" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_source_permission_group
    where company_id = #{companyId,jdbcType=BIGINT}
    and security_level = #{securityLevel,jdbcType=TINYINT}
    order by gmt_modified desc
  </select>

  <select id="permissionListByPositionId" resultType="com.das.museum.infr.dataobjectexpand.PermissionByPositionIdDO">
    select
      A.job_id jobId,
      A.permission_group_id permissionGroupId,
      B.security_level securityLevel,
      B.data_permission dataPermission,
      B.function_permission functionPermission
    from szzy_job_permission A
           left join szzy_source_permission_group B on A.permission_group_id = B.id
    where A.company_id = #{companyId,jdbcType=BIGINT}
      and A.job_id = #{positionId,jdbcType=BIGINT}
      and B.security_level = #{securityLevel,jdbcType=TINYINT}
  </select>

  <select id="ListByPositionId" resultType="com.das.museum.infr.dataobjectexpand.PermissionByPositionIdDO">
    select
      A.job_id jobId,
      A.permission_group_id permissionGroupId,
      B.security_level securityLevel,
      B.data_permission dataPermission,
      B.function_permission functionPermission
    from szzy_job_permission A
           left join szzy_source_permission_group B on A.permission_group_id = B.id
    where A.company_id = #{companyId,jdbcType=BIGINT}
      and A.job_id = #{positionId,jdbcType=BIGINT}
  </select>


  <select id="selectPermissionList" resultType="com.das.museum.infr.dataobjectexpand.PermissionByPositionIdDO">
    select
      A.job_id jobId,
      A.permission_group_id permissionGroupId,
      B.security_level securityLevel,
      B.data_permission dataPermission,
      B.function_permission functionPermission
    from szzy_job_permission A
           left join szzy_source_permission_group B on A.permission_group_id = B.id
    where A.company_id = #{companyId,jdbcType=BIGINT}
      and A.job_id = #{positionId,jdbcType=BIGINT}
      and data_permission = #{dataPermission,jdbcType=TINYINT}
  </select>

  <delete id="deleteById">
    delete from szzy_source_permission_group
    where
      company_id = #{companyId,jdbcType=BIGINT}
      and id = #{id,jdbcType=BIGINT}
  </delete>
</mapper>