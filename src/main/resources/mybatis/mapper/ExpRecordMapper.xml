<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ExpRecordMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ExpRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_unique_code" jdbcType="VARCHAR" property="companyUniqueCode" />
    <result column="record_unique_code" jdbcType="VARCHAR" property="recordUniqueCode" />
    <result column="record_name" jdbcType="VARCHAR" property="recordName" />
    <result column="record_duration" jdbcType="BIGINT" property="recordDuration" />
    <result column="record_size" jdbcType="BIGINT" property="recordSize" />
    <result column="record_status" jdbcType="TINYINT" property="recordStatus" />
    <result column="document_id" jdbcType="BIGINT" property="documentId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, company_unique_code, record_unique_code, record_name, record_duration, 
    record_size, record_status, document_id, remark, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from exp_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from exp_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ExpRecordDO" useGeneratedKeys="true">
    insert into exp_record (company_id, company_unique_code, record_unique_code, 
      record_name, record_duration, record_size, 
      record_status, document_id, remark, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{companyUniqueCode,jdbcType=VARCHAR}, #{recordUniqueCode,jdbcType=VARCHAR}, 
      #{recordName,jdbcType=VARCHAR}, #{recordDuration,jdbcType=BIGINT}, #{recordSize,jdbcType=BIGINT}, 
      #{recordStatus,jdbcType=TINYINT}, #{documentId,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ExpRecordDO" useGeneratedKeys="true">
    insert into exp_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyUniqueCode != null">
        company_unique_code,
      </if>
      <if test="recordUniqueCode != null">
        record_unique_code,
      </if>
      <if test="recordName != null">
        record_name,
      </if>
      <if test="recordDuration != null">
        record_duration,
      </if>
      <if test="recordSize != null">
        record_size,
      </if>
      <if test="recordStatus != null">
        record_status,
      </if>
      <if test="documentId != null">
        document_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyUniqueCode != null">
        #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="recordUniqueCode != null">
        #{recordUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="recordName != null">
        #{recordName,jdbcType=VARCHAR},
      </if>
      <if test="recordDuration != null">
        #{recordDuration,jdbcType=BIGINT},
      </if>
      <if test="recordSize != null">
        #{recordSize,jdbcType=BIGINT},
      </if>
      <if test="recordStatus != null">
        #{recordStatus,jdbcType=TINYINT},
      </if>
      <if test="documentId != null">
        #{documentId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ExpRecordDO">
    update exp_record
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyUniqueCode != null">
        company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="recordUniqueCode != null">
        record_unique_code = #{recordUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="recordName != null">
        record_name = #{recordName,jdbcType=VARCHAR},
      </if>
      <if test="recordDuration != null">
        record_duration = #{recordDuration,jdbcType=BIGINT},
      </if>
      <if test="recordSize != null">
        record_size = #{recordSize,jdbcType=BIGINT},
      </if>
      <if test="recordStatus != null">
        record_status = #{recordStatus,jdbcType=TINYINT},
      </if>
      <if test="documentId != null">
        document_id = #{documentId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ExpRecordDO">
    update exp_record
    set company_id = #{companyId,jdbcType=BIGINT},
      company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      record_unique_code = #{recordUniqueCode,jdbcType=VARCHAR},
      record_name = #{recordName,jdbcType=VARCHAR},
      record_duration = #{recordDuration,jdbcType=BIGINT},
      record_size = #{recordSize,jdbcType=BIGINT},
      record_status = #{recordStatus,jdbcType=TINYINT},
      document_id = #{documentId,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listPageByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from exp_record
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    <if test="recordName != null">
      and record_name like concat('%', #{recordName,jdbcType=VARCHAR}, '%')
    </if>
    order by gmt_create desc
  </select>

  <select id="listByRecordName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from exp_record
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    <if test="recordName != null">
      and record_name = #{recordName,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectByRecordUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from exp_record
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR} and record_unique_code = #{recordUniqueCode,jdbcType=VARCHAR}
  </select>

  <select id="listByRecordUniqueCodes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from exp_record
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR} and record_unique_code in
    <foreach collection="recordUniqueCodes" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <update id="updateByRecordUniqueCode" parameterType="com.das.museum.infr.dataobject.ExpRecordDO">
    update exp_record
    <set>
      <if test="recordName != null">
        record_name = #{recordName,jdbcType=VARCHAR},
      </if>
      <if test="recordDuration != null">
        record_duration = #{recordDuration,jdbcType=BIGINT},
      </if>
      <if test="recordSize != null">
        record_size = #{recordSize,jdbcType=BIGINT},
      </if>
      <if test="recordStatus != null">
        record_status = #{recordStatus,jdbcType=TINYINT},
      </if>
      <if test="documentId != null">
        document_id = #{documentId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where record_unique_code = #{recordUniqueCode,jdbcType=VARCHAR}
  </update>
</mapper>