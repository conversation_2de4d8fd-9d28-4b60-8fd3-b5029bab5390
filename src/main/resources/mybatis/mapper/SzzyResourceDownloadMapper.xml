<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SzzyResourceDownloadMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SzzyResourceDownloadDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="document_id" jdbcType="BIGINT" property="documentId" />
    <result column="resource_id" jdbcType="BIGINT" property="resourceId" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, user_id, document_id, resource_id, creator, modifier, gmt_create, 
    gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from szzy_resource_download
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szzy_resource_download
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyResourceDownloadDO" useGeneratedKeys="true">
    insert into szzy_resource_download (company_id, user_id, document_id, 
      resource_id, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{documentId,jdbcType=BIGINT}, 
      #{resourceId,jdbcType=BIGINT}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyResourceDownloadDO" useGeneratedKeys="true">
    insert into szzy_resource_download
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="documentId != null">
        document_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="documentId != null">
        #{documentId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SzzyResourceDownloadDO">
    update szzy_resource_download
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="documentId != null">
        document_id = #{documentId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SzzyResourceDownloadDO">
    update szzy_resource_download
    set company_id = #{companyId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      document_id = #{documentId,jdbcType=BIGINT},
      resource_id = #{resourceId,jdbcType=BIGINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByParams" resultType="com.das.museum.infr.dataobjectexpand.MyDownloadListDO">
    select
      A.id id,
      A.document_id documentId,
      A.resource_id resourceId,
      B.source_file_name documentName,
      C.resource_name resourceName,
      C.resource_uri_name resourceUriName,
      B.file_type fileType,
      B.file_format fileFormat,
      B.url fileUrl,
      D.three_model_url threeModelUrl,
      D.three_model_status threeModelStatus,
      D.error_info errorInfo,
      A.gmt_create gmtCreate
    from szzy_resource_download A
    left join comm_document B on A.document_id = B.id
    left join szzy_resource C on A.resource_id = C.id
    left join szzy_resource_search D on A.document_id = D.source_doc_id
    where A.company_id = #{companyId,jdbcType=BIGINT}
    and A.user_id = #{userId,jdbcType=BIGINT}
    <if test="documentName != null and documentName != '' ">
      and B.source_file_name like CONCAT('%',#{documentName,jdbcType=VARCHAR},'%')
    </if>
    <if test="resourceName != null and resourceName != '' ">
      and C.resource_name like CONCAT('%',#{resourceName,jdbcType=VARCHAR},'%')
    </if>
    <if test="resourcePoolCode != null and resourcePoolCode != '' ">
      and C.resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR}
    </if>
    order by A.${sortBy}
  </select>

</mapper>