<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionInfoMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionInfoDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="register_no" jdbcType="VARCHAR" property="registerNo"/>
        <result column="classify" jdbcType="BIGINT" property="classify"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="actual_count" jdbcType="INTEGER" property="actualCount"/>
        <result column="count_unit" jdbcType="BIGINT" property="countUnit"/>
        <result column="collection_count" jdbcType="INTEGER" property="collectionCount"/>
        <result column="quality_range" jdbcType="BIGINT" property="qualityRange"/>
        <result column="era" jdbcType="VARCHAR" property="era"/>
        <result column="age" jdbcType="BIGINT" property="age"/>
        <result column="texture_category" jdbcType="BIGINT" property="textureCategory"/>
        <result column="texture" jdbcType="VARCHAR" property="texture"/>
        <result column="category" jdbcType="BIGINT" property="category"/>
        <result column="size" jdbcType="VARCHAR" property="size"/>
        <result column="size_unit" jdbcType="BIGINT" property="sizeUnit"/>
        <result column="complete_degree" jdbcType="BIGINT" property="completeDegree"/>
        <result column="complete_info" jdbcType="VARCHAR" property="completeInfo"/>
        <result column="initial_level" jdbcType="BIGINT" property="initialLevel"/>
        <result column="amount" jdbcType="BIGINT" property="amount"/>
        <result column="collect_type" jdbcType="BIGINT" property="collectType"/>
        <result column="collect_date" jdbcType="TIMESTAMP" property="collectDate"/>
        <result column="holder" jdbcType="VARCHAR" property="holder"/>
        <result column="pay_voucher_no" jdbcType="VARCHAR" property="payVoucherNo"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="appendage_desc" jdbcType="VARCHAR" property="appendageDesc"/>
        <result column="source_info" jdbcType="VARCHAR" property="sourceInfo"/>
        <result column="introduce" jdbcType="VARCHAR" property="introduce"/>
        <result column="whereabouts" jdbcType="VARCHAR" property="whereabouts"/>
        <result column="input_mode" jdbcType="VARCHAR" property="inputMode"/>
        <result column="input_type" jdbcType="VARCHAR" property="inputType"/>
        <result column="collection_status" jdbcType="VARCHAR" property="collectionStatus"/>
        <result column="process_status" jdbcType="VARCHAR" property="processStatus"/>
        <result column="in_museum_certificate_no" jdbcType="VARCHAR" property="inMuseumCertificateNo"/>
        <result column="in_museum_date" jdbcType="TIMESTAMP" property="inMuseumDate"/>
        <result column="in_museum_status" jdbcType="VARCHAR" property="inMuseumStatus"/>
        <result column="in_tibetan_date" jdbcType="TIMESTAMP" property="inTibetanDate"/>
        <result column="appraiser_name" jdbcType="VARCHAR" property="appraiserName"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="warehouse_status" jdbcType="VARCHAR" property="warehouseStatus"/>
        <result column="in_warehouse_date" jdbcType="TIMESTAMP" property="inWarehouseDate"/>
        <result column="out_warehouse_date" jdbcType="TIMESTAMP" property="outWarehouseDate"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , company_id, register_no, classify, `name`, actual_count, count_unit, collection_count,
    quality_range, era, age, texture_category, texture, category, `size`, size_unit, 
    complete_degree, complete_info, initial_level, amount, collect_type, collect_date, 
    holder, pay_voucher_no, remark, appendage_desc, source_info, introduce, whereabouts, 
    input_mode, input_type, collection_status, process_status, in_museum_certificate_no, 
    in_museum_date, in_museum_status, in_tibetan_date, appraiser_name, warehouse_id, 
    warehouse_status, in_warehouse_date, out_warehouse_date, document_id, creator, modifier, 
    gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from scs_collection_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionInfoDO" useGeneratedKeys="true">
        insert into scs_collection_info (company_id, register_no, classify,
                                         `name`, actual_count, count_unit,
                                         collection_count, quality_range, era,
                                         age, texture_category, texture,
                                         category, `size`, size_unit,
                                         complete_degree, complete_info, initial_level,
                                         amount, collect_type, collect_date,
                                         holder, pay_voucher_no, remark,
                                         appendage_desc, source_info, introduce,
                                         whereabouts, input_mode, input_type,
                                         collection_status, process_status, in_museum_certificate_no,
                                         in_museum_date, in_museum_status, in_tibetan_date,
                                         appraiser_name, warehouse_id, warehouse_status,
                                         in_warehouse_date, out_warehouse_date,
                                         document_id, creator, modifier,
                                         gmt_create, gmt_modified)
        values (#{companyId,jdbcType=BIGINT}, #{registerNo,jdbcType=VARCHAR}, #{classify,jdbcType=BIGINT},
                #{name,jdbcType=VARCHAR}, #{actualCount,jdbcType=INTEGER}, #{countUnit,jdbcType=BIGINT},
                #{collectionCount,jdbcType=INTEGER}, #{qualityRange,jdbcType=BIGINT}, #{era,jdbcType=VARCHAR},
                #{age,jdbcType=BIGINT}, #{textureCategory,jdbcType=BIGINT}, #{texture,jdbcType=VARCHAR},
                #{category,jdbcType=BIGINT}, #{size,jdbcType=VARCHAR}, #{sizeUnit,jdbcType=BIGINT},
                #{completeDegree,jdbcType=BIGINT}, #{completeInfo,jdbcType=VARCHAR}, #{initialLevel,jdbcType=BIGINT},
                #{amount,jdbcType=BIGINT}, #{collectType,jdbcType=BIGINT}, #{collectDate,jdbcType=TIMESTAMP},
                #{holder,jdbcType=VARCHAR}, #{payVoucherNo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{appendageDesc,jdbcType=VARCHAR}, #{sourceInfo,jdbcType=VARCHAR}, #{introduce,jdbcType=VARCHAR},
                #{whereabouts,jdbcType=VARCHAR}, #{inputMode,jdbcType=VARCHAR}, #{inputType,jdbcType=VARCHAR},
                #{collectionStatus,jdbcType=VARCHAR}, #{processStatus,jdbcType=VARCHAR},
                #{inMuseumCertificateNo,jdbcType=VARCHAR},
                #{inMuseumDate,jdbcType=TIMESTAMP}, #{inMuseumStatus,jdbcType=VARCHAR},
                #{inTibetanDate,jdbcType=TIMESTAMP},
                #{appraiserName,jdbcType=VARCHAR}, #{warehouseId,jdbcType=BIGINT}, #{warehouseStatus,jdbcType=VARCHAR},
                #{inWarehouseDate,jdbcType=TIMESTAMP}, #{outWarehouseDate,jdbcType=TIMESTAMP},
                #{documentId,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT},
                #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionInfoDO" useGeneratedKeys="true">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into scs_collection_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="registerNo != null">
                register_no,
            </if>
            <if test="classify != null">
                classify,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="actualCount != null">
                actual_count,
            </if>
            <if test="countUnit != null">
                count_unit,
            </if>
            <if test="collectionCount != null">
                collection_count,
            </if>
            <if test="qualityRange != null">
                quality_range,
            </if>
            <if test="era != null">
                era,
            </if>
            <if test="age != null">
                age,
            </if>
            <if test="textureCategory != null">
                texture_category,
            </if>
            <if test="texture != null">
                texture,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="size != null">
                `size`,
            </if>
            <if test="sizeUnit != null">
                size_unit,
            </if>
            <if test="completeDegree != null">
                complete_degree,
            </if>
            <if test="completeInfo != null">
                complete_info,
            </if>
            <if test="initialLevel != null">
                initial_level,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="collectType != null">
                collect_type,
            </if>
            <if test="collectDate != null">
                collect_date,
            </if>
            <if test="holder != null">
                holder,
            </if>
            <if test="payVoucherNo != null">
                pay_voucher_no,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="appendageDesc != null">
                appendage_desc,
            </if>
            <if test="sourceInfo != null">
                source_info,
            </if>
            <if test="introduce != null">
                introduce,
            </if>
            <if test="whereabouts != null">
                whereabouts,
            </if>
            <if test="inputMode != null">
                input_mode,
            </if>
            <if test="inputType != null">
                input_type,
            </if>
            <if test="collectionStatus != null">
                collection_status,
            </if>
            <if test="processStatus != null">
                process_status,
            </if>
            <if test="inMuseumCertificateNo != null">
                in_museum_certificate_no,
            </if>
            <if test="inMuseumDate != null">
                in_museum_date,
            </if>
            <if test="inMuseumStatus != null">
                in_museum_status,
            </if>
            <if test="inTibetanDate != null">
                in_tibetan_date,
            </if>
            <if test="appraiserName != null">
                appraiser_name,
            </if>
            <if test="warehouseId != null">
                warehouse_id,
            </if>
            <if test="warehouseStatus != null">
                warehouse_status,
            </if>
            <if test="inWarehouseDate != null">
                in_warehouse_date,
            </if>
            <if test="outWarehouseDate != null">
                out_warehouse_date,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="classify != null">
                #{classify,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="actualCount != null">
                #{actualCount,jdbcType=INTEGER},
            </if>
            <if test="countUnit != null">
                #{countUnit,jdbcType=BIGINT},
            </if>
            <if test="collectionCount != null">
                #{collectionCount,jdbcType=INTEGER},
            </if>
            <if test="qualityRange != null">
                #{qualityRange,jdbcType=BIGINT},
            </if>
            <if test="era != null">
                #{era,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                #{age,jdbcType=BIGINT},
            </if>
            <if test="textureCategory != null">
                #{textureCategory,jdbcType=BIGINT},
            </if>
            <if test="texture != null">
                #{texture,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=BIGINT},
            </if>
            <if test="size != null">
                #{size,jdbcType=VARCHAR},
            </if>
            <if test="sizeUnit != null">
                #{sizeUnit,jdbcType=BIGINT},
            </if>
            <if test="completeDegree != null">
                #{completeDegree,jdbcType=BIGINT},
            </if>
            <if test="completeInfo != null">
                #{completeInfo,jdbcType=VARCHAR},
            </if>
            <if test="initialLevel != null">
                #{initialLevel,jdbcType=BIGINT},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=BIGINT},
            </if>
            <if test="collectType != null">
                #{collectType,jdbcType=BIGINT},
            </if>
            <if test="collectDate != null">
                #{collectDate,jdbcType=TIMESTAMP},
            </if>
            <if test="holder != null">
                #{holder,jdbcType=VARCHAR},
            </if>
            <if test="payVoucherNo != null">
                #{payVoucherNo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="appendageDesc != null">
                #{appendageDesc,jdbcType=VARCHAR},
            </if>
            <if test="sourceInfo != null">
                #{sourceInfo,jdbcType=VARCHAR},
            </if>
            <if test="introduce != null">
                #{introduce,jdbcType=VARCHAR},
            </if>
            <if test="whereabouts != null">
                #{whereabouts,jdbcType=VARCHAR},
            </if>
            <if test="inputMode != null">
                #{inputMode,jdbcType=VARCHAR},
            </if>
            <if test="inputType != null">
                #{inputType,jdbcType=VARCHAR},
            </if>
            <if test="collectionStatus != null">
                #{collectionStatus,jdbcType=VARCHAR},
            </if>
            <if test="processStatus != null">
                #{processStatus,jdbcType=VARCHAR},
            </if>
            <if test="inMuseumCertificateNo != null">
                #{inMuseumCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="inMuseumDate != null">
                #{inMuseumDate,jdbcType=TIMESTAMP},
            </if>
            <if test="inMuseumStatus != null">
                #{inMuseumStatus,jdbcType=VARCHAR},
            </if>
            <if test="inTibetanDate != null">
                #{inTibetanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="appraiserName != null">
                #{appraiserName,jdbcType=VARCHAR},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=BIGINT},
            </if>
            <if test="warehouseStatus != null">
                #{warehouseStatus,jdbcType=VARCHAR},
            </if>
            <if test="inWarehouseDate != null">
                #{inWarehouseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="outWarehouseDate != null">
                #{outWarehouseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsCollectionInfoDO">
        update scs_collection_info
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="registerNo != null">
                register_no = #{registerNo,jdbcType=VARCHAR},
            </if>
            <if test="classify != null">
                classify = #{classify,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="actualCount != null">
                actual_count = #{actualCount,jdbcType=INTEGER},
            </if>
            <if test="countUnit != null">
                count_unit = #{countUnit,jdbcType=BIGINT},
            </if>
            <if test="collectionCount != null">
                collection_count = #{collectionCount,jdbcType=INTEGER},
            </if>
            <if test="qualityRange != null">
                quality_range = #{qualityRange,jdbcType=BIGINT},
            </if>
            <if test="era != null">
                era = #{era,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                age = #{age,jdbcType=BIGINT},
            </if>
            <if test="textureCategory != null">
                texture_category = #{textureCategory,jdbcType=BIGINT},
            </if>
            <if test="texture != null">
                texture = #{texture,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=BIGINT},
            </if>
            <if test="size != null">
                `size` = #{size,jdbcType=VARCHAR},
            </if>
            <if test="sizeUnit != null">
                size_unit = #{sizeUnit,jdbcType=BIGINT},
            </if>
            <if test="completeDegree != null">
                complete_degree = #{completeDegree,jdbcType=BIGINT},
            </if>
            <if test="completeInfo != null">
                complete_info = #{completeInfo,jdbcType=VARCHAR},
            </if>
            <if test="initialLevel != null">
                initial_level = #{initialLevel,jdbcType=BIGINT},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=BIGINT},
            </if>
            <if test="collectType != null">
                collect_type = #{collectType,jdbcType=BIGINT},
            </if>
            <if test="collectDate != null">
                collect_date = #{collectDate,jdbcType=TIMESTAMP},
            </if>
            <if test="holder != null">
                holder = #{holder,jdbcType=VARCHAR},
            </if>
            <if test="payVoucherNo != null">
                pay_voucher_no = #{payVoucherNo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="appendageDesc != null">
                appendage_desc = #{appendageDesc,jdbcType=VARCHAR},
            </if>
            <if test="sourceInfo != null">
                source_info = #{sourceInfo,jdbcType=VARCHAR},
            </if>
            <if test="introduce != null">
                introduce = #{introduce,jdbcType=VARCHAR},
            </if>
            <if test="whereabouts != null">
                whereabouts = #{whereabouts,jdbcType=VARCHAR},
            </if>
            <if test="inputMode != null">
                input_mode = #{inputMode,jdbcType=VARCHAR},
            </if>
            <if test="inputType != null">
                input_type = #{inputType,jdbcType=VARCHAR},
            </if>
            <if test="collectionStatus != null">
                collection_status = #{collectionStatus,jdbcType=VARCHAR},
            </if>
            <if test="processStatus != null">
                process_status = #{processStatus,jdbcType=VARCHAR},
            </if>
            <if test="inMuseumCertificateNo != null">
                in_museum_certificate_no = #{inMuseumCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="inMuseumDate != null">
                in_museum_date = #{inMuseumDate,jdbcType=TIMESTAMP},
            </if>
            <if test="inMuseumStatus != null">
                in_museum_status = #{inMuseumStatus,jdbcType=VARCHAR},
            </if>
            <if test="inTibetanDate != null">
                in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="appraiserName != null">
                appraiser_name = #{appraiserName,jdbcType=VARCHAR},
            </if>
            <if test="warehouseId != null">
                warehouse_id = #{warehouseId,jdbcType=BIGINT},
            </if>
            <if test="warehouseStatus != null">
                warehouse_status = #{warehouseStatus,jdbcType=VARCHAR},
            </if>
            <if test="inWarehouseDate != null">
                in_warehouse_date = #{inWarehouseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="outWarehouseDate != null">
                out_warehouse_date = #{outWarehouseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionInfoDO">
        update scs_collection_info
        set company_id               = #{companyId,jdbcType=BIGINT},
            register_no              = #{registerNo,jdbcType=VARCHAR},
            classify                 = #{classify,jdbcType=BIGINT},
            `name`                   = #{name,jdbcType=VARCHAR},
            actual_count             = #{actualCount,jdbcType=INTEGER},
            count_unit               = #{countUnit,jdbcType=BIGINT},
            collection_count         = #{collectionCount,jdbcType=INTEGER},
            quality_range            = #{qualityRange,jdbcType=BIGINT},
            era                      = #{era,jdbcType=VARCHAR},
            age                      = #{age,jdbcType=BIGINT},
            texture_category         = #{textureCategory,jdbcType=BIGINT},
            texture                  = #{texture,jdbcType=VARCHAR},
            category                 = #{category,jdbcType=BIGINT},
            `size`                   = #{size,jdbcType=VARCHAR},
            size_unit                = #{sizeUnit,jdbcType=BIGINT},
            complete_degree          = #{completeDegree,jdbcType=BIGINT},
            complete_info            = #{completeInfo,jdbcType=VARCHAR},
            initial_level            = #{initialLevel,jdbcType=BIGINT},
            amount                   = #{amount,jdbcType=BIGINT},
            collect_type             = #{collectType,jdbcType=BIGINT},
            collect_date             = #{collectDate,jdbcType=TIMESTAMP},
            holder                   = #{holder,jdbcType=VARCHAR},
            pay_voucher_no           = #{payVoucherNo,jdbcType=VARCHAR},
            remark                   = #{remark,jdbcType=VARCHAR},
            appendage_desc           = #{appendageDesc,jdbcType=VARCHAR},
            source_info              = #{sourceInfo,jdbcType=VARCHAR},
            introduce                = #{introduce,jdbcType=VARCHAR},
            whereabouts              = #{whereabouts,jdbcType=VARCHAR},
            input_mode               = #{inputMode,jdbcType=VARCHAR},
            input_type               = #{inputType,jdbcType=VARCHAR},
            collection_status        = #{collectionStatus,jdbcType=VARCHAR},
            process_status           = #{processStatus,jdbcType=VARCHAR},
            in_museum_certificate_no = #{inMuseumCertificateNo,jdbcType=VARCHAR},
            in_museum_date           = #{inMuseumDate,jdbcType=TIMESTAMP},
            in_museum_status         = #{inMuseumStatus,jdbcType=VARCHAR},
            in_tibetan_date          = #{inTibetanDate,jdbcType=TIMESTAMP},
            appraiser_name           = #{appraiserName,jdbcType=VARCHAR},
            warehouse_id             = #{warehouseId,jdbcType=BIGINT},
            warehouse_status         = #{warehouseStatus,jdbcType=VARCHAR},
            in_warehouse_date        = #{inWarehouseDate,jdbcType=TIMESTAMP},
            out_warehouse_date       = #{outWarehouseDate,jdbcType=TIMESTAMP},
            document_id              = #{documentId,jdbcType=VARCHAR},
            creator                  = #{creator,jdbcType=BIGINT},
            modifier                 = #{modifier,jdbcType=BIGINT},
            gmt_create               = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified             = #{gmtModified,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->
    <delete id="deleteByIds">
        delete from scs_collection_info
        where company_id = #{companyId,jdbcType=BIGINT} and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteById">
        delete from scs_collection_info
        where company_id = #{companyId,jdbcType=BIGINT} and id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByIdsAndInputModeAndType">
        delete from scs_collection_info
        where company_id = #{companyId,jdbcType=BIGINT} and input_mode = #{inputMode,jdbcType=VARCHAR}
        and input_type = #{inputType,jdbcType=VARCHAR} and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </delete>

    <select id="listByBizIdAndBizType" resultMap="BaseResultMap">
        select
        t1.*
        from scs_collection_info t1
        LEFT JOIN scs_biz_collection_rel t2 ON t1.id = t2.collection_id
        WHERE t1.company_id = #{companyId,jdbcType=BIGINT} and t2.biz_id = #{bizId,jdbcType=BIGINT}
        and t2.biz_type = #{bizType,jdbcType=VARCHAR}
        <if test="era != null">
            and t1.era like concat('%', #{era,jdbcType=VARCHAR}, '%')
        </if>
        <if test="collectionName != null">
            and t1.name like concat('%', #{collectionName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="payVoucherNo != null">
            and t1.pay_voucher_no like concat('%', #{payVoucherNo,jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <update id="updateWarehouseByIds">
        update scs_collection_info
        set
        warehouse_id = #{warehouseId,jdbcType=BIGINT},
        modifier = #{modifier,jdbcType=BIGINT}
        where company_id = #{companyId,jdbcType=BIGINT} and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_info
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="listByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_info
        where company_id = #{companyId,jdbcType=BIGINT} and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <select id="selectByRegisterNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_info
        where company_id = #{companyId,jdbcType=BIGINT} and register_no = #{registerNo,jdbcType=VARCHAR}
    </select>

    <select id="queryCollectionInfoById"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_info
        where id = #{id,jdbcType=BIGINT}
        and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="queryCollectionInfoByIdentifyId"  resultType="com.das.museum.infr.dataobjectexpand.IdentifyCollectionInfoDO">
        SELECT
            A.id collectionId,
            A.name name,
            A.document_id documentId,
            A.collection_count collectionCount,
            A.count_unit countUnit,
            A.actual_count actualCount,
            A.era era,
            A.texture_category textureCategory,
            A.texture texture,
            A.initial_level initialLevel,
            A.size size,
            A.complete_degree completeDegree,
            A.complete_info completeInfo,
            A.amount amount,
            A.size_unit sizeUnit,
            A.holder holder,
            A.collect_type collectType,
            A.collect_date collectDate,
            A.pay_voucher_no payVoucherNo,
            A.introduce introduce,
            A.input_mode inputMode,
            A.input_type inputType,
            A.remark remark,
            C.appraiser_id appraiserId,
            C.appraiser_name appraiserName,
            C.identify_date identifyDate,
            C.identify_agency identifyAgency,
            C.identify_opinion identifyOpinion,
            C.identify_remark identifyRemark,
            C.document_id identifyDocumentId
            FROM
            scs_collection_info A
            LEFT JOIN scs_biz_collection_rel B ON B.collection_id = A.id
            LEFT JOIN scs_identify_info C ON B.biz_id = C.identify_certificate_id and A.id = C.collection_id
        where B.biz_type = #{bizType,jdbcType=VARCHAR}
          and B.biz_id = #{bizId,jdbcType=BIGINT}
    </select>

    <update id="updateCollectionStatus">
        update scs_collection_info
        <set>
            <if test="processStatus != null">
                process_status = #{processStatus,jdbcType=VARCHAR},
            </if>
            <if test="inMuseumDate != null">
                in_museum_date = #{inMuseumDate,jdbcType=TIMESTAMP},
            </if>
            <if test="inMuseumStatus != null">
                in_museum_status = #{inMuseumStatus,jdbcType=VARCHAR},
            </if>
            <if test="inTibetanDate != null">
                in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where company_id = #{companyId,jdbcType=BIGINT}
        and id in
        <foreach collection="collectionIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

</mapper>