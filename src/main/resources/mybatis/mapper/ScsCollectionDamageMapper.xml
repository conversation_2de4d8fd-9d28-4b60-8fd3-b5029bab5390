<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionDamageMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionDamageDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="responsible_id" jdbcType="BIGINT" property="responsibleId"/>
        <result column="responsible_name" jdbcType="VARCHAR" property="responsibleName"/>
        <result column="damage_info" jdbcType="VARCHAR" property="damageInfo"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="handle_info" jdbcType="VARCHAR" property="handleInfo"/>
        <result column="process_info" jdbcType="VARCHAR" property="processInfo"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="damage_date" jdbcType="TIMESTAMP" property="damageDate"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, collection_id, address, responsible_id, responsible_name, damage_info, 
    reason, handle_info, process_info, remark, damage_date, document_id, creator, modifier, 
    gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_damage
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_damage
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionDamageDO" useGeneratedKeys="true">
    insert into scs_collection_damage (company_id, collection_id, address, 
      responsible_id, responsible_name, damage_info, 
      reason, handle_info, process_info, 
      remark, damage_date, document_id, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{collectionId,jdbcType=BIGINT}, #{address,jdbcType=VARCHAR}, 
      #{responsibleId,jdbcType=BIGINT}, #{responsibleName,jdbcType=VARCHAR}, #{damageInfo,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{handleInfo,jdbcType=VARCHAR}, #{processInfo,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{damageDate,jdbcType=TIMESTAMP}, #{documentId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionDamageDO" useGeneratedKeys="true">
        insert into scs_collection_damage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="responsibleId != null">
                responsible_id,
            </if>
            <if test="responsibleName != null">
                responsible_name,
            </if>
            <if test="damageInfo != null">
                damage_info,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="handleInfo != null">
                handle_info,
            </if>
            <if test="processInfo != null">
                process_info,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="damageDate != null">
                damage_date,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="responsibleId != null">
                #{responsibleId,jdbcType=BIGINT},
            </if>
            <if test="responsibleName != null">
                #{responsibleName,jdbcType=VARCHAR},
            </if>
            <if test="damageInfo != null">
                #{damageInfo,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="handleInfo != null">
                #{handleInfo,jdbcType=VARCHAR},
            </if>
            <if test="processInfo != null">
                #{processInfo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="damageDate != null">
                #{damageDate,jdbcType=TIMESTAMP},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsCollectionDamageDO">
        update scs_collection_damage
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="responsibleId != null">
                responsible_id = #{responsibleId,jdbcType=BIGINT},
            </if>
            <if test="responsibleName != null">
                responsible_name = #{responsibleName,jdbcType=VARCHAR},
            </if>
            <if test="damageInfo != null">
                damage_info = #{damageInfo,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="handleInfo != null">
                handle_info = #{handleInfo,jdbcType=VARCHAR},
            </if>
            <if test="processInfo != null">
                process_info = #{processInfo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="damageDate != null">
                damage_date = #{damageDate,jdbcType=TIMESTAMP},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionDamageDO">
    update scs_collection_damage
    set company_id = #{companyId,jdbcType=BIGINT},
      collection_id = #{collectionId,jdbcType=BIGINT},
      address = #{address,jdbcType=VARCHAR},
      responsible_id = #{responsibleId,jdbcType=BIGINT},
      responsible_name = #{responsibleName,jdbcType=VARCHAR},
      damage_info = #{damageInfo,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      handle_info = #{handleInfo,jdbcType=VARCHAR},
      process_info = #{processInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      damage_date = #{damageDate,jdbcType=TIMESTAMP},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <delete id="deleteById">
    delete from scs_collection_damage
    where company_id = #{companyId,jdbcType=BIGINT} and id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_damage
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="listPageByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_damage
        where company_id = #{companyId,jdbcType=BIGINT} and collection_id = #{collectionId,jdbcType=BIGINT}
        order by ${sortBy}
    </select>
</mapper>