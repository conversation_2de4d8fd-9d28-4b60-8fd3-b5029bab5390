<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionBookMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionBookDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="author" jdbcType="VARCHAR" property="author"/>
        <result column="record_date" jdbcType="TIMESTAMP" property="recordDate"/>
        <result column="book_level" jdbcType="BIGINT" property="bookLevel"/>
        <result column="press_name" jdbcType="VARCHAR" property="pressName"/>
        <result column="article" jdbcType="VARCHAR" property="article"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, collection_id, author, record_date, book_level, press_name, article, 
    content, remark, document_id, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_book
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_book
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionBookDO" useGeneratedKeys="true">
    insert into scs_collection_book (company_id, collection_id, author, 
      record_date, book_level, press_name, 
      article, content, remark, 
      document_id, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{collectionId,jdbcType=BIGINT}, #{author,jdbcType=VARCHAR}, 
      #{recordDate,jdbcType=TIMESTAMP}, #{bookLevel,jdbcType=BIGINT}, #{pressName,jdbcType=VARCHAR}, 
      #{article,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{documentId,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionBookDO" useGeneratedKeys="true">
        insert into scs_collection_book
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="author != null">
                author,
            </if>
            <if test="recordDate != null">
                record_date,
            </if>
            <if test="bookLevel != null">
                book_level,
            </if>
            <if test="pressName != null">
                press_name,
            </if>
            <if test="article != null">
                article,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="author != null">
                #{author,jdbcType=VARCHAR},
            </if>
            <if test="recordDate != null">
                #{recordDate,jdbcType=TIMESTAMP},
            </if>
            <if test="bookLevel != null">
                #{bookLevel,jdbcType=BIGINT},
            </if>
            <if test="pressName != null">
                #{pressName,jdbcType=VARCHAR},
            </if>
            <if test="article != null">
                #{article,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsCollectionBookDO">
        update scs_collection_book
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="author != null">
                author = #{author,jdbcType=VARCHAR},
            </if>
            <if test="recordDate != null">
                record_date = #{recordDate,jdbcType=TIMESTAMP},
            </if>
            <if test="bookLevel != null">
                book_level = #{bookLevel,jdbcType=BIGINT},
            </if>
            <if test="pressName != null">
                press_name = #{pressName,jdbcType=VARCHAR},
            </if>
            <if test="article != null">
                article = #{article,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionBookDO">
    update scs_collection_book
    set company_id = #{companyId,jdbcType=BIGINT},
      collection_id = #{collectionId,jdbcType=BIGINT},
      author = #{author,jdbcType=VARCHAR},
      record_date = #{recordDate,jdbcType=TIMESTAMP},
      book_level = #{bookLevel,jdbcType=BIGINT},
      press_name = #{pressName,jdbcType=VARCHAR},
      article = #{article,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <delete id="deleteById">
    delete from scs_collection_book
    where company_id = #{companyId,jdbcType=BIGINT} and id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_book
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="listPageByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_book
        where company_id = #{companyId,jdbcType=BIGINT} and collection_id = #{collectionId,jdbcType=BIGINT}
        order by ${sortBy}
    </select>
</mapper>