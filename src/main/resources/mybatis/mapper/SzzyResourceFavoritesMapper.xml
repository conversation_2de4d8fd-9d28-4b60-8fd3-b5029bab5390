<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SzzyResourceFavoritesMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SzzyResourceFavoritesDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="resource_id" jdbcType="BIGINT" property="resourceId" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="resource_type" jdbcType="TINYINT" property="resourceType"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, user_id, resource_id, creator, modifier, gmt_create, gmt_modified, resource_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from szzy_resource_favorites
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szzy_resource_favorites
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyResourceFavoritesDO" useGeneratedKeys="true">
    insert into szzy_resource_favorites (company_id, user_id, resource_id, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{resourceId,jdbcType=BIGINT}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{resourceType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SzzyResourceFavoritesDO" useGeneratedKeys="true">
    insert into szzy_resource_favorites
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SzzyResourceFavoritesDO">
    update szzy_resource_favorites
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SzzyResourceFavoritesDO">
    update szzy_resource_favorites
    set company_id = #{companyId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      resource_id = #{resourceId,jdbcType=BIGINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      resource_type = #{resourceType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- ===========================手动方法分割线================================= -->

  <delete id="deleteByResourceId" parameterType="java.lang.Long">
    delete from szzy_resource_favorites
    where resource_id = #{resourceId,jdbcType=BIGINT}
    and user_Id = #{userId,jdbcType=BIGINT}
    and company_id = #{companyId,jdbcType=BIGINT}
  </delete>

  <select id="selectResourceIdsByUserIdAndCompanyId" resultType="com.das.museum.infr.dataobjectexpand.SzzyResourceFavoritesListDO">
    select * from
    (
      select
      srf1.resource_id resourceId,
      sr.resource_pool_code resourcePoolCode,
      sr.resource_uri_id resourceUriId,
      sr.resource_uri_name resourceUriName,
      sr.resource_code resourceCode,
      sr.resource_name resourceName,
      srf1.resource_type resourceType,
      sr.secret_level secretLevel,
      sr.document_id documentId,
      srf1.creator creator,
      srf1.gmt_create gmtCreate
      from szzy_resource_favorites srf1 inner join szzy_resource sr on srf1.resource_id = sr.id
      where srf1.user_id = #{userId,jdbcType=BIGINT}
      and srf1.company_id = #{companyId,jdbcType=BIGINT}
      and srf1.resource_type = 0
      union
      select
      srf2.resource_id resourceId,
      'CP_BODY' resourcePoolCode,
      '7,8' resourceUriId,
      '藏品,藏品本体档案' resourceUriName,
      '' resourceCode,
      scri.name resourceName,
      srf2.resource_type resourceType,
      2 secretLevel,
      '' documentId,
      srf2.creator creator,
      srf2.gmt_create gmtCreate
      from szzy_resource_favorites srf2 inner join scs_collection_register_info scri on srf2.resource_id = scri.id
      where srf2.user_id = #{userId,jdbcType=BIGINT}
      and srf2.company_id = #{companyId,jdbcType=BIGINT}
      and srf2.resource_type = 1
      and scri.register_status in ('ARTS','AIWH','AOWH','POWH','WLGF','PPOWH')
    ) A
    where 1 = 1
    <if test="timeStart != null and timeStart != ''">
        and A.gmtCreate <![CDATA[ >= ]]> #{timeStart,jdbcType=VARCHAR}
    </if>
    <if test="timeEnd != null and timeEnd != ''">
        and A.gmtCreate <![CDATA[ <= ]]> #{timeEnd,jdbcType=VARCHAR}
    </if>
    <if test="resourcePoolCode != null and resourcePoolCode != ''">
        and A.resourcePoolCode = #{resourcePoolCode,jdbcType=VARCHAR}
    </if>
    <if test="resourceName != null and resourceName != ''">
        and A.resourceName like CONCAT('%',#{resourceName,jdbcType=VARCHAR},'%')
    </if>
    order by A.gmtCreate desc
  </select>

  <select id="selectIsFavoriteCollectionByUserId" resultMap="BaseResultMap">
    select
      srf.id, srf.company_id, srf.user_id, srf.resource_id, srf.creator, srf.modifier, srf.gmt_create, srf.gmt_modified, srf.resource_type
    from szzy_resource_favorites srf
    join scs_collection_register_info scri on srf.resource_id = scri.collection_id
    where srf.user_id = #{userId,jdbcType=BIGINT}
    and srf.company_id = #{companyId,jdbcType=BIGINT}
    and srf.resource_type = #{resourceType,jdbcType=TINYINT}
    and scri.collection_id = #{resourceId,jdbcType=BIGINT}
  </select>
  <select id="selectIsFavoriteByUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from szzy_resource_favorites srf
    where user_id = #{userId,jdbcType=BIGINT}
      and company_id = #{companyId,jdbcType=BIGINT}
      and resource_type = #{resourceType,jdbcType=TINYINT}
      and resource_id = #{resourceId,jdbcType=BIGINT}
  </select>
</mapper>