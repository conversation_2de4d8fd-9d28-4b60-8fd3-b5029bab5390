<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionAppendageMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionAppendageDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="actual_count" jdbcType="INTEGER" property="actualCount"/>
        <result column="metering_unit" jdbcType="BIGINT" property="meteringUnit"/>
        <result column="metering_unit_name" jdbcType="VARCHAR" property="meteringUnitName"/>
        <result column="shape" jdbcType="VARCHAR" property="shape"/>
        <result column="complete_degree" jdbcType="BIGINT" property="completeDegree"/>
        <result column="size" jdbcType="VARCHAR" property="size"/>
        <result column="texture" jdbcType="VARCHAR" property="texture"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="current_situation" jdbcType="VARCHAR" property="currentSituation"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, collection_id, `name`, actual_count, metering_unit, metering_unit_name, 
    shape, complete_degree, `size`, texture, weight, current_situation, remark, document_id, 
    creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_appendage
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_appendage
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionAppendageDO" useGeneratedKeys="true">
    insert into scs_collection_appendage (company_id, collection_id, `name`, 
      actual_count, metering_unit, metering_unit_name, 
      shape, complete_degree, `size`, 
      texture, weight, current_situation, 
      remark, document_id, creator, 
      modifier, gmt_create, gmt_modified
      )
    values (#{companyId,jdbcType=BIGINT}, #{collectionId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{actualCount,jdbcType=INTEGER}, #{meteringUnit,jdbcType=BIGINT}, #{meteringUnitName,jdbcType=VARCHAR}, 
      #{shape,jdbcType=VARCHAR}, #{completeDegree,jdbcType=BIGINT}, #{size,jdbcType=VARCHAR}, 
      #{texture,jdbcType=VARCHAR}, #{weight,jdbcType=VARCHAR}, #{currentSituation,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{documentId,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionAppendageDO" useGeneratedKeys="true">
        insert into scs_collection_appendage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="actualCount != null">
                actual_count,
            </if>
            <if test="meteringUnit != null">
                metering_unit,
            </if>
            <if test="meteringUnitName != null">
                metering_unit_name,
            </if>
            <if test="shape != null">
                shape,
            </if>
            <if test="completeDegree != null">
                complete_degree,
            </if>
            <if test="size != null">
                `size`,
            </if>
            <if test="texture != null">
                texture,
            </if>
            <if test="weight != null">
                weight,
            </if>
            <if test="currentSituation != null">
                current_situation,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="actualCount != null">
                #{actualCount,jdbcType=INTEGER},
            </if>
            <if test="meteringUnit != null">
                #{meteringUnit,jdbcType=BIGINT},
            </if>
            <if test="meteringUnitName != null">
                #{meteringUnitName,jdbcType=VARCHAR},
            </if>
            <if test="shape != null">
                #{shape,jdbcType=VARCHAR},
            </if>
            <if test="completeDegree != null">
                #{completeDegree,jdbcType=BIGINT},
            </if>
            <if test="size != null">
                #{size,jdbcType=VARCHAR},
            </if>
            <if test="texture != null">
                #{texture,jdbcType=VARCHAR},
            </if>
            <if test="weight != null">
                #{weight,jdbcType=VARCHAR},
            </if>
            <if test="currentSituation != null">
                #{currentSituation,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsCollectionAppendageDO">
        update scs_collection_appendage
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="actualCount != null">
                actual_count = #{actualCount,jdbcType=INTEGER},
            </if>
            <if test="meteringUnit != null">
                metering_unit = #{meteringUnit,jdbcType=BIGINT},
            </if>
            <if test="meteringUnitName != null">
                metering_unit_name = #{meteringUnitName,jdbcType=VARCHAR},
            </if>
            <if test="shape != null">
                shape = #{shape,jdbcType=VARCHAR},
            </if>
            <if test="completeDegree != null">
                complete_degree = #{completeDegree,jdbcType=BIGINT},
            </if>
            <if test="size != null">
                `size` = #{size,jdbcType=VARCHAR},
            </if>
            <if test="texture != null">
                texture = #{texture,jdbcType=VARCHAR},
            </if>
            <if test="weight != null">
                weight = #{weight,jdbcType=VARCHAR},
            </if>
            <if test="currentSituation != null">
                current_situation = #{currentSituation,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionAppendageDO">
    update scs_collection_appendage
    set company_id = #{companyId,jdbcType=BIGINT},
      collection_id = #{collectionId,jdbcType=BIGINT},
      `name` = #{name,jdbcType=VARCHAR},
      actual_count = #{actualCount,jdbcType=INTEGER},
      metering_unit = #{meteringUnit,jdbcType=BIGINT},
      metering_unit_name = #{meteringUnitName,jdbcType=VARCHAR},
      shape = #{shape,jdbcType=VARCHAR},
      complete_degree = #{completeDegree,jdbcType=BIGINT},
      `size` = #{size,jdbcType=VARCHAR},
      texture = #{texture,jdbcType=VARCHAR},
      weight = #{weight,jdbcType=VARCHAR},
      current_situation = #{currentSituation,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <delete id="deleteById">
    delete from scs_collection_appendage
    where company_id = #{companyId,jdbcType=BIGINT} and id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_appendage
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="listPageByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_appendage
        where company_id = #{companyId,jdbcType=BIGINT} and collection_id = #{collectionId,jdbcType=BIGINT}
        order by ${sortBy}
    </select>
</mapper>