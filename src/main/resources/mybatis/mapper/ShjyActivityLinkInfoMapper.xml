<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ShjyActivityLinkInfoMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ShjyActivityLinkInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_unique_code" jdbcType="VARCHAR" property="companyUniqueCode" />
    <result column="activity_unique_code" jdbcType="VARCHAR" property="activityUniqueCode" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="link_title" jdbcType="VARCHAR" property="linkTitle" />
    <result column="link_address" jdbcType="VARCHAR" property="linkAddress" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, company_unique_code, activity_unique_code, activity_id, link_title, 
    link_address, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from shjy_activity_link_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from shjy_activity_link_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ShjyActivityLinkInfoDO" useGeneratedKeys="true">
    insert into shjy_activity_link_info (company_id, company_unique_code, activity_unique_code, 
      activity_id, link_title, link_address, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{companyUniqueCode,jdbcType=VARCHAR}, #{activityUniqueCode,jdbcType=VARCHAR}, 
      #{activityId,jdbcType=BIGINT}, #{linkTitle,jdbcType=VARCHAR}, #{linkAddress,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.ShjyActivityLinkInfoDO" useGeneratedKeys="true">
    insert into shjy_activity_link_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyUniqueCode != null">
        company_unique_code,
      </if>
      <if test="activityUniqueCode != null">
        activity_unique_code,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="linkTitle != null">
        link_title,
      </if>
      <if test="linkAddress != null">
        link_address,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyUniqueCode != null">
        #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityUniqueCode != null">
        #{activityUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="linkTitle != null">
        #{linkTitle,jdbcType=VARCHAR},
      </if>
      <if test="linkAddress != null">
        #{linkAddress,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ShjyActivityLinkInfoDO">
    update shjy_activity_link_info
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyUniqueCode != null">
        company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityUniqueCode != null">
        activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="linkTitle != null">
        link_title = #{linkTitle,jdbcType=VARCHAR},
      </if>
      <if test="linkAddress != null">
        link_address = #{linkAddress,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ShjyActivityLinkInfoDO">
    update shjy_activity_link_info
    set company_id = #{companyId,jdbcType=BIGINT},
      company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR},
      activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR},
      activity_id = #{activityId,jdbcType=BIGINT},
      link_title = #{linkTitle,jdbcType=VARCHAR},
      link_address = #{linkAddress,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectListByParams" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from shjy_activity_link_info
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    and activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR}
  </select>

  <select id="selectLinkById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from shjy_activity_link_info
    where company_unique_code = #{companyUniqueCode,jdbcType=VARCHAR}
    and activity_unique_code = #{activityUniqueCode,jdbcType=VARCHAR}
    and id = #{id,jdbcType=BIGINT}
  </select>

</mapper>