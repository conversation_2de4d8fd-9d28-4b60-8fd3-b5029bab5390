<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.SafeTemperatureHumidityRecordMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.SafeTemperatureHumidityRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="current_temperature" jdbcType="VARCHAR" property="currentTemperature" />
    <result column="current_humidity" jdbcType="VARCHAR" property="currentHumidity" />
    <result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
    <result column="is_sync" jdbcType="TINYINT" property="isSync" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, device_code, current_temperature, current_humidity, report_time, 
    is_sync
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from safe_temperature_humidity_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from safe_temperature_humidity_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SafeTemperatureHumidityRecordDO" useGeneratedKeys="true">
    insert into safe_temperature_humidity_record (company_id, device_code, current_temperature, 
      current_humidity, report_time, is_sync
      )
    values (#{companyId,jdbcType=BIGINT}, #{deviceCode,jdbcType=VARCHAR}, #{currentTemperature,jdbcType=VARCHAR}, 
      #{currentHumidity,jdbcType=VARCHAR}, #{reportTime,jdbcType=TIMESTAMP}, #{isSync,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.SafeTemperatureHumidityRecordDO" useGeneratedKeys="true">
    insert into safe_temperature_humidity_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="deviceCode != null">
        device_code,
      </if>
      <if test="currentTemperature != null">
        current_temperature,
      </if>
      <if test="currentHumidity != null">
        current_humidity,
      </if>
      <if test="reportTime != null">
        report_time,
      </if>
      <if test="isSync != null">
        is_sync,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="deviceCode != null">
        #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="currentTemperature != null">
        #{currentTemperature,jdbcType=VARCHAR},
      </if>
      <if test="currentHumidity != null">
        #{currentHumidity,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isSync != null">
        #{isSync,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.SafeTemperatureHumidityRecordDO">
    update safe_temperature_humidity_record
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="deviceCode != null">
        device_code = #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="currentTemperature != null">
        current_temperature = #{currentTemperature,jdbcType=VARCHAR},
      </if>
      <if test="currentHumidity != null">
        current_humidity = #{currentHumidity,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        report_time = #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isSync != null">
        is_sync = #{isSync,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.SafeTemperatureHumidityRecordDO">
    update safe_temperature_humidity_record
    set company_id = #{companyId,jdbcType=BIGINT},
      device_code = #{deviceCode,jdbcType=VARCHAR},
      current_temperature = #{currentTemperature,jdbcType=VARCHAR},
      current_humidity = #{currentHumidity,jdbcType=VARCHAR},
      report_time = #{reportTime,jdbcType=TIMESTAMP},
      is_sync = #{isSync,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listPageByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from safe_temperature_humidity_record
    where device_code = #{deviceCode,jdbcType=VARCHAR}
    and company_id = #{companyId,jdbcType=BIGINT}
    <if test="startTime != null ">
      and report_time >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null ">
      and report_time &lt;= #{endTime,jdbcType=TIMESTAMP}
    </if>
    <if test="onlyError == 1 ">
      and (
        (
            CONVERT(current_temperature,decimal(3,1)) &lt; CONVERT(#{minTemperature,jdbcType=VARCHAR},decimal(3,1)) or CONVERT(current_temperature,decimal(3,1))  >
            CONVERT(#{maxTemperature,jdbcType=VARCHAR},decimal(3,1))
        )
        or
        (
            CONVERT(current_humidity,decimal(3,1)) &lt; CONVERT(#{minHumidity,jdbcType=VARCHAR},decimal(3,1)) or CONVERT(current_humidity,decimal(3,1))  >
            CONVERT(#{maxHumidity,jdbcType=VARCHAR},decimal(3,1))
        )
      )
    </if>
    order by report_time desc
  </select>

  <select id="listSyncData" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from safe_temperature_humidity_record
    where company_id = #{companyId,jdbcType=BIGINT} and is_sync = #{isSync,jdbcType=TINYINT} order by report_time desc
  </select>

  <select id="selectSyncDataOne" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from safe_temperature_humidity_record
    where device_code = #{deviceCode,jdbcType=VARCHAR} order by report_time desc limit 1
  </select>

  <update id="updateByIds">
    update safe_temperature_humidity_record set is_sync = #{isSync,jdbcType=TINYINT}
    where company_id = #{companyId,jdbcType=BIGINT} and id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
    #{id}
  </foreach>
  </update>
</mapper>