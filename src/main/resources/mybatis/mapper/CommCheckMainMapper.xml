<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.CommCheckMainMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.CommCheckMainDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="belong_model" jdbcType="VARCHAR" property="belongModel"/>
        <result column="model_id" jdbcType="BIGINT" property="modelId"/>
        <result column="check_status" jdbcType="VARCHAR" property="checkStatus"/>
        <result column="start_man_id" jdbcType="BIGINT" property="startManId"/>
        <result column="start_man_name" jdbcType="VARCHAR" property="startManName"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, belong_model, model_id, check_status, start_man_id, start_man_name, start_time,
    creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_main
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_check_main
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.CommCheckMainDO"
            useGeneratedKeys="true">
    insert into comm_check_main (company_id, belong_model, model_id, check_status,
      start_man_id, start_man_name, start_time, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{belongModel,jdbcType=VARCHAR}, #{modelId,jdbcType=BIGINT}, #{checkStatus,jdbcType=VARCHAR},
      #{startManId,jdbcType=BIGINT}, #{startManName,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.CommCheckMainDO" useGeneratedKeys="true">
        insert into comm_check_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="belongModel != null">
                belong_model,
            </if>
            <if test="modelId != null">
                model_id,
            </if>
            <if test="checkStatus != null">
                check_status,
            </if>
            <if test="startManId != null">
                start_man_id,
            </if>
            <if test="startManName != null">
                start_man_name,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="belongModel != null">
                #{belongModel,jdbcType=VARCHAR},
            </if>
            <if test="modelId != null">
                #{modelId,jdbcType=BIGINT},
            </if>
            <if test="checkStatus != null">
                #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="startManId != null">
                #{startManId,jdbcType=BIGINT},
            </if>
            <if test="startManName != null">
                #{startManName,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.CommCheckMainDO">
        update comm_check_main
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="belongModel != null">
                belong_model = #{belongModel,jdbcType=VARCHAR},
            </if>
            <if test="modelId != null">
                model_id = #{modelId,jdbcType=BIGINT},
            </if>
            <if test="checkStatus != null">
                check_status = #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="startManId != null">
                start_man_id = #{startManId,jdbcType=BIGINT},
            </if>
            <if test="startManName != null">
                start_man_name = #{startManName,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.CommCheckMainDO">
    update comm_check_main
    set company_id = #{companyId,jdbcType=BIGINT},
      belong_model = #{belongModel,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=BIGINT},
      check_status = #{checkStatus,jdbcType=VARCHAR},
      start_man_id = #{startManId,jdbcType=BIGINT},
      start_man_name = #{startManName,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->
    <select id="selectByModelAndId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_main
        where company_id = #{companyId,jdbcType=BIGINT}
        and belong_model = #{belongModel,jdbcType=VARCHAR}
        and model_id = #{modelId,jdbcType=BIGINT}
    </select>

    <select id="selectByModelAndIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_main
        where company_id = #{companyId,jdbcType=BIGINT}
        and belong_model = #{belongModel,jdbcType=VARCHAR}
        <if test="modelIds != null and modelIds.size() > 0">
            and model_id IN
            <foreach collection="modelIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="listByStartManId" resultType="com.das.museum.infr.dataobjectexpand.MyListApplyDO">
        select
            A.model_id applyId,
            A.id mainId,
            B.source_id sourceId,
            B.source_name sourceName,
            B.source_path sourcePath,
            B.security_level securityLevel,
            B.apply_use applyUse,
            B.use_time_limit useTimeLimit,
            B.use_reason useReason,
            A.check_status checkStatus,
            A.start_man_name startManName,
            A.start_time startTime,
            A.gmt_modified modeifiedTime,
            B.pass_time passTime
        from comm_check_main A
        left join szzy_source_apply_info B on A.model_id = B.id
        left join szzy_resource C on B.source_id = C.id
        where A.company_id = #{companyId,jdbcType=BIGINT}
        and A.start_man_id = #{startManId,jdbcType=BIGINT}
        and A.belong_model = #{belongModel,jdbcType=VARCHAR}
        <if test="checkStatus != null and checkStatus != '' ">
            and A.check_status = #{checkStatus,jdbcType=VARCHAR}
        </if>
        <if test="resourcePoolCode != null and resourcePoolCode != '' ">
            and C.resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR}
        </if>
        <if test="sourceName != null and sourceName != '' ">
            and B.source_name like CONCAT('%', #{sourceName,jdbcType=VARCHAR},'%')
        </if>
        <if test="startTime != null and startTime != '' ">
            and A.gmt_create >= #{startTime,jdbcType=VARCHAR}
        </if>
        <if test="endTime != null and endTime != '' ">
            and A.gmt_create <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
        </if>
        order by A.${sortBy}
    </select>


    <select id="listByCheckManId" resultType="com.das.museum.infr.dataobjectexpand.MyCheckApplyDO">
        select
            B.model_id applyId,
            A.main_id mainId,
            C.source_id sourceId,
            C.source_name sourceName,
            C.source_path sourcePath,
            C.security_level securityLevel,
            C.apply_use applyUse,
            C.use_time_limit useTimeLimit,
            C.use_reason useReason,
            A.check_status checkStatus,
            B.start_man_name startManName,
            B.start_time startTime,
            A.check_time modeifiedTime,
            C.pass_time passTime
        from comm_check_child A
        left join comm_check_main B on A.main_id = B.id
        left join szzy_source_apply_info C on B.model_id = C.id
        left join szzy_resource D on C.source_id = D.id
        where B.company_id = #{companyId,jdbcType=BIGINT}
          and CONCAT (',' ,A.need_man_id , ',') REGEXP concat(',(',#{userId,jdbcType=VARCHAR},'),')
          and B.belong_model = #{belongModel,jdbcType=VARCHAR}
        <if test="dealStatus == 0 ">
            and A.check_status = 'ATD'
        </if>
        <if test="dealStatus == 1 ">
            and A.check_status in ('ATP','NATP')
        </if>
        <if test="resourcePoolCode != null and resourcePoolCode != '' ">
            and D.resource_pool_code = #{resourcePoolCode,jdbcType=VARCHAR}
        </if>
        <if test="sourceName != null and sourceName != '' ">
            and C.source_name like CONCAT('%', #{sourceName,jdbcType=VARCHAR},'%')
        </if>
        <if test="startTime != null and startTime != '' ">
            and A.gmt_create >= #{startTime,jdbcType=VARCHAR}
        </if>
        <if test="endTime != null and endTime != '' ">
            and A.gmt_create <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
        </if>
        order by A.check_status,A.gmt_create
    </select>

    <select id="queryMyStartList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_main
        where company_id = #{companyId,jdbcType=BIGINT}
        and start_man_id = #{userId,jdbcType=BIGINT}
        <if test="checkStatus != null and checkStatus != '' ">
            and check_status =  #{checkStatus,jdbcType=VARCHAR}
        </if>
        <if test="belongModels != null and belongModels.size() > 0">
            and belong_model in
            <foreach collection="belongModels" open="(" close=")" separator="," item="belongModel" index="index">
                #{belongModel}
            </foreach>
        </if>
        <if test="orderBy == 0 ">
            order by start_time
        </if>
        <if test="orderBy == 1 ">
            order by start_time desc
        </if>
        <if test="orderBy == null">
            order by start_time desc
        </if>
    </select>

    <select id="queryMyStartStatisticsList" resultType="com.das.museum.infr.dataobjectexpand.MyCheckStatisticsDO">
        select
            belong_model belongModel,
            count(belong_model) num
        from comm_check_main
        where company_id = #{companyId,jdbcType=BIGINT}
        and start_man_id = #{userId,jdbcType=BIGINT}
        group by belong_model
    </select>

    <select id="listByBelongModel" resultType="java.lang.String">
        SELECT t2.collection_id FROM comm_check_main t1 LEFT JOIN comm_check_child t2 ON t1.id = t2.main_id
        WHERE t1.company_id = #{companyId,jdbcType=BIGINT} AND t1.belong_model = #{belongModel,jdbcType=VARCHAR}
          AND t1.check_status ='ATP' AND t2.end_node = 1;
    </select>

    <delete id="deleteByModelId">
    delete from comm_check_main
    where model_id = #{modelId,jdbcType=BIGINT} and belong_model = #{belongModel,jdbcType=VARCHAR}
    </delete>

    <select id="selectByCompanyIdAndId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from comm_check_main
        where company_id = #{companyId,jdbcType=BIGINT}
        and id = #{id,jdbcType=BIGINT}
    </select>
</mapper>