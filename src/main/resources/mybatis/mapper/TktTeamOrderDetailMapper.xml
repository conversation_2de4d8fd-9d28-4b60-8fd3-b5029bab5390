<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.TktTeamOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.TktTeamOrderDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="team_order_no" jdbcType="VARCHAR" property="teamOrderNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="ticket_name" jdbcType="VARCHAR" property="ticketName" />
    <result column="ticket_code" jdbcType="VARCHAR" property="ticketCode" />
    <result column="ticket_price" jdbcType="BIGINT" property="ticketPrice" />
    <result column="team_name" jdbcType="VARCHAR" property="teamName" />
    <result column="leader_name" jdbcType="VARCHAR" property="leaderName" />
    <result column="leader_phone" jdbcType="VARCHAR" property="leaderPhone" />
    <result column="leader_certificate_type" jdbcType="TINYINT" property="leaderCertificateType" />
    <result column="leader_certificate_no" jdbcType="VARCHAR" property="leaderCertificateNo" />
    <result column="visit_purpose" jdbcType="VARCHAR" property="visitPurpose" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="qr_code_url" jdbcType="VARCHAR" property="qrCodeUrl" />
    <result column="reserve_date" jdbcType="TIMESTAMP" property="reserveDate" />
    <result column="time_period_id" jdbcType="BIGINT" property="timePeriodId" />
    <result column="time_period_start" jdbcType="TIME" property="timePeriodStart" />
    <result column="time_period_end" jdbcType="TIME" property="timePeriodEnd" />
    <result column="write_off_date" jdbcType="TIMESTAMP" property="writeOffDate" />
    <result column="write_off_count" jdbcType="TINYINT" property="writeOffCount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="writeoff_user_name" jdbcType="VARCHAR" property="writeOffUserName"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, department_id, order_id, team_order_no, order_no, ticket_name, ticket_code, 
    ticket_price, team_name, leader_name, leader_phone, leader_certificate_type, leader_certificate_no, 
    visit_purpose, `status`, qr_code_url, reserve_date, time_period_id, time_period_start, 
    time_period_end, write_off_date, write_off_count, remark, creator, modifier, gmt_create, 
    gmt_modified, writeoff_user_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tkt_team_order_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tkt_team_order_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTeamOrderDetailDO" useGeneratedKeys="true">
    insert into tkt_team_order_detail (company_id, department_id, order_id, 
      team_order_no, order_no, ticket_name, 
      ticket_code, ticket_price, team_name, 
      leader_name, leader_phone, leader_certificate_type, 
      leader_certificate_no, visit_purpose, `status`, 
      qr_code_url, reserve_date, time_period_id, 
      time_period_start, time_period_end, write_off_date, 
      write_off_count, remark, creator, 
      modifier, gmt_create, gmt_modified, writeoff_user_name
      )
    values (#{companyId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, 
      #{teamOrderNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{ticketName,jdbcType=VARCHAR}, 
      #{ticketCode,jdbcType=VARCHAR}, #{ticketPrice,jdbcType=BIGINT}, #{teamName,jdbcType=VARCHAR}, 
      #{leaderName,jdbcType=VARCHAR}, #{leaderPhone,jdbcType=VARCHAR}, #{leaderCertificateType,jdbcType=TINYINT}, 
      #{leaderCertificateNo,jdbcType=VARCHAR}, #{visitPurpose,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{qrCodeUrl,jdbcType=VARCHAR}, #{reserveDate,jdbcType=TIMESTAMP}, #{timePeriodId,jdbcType=BIGINT}, 
      #{timePeriodStart,jdbcType=TIME}, #{timePeriodEnd,jdbcType=TIME}, #{writeOffDate,jdbcType=TIMESTAMP}, 
      #{writeOffCount,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.museum.infr.dataobject.TktTeamOrderDetailDO" useGeneratedKeys="true">
    insert into tkt_team_order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="teamOrderNo != null">
        team_order_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="ticketName != null">
        ticket_name,
      </if>
      <if test="ticketCode != null">
        ticket_code,
      </if>
      <if test="ticketPrice != null">
        ticket_price,
      </if>
      <if test="teamName != null">
        team_name,
      </if>
      <if test="leaderName != null">
        leader_name,
      </if>
      <if test="leaderPhone != null">
        leader_phone,
      </if>
      <if test="leaderCertificateType != null">
        leader_certificate_type,
      </if>
      <if test="leaderCertificateNo != null">
        leader_certificate_no,
      </if>
      <if test="visitPurpose != null">
        visit_purpose,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="qrCodeUrl != null">
        qr_code_url,
      </if>
      <if test="reserveDate != null">
        reserve_date,
      </if>
      <if test="timePeriodId != null">
        time_period_id,
      </if>
      <if test="timePeriodStart != null">
        time_period_start,
      </if>
      <if test="timePeriodEnd != null">
        time_period_end,
      </if>
      <if test="writeOffDate != null">
        write_off_date,
      </if>
      <if test="writeOffCount != null">
        write_off_count,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="writeOffUserName != null">
        writeoff_user_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="teamOrderNo != null">
        #{teamOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="ticketName != null">
        #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketPrice != null">
        #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="teamName != null">
        #{teamName,jdbcType=VARCHAR},
      </if>
      <if test="leaderName != null">
        #{leaderName,jdbcType=VARCHAR},
      </if>
      <if test="leaderPhone != null">
        #{leaderPhone,jdbcType=VARCHAR},
      </if>
      <if test="leaderCertificateType != null">
        #{leaderCertificateType,jdbcType=TINYINT},
      </if>
      <if test="leaderCertificateNo != null">
        #{leaderCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="visitPurpose != null">
        #{visitPurpose,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="qrCodeUrl != null">
        #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="reserveDate != null">
        #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodId != null">
        #{timePeriodId,jdbcType=BIGINT},
      </if>
      <if test="timePeriodStart != null">
        #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="writeOffDate != null">
        #{writeOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffCount != null">
        #{writeOffCount,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffUserName != null">
        #{writeOffUserName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.TktTeamOrderDetailDO">
    update tkt_team_order_detail
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="teamOrderNo != null">
        team_order_no = #{teamOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="ticketName != null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        ticket_code = #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketPrice != null">
        ticket_price = #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="teamName != null">
        team_name = #{teamName,jdbcType=VARCHAR},
      </if>
      <if test="leaderName != null">
        leader_name = #{leaderName,jdbcType=VARCHAR},
      </if>
      <if test="leaderPhone != null">
        leader_phone = #{leaderPhone,jdbcType=VARCHAR},
      </if>
      <if test="leaderCertificateType != null">
        leader_certificate_type = #{leaderCertificateType,jdbcType=TINYINT},
      </if>
      <if test="leaderCertificateNo != null">
        leader_certificate_no = #{leaderCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="visitPurpose != null">
        visit_purpose = #{visitPurpose,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="qrCodeUrl != null">
        qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="reserveDate != null">
        reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodId != null">
        time_period_id = #{timePeriodId,jdbcType=BIGINT},
      </if>
      <if test="timePeriodStart != null">
        time_period_start = #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        time_period_end = #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="writeOffDate != null">
        write_off_date = #{writeOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffCount != null">
        write_off_count = #{writeOffCount,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffUserName != null">
        writeoff_user_name = #{writeOffUserName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.TktTeamOrderDetailDO">
    update tkt_team_order_detail
    set company_id = #{companyId,jdbcType=BIGINT},
      department_id = #{departmentId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      team_order_no = #{teamOrderNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      ticket_name = #{ticketName,jdbcType=VARCHAR},
      ticket_code = #{ticketCode,jdbcType=VARCHAR},
      ticket_price = #{ticketPrice,jdbcType=BIGINT},
      team_name = #{teamName,jdbcType=VARCHAR},
      leader_name = #{leaderName,jdbcType=VARCHAR},
      leader_phone = #{leaderPhone,jdbcType=VARCHAR},
      leader_certificate_type = #{leaderCertificateType,jdbcType=TINYINT},
      leader_certificate_no = #{leaderCertificateNo,jdbcType=VARCHAR},
      visit_purpose = #{visitPurpose,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      time_period_id = #{timePeriodId,jdbcType=BIGINT},
      time_period_start = #{timePeriodStart,jdbcType=TIME},
      time_period_end = #{timePeriodEnd,jdbcType=TIME},
      write_off_date = #{writeOffDate,jdbcType=TIMESTAMP},
      write_off_count = #{writeOffCount,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      writeoff_user_name = #{writeOffUserName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectListByOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_team_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    and order_id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="selectByIdAndCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_team_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    and order_id = #{orderId,jdbcType=BIGINT}
  </select>

  <select id="selectByTeamOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_team_order_detail
    where team_order_no = #{teamOrderNo,jdbcType=VARCHAR}
  </select>

  <update id="updateByTeamOrderNo" parameterType="com.das.museum.infr.dataobject.TktTeamOrderDetailDO">
    update tkt_team_order_detail
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="ticketName != null">
        ticket_name = #{ticketName,jdbcType=VARCHAR},
      </if>
      <if test="ticketCode != null">
        ticket_code = #{ticketCode,jdbcType=VARCHAR},
      </if>
      <if test="ticketPrice != null">
        ticket_price = #{ticketPrice,jdbcType=BIGINT},
      </if>
      <if test="teamName != null">
        team_name = #{teamName,jdbcType=VARCHAR},
      </if>
      <if test="leaderName != null">
        leader_name = #{leaderName,jdbcType=VARCHAR},
      </if>
      <if test="leaderPhone != null">
        leader_phone = #{leaderPhone,jdbcType=VARCHAR},
      </if>
      <if test="leaderCertificateType != null">
        leader_certificate_type = #{leaderCertificateType,jdbcType=TINYINT},
      </if>
      <if test="leaderCertificateNo != null">
        leader_certificate_no = #{leaderCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="visitPurpose != null">
        visit_purpose = #{visitPurpose,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="qrCodeUrl != null">
        qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="reserveDate != null">
        reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodId != null">
        time_period_id = #{timePeriodId,jdbcType=BIGINT},
      </if>
      <if test="timePeriodStart != null">
        time_period_start = #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        time_period_end = #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="writeOffDate != null">
        write_off_date = #{writeOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffCount != null">
        write_off_count = #{writeOffCount,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffUserName != null">
        writeoff_user_name = #{writeOffUserName,jdbcType=VARCHAR},
      </if>
    </set>
    where team_order_no = #{teamOrderNo,jdbcType=VARCHAR}
  </update>

  <select id="listTeamByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_team_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="keyword != null and keyword != ''">
      and (team_name like CONCAT('%', #{keyword,jdbcType=VARCHAR},'%') or
           leader_name like CONCAT('%', #{keyword,jdbcType=VARCHAR},'%') or
           leader_certificate_no like CONCAT('%', #{keyword,jdbcType=VARCHAR},'%') or ticket_name like CONCAT('%', #{keyword,jdbcType=VARCHAR},'%'))
    </if>
    <if test="status != null">
      and `status` = #{status,jdbcType=TINYINT}
    </if>
    <if test="createTimeStart != null and createTimeEnd != null">
      and gmt_create &gt;= #{createTimeStart,jdbcType=TIMESTAMP} and gmt_create &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
    </if>
    order by ${sortBy}
  </select>

  <select id="listTeamByTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_team_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="startTime != null">
      and reserve_date &gt;= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and reserve_date &lt;= #{endTime,jdbcType=TIMESTAMP}
    </if>
    <if test="status != null">
      and `status` = #{status,jdbcType=TINYINT}
    </if>
 </select>

  <select id="selectHasWriteOffList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_team_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    and `status` = 2
    <if test="startTime != null">
      and write_off_date &gt;= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and write_off_date &lt;= #{endTime,jdbcType=TIMESTAMP}
    </if>
  </select>

  <update id="updateByTeamOrderNos">
    update tkt_personal_order_detail set `status` = #{status,jdbcType=TINYINT}
    where team_order_no in
    <foreach collection="teamOrderNos" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </update>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tkt_team_order_detail
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="start != null ">
      and write_off_date &gt;= #{start,jdbcType=TIMESTAMP}
    </if>
    <if test="end != null ">
      and write_off_date &lt;= #{end,jdbcType=TIMESTAMP}
    </if>
  </select>
</mapper>