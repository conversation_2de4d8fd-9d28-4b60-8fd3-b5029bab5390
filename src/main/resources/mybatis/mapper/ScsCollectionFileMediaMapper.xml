<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.museum.infr.mapper.ScsCollectionFileMediaMapper">
    <resultMap id="BaseResultMap" type="com.das.museum.infr.dataobject.ScsCollectionFileMediaDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="media_type" jdbcType="VARCHAR" property="mediaType"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_no" jdbcType="VARCHAR" property="fileNo"/>
        <result column="producer_name" jdbcType="VARCHAR" property="producerName"/>
        <result column="proportion" jdbcType="VARCHAR" property="proportion"/>
        <result column="shot_date" jdbcType="TIMESTAMP" property="shotDate"/>
        <result column="production_date" jdbcType="TIMESTAMP" property="productionDate"/>
        <result column="clarity" jdbcType="TINYINT" property="clarity"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, company_id, collection_id, media_type, file_name, file_no, producer_name, proportion, 
    shot_date, production_date, clarity, remark, document_id, creator, modifier, gmt_create, 
    gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_file_media
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scs_collection_file_media
    where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionFileMediaDO" useGeneratedKeys="true">
    insert into scs_collection_file_media (company_id, collection_id, media_type, 
      file_name, file_no, producer_name, 
      proportion, shot_date, production_date, 
      clarity, remark, document_id, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{companyId,jdbcType=BIGINT}, #{collectionId,jdbcType=BIGINT}, #{mediaType,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{fileNo,jdbcType=VARCHAR}, #{producerName,jdbcType=VARCHAR}, 
      #{proportion,jdbcType=VARCHAR}, #{shotDate,jdbcType=TIMESTAMP}, #{productionDate,jdbcType=TIMESTAMP}, 
      #{clarity,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{documentId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.museum.infr.dataobject.ScsCollectionFileMediaDO" useGeneratedKeys="true">
        insert into scs_collection_file_media
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="mediaType != null">
                media_type,
            </if>
            <if test="fileName != null">
                file_name,
            </if>
            <if test="fileNo != null">
                file_no,
            </if>
            <if test="producerName != null">
                producer_name,
            </if>
            <if test="proportion != null">
                proportion,
            </if>
            <if test="shotDate != null">
                shot_date,
            </if>
            <if test="productionDate != null">
                production_date,
            </if>
            <if test="clarity != null">
                clarity,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="mediaType != null">
                #{mediaType,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileNo != null">
                #{fileNo,jdbcType=VARCHAR},
            </if>
            <if test="producerName != null">
                #{producerName,jdbcType=VARCHAR},
            </if>
            <if test="proportion != null">
                #{proportion,jdbcType=VARCHAR},
            </if>
            <if test="shotDate != null">
                #{shotDate,jdbcType=TIMESTAMP},
            </if>
            <if test="productionDate != null">
                #{productionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="clarity != null">
                #{clarity,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.museum.infr.dataobject.ScsCollectionFileMediaDO">
        update scs_collection_file_media
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="mediaType != null">
                media_type = #{mediaType,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileNo != null">
                file_no = #{fileNo,jdbcType=VARCHAR},
            </if>
            <if test="producerName != null">
                producer_name = #{producerName,jdbcType=VARCHAR},
            </if>
            <if test="proportion != null">
                proportion = #{proportion,jdbcType=VARCHAR},
            </if>
            <if test="shotDate != null">
                shot_date = #{shotDate,jdbcType=TIMESTAMP},
            </if>
            <if test="shotDate == null">
                shot_date = NULL,
            </if>
            <if test="productionDate != null">
                production_date = #{productionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="productionDate == null">
                production_date = NULL,
            </if>
            <if test="clarity != null">
                clarity = #{clarity,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.museum.infr.dataobject.ScsCollectionFileMediaDO">
    update scs_collection_file_media
    set company_id = #{companyId,jdbcType=BIGINT},
      collection_id = #{collectionId,jdbcType=BIGINT},
      media_type = #{mediaType,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_no = #{fileNo,jdbcType=VARCHAR},
      producer_name = #{producerName,jdbcType=VARCHAR},
      proportion = #{proportion,jdbcType=VARCHAR},
      shot_date = #{shotDate,jdbcType=TIMESTAMP},
      production_date = #{productionDate,jdbcType=TIMESTAMP},
      clarity = #{clarity,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      document_id = #{documentId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <delete id="deleteById">
    delete from scs_collection_file_media
    where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_file_media
        where id = #{id,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
    </select>

    <select id="listPageByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scs_collection_file_media
        where company_id = #{companyId,jdbcType=BIGINT} and collection_id = #{collectionId,jdbcType=BIGINT} and
        media_type = #{mediaType,jdbcType=VARCHAR}
        order by ${sortBy}
    </select>
</mapper>