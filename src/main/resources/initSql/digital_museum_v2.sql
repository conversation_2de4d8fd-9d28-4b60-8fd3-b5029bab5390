-- 2023-07-12

DROP TABLE IF EXISTS `acc_company`;
CREATE TABLE `acc_company`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
  `company_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司名称',
  `contact_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人名称',
  `contact_phone` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人电话',
  `company_level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '公司级别',
  `try_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型: 0:试用, 1:正式',
  `try_deadline_date` datetime(0) NOT NULL COMMENT '试用截止日期',
  `address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '公司信息表' ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `acc_permission`;
CREATE TABLE `acc_permission`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `permission_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限名称',
  `permission_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限编码',
  `permission_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限类型: MENU:菜单权限, FCN:功能权限, DATA:数据权限',
  `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '父分类id',
  `level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '分类层级',
  `sort_index` tinyint(4) NOT NULL DEFAULT 1 COMMENT '排序',
  `permission` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1,1,1' COMMENT '分类权限',
  `permission_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '启用状态: 0:不启用, 1:启用',
  `function_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '功能权限启用状态: 0:不启用, 1:启用',
  `is_delete` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除: 1:是, 0:否',
  `resource_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资源url',
  `resource_icon` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资源icon',
  `remark` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '权限信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `acc_permission_item_rel`;
CREATE TABLE `acc_permission_item_rel`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `ancestor_id` bigint(20) NOT NULL COMMENT '祖先：上级节点的id',
  `descendant_id` bigint(20) NOT NULL COMMENT '子代：下级节点的id',
  `distance` int(11) NOT NULL COMMENT '距离：子代到祖先中间隔了几级',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '权限项关联关系表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `acc_position`;
CREATE TABLE `acc_position`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `position_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位名称',
  `department_id` bigint(20) NOT NULL COMMENT '部门id',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_delete` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除: 1:是, 0:否',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '职位信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `acc_role`;
CREATE TABLE `acc_role`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `role_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态: 1:启用, 0:禁用',
  `is_delete` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除: 1:是, 0:否',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `acc_role_permission_rel`;
CREATE TABLE `acc_role_permission_rel`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `permission_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限编码',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色-权限关联信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `acc_user`;
CREATE TABLE `acc_user`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
  `real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户真实名字',
  `login_account` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户登录账号',
  `password` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户密码',
  `source_password` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '源用户密码',
  `avatar` bigint(20) NULL DEFAULT NULL COMMENT '用户头像: document_id',
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户手机号',
  `email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户邮箱',
  `department_id` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
  `position_id` bigint(20) NULL DEFAULT NULL COMMENT '职位id',
  `post_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '岗位编码:',
  `user_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户类型:saaspc',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `leader_id` bigint(20) NULL DEFAULT NULL COMMENT '上级id',
  `account_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '账号状态: 1:启用, 0:禁用',
  `is_delete` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除: 1:是, 0:否',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `acc_user_role_rel`;
CREATE TABLE `acc_user_role_rel`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户角色-关联信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `comm_audit_workflow_config`;
CREATE TABLE `comm_audit_workflow_config`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `biz_id` bigint(20) NOT NULL COMMENT '所属流程节点id:classification_id',
  `flow_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核流程名称',
  `reviewer` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核人ids, 逗号分割',
  `reviewer_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核人名字, 逗号分割',
  `sort_index` tinyint(4) NOT NULL DEFAULT 1 COMMENT '排序',
  `prev_node_id` bigint(20) NULL DEFAULT NULL COMMENT '前一个流程配置id',
  `next_node_id` bigint(20) NULL DEFAULT NULL COMMENT '下一个流程配置id',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品审核流程信息配置表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `comm_check_child`;
CREATE TABLE `comm_check_child`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `main_id` bigint(20) NULL DEFAULT NULL COMMENT '主流程id',
  `flow_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核流程名称',
  `node_index` int(10) NULL DEFAULT NULL COMMENT '节点序号',
  `end_node` int(1) NULL DEFAULT 0 COMMENT '是否是最后一个节点（0否 1是）',
  `check_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当前节点审核状态（NAT:未到达, ATD:审核中, ATP审核通过, NATP:审核不通过）',
  `collection_id` varchar(8192) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当前节点审核的藏品集合，多个逗号隔开（藏品征集、鉴定业务）',
  `arrive_time` datetime(0) NULL DEFAULT NULL COMMENT '审核到达时间',
  `need_man_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '待审核人id',
  `need_man_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '待审核人姓名',
  `check_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `check_man_id` bigint(20) NULL DEFAULT NULL COMMENT '实际审核人id',
  `check_man_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实际审核人姓名',
  `check_opinion` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核意见',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '审核流程子表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `comm_check_log`;
CREATE TABLE `comm_check_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `main_id` bigint(20) NULL DEFAULT NULL COMMENT '流程主表主键id',
  `belong_model` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程所属模块(CLT:藏品征集, IDY:藏品鉴定, MSM:藏品入馆, TBT:藏品入藏, IWH:藏品入库, OUT:藏品出库,LOGIN:藏品登录, LOGOUT:藏品注销)',
  `model_id` bigint(20) NULL DEFAULT NULL COMMENT '对应审核功能模块表主键id',
  `check_man_id` bigint(20) NOT NULL COMMENT '实际审核人id',
  `check_man_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '实际审核人姓名',
  `check_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核状态（ATP：未通过,NATP：通过）',
  `check_opinion` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核意见',
  `check_time` datetime(0) NULL DEFAULT NULL COMMENT '处理时间',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '审核流程日志表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `comm_check_main`;
CREATE TABLE `comm_check_main`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `belong_model` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程所属模块(CLT:藏品征集, IDY:藏品鉴定, MSM:藏品入馆, TBT:藏品入藏, IWH:藏品入库, OUT:藏品出库, OUTIN:藏品出入库，LOGIN:藏品登录，LOGOUT:藏品注销)',
  `model_id` bigint(20) NULL DEFAULT NULL COMMENT '对应审核功能模块表主键id',
  `check_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'ATD' COMMENT '审批流程状态(USB:未提交,ATD:审核中, ATP审核通过, NATP:审核不通过,REVE:已撤销,BACK:退回)',
  `start_man_id` bigint(20) NOT NULL COMMENT '提交审核人id',
  `start_man_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '提交审核人名称',
  `start_time` datetime(0) NOT NULL COMMENT '提交审核时间',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '审核流程主表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `comm_classification`;
CREATE TABLE `comm_classification`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `biz_id` bigint(20) NULL DEFAULT NULL COMMENT '业务id',
  `biz_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务编码',
  `biz_number` bigint(20) NULL DEFAULT NULL COMMENT '业务编号',
  `biz_type` tinyint(4) NOT NULL COMMENT '业务类型',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名字',
  `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '父分类id',
  `level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '分类层级',
  `sort_index` tinyint(4) NOT NULL DEFAULT 1 COMMENT '排序',
  `permission` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1,1,1' COMMENT '分类权限',
  `enabled_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '启用状态: 0:不启用, 1:启用',
  `leaf_node` tinyint(4) NOT NULL DEFAULT 1 COMMENT '叶子结点: 0:否, 1:是',
  `remark` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `comm_classification_item_rel`;
CREATE TABLE `comm_classification_item_rel`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `ancestor_id` bigint(20) NOT NULL COMMENT '祖先：上级节点的id',
  `descendant_id` bigint(20) NOT NULL COMMENT '子代：下级节点的id',
  `distance` int(11) NOT NULL COMMENT '距离：子代到祖先中间隔了几级',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类项关联关系表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `comm_document`;
CREATE TABLE `comm_document`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `file_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件类型: SCAS:扫描件, TEXT:文本文件, IMAGE:图片, AUDIO:音频, VIDEO:视频',
  `file_format` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件格式',
  `source_file_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '原文件名字',
  `save_file_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '保存文件名字',
  `file_size` bigint(20) NULL DEFAULT NULL COMMENT '文件大小',
  `bucket` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件保存bucket',
  `path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件保存路径',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件URL',
  `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件描述',
  `is_delete` tinyint(4) NULL DEFAULT 0,
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `comm_document_extend`;
CREATE TABLE `comm_document_extend`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `source_doc_id` bigint(20) NOT NULL COMMENT '原文件id',
  `process_type` tinyint(4) UNSIGNED NOT NULL COMMENT '处理类型: 1:压缩',
  `file_format` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件格式',
  `save_file_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '保存文件名字',
  `file_size` bigint(20) NULL DEFAULT NULL COMMENT '文件大小',
  `bucket` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件保存bucket',
  `path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件保存路径',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件URL',
  `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件描述',
  `is_delete` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除: 0:否, 1:是',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件信息扩展表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `comm_incr_segment`;
CREATE TABLE `comm_incr_segment`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司唯一编码',
  `biz_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务编码',
  `biz_dimension` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务计数维度编码',
  `biz_curr_index` bigint(20) NOT NULL DEFAULT 1 COMMENT '业务自增索引',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '业务自增索引表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `comm_workflow_config`;
CREATE TABLE `comm_workflow_config`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `classification_id` bigint(20) NULL DEFAULT NULL COMMENT '分类信息表id',
  `auto_remove` tinyint(4) NOT NULL DEFAULT 1 COMMENT '自动去重: 0:否, 1:是',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '审批流程详细配置表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `exp_explain_info`;
CREATE TABLE `exp_explain_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `company_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司唯一编码',
  `explain_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '讲解唯一编码',
  `record_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '录音唯一编码',
  `explain_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '讲解名称',
  `explain_index` bigint(20) NOT NULL COMMENT '讲解顺序',
  `explain_words` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '讲解词',
  `explain_illustration_ids` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '讲解配图',
  `explain_video_ids` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '讲解视频',
  `explain_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '讲解类型: 1:展品讲解, 2:展厅讲解, 3:单元讲解, 4:展览讲解',
  `explain_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '讲解状态: 0:未发布, 1:已发布',
  `exhibition_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所在展览名称',
  `showroom_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所在展厅名称',
  `unit_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所在单元名称',
  `showpiece_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '展品名称',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '讲解备注',
  `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '讲解信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `exp_explain_play_record`;
CREATE TABLE `exp_explain_play_record`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司唯一编码',
  `explain_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '讲解唯一编码',
  `open_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '微信用户唯一标识',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '讲解播放记录表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `exp_record`;
CREATE TABLE `exp_record`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `company_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司唯一编码',
  `record_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '录音唯一编码',
  `record_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '录音名称',
  `record_duration` bigint(20) NOT NULL COMMENT '录音时长',
  `record_size` bigint(20) NOT NULL COMMENT '录音大小',
  `record_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '录音状态: 0:上传中, 1:上传成功, 2:上传失败',
  `document_id` bigint(20) NOT NULL COMMENT '录音文件id',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '录音备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '讲解录音信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `gzt_common_application`;
CREATE TABLE `gzt_common_application`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `application_id` bigint(20) NOT NULL COMMENT '应用id',
  `application_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用名称',
  `application_url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `application_icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `show_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'tab菜单显示名字',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '常用应用表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `gzt_user_work_request`;
CREATE TABLE `gzt_user_work_request`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `apply_type` tinyint(4) NOT NULL COMMENT '请示类型，1：请假，2：出差，3：公出，4：常规工作请示',
  `start_time` datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
  `out_days` int(10) NULL DEFAULT NULL COMMENT '天数',
  `destination` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出差/公务外出目的地',
  `item_description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出差/公务外出事项说明',
  `leave_type_id` bigint(20) NULL DEFAULT NULL COMMENT '请假类型,字典id',
  `leave_reason` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请假原因说明',
  `request_content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作请示内容',
  `document_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作请示附件,多个用,分割',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工工作请示表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `gzt_wait_deal_calendar`;
CREATE TABLE `gzt_wait_deal_calendar`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `wait_deal_day` date NOT NULL COMMENT '待办任务日期',
  `wait_deal_content` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '待办内容',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '待办日历' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `gzt_work_request_flow`;
CREATE TABLE `gzt_work_request_flow`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `apply_type` tinyint(4) NOT NULL COMMENT '流程类型，1：请假，2：出差，3：公出，4：常规工作请示',
  `flow_level` int(10) NOT NULL COMMENT '流程级数',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工作请示流程配置表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `safe_temperature_humidity_device`;
CREATE TABLE `safe_temperature_humidity_device`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `device_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备编码',
  `region_name` tinyint(4) NOT NULL COMMENT '所在区域(1展厅,2库房,3其他)',
  `device_address` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备位置',
  `min_temperature` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最低温度阈值',
  `max_temperature` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最高温度阈值',
  `min_humidity` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最低湿度阈值',
  `max_humidity` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最高湿度阈值',
  `current_temperature` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最新上报温度',
  `current_humidity` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最新上报湿度',
  `report_time` datetime(0) NULL DEFAULT NULL COMMENT '最新上报时间',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '温湿度设备信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `safe_temperature_humidity_record`;
CREATE TABLE `safe_temperature_humidity_record`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `device_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备编码',
  `current_temperature` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '温度',
  `current_humidity` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '湿度',
  `report_time` datetime(0) NULL DEFAULT NULL COMMENT '上报时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '温湿度上报记录表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_biz_collection_rel`;
CREATE TABLE `scs_biz_collection_rel`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `biz_id` bigint(20) NOT NULL COMMENT '业务id',
  `biz_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务类型: CLT:藏品征集, IDY:藏品鉴定, MSM:藏品入馆, TBT:藏品入藏, IWH:藏品入库, OUT:藏品出库',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '业务-藏品关联表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collect_certificate_info`;
CREATE TABLE `scs_collect_certificate_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collect_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收字号(征集凭证编号)',
  `prefix_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收字号前缀名称',
  `suffix_number` bigint(20) NULL DEFAULT NULL COMMENT '收字号后缀编号',
  `batch` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '征集批次',
  `transferor` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '移交人',
  `department_head` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '移交部门负责人',
  `submitter` bigint(20) NULL DEFAULT NULL COMMENT '提交人',
  `submitter_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人名字',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `auto_audit` tinyint(4) NOT NULL DEFAULT 0 COMMENT '自动提审 0:否 1:是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '征集凭证表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_ancient_book`;
CREATE TABLE `scs_collection_ancient_book`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `classification` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类',
  `version` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版本',
  `archive` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '存卷',
  `frame` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '装帧',
  `format` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版式',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品古籍文献信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_appendage`;
CREATE TABLE `scs_collection_appendage`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附属物名称',
  `actual_count` int(11) NOT NULL COMMENT '实际数量',
  `metering_unit` bigint(20) NULL DEFAULT NULL COMMENT '计量单位:classification_id',
  `metering_unit_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计量单位',
  `shape` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '形态',
  `complete_degree` bigint(20) NULL DEFAULT NULL COMMENT '完残程度:classification_id',
  `size` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体尺寸',
  `texture` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '质地',
  `weight` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重量',
  `current_situation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '现状',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属物件备注',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品附属物件信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_book`;
CREATE TABLE `scs_collection_book`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `author` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '作者',
  `record_date` datetime(0) NOT NULL COMMENT '著录时间',
  `book_level` bigint(20) NULL DEFAULT NULL COMMENT '级别:classification_id',
  `press_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出版机构及书名',
  `article` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文章章节及页码',
  `content` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主要内容',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品研究著录信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_damage`;
CREATE TABLE `scs_collection_damage`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '地点',
  `responsible_id` bigint(20) NULL DEFAULT NULL COMMENT '责任人id',
  `responsible_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '责任人名字',
  `damage_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '损坏情况',
  `reason` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '损坏原因',
  `handle_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理情况',
  `process_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事故经过',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '损坏备注',
  `damage_date` datetime(0) NOT NULL COMMENT '损坏日期',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品损坏信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_draw`;
CREATE TABLE `scs_collection_draw`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `title` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题名',
  `draw_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  `draughtsman_id` bigint(20) NULL DEFAULT NULL COMMENT '绘图者id',
  `draughtsman_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '绘图者名称',
  `proportion` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '比例',
  `draw_date` datetime(0) NULL DEFAULT NULL COMMENT '绘图日期',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品绘图信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_favorite`;
CREATE TABLE `scs_collection_favorite`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `favorite_count` bigint(20) NOT NULL COMMENT '收藏次数',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数字资源收藏表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_file_media`;
CREATE TABLE `scs_collection_file_media`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `media_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '流媒体文件类型: 图片:IMAGE, 视频:VIDEO, 音频:AUDIO',
  `file_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件名称',
  `file_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件编号',
  `producer_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '制作人',
  `proportion` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '比例',
  `shot_date` datetime(0) NULL DEFAULT NULL COMMENT '拍摄日期',
  `production_date` datetime(0) NULL DEFAULT NULL COMMENT '生产日期',
  `clarity` tinyint(4) NULL DEFAULT NULL COMMENT '清晰度: pq:普清, hd:高清',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品流媒体信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_identify`;
CREATE TABLE `scs_collection_identify`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `identify_type` bigint(20) NOT NULL COMMENT '鉴定类型：classification_id',
  `appraiser_id` bigint(20) NULL DEFAULT NULL COMMENT '鉴定人id',
  `appraiser_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鉴定人名字',
  `org_agency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组织机构',
  `identify_agency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鉴定机构',
  `approve_agency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批准机构',
  `identify_opinion` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鉴定意见',
  `identify_remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鉴定机构',
  `identify_date` datetime(0) NOT NULL COMMENT '鉴定日期',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品鉴定信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_info`;
CREATE TABLE `scs_collection_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `register_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '总登记号',
  `classify` bigint(20) NULL DEFAULT NULL COMMENT '分类号:classification_id',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '藏品名称',
  `actual_count` int(11) NULL DEFAULT NULL COMMENT '实际数量',
  `count_unit` bigint(20) NULL DEFAULT NULL COMMENT '计数单位:classification_id',
  `collection_count` int(11) NULL DEFAULT NULL COMMENT '数量',
  `quality_range` bigint(20) NULL DEFAULT NULL COMMENT '质量范围:classification_id',
  `era` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年代(填)',
  `age` bigint(20) NULL DEFAULT NULL COMMENT '年代(选):classification_id',
  `texture_category` bigint(20) NULL DEFAULT NULL COMMENT '质地类别:classification_id',
  `texture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '质地:classification_id, 逗号分割',
  `category` bigint(20) NULL DEFAULT NULL COMMENT '文物类别：classification_id',
  `size` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体尺寸',
  `size_unit` bigint(20) NULL DEFAULT NULL COMMENT '尺寸单位:classification_id',
  `complete_degree` bigint(20) NULL DEFAULT NULL COMMENT '完残程度:classification_id',
  `complete_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '完残情况',
  `initial_level` bigint(20) NULL DEFAULT NULL COMMENT '藏品初定级别:classification_id',
  `amount` bigint(20) NULL DEFAULT NULL COMMENT '金额',
  `collect_type` bigint(20) NULL DEFAULT NULL COMMENT '征集方式:classification_id',
  `collect_date` datetime(0) NULL DEFAULT NULL COMMENT '征集日期',
  `holder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原持有人/持有单位',
  `pay_voucher_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款凭证号',
  `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品备注',
  `appendage_desc` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属物描述',
  `source_info` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源信息',
  `introduce` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品介绍',
  `whereabouts` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品去向:TBT:入藏, IFMNC: 入信息资料中心, OTR:其他',
  `input_mode` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '录入方式: CTE:征集录入, ANE:鉴定录入, ALY:入馆录入, CNE:入藏录入',
  `input_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '录入类型: MLE:手动录入, ACE:自动录入',
  `collection_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品状态: CLTAT:征集审核, CLTATP:征集审核通过, MSMAT:入馆审核, MSMATP:入馆审核完成, TBTAT:入藏审核, TBTATP:入藏审核完成',
  `process_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文物状态: NTBT:未入藏, CNAD:已分配保管员, CCKD已查收, RTND已退回）',
  `in_museum_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入馆凭证编号',
  `in_museum_date` datetime(0) NULL DEFAULT NULL COMMENT '入馆时间',
  `in_museum_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入馆状态: NMSM:未入馆, AMSM:已入馆',
  `in_tibetan_date` datetime(0) NULL DEFAULT NULL COMMENT '入藏时间',
  `appraiser_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鉴定人名字',
  `warehouse_id` bigint(20) NULL DEFAULT NULL COMMENT '库房id',
  `warehouse_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '库房状态: WH:在库, POWH:预出库, OWH:出库, IWH:入库',
  `in_warehouse_date` datetime(0) NULL DEFAULT NULL COMMENT '入库时间',
  `out_warehouse_date` datetime(0) NULL DEFAULT NULL COMMENT '出库时间',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_login_import`;
CREATE TABLE `scs_collection_login_import`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `import_status` tinyint(4) NULL DEFAULT NULL COMMENT '导入状态：0 导入中，1 导入成功 , 2 导入失败',
  `success_num` bigint(20) NULL DEFAULT NULL COMMENT '成功数量',
  `failed_num` bigint(20) NULL DEFAULT NULL COMMENT '失败数量',
  `failed_reason` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '失败原因描述',
  `result_confirm` tinyint(4) NOT NULL DEFAULT 0 COMMENT '导入结果是否已确认：0 否，1 是 ',
  `use_time` bigint(20) NULL DEFAULT NULL COMMENT '本次导入耗时',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品登录批量导入记录表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_mount`;
CREATE TABLE `scs_collection_mount`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `contract_unit` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '承制单位',
  `producer_id` bigint(20) NULL DEFAULT NULL COMMENT '制作人id',
  `producer_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '制作人',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '装裱备注',
  `mount_date` datetime(0) NOT NULL COMMENT '装裱日期',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品装裱信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_note`;
CREATE TABLE `scs_collection_note`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `content` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '便签内容',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品便签信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_register_attr_ex`;
CREATE TABLE `scs_collection_register_attr_ex`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `register_info_id` bigint(20) NOT NULL COMMENT '总登记号id',
  `register_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '总登记号',
  `seller` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出售人',
  `buy_date` datetime(0) NULL DEFAULT NULL COMMENT '收购日期',
  `buy_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收购经手人',
  `buy_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收购地址',
  `donors` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '捐赠人',
  `donate_date` datetime(0) NULL DEFAULT NULL COMMENT '捐赠日期',
  `donate_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '捐赠经手人',
  `donate_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '捐赠地址',
  `collector` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采集人',
  `collect_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采集经手人',
  `collect_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采集地址',
  `excavator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发掘人',
  `excavation_date` datetime(0) NULL DEFAULT NULL COMMENT '发掘日期',
  `excavator_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发掘地址',
  `transfer_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '移交单位',
  `transfer_date` datetime(0) NULL DEFAULT NULL COMMENT '移交日期',
  `transfer_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '移交经手人',
  `appropriation_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拨交单位',
  `appropriation_date` datetime(0) NULL DEFAULT NULL COMMENT '拨交日期',
  `appropriation_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拨交经手人',
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '形状内容描述',
  `process` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '搜集过程',
  `hand_down` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流传经历',
  `status_record` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '现状记录',
  `appendix` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附录',
  `synopsis` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容提要',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `collect_date` datetime(0) NULL DEFAULT NULL COMMENT '采集日期',
  `select_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拣选经手人',
  `select_date` datetime(0) NULL DEFAULT NULL COMMENT '拣选日期',
  `exchange_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '依法交换经手人',
  `exchange_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '依法交换单位',
  `exchange_date` datetime(0) NULL DEFAULT NULL COMMENT '依法交换日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品登录藏品属性信息扩展表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_register_attr_ex_snapshot`;
CREATE TABLE `scs_collection_register_attr_ex_snapshot`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `register_info_snapshot_id` bigint(20) NOT NULL COMMENT '总登记号快照id',
  `register_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '总登记号',
  `seller` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出售人',
  `buy_date` datetime(0) NULL DEFAULT NULL COMMENT '收购日期',
  `buy_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收购经手人',
  `buy_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收购地址',
  `donors` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '捐赠人',
  `donate_date` datetime(0) NULL DEFAULT NULL COMMENT '捐赠日期',
  `donate_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '捐赠经手人',
  `donate_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '捐赠地址',
  `collector` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采集人',
  `collect_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采集经手人',
  `collect_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采集地址',
  `excavator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发掘人',
  `excavation_date` datetime(0) NULL DEFAULT NULL COMMENT '发掘日期',
  `excavator_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发掘地址',
  `transfer_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '移交单位',
  `transfer_date` datetime(0) NULL DEFAULT NULL COMMENT '移交日期',
  `transfer_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '移交经手人',
  `appropriation_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拨交单位',
  `appropriation_date` datetime(0) NULL DEFAULT NULL COMMENT '拨交日期',
  `appropriation_handler` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拨交经手人',
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '形状内容描述',
  `process` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '搜集过程',
  `hand_down` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流传经历',
  `status_record` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '现状记录',
  `appendix` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附录',
  `synopsis` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容提要',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品登录藏品属性信息扩展快照表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_register_attr_info`;
CREATE TABLE `scs_collection_register_attr_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `register_info_id` bigint(20) NOT NULL COMMENT '总登记号id',
  `register_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '总登记号',
  `length` bigint(20) NULL DEFAULT NULL COMMENT '长度',
  `width` bigint(20) NULL DEFAULT NULL COMMENT '宽度',
  `height` bigint(20) NULL DEFAULT NULL COMMENT '高度',
  `presence` bigint(20) NULL DEFAULT NULL COMMENT '腹围',
  `caliber` bigint(20) NULL DEFAULT NULL COMMENT '口径',
  `bottom_diameter` bigint(20) NULL DEFAULT NULL COMMENT '底径',
  `size` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体尺寸',
  `quality_range` bigint(20) NULL DEFAULT NULL COMMENT '质量范围:classification_id',
  `quality_unit` bigint(20) NULL DEFAULT NULL COMMENT '质量单位:classification_id',
  `specific_quality` bigint(20) NULL DEFAULT NULL COMMENT '具体质量',
  `actual_count` int(11) NULL DEFAULT NULL COMMENT '实际数量',
  `count_unit` bigint(20) NULL DEFAULT NULL COMMENT '计数单位:classification_id',
  `collection_count` int(11) NULL DEFAULT NULL COMMENT '数量',
  `count_remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品数量备注',
  `save_status` bigint(20) NULL DEFAULT NULL COMMENT '保存状态:classification_id',
  `complete_degree` bigint(20) NULL DEFAULT NULL COMMENT '完残程度：classification_id',
  `complete_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '完残情况',
  `texture_category` bigint(20) NULL DEFAULT NULL COMMENT '质地类别:classification_id',
  `texture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '质地:classification_id, 逗号分割',
  `texture_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '完残情况',
  `source` bigint(20) NULL DEFAULT NULL COMMENT '藏品来源:classification_id',
  `specific_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体来源',
  `excavated_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出土位置',
  `excavated_date` datetime(0) NULL DEFAULT NULL COMMENT '出土日期',
  `holder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原持有人/持有单位',
  `pay_voucher_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款凭证号',
  `amount` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '金额',
  `collection_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品登录管理属性信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_register_attr_info_snapshot`;
CREATE TABLE `scs_collection_register_attr_info_snapshot`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `register_info_snapshot_id` bigint(20) NOT NULL COMMENT '总登记号快照id',
  `register_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '总登记号',
  `length` bigint(20) NULL DEFAULT NULL COMMENT '长度',
  `width` bigint(20) NULL DEFAULT NULL COMMENT '宽度',
  `height` bigint(20) NULL DEFAULT NULL COMMENT '高度',
  `presence` bigint(20) NULL DEFAULT NULL COMMENT '腹围',
  `caliber` bigint(20) NULL DEFAULT NULL COMMENT '口径',
  `bottom_diameter` bigint(20) NULL DEFAULT NULL COMMENT '底径',
  `size` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体尺寸',
  `quality_range` bigint(20) NULL DEFAULT NULL COMMENT '质量范围:classification_id',
  `quality_unit` bigint(20) NULL DEFAULT NULL COMMENT '质量单位:classification_id',
  `specific_quality` bigint(20) NULL DEFAULT NULL COMMENT '具体质量',
  `actual_count` int(11) NULL DEFAULT NULL COMMENT '实际数量',
  `count_unit` bigint(20) NULL DEFAULT NULL COMMENT '计数单位:classification_id',
  `collection_count` int(11) NULL DEFAULT NULL COMMENT '数量',
  `count_remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品数量备注',
  `save_status` bigint(20) NULL DEFAULT NULL COMMENT '保存状态:classification_id',
  `complete_degree` bigint(20) NULL DEFAULT NULL COMMENT '完残程度：classification_id',
  `complete_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '完残情况',
  `texture_category` bigint(20) NULL DEFAULT NULL COMMENT '质地类别:classification_id',
  `texture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '质地:classification_id, 逗号分割',
  `texture_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '完残情况',
  `source` bigint(20) NULL DEFAULT NULL COMMENT '藏品来源:classification_id',
  `specific_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体来源',
  `excavated_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出土位置',
  `excavated_date` datetime(0) NULL DEFAULT NULL COMMENT '出土日期',
  `holder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原持有人/持有单位',
  `pay_voucher_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款凭证号',
  `amount` bigint(20) NULL DEFAULT NULL COMMENT '金额',
  `collection_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品登录管理属性信息快照表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_register_info`;
CREATE TABLE `scs_collection_register_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `register_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '总登记号',
  `rfid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'rfid编号',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '藏品名称',
  `original_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原号',
  `original_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原名',
  `classify` bigint(20) NULL DEFAULT NULL COMMENT '分类号:classification_id',
  `classify_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类号描述',
  `identify_level` bigint(20) NULL DEFAULT NULL COMMENT '藏品级别:classification_id',
  `category` bigint(20) NULL DEFAULT NULL COMMENT '文物类别：classification_id',
  `in_tibetan_age_range` bigint(20) NULL DEFAULT NULL COMMENT '入藏年代(选):classification_id',
  `in_tibetan_date` datetime(0) NULL DEFAULT NULL COMMENT '入藏时间',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所在地址',
  `specific_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体信息',
  `era` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年代(填)',
  `age` bigint(20) NULL DEFAULT NULL COMMENT '年代(选):classification_id',
  `use` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用途',
  `color` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '色泽',
  `author` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者',
  `author_biography` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者小传',
  `attachment_desc` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属物说明',
  `in_cupboard_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入柜状态: NCBD:未入柜, ACBD:已入柜',
  `submitter` bigint(20) NULL DEFAULT NULL COMMENT '提交人',
  `submitter_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人名字',
  `register_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品状态: NRTS:未登录, ARTS:已登录, POWH:预出库, AOWH:出库, AIWH:入库, ALGF:已注销',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `auto_audit` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否自动提交审核 0: 否 1: 是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品登录基础信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_register_info_snapshot`;
CREATE TABLE `scs_collection_register_info_snapshot`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `register_info_id` bigint(20) NOT NULL COMMENT '总登记号id',
  `register_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '总登记号',
  `rfid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'rfid编号',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '藏品名称',
  `original_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原号',
  `original_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原名',
  `classify` bigint(20) NULL DEFAULT NULL COMMENT '分类号:classification_id',
  `classify_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类号描述',
  `identify_level` bigint(20) NULL DEFAULT NULL COMMENT '藏品级别:classification_id',
  `category` bigint(20) NULL DEFAULT NULL COMMENT '文物类别：classification_id',
  `in_tibetan_age_range` bigint(20) NULL DEFAULT NULL COMMENT '入藏年代(选):classification_id',
  `in_tibetan_date` datetime(0) NULL DEFAULT NULL COMMENT '入藏时间',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所在地址',
  `specific_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体信息',
  `era` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年代(填)',
  `age` bigint(20) NULL DEFAULT NULL COMMENT '年代(选):classification_id',
  `use` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用途',
  `color` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '色泽',
  `author` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者',
  `author_biography` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者小传',
  `attachment_desc` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属物说明',
  `in_cupboard_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入柜状态: NCBD:未入柜, ACBD:已入柜',
  `submitter` bigint(20) NULL DEFAULT NULL COMMENT '提交人',
  `submitter_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人名字',
  `register_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品状态: NRTS:未登录, ARTS:已登录, AOWH:出库, AIWH:入库, ALGF:已注销',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品登录基础快照表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_register_manage_info`;
CREATE TABLE `scs_collection_register_manage_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `register_info_id` bigint(20) NOT NULL COMMENT '总登记号id',
  `register_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '总登记号',
  `collect_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '(征集凭证)收字号',
  `collect_date` datetime(0) NULL DEFAULT NULL COMMENT '征集时间',
  `identify_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鉴定凭证号',
  `identify_date` datetime(0) NULL DEFAULT NULL COMMENT '鉴定时间',
  `in_museum_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入馆凭证号',
  `in_museum_date` datetime(0) NULL DEFAULT NULL COMMENT '入馆时间',
  `in_tibetan_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '(入藏凭证)藏字号',
  `in_tibetan_date` datetime(0) NULL DEFAULT NULL COMMENT '入藏时间',
  `appraiser_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鉴定人名字',
  `ordinal` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '序号',
  `warehouse_id` bigint(20) NULL DEFAULT NULL COMMENT '库房id:classification_id',
  `cupboard_id` bigint(20) NULL DEFAULT NULL COMMENT '柜架id:classification_id',
  `floor_id` bigint(20) NULL DEFAULT NULL COMMENT '层id:classification_id',
  `drawer_id` bigint(20) NULL DEFAULT NULL COMMENT '屉id:classification_id',
  `column_id` bigint(20) NULL DEFAULT NULL COMMENT '列id:classification_id',
  `number_id` bigint(20) NULL DEFAULT NULL COMMENT '号id:classification_id',
  `package_id` bigint(20) NULL DEFAULT NULL COMMENT '藏品包装id:classification_id',
  `package_size` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品包装尺寸',
  `is_box` tinyint(1) NULL DEFAULT NULL COMMENT '是否有囊匣: 1:是,0:否',
  `is_doubtful` tinyint(1) NULL DEFAULT NULL COMMENT '存疑: 1:是,0:否',
  `is_foreign` tinyint(1) NULL DEFAULT NULL COMMENT '对外: 1:是,0:否',
  `is_booking` tinyint(1) NULL DEFAULT NULL COMMENT '预订: 1:是,0:否',
  `booking_info` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预定信息',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品登录管理信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_register_manage_info_snapshot`;
CREATE TABLE `scs_collection_register_manage_info_snapshot`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `register_info_snapshot_id` bigint(20) NOT NULL COMMENT '总登记号快照id',
  `register_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '总登记号',
  `collect_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '(征集凭证)收字号',
  `collect_date` datetime(0) NULL DEFAULT NULL COMMENT '征集时间',
  `identify_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鉴定凭证号',
  `identify_date` datetime(0) NULL DEFAULT NULL COMMENT '鉴定时间',
  `in_museum_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入馆凭证号',
  `in_museum_date` datetime(0) NULL DEFAULT NULL COMMENT '入馆时间',
  `in_tibetan_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '(入藏凭证)藏字号',
  `in_tibetan_date` datetime(0) NULL DEFAULT NULL COMMENT '入藏时间',
  `appraiser_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鉴定人名字',
  `ordinal` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '序号',
  `warehouse_id` bigint(20) NULL DEFAULT NULL COMMENT '库房id:classification_id',
  `cupboard_id` bigint(20) NULL DEFAULT NULL COMMENT '柜架id:classification_id',
  `floor_id` bigint(20) NULL DEFAULT NULL COMMENT '层id:classification_id',
  `drawer_id` bigint(20) NULL DEFAULT NULL COMMENT '屉id:classification_id',
  `column_id` bigint(20) NULL DEFAULT NULL COMMENT '列id:classification_id',
  `number_id` bigint(20) NULL DEFAULT NULL COMMENT '号id:classification_id',
  `package_id` bigint(20) NULL DEFAULT NULL COMMENT '藏品包装id:classification_id',
  `package_size` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品包装尺寸',
  `is_box` tinyint(1) NULL DEFAULT NULL COMMENT '是否有囊匣: 1:是,0:否',
  `is_doubtful` tinyint(1) NULL DEFAULT NULL COMMENT '存疑: 1:是,0:否',
  `is_foreign` tinyint(1) NULL DEFAULT NULL COMMENT '对外: 1:是,0:否',
  `is_booking` tinyint(1) NULL DEFAULT NULL COMMENT '预订: 1:是,0:否',
  `booking_info` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预定信息',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品登录管理信息快照表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_rubbing`;
CREATE TABLE `scs_collection_rubbing`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `book_style` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '书体',
  `mount_situation` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '装裱形势',
  `rubbing_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拓片号',
  `recognition` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '款识',
  `seal` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '印章',
  `postscript` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '题跋',
  `interpretation` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '释文',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品拓片信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_three_model`;
CREATE TABLE `scs_collection_three_model`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `register_info_id` bigint(20) NOT NULL COMMENT '总登记号id',
  `document_id` bigint(20) NOT NULL COMMENT '文件id',
  `three_model_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '3D模型url',
  `three_model_status` tinyint(4) NULL DEFAULT NULL COMMENT '3D模型状态',
  `application_level` tinyint(4) NULL DEFAULT NULL COMMENT '应用等级，1展示级，2存储级，3复制级',
  `file_batch` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件批次',
  `make_unit` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '制作单位',
  `make_time` datetime(0) NULL DEFAULT NULL COMMENT '制作日期',
  `collect_device` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采集设备',
  `top_tip` int(10) NULL DEFAULT NULL COMMENT '顶点数',
  `triangle_plane` int(10) NULL DEFAULT NULL COMMENT '三角面数',
  `coordinate_system` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模型坐标系',
  `texture_format` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纹理格式',
  `color_mode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '色彩模式',
  `color_bits` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '色彩位数',
  `resolution_ratio` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '贴图分辨率',
  `geometric_accuracy` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '几何精度',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品三维模型信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_two_image`;
CREATE TABLE `scs_collection_two_image`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `register_info_id` bigint(20) NOT NULL COMMENT '总登记号id',
  `document_id` bigint(20) NOT NULL COMMENT '文件id',
  `file_batch` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件批次',
  `make_unit` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '制作单位',
  `make_time` datetime(0) NULL DEFAULT NULL COMMENT '制作日期',
  `resolution_ratio` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分辨率',
  `collect_device` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采集设备',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品二维影像信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_update_log`;
CREATE TABLE `scs_collection_update_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `register_info_id` bigint(20) NOT NULL COMMENT '总登记号id',
  `register_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '总登记号',
  `creator_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新记录人名称',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品登录修改日志表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_update_log_detail`;
CREATE TABLE `scs_collection_update_log_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `update_log_id` bigint(20) NOT NULL COMMENT '修改日志id',
  `field` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '修改字段',
  `field_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '修改字段英文名称',
  `old_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '旧值',
  `new_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '新值',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品登录修改日志详情表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_collection_utensil`;
CREATE TABLE `scs_collection_utensil`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `inscription` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '铭文',
  `ornament` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '纹饰',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品器物信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_identify_certificate_info`;
CREATE TABLE `scs_identify_certificate_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `identify_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鉴定凭证编号',
  `prefix_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鉴定凭证号前缀名称(鉴定年份)',
  `suffix_number` bigint(20) NULL DEFAULT NULL COMMENT '鉴定凭证号后缀编号',
  `batch` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鉴定批次',
  `submitter` bigint(20) NULL DEFAULT NULL COMMENT '提交人',
  `submitter_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人名称',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `auto_audit` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否自动提交审核 0: 否 1: 是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品鉴定凭证表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_identify_info`;
CREATE TABLE `scs_identify_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `identify_certificate_id` bigint(20) NOT NULL COMMENT '鉴定凭证编号',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `appraiser_id` bigint(20) NULL DEFAULT NULL COMMENT '鉴定人id',
  `appraiser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鉴定人名称',
  `identify_agency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鉴定机构',
  `identify_opinion` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鉴定意见',
  `identify_remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鉴定备注',
  `identify_date` datetime(0) NOT NULL COMMENT '鉴定日期',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '藏品鉴定信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_in_museum_certificate_info`;
CREATE TABLE `scs_in_museum_certificate_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `in_museum_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '入馆凭证编号',
  `prefix_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '入馆凭证号前缀名称(入馆年份)',
  `suffix_number` bigint(20) NULL DEFAULT NULL COMMENT '入馆凭证号后缀编号',
  `batch` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入馆批次',
  `submitter` bigint(20) NULL DEFAULT NULL COMMENT '提交人',
  `submitter_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人名称',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `auto_audit` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否自动提交审核 0: 否 1: 是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '入馆凭证信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_in_tibetan_certificate_info`;
CREATE TABLE `scs_in_tibetan_certificate_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `in_tibetan_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '藏字号(入藏凭证编号)',
  `prefix_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '藏字号前缀名称',
  `suffix_number` bigint(20) NULL DEFAULT NULL COMMENT '藏字号后缀编号',
  `submitter` bigint(20) NULL DEFAULT NULL COMMENT '提交人',
  `submitter_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人名称',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `auto_audit` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否自动提交审核 0: 否 1: 是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '入藏凭证信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_in_warehouse_log`;
CREATE TABLE `scs_in_warehouse_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `out_in_warehouse_id` bigint(20) NOT NULL COMMENT '出入库凭证id',
  `out_in_warehouse_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '出入库凭证编号',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `transferor` bigint(20) NULL DEFAULT NULL COMMENT '移交人',
  `transferor_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '移交人名称',
  `receiver` bigint(20) NULL DEFAULT NULL COMMENT '接收人',
  `receiver_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接收人名称',
  `return_date` datetime(0) NOT NULL COMMENT '归还日期',
  `return_address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '归还地点',
  `collection_remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文物备注',
  `return_remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '归还备注',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '入库藏品信息日志表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_logoff_certificate_info`;
CREATE TABLE `scs_logoff_certificate_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `logoff_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '注销凭证编号',
  `prefix_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '注销凭证号前缀名称(注销年份)',
  `suffix_number` bigint(20) NULL DEFAULT NULL COMMENT '注销凭证后缀编号',
  `logoff_date` datetime(0) NOT NULL COMMENT '注销日期',
  `approve_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批准文号',
  `submitter` bigint(20) NULL DEFAULT NULL COMMENT '提交人',
  `submitter_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人名称',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `auto_audit` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否自动提交审核 0: 否 1: 是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '注销凭证信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_logoff_collection_info`;
CREATE TABLE `scs_logoff_collection_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `logoff_id` bigint(20) NOT NULL COMMENT '注销凭证id',
  `logoff_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '注销凭证编号',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `register_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '总登记号',
  `classify` bigint(20) NULL DEFAULT NULL COMMENT '分类号:classification_id',
  `classify_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类号名称',
  `collection_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '藏品名称',
  `era` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年代(填)',
  `age` bigint(20) NULL DEFAULT NULL COMMENT '年代(选):classification_id',
  `age_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年代(选)名称',
  `collection_count` int(11) NULL DEFAULT NULL COMMENT '数量',
  `count_unit` bigint(20) NULL DEFAULT NULL COMMENT '计数单位:classification_id',
  `count_unit_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计数单位名称',
  `identify_level` bigint(20) NULL DEFAULT NULL COMMENT '藏品级别:classification_id',
  `identify_level_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品级别名称',
  `situation` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '现状',
  `reason` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原因',
  `whereabouts` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '去向',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '注销藏品信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_out_in_warehouse_certificate_info`;
CREATE TABLE `scs_out_in_warehouse_certificate_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `out_in_warehouse_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '出入库凭证编号',
  `prefix_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '出入库凭证编号前缀名称',
  `suffix_number` bigint(20) NULL DEFAULT NULL COMMENT '出入库凭证编号后缀编号',
  `reason` bigint(20) NOT NULL COMMENT '出库原因:classification_id',
  `reason_info` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体出库原因',
  `use_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提用人',
  `user_department_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提用部门',
  `use_address` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用地点',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `use_date` datetime(0) NOT NULL COMMENT '提用日期',
  `pre_return_date` datetime(0) NOT NULL COMMENT '预归还日期',
  `submitter` bigint(20) NULL DEFAULT NULL COMMENT '提交人',
  `submitter_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人名称',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `auto_audit` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否自动提交审核 0: 否 1: 是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '出入库凭证信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `scs_out_warehouse_log`;
CREATE TABLE `scs_out_warehouse_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `out_in_warehouse_id` bigint(20) NOT NULL COMMENT '出入库凭证id',
  `out_in_warehouse_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '出入库凭证编号',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '出库藏品信息日志表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `shjy_activity_apply_info`;
CREATE TABLE `shjy_activity_apply_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `company_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司唯一编码',
  `activity_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动唯一编码',
  `activity_id` bigint(20) NOT NULL COMMENT '所属活动id',
  `venue_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '场馆名称',
  `activity_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动名称',
  `activity_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动单号',
  `apply_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '报名人姓名',
  `apply_user_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '申请人信息表',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '活动报名信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `shjy_activity_link_info`;
CREATE TABLE `shjy_activity_link_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `company_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司唯一编码',
  `activity_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动唯一编码',
  `activity_id` bigint(20) NOT NULL COMMENT '所属活动id',
  `link_title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '链接标题',
  `link_address` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '链接地址',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '活动链接信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `shjy_activity_manage`;
CREATE TABLE `shjy_activity_manage`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `company_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司唯一编码',
  `activity_unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动唯一编码',
  `activity_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动名称',
  `apply_type` bigint(20) NOT NULL COMMENT '活动类型id',
  `start_time` datetime(0) NOT NULL COMMENT '活动开始时间',
  `end_time` datetime(0) NOT NULL COMMENT '活动结束时间',
  `activity_address` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动地址',
  `limit_people` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否限制报名名额 0:不限，1:限制',
  `limit_num` int(10) NULL DEFAULT 0 COMMENT '报名名额',
  `apply_num` int(10) NULL DEFAULT NULL COMMENT '已报名人数',
  `apply_notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报名须知',
  `cover_picture` bigint(20) NOT NULL COMMENT '封面图片id',
  `activity_introduce` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动介绍',
  `process_files` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '过程文件id,多个用英文逗号隔开',
  `publish_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发布状态: 0:未发布, 1:已发布',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '活动管理信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `swzs_cultural_auto_build`;
CREATE TABLE `swzs_cultural_auto_build`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `app_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用id',
  `model_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模型名称',
  `build_status` tinyint(4) NOT NULL COMMENT '模型构建状态',
  `build_process` bigint(20) NOT NULL COMMENT '构建进度',
  `download_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模型下载地址',
  `url_create_time` datetime(0) NOT NULL COMMENT '构建链接生成时间',
  `switch_status` tinyint(4) NOT NULL COMMENT '模型转换状态',
  `model_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模型浏览链接',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `cover_url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '三维全息自动建模功能表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `swzs_cultural_news`;
CREATE TABLE `swzs_cultural_news`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `news_title` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资讯标题',
  `news_type` tinyint(4) NOT NULL COMMENT '资讯类型: 1:馆务公开, 2:新闻报道, 3:公告通知',
  `news_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '正文内容',
  `publish_status` tinyint(4) NOT NULL COMMENT '发布状态: 0:未发布, 1:已发布',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `publish_time` datetime(0) NULL DEFAULT NULL COMMENT '发布时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资讯管理表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `swzs_cultural_relics`;
CREATE TABLE `swzs_cultural_relics`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `number` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文物编号',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文物名称',
  `level_id` bigint(20) NOT NULL COMMENT '文物级别id',
  `level_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文物级别名称',
  `complete_degree_id` bigint(20) NOT NULL COMMENT '完成程度id',
  `complete_degree_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '完成程度名称',
  `dynasty_id` bigint(20) NULL DEFAULT NULL COMMENT '朝代id',
  `dynasty_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '朝代名称',
  `ad_year_id` bigint(20) NULL DEFAULT NULL COMMENT '公元纪年id',
  `ad_year_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公元纪年名称',
  `specific_age` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体年代',
  `texture_id` bigint(20) NULL DEFAULT NULL COMMENT '质地id',
  `texture_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '质地名称',
  `source_id` bigint(20) NULL DEFAULT NULL COMMENT '来源id',
  `source_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源名称',
  `size` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '尺寸',
  `keyword` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关键字',
  `cover_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '简介',
  `three_model_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '3D模型url',
  `three_model_status` tinyint(4) NULL DEFAULT NULL COMMENT '3D模型状态',
  `two_model_id` varchar(8192) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '二维文件id',
  `compared_cultural_relics_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对比文物id',
  `similar_cultural_relics_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '相似文物id',
  `audio_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '音频id',
  `video_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '视频id',
  `academic_literature_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学术文献id',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '文物状态: 0:未展示, 1:展示中',
  `data_status` tinyint(4) NOT NULL COMMENT '数据状态: 0:无, 1:二维和三维, 2:二维, 3:三维',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `auto_display` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否自动去展示：0不自动展示，1自动展示',
  `is_new` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否是新增的同步资源 0: 不是 1: 是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '三维全息信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `swzs_cultural_relics_access`;
CREATE TABLE `swzs_cultural_relics_access`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '公司id',
  `cultural_relics_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文物id',
  `number` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文物编号',
  `type` tinyint(4) NOT NULL COMMENT '统计类型: 1:全息, 2:全景',
  `access_count` bigint(20) NOT NULL COMMENT '访问数',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '三维访问统计信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `swzs_cultural_relics_comment`;
CREATE TABLE `swzs_cultural_relics_comment`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '公司id',
  `cultural_relics_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文物id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `type` tinyint(4) NOT NULL COMMENT '统计类型: 1:全息, 2:全景',
  `content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `status` tinyint(4) NOT NULL COMMENT '评论状态: 0:不可见, 1:可见',
  `modifier` bigint(20) NOT NULL DEFAULT -1 COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '三维评论信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `swzs_cultural_relics_config`;
CREATE TABLE `swzs_cultural_relics_config`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `domain_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台域名',
  `panoramic_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '全景url',
  `venue_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '场馆名称',
  `venue_logo_id` bigint(20) NOT NULL COMMENT '场馆logo id',
  `venue_home_style_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '首页风格url',
  `venue_home_style_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '首页风格id',
  `venue_remark` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '场馆介绍',
  `venue_picture_id` bigint(20) NULL DEFAULT NULL COMMENT '场馆图片id',
  `venue_phone` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `close_day` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '闭馆日',
  `is_holidays_postpone` tinyint(4) NULL DEFAULT NULL COMMENT '法定节假日是否顺延: 0:否, 1:是',
  `open_time` time(0) NULL DEFAULT NULL COMMENT '开馆时间',
  `close_time` time(0) NULL DEFAULT NULL COMMENT '闭馆时间',
  `stop_time` time(0) NULL DEFAULT NULL COMMENT '停止入馆时间',
  `ad_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市编码',
  `city_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市名称',
  `ad_code_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市编码路径',
  `city_name_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市名称路径',
  `address` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `notice` varchar(8192) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参观通知',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `left` tinyint(4) NOT NULL COMMENT '是否显示在左上角：0否，1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '三维全息配置信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `swzs_cultural_relics_like`;
CREATE TABLE `swzs_cultural_relics_like`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '公司id',
  `cultural_relics_id` bigint(20) NOT NULL COMMENT '文物id',
  `number` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文物编号',
  `type` tinyint(4) NOT NULL COMMENT '统计类型: 1:全息, 2:全景',
  `like_count` bigint(20) NOT NULL COMMENT '点赞数',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '三维统点赞计信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `swzs_cultural_relics_share`;
CREATE TABLE `swzs_cultural_relics_share`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '公司id',
  `cultural_relics_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文物id',
  `number` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文物编号',
  `type` tinyint(4) NOT NULL COMMENT '统计类型: 1:全息, 2:全景',
  `share_count` bigint(20) NOT NULL COMMENT '分享数',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '三维分享统计信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `sync_province_museum_auth`;
CREATE TABLE `sync_province_museum_auth`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `museum_base_id` bigint(20) NOT NULL COMMENT '省平台公司id',
  `unique_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司编码',
  `province_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台url',
  `key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '秘钥',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '博物馆授权信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `szzy_collection_favorite`;
CREATE TABLE `szzy_collection_favorite`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `collection_id` bigint(20) NOT NULL COMMENT '藏品id',
  `favorite_count` bigint(20) NOT NULL COMMENT '收藏次数',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数字资源收藏表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `szzy_job_permission`;
CREATE TABLE `szzy_job_permission`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '公司id',
  `permission_group_id` bigint(20) NOT NULL COMMENT '权限组id',
  `job_id` bigint(20) NOT NULL COMMENT '职位id',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '权限组-职位关联表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `szzy_operate_log`;
CREATE TABLE `szzy_operate_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '公司id',
  `content_id` bigint(20) NULL DEFAULT NULL COMMENT '关联的操作内容id',
  `operate_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作名称',
  `operate_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '操作时间',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人id',
  `operator_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `szzy_resource`;
CREATE TABLE `szzy_resource`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '父id',
  `resource_pool_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资源库编码',
  `resource_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源编码',
  `resource_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源名称',
  `resource_type` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '资源类型: 0:资源库, 1:文件, 2:文件夹',
  `resource_keyword` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'resource_keyword',
  `resource_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '元数据',
  `secret_level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '保密等级L 1:普通, 2:限制, 3:保密',
  `front_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '前端扩展信息',
  `resource_uri_id` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'uri_id',
  `resource_uri_name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'uri_name',
  `resource_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'url',
  `resource_route` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'route',
  `build_id` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '建设人员id',
  `build_count` bigint(20) NULL DEFAULT 0 COMMENT '建设人数',
  `favorite_count` bigint(20) NULL DEFAULT 0 COMMENT '收藏次数',
  `like_count` bigint(20) NULL DEFAULT 0 COMMENT '攒点次数',
  `document_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附件id',
  `is_delete` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除: 1:是, 0:否',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资源库表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `szzy_resource_doc_recycle_bin`;
CREATE TABLE `szzy_resource_doc_recycle_bin`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `source_doc_id` bigint(20) NOT NULL COMMENT '原文件id',
  `resource_id` bigint(20) NOT NULL COMMENT '资源id',
  `resource_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源名称',
  `file_format` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件格式',
  `save_file_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '保存文件名字',
  `file_size` bigint(20) NULL DEFAULT NULL COMMENT '文件大小',
  `resource_uri_id` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'uri_id',
  `resource_uri_name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'uri_name',
  `resource_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'url',
  `resource_route` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'route',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `three_model_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '三维资源预览路径',
  `three_model_status` tinyint(4) NULL DEFAULT NULL COMMENT '模型转换状态',
  `error_info` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '三维转换错误信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资源附件回收站表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `szzy_resource_download`;
CREATE TABLE `szzy_resource_download`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `document_id` bigint(20) NOT NULL COMMENT '附件id',
  `resource_id` bigint(20) NOT NULL COMMENT '资源id',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资源下载记录表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `szzy_resource_favorites`;
CREATE TABLE `szzy_resource_favorites`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `resource_id` bigint(20) NOT NULL COMMENT '资源id',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `resource_type` tinyint(4) UNSIGNED NOT NULL COMMENT '资源类型 0: 数字资源 1: 藏品资源',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数字资源收藏表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `szzy_resource_item_rel`;
CREATE TABLE `szzy_resource_item_rel`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `ancestor_id` bigint(20) NOT NULL COMMENT '祖先：上级节点的id',
  `descendant_id` bigint(20) NOT NULL COMMENT '子代：下级节点的id',
  `distance` int(11) NOT NULL COMMENT '距离：子代到祖先中间隔了几级',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资源库附件关系' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `szzy_resource_search`;
CREATE TABLE `szzy_resource_search`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `resource_id` bigint(20) NOT NULL COMMENT '资源id',
  `source_doc_id` bigint(20) NULL DEFAULT NULL COMMENT '原文件id',
  `resource_pool_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源库编码',
  `resource_type` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '资源类型: 0:资源库, 1:文件, 2:文件夹, 3:附件',
  `secret_level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '保密等级: 1:普通, 2:限制, 3:保密',
  `resource_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源名称',
  `collection_name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '藏品名称',
  `related_user_name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联人名称',
  `resource_uri_id` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'uri_id',
  `resource_uri_name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'uri_name',
  `resource_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'url',
  `resource_route` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'route',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `related_research_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联研究',
  `three_model_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '三维资源预览路径',
  `three_model_status` tinyint(4) NULL DEFAULT NULL COMMENT '模型转换状态',
  `error_info` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '三维转换错误信息',
  `collection_id` bigint(20) NULL DEFAULT NULL COMMENT '藏品id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检索信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `szzy_source_apply_info`;
CREATE TABLE `szzy_source_apply_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '公司id',
  `source_id` bigint(20) NOT NULL COMMENT '资源id（关联申请的资源）',
  `source_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源名称',
  `source_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资源路径',
  `security_level` tinyint(4) NOT NULL COMMENT '资源保密等级：0：普通，1：限制，2：机密',
  `apply_use` tinyint(4) NOT NULL COMMENT '申请用途:0:查看详情，1:下载，2:查看并下载',
  `use_time_limit` tinyint(4) NOT NULL COMMENT '使用时限:0：7天，1：永久',
  `use_reason` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '原因说明',
  `apply_time` datetime(0) NOT NULL COMMENT '申请时间',
  `apply_status` tinyint(4) NOT NULL COMMENT '申请状态：0申请中，1已通过，2未通过',
  `pass_time` datetime(0) NULL DEFAULT NULL COMMENT '申请通过时间',
  `apply_user_id` bigint(20) NOT NULL COMMENT '申请人id',
  `apply_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '申请人名称',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资源申请详情表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `szzy_source_permission_group`;
CREATE TABLE `szzy_source_permission_group`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '公司id',
  `security_level` tinyint(4) NOT NULL COMMENT '资源保密等级：0：普通，1：限制，2：机密',
  `group_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限组名称',
  `data_permission` tinyint(4) NOT NULL COMMENT '数据权限：0：不可见，1：可见',
  `function_permission` tinyint(4) NOT NULL COMMENT '功能权限：0：无权限，1：仅查看，2：可查看下载',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数字资源权限组表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `tkt_order_base`;
CREATE TABLE `tkt_order_base`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `department_id` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
  `venue_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '场馆名称',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `order_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `ticket_name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '票种名称,分割',
  `ticket_code` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '票种编码,分割',
  `total_price` bigint(20) NOT NULL COMMENT '订单总价格',
  `total_ticket_count` bigint(20) NOT NULL COMMENT '订单总票数',
  `total_people_count` bigint(20) NOT NULL COMMENT '订单总人数',
  `channel_type` tinyint(4) UNSIGNED NOT NULL COMMENT '投放渠道预约规则类型: 1:现场窗口, 2:微信小程序',
  `order_type` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '订单类型: 1:个人预约, 2:团队预约',
  `reserve_type` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '预约类型: 1:提前预约, 2:免预约通行',
  `status` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单状态: 0:未使用, 1:未核销, 2:已核销, 3:已过期',
  `reserve_date` datetime(0) NOT NULL COMMENT '预约日期',
  `time_period_id` bigint(20) NOT NULL COMMENT '分时场次id',
  `time_period_start` time(0) NULL DEFAULT NULL COMMENT '分时场次开始时间',
  `time_period_end` time(0) NULL DEFAULT NULL COMMENT '分时场次结束时间',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单信息基础表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `tkt_personal_order_detail`;
CREATE TABLE `tkt_personal_order_detail`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `department_id` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
  `order_id` bigint(20) NOT NULL COMMENT '订单id',
  `order_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `personal_order_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子订单号',
  `ticket_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '票种名称',
  `ticket_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '票种编码',
  `ticket_price` bigint(20) NOT NULL COMMENT '票种价格',
  `tourist_id` bigint(20) NULL DEFAULT NULL COMMENT '游客id',
  `tourist_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '游客名称',
  `tourist_certificate_type` tinyint(4) NOT NULL COMMENT '证件类型: 1:身份证, 2:护照, 3:港澳通行证',
  `tourist_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '证件号码',
  `status` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单明细状态: 0:未使用, 1:未核销, 2:已核销, 3:已过期',
  `qr_code_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '二维码url',
  `reserve_date` datetime(0) NOT NULL COMMENT '预约日期',
  `time_period_id` bigint(20) NOT NULL COMMENT '分时场次id',
  `time_period_start` time(0) NULL DEFAULT NULL COMMENT '分时场次开始时间',
  `time_period_end` time(0) NULL DEFAULT NULL COMMENT '分时场次结束时间',
  `write_off_date` datetime(0) NULL DEFAULT NULL COMMENT '核销日期',
  `write_off_count` tinyint(4) NOT NULL DEFAULT 0 COMMENT '核销次数',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `writeoff_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销人员姓名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '个人订单明细信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `tkt_team_order_detail`;
CREATE TABLE `tkt_team_order_detail`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `department_id` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
  `order_id` bigint(20) NOT NULL COMMENT '订单id',
  `team_order_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子订单号',
  `order_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `ticket_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '票种名称',
  `ticket_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '票种编码',
  `ticket_price` bigint(20) NOT NULL COMMENT '票种价格',
  `team_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '团队名称',
  `leader_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '领队人名称',
  `leader_phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '领队人手机号',
  `leader_certificate_type` tinyint(4) NOT NULL COMMENT '证件类型: 1:身份证, 2:护照, 3:港澳通行证',
  `leader_certificate_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '证件号码',
  `visit_purpose` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '参观目的',
  `status` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单明细状态: 0:未使用, 1:未核销, 2:已核销, 3:已过期',
  `qr_code_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '二维码url',
  `reserve_date` datetime(0) NOT NULL COMMENT '预约日期',
  `time_period_id` bigint(20) NOT NULL COMMENT '分时场次id',
  `time_period_start` time(0) NULL DEFAULT NULL COMMENT '分时场次开始时间',
  `time_period_end` time(0) NULL DEFAULT NULL COMMENT '分时场次结束时间',
  `write_off_date` datetime(0) NULL DEFAULT NULL COMMENT '核销日期',
  `write_off_count` tinyint(4) NOT NULL DEFAULT 0 COMMENT '核销次数',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `writeoff_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销人员姓名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '团队订单明细信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `tkt_ticket_channel_pool`;
CREATE TABLE `tkt_ticket_channel_pool`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `department_id` bigint(20) NOT NULL COMMENT '部门id',
  `ticket_id` bigint(20) NOT NULL COMMENT '票种id',
  `ticket_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '票种名称',
  `ticket_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '票种编码',
  `ticket_type` tinyint(4) UNSIGNED NOT NULL COMMENT '票种类型: 0:免费票, 1:收费票',
  `ticket_price` bigint(20) NOT NULL COMMENT '票种价格',
  `ticket_status` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '票种状态: 0:禁用, 1:启用',
  `reserve_notice` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预约须知',
  `ticket_remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '票种备注',
  `channel_type` tinyint(4) UNSIGNED NOT NULL COMMENT '投放渠道类型:: 1:现场窗口, 2:微信小程序',
  `put_status` tinyint(4) UNSIGNED NOT NULL COMMENT '投放渠道状态: 0:未开始, 1:投放中, 2:已停止',
  `put_date` datetime(0) NULL DEFAULT NULL COMMENT '投放日期',
  `put_valid_type` tinyint(4) UNSIGNED NOT NULL COMMENT '渠道有效期类型: 0:长期有效, 1:限时有效',
  `put_start_date` datetime(0) NULL DEFAULT NULL COMMENT '开始日期',
  `put_end_date` datetime(0) NULL DEFAULT NULL COMMENT '截止日期',
  `put_rule_config` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '投放渠道规则配置',
  `put_remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '投放渠道备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '票种投放渠道信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `tkt_ticket_pool`;
CREATE TABLE `tkt_ticket_pool`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `department_id` bigint(20) NOT NULL COMMENT '部门id',
  `ticket_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '票种名称',
  `ticket_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '票种编码',
  `ticket_type` tinyint(4) UNSIGNED NOT NULL COMMENT '票种类型: 0:免费票, 1:收费票',
  `ticket_price` bigint(20) NOT NULL COMMENT '票种价格',
  `status` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '票种状态: 0:禁用, 1:启用, 2:注销',
  `reserve_notice` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预约须知',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_delete` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除: 0:否, 1:是',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '票种池信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `tkt_ticket_reserve_rule_base`;
CREATE TABLE `tkt_ticket_reserve_rule_base`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `department_id` bigint(20) NOT NULL COMMENT '部门id',
  `ticket_id` bigint(20) NULL DEFAULT NULL COMMENT '票种id',
  `ticket_channel_id` bigint(20) NULL DEFAULT NULL COMMENT '票种投放渠道id',
  `channel_type` tinyint(4) UNSIGNED NOT NULL COMMENT '投放渠道预约规则类型: 1:现场窗口, 2:微信小程序',
  `is_today` tinyint(4) UNSIGNED NOT NULL COMMENT '可预约当天: 0:否, 1:是',
  `today_stop_time` time(0) NULL DEFAULT NULL COMMENT '预约当天截止时间',
  `advance_future_day` bigint(20) UNSIGNED NOT NULL COMMENT '可提前预约未来几天',
  `single_limit` tinyint(4) UNSIGNED NOT NULL COMMENT '单次最多预约人数(仅限制个人预约类型)',
  `certificate_limit` tinyint(4) UNSIGNED NOT NULL COMMENT '一证件号一天最多预约次数',
  `id_card` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '身份证: 0:否, 1:是',
  `passport` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '护照: 0:否, 1:是',
  `hk_mac_permit` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '港澳居民居住证: 0:否, 1:是',
  `time_limit_type` tinyint(4) UNSIGNED NULL DEFAULT 0 COMMENT '分时限制类型: 0:不限, 1:按工作日/法定节假日限制',
  `is_team_reserve` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '团队预约: 0:否, 1:是',
  `reserv_rule_config` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预约规则备注',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '投放渠道备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '预约规则基础信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `tkt_ticket_reserve_rule_team`;
CREATE TABLE `tkt_ticket_reserve_rule_team`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `department_id` bigint(20) NOT NULL COMMENT '部门id',
  `reserve_rule_base_id` bigint(20) NOT NULL COMMENT '票种id',
  `advance_day` bigint(20) UNSIGNED NOT NULL COMMENT '需要提前几天预约',
  `upper_limit` bigint(20) NOT NULL DEFAULT -1 COMMENT '人数限制上限, -1:无限制',
  `lower_limit` bigint(20) NOT NULL DEFAULT -1 COMMENT '人数限制下限, -1:无限制',
  `register_require` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '登记要求: 0:预约时仅登记领队人信息，无需填写团队内每个人的信息, 1:预约时需登记全部入场人员信息（姓名、身份证、手机号）',
  `reserv_rule_config` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '投放渠道团队预约规则配置',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '团队预约规则备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '团队预约规则信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `tkt_ticket_write_off_rule`;
CREATE TABLE `tkt_ticket_write_off_rule`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `department_id` bigint(20) NOT NULL COMMENT '部门id',
  `ticket_id` bigint(20) NULL DEFAULT NULL COMMENT '票种id',
  `ticket_channel_id` bigint(20) NULL DEFAULT NULL COMMENT '票种投放渠道id',
  `channel_type` tinyint(4) UNSIGNED NOT NULL COMMENT '投放渠道预约规则类型: 0:微信小程序, 1:现场窗口',
  `is_today_cross` tinyint(4) UNSIGNED NOT NULL COMMENT '当天跨场次可核销: 0:否, 1:是',
  `no_reserve` tinyint(4) UNSIGNED NOT NULL COMMENT '免预约核销: 0:否, 1:是',
  `today_stop_time` time(0) NOT NULL COMMENT '核销今天截止时间',
  `write_off_limit` bigint(20) NOT NULL DEFAULT -1 COMMENT '核销限制上限, -1:无限制',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销规则备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '核销规则信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `tkt_ticket_write_off_user`;
CREATE TABLE `tkt_ticket_write_off_user`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户id',
  `department_id` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名称',
  `real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户真实名字',
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户手机号',
  `type` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户类型: 0:内部用户, 1:外部用户',
  `account_status` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '账号状态: 0:禁用, 1:启用',
  `wechat_bind_status` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '微信绑定状态: 0:未绑定, 1:已绑定',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '核销用户信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `tkt_time_period`;
CREATE TABLE `tkt_time_period`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `department_id` bigint(20) NOT NULL COMMENT '部门id',
  `biz_id` bigint(20) NOT NULL COMMENT '业务id',
  `biz_type` tinyint(4) NOT NULL COMMENT '业务类型: 1:微信小程序, 2:现场窗口',
  `time_start` time(0) NOT NULL COMMENT '开始时间',
  `time_end` time(0) NOT NULL COMMENT '结束时间',
  `upper_limit` bigint(20) NOT NULL DEFAULT -1 COMMENT '人数限制上限, -1:无限制',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分时场次备注',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分时场次信息表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `tkt_wechat_venue`;
CREATE TABLE `tkt_wechat_venue`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `company_id` bigint(20) NOT NULL COMMENT '公司id',
  `department_id` bigint(20) NOT NULL COMMENT '部门id',
  `ticket_channel_id` bigint(20) NULL DEFAULT NULL COMMENT '票种投放渠道id',
  `channel_type` tinyint(4) UNSIGNED NOT NULL COMMENT '投放渠道预约规则类型: 1:现场窗口, 2:微信小程序',
  `venue_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '场馆名称',
  `venue_phone` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `close_day` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '闭馆日',
  `is_holidays_postpone` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '法定节假日是否顺延: 0:否, 1:是',
  `is_temporary_close` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否临时闭馆: 0:否, 1:是',
  `temporary_close_start_date` datetime(0) NULL DEFAULT NULL COMMENT '临时闭馆开始日期',
  `temporary_close_end_date` datetime(0) NULL DEFAULT NULL COMMENT '临时闭馆结束日期',
  `temporary_close_notice` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '临时闭馆公告',
  `open_time` time(0) NOT NULL COMMENT '开馆时间',
  `close_time` time(0) NOT NULL COMMENT '闭馆时间',
  `stop_time` time(0) NOT NULL COMMENT '停止入馆时间',
  `ad_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市编码',
  `city_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市名称',
  `ad_code_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市编码路径',
  `city_name_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市名称路径',
  `address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `notice` varchar(8192) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '参观通知',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `document_id` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件ids',
  `creator` bigint(20) NOT NULL COMMENT '创建人',
  `modifier` bigint(20) NOT NULL COMMENT '修改人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `venue_lng` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '场馆经度',
  `venue_lat` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '场馆纬度',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '场馆信息表' ROW_FORMAT = Dynamic;

