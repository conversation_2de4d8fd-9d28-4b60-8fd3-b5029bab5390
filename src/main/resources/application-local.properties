# mysql config
spring.datasource.druid.name=digital-museum
spring.datasource.druid.url=*****************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.druid.username=root
spring.datasource.druid.password=123456
spring.datasource.druid.initial-size=5
spring.datasource.druid.maximum-pool-size=100
spring.datasource.druid.max-wait=60000
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-active=20
spring.datasource.druid.validation-query=SELECT 1
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-while-return=true
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictableIdle-time-millis=300000
spring.datasource.druid.connection-init-sqls=set names utf8mb4;
spring.datasource.druid.filters=stat,slf4j
# redis config
#Redis默认使用库
spring.redis.database=0
#Redis本地服务器地址
spring.redis.host=localhost
#Redis服务器端口,默认为6379
spring.redis.port=6379
#Redis服务器连接密码，默认为空，若有设置按设置的来
#spring.redis.password=
#Redis超时时间
spring.redis.timeout=1000
#连接池最大连接数，若为负责则表示没有任何限制
spring.redis.lettuce.pool.max-active=8
#连接池最大阻塞等待时间，若为负责则表示没有任何限制
spring.redis.lettuce.pool.max-wait=5000
#连接池中的最大空闲连接
spring.redis.lettuce.pool.max-idle=8
#连接池中的最小空闲连接
spring.redis.lettuce.pool.min-idle=0
logging.file.path=/data/logs/${spring.application.name}
logging.level.root=info
logging.level.sql=debug
#file url
file.base.url=/data/file/${spring.application.name}
#minio
#外网访问的minio基础路径
minio.visitUrl=http://*************:9000
#内网访问的minio基础路径
minio.endpoint=http://*************:9000
minio.bucketName=local-museum
minio.accessKey=owHputeEt38mwaQl
minio.secretKey=DCh4qNlNmfrcZ01Xk2sZnb3Oi42UER5w
minio.delDirectory=delete
# multipart
spring.servlet.multipart.max-file-size=-1
spring.servlet.multipart.max-request-size=-1

# 配置 Sa-Token 单独使用的 Redis 连接
# Redis数据库索引（默认为0）
sa-token.alone-redis.database=1
# Redis服务器地址
sa-token.alone-redis.host=localhost
# Redis服务器连接端口
sa-token.alone-redis.port=6379
# Redis服务器连接密码（默认为空）
#sa-token.alone-redis.password=
# 连接超时时间
sa-token.alone-redis.timeout=10s
#连接池最大连接数，若为负责则表示没有任何限制
sa-token.alone-redis.lettuce.pool.max-active=8
#连接池最大阻塞等待时间，若为负责则表示没有任何限制
sa-token.alone-redis.lettuce.pool.max-wait=5000
#连接池中的最大空闲连接
sa-token.alone-redis.lettuce.pool.max-idle=8
#连接池中的最小空闲连接
sa-token.alone-redis.lettuce.pool.min-idle=0

md5.salt=das123456
aes.passkey=das123456

md5.digester.salt=123456

data.aes.key=123456

# ticket http
ticket.http.protocol=http
ticket.http.ipAddress=localhost:7107
ticket.http.timeout=5000

# 三维模型虚拟地址
ftp.uploadConf.ftpUrl=/wbResource/model/
# 三维模型真实地址
ftp.uploadConf.ftpPath=E:/model/


# 本省的身份证开头配置
system.location.adCode=220
# 静态资源访问路径
system.static.resource.url=http://*************:8085

# 导出凭证word模板存放路径
exportWord.inFilePath=/data/template/
# 导出凭证word生成后临时存放路径
exportWord.outFilePath=/data/outFile/

# 温湿度上报间隔(默认3小时，单位分钟，上报允许2分钟延迟)
temperature.time.interval=182

# 伪满皇宫闸机数据获取地址
weiman.ticket.url=https://index.wmhg.com.cn/api/get_id_card_list

# 全景地址配置
quanxi.url=https://quanxi.diaolongyun.com?uniqueCode=

# 精创温湿度设备参数
jc.url=https://www.i-elitech.net
jc.keyId=06838413
jc.keySecret=BNSFgRRITiTBpGGiXmcm
jc.userName=diaolongyun
jc.password=diaolong6688*
